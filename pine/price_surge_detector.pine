//@version=6
// ╔══════════════════════════════════════════════════════════════════════════════
// ║ 价格突破检测指标
// ║ 功能：检测5分钟K线涨幅超过10%后的形态
// 1. 在10%涨幅的阳线之后
// 2. 出现1-4根连续阴线
// 3. 然后出现阳线止跌
// 4. 从突破K线位置(surge_bar)到当前K线画一条蓝色水平线
// ║ 支持连续多根10%阳线，自动重置追踪
// ║ 作者：Claude AI（修改自）
// ║ 最后更新：2025-06-14 23:41:00
// ╚══════════════════════════════════════════════════════════════════════════════

// 定义指标名称和显示设置
indicator("价格突破检测", overlay=true)

// 时间周期检查
// 由于策略设计仅适用于5分钟图表，此处进行强制检查
if timeframe.period != "5"
    runtime.error("此指标只能在5分钟图表上运行")

// ═══════════════════════════════════════════════════════════════════════════════
// 基础变量定义
// ═══════════════════════════════════════════════════════════════════════════════

// 计算当前K线相对于前一根K线的价格变化百分比
price_change_percent = ((close - close[1]) / close[1]) * 100
// 判断当前K线是否为阴线（收盘价低于开盘价）
is_down_candle = close < open
// 判断当前K线是否为阳线（收盘价高于开盘价）
is_up_candle = close > open

// ═══════════════════════════════════════════════════════════════════════════════
// 状态变量定义 - 使用var关键字确保变量在图表重新计算时保持状态
// ═══════════════════════════════════════════════════════════════════════════════

var float surge_price = na     // 突破阳线的开盘价
var int surge_bar = na         // 突破阳线的bar_index
var float surge_low = na       // 突破阳线的最低价（新增）
var int down_count = 0         // 连续阴线数
// 标记是否正在追踪形态
var bool in_tracking = false
// 标记是否在等待第一根阴线出现
var bool waiting_down = false

// ═══════════════════════════════════════════════════════════════════════════════
// 主要逻辑实现
// ═══════════════════════════════════════════════════════════════════════════════

// 检测突破条件：当前K线涨幅超过10%
// 无论是否在追踪状态，只要出现新的突破，就重新开始追踪
if price_change_percent >= 10 and not in_tracking
    // 记录突破时的开盘价作为参考价格
    surge_price := open
    // 记录突破K线的位置
    surge_bar := bar_index
    surge_low := low               // 记录该阳线最低价
    // 重置阴线计数
    down_count := 0
    // 激活追踪状态
    in_tracking := true
    // 设置等待阴线标记
    waiting_down := true
    // 可选：在突破位置添加标签
    // label.new(bar_index, high + (high * 0.002), "突破K线\n涨幅: " + str.tostring(price_change_percent) + "%", 
    //          color=color.green, style=label.style_label_down)

// 形态追踪逻辑
if in_tracking
    if waiting_down
        // 跳过突破K线，从下一根K线开始计数
        waiting_down := false
    else
        if is_down_candle
            // 新增：若当前K线跌破10%阳线最低价，形态失效
            if low < surge_low
                surge_price := na
                surge_bar := na
                surge_low := na
                down_count := 0
                in_tracking := false
                waiting_down := false
            else
                down_count := down_count + 1
                if down_count > 4
                    surge_price := na
                    surge_bar := na
                    surge_low := na
                    down_count := 0
                    in_tracking := false
                    waiting_down := false
        else if is_up_candle
            if down_count >= 1 and down_count <= 4
                // 出现止跌阳线，且阴线数量在1-4根之间，形态有效
                // 绘制水平线标记形态
                line.new(surge_bar, surge_price, bar_index, surge_price, 
                         color=color.rgb(170, 179, 249), width=5, style=line.style_dashed)
                // 可选：添加形态完成标签
                // label.new(bar_index, surge_price, 
                //     text="✓ 有效形态\n连续" + str.tostring(down_count) + "根阴线",
                //     color=color.red, textcolor=color.white,
                //     style=label.style_label_down, size=size.large)
            // 形态完成或失效，重置所有状态变量
            surge_price := na
            surge_bar := na
            down_count := 0
            in_tracking := false
            waiting_down := false