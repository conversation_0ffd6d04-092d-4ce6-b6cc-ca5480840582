//@version=6
indicator("30天日线数据", overlay=true)

// 自动适配历史数据长度，避免新股数据不足导致越界
maxAvailable = bar_index + 1
lookback = math.min(61, maxAvailable)
[openArr, closeArr, volArr, timeArr] = request.security(syminfo.tickerid, "1D", [open, close, volume, time])

// 获取当前K线的日期（年月日）
curYear  = year(time)
curMonth = month(time)
curDay   = dayofmonth(time)

// 判断是否为新的一天
isNewDay = na(curYear[1]) or curYear != curYear[1] or curMonth != curMonth[1] or curDay != curDay[1]

// 累加今日成交量
var float todayVolume = na
todayVolume := isNewDay ? volume : nz(todayVolume[1]) + volume

// 存储需要画线的索引
var int[] idxArray = array.new_int(0)
array.clear(idxArray)

// 找出需要画线的日线索引
var string allDates = ""
fuck = 0.0
var line[] myLines = array.new_line()
var label[] myLabels = array.new_label()

if barstate.islast
    // 删除所有旧的line
    if array.size(myLines) > 0
        for i = 0 to array.size(myLines) - 1
            // 删除图表上的线对象
            line.delete(array.get(myLines, i))
        // 清空数组
        array.clear(myLines)
    // 删除所有旧的label
    if array.size(myLabels) > 0
        for i = 0 to array.size(myLabels) - 1
            label.delete(array.get(myLabels, i))
        array.clear(myLabels)

    for i = 1 to lookback - 1
        // 判断该条数据是否是"今天"的
        isToday = year(timeArr[i]) == year(time) and 
                month(timeArr[i]) == month(time) and 
                dayofmonth(timeArr[i]) == dayofmonth(time)

        if not isToday and volArr[i] > todayVolume and volArr[i] != 0
        // 索引从2开始，已经跳过了当天数据
        // if not na(volArr[i]) and not na(timeArr[i]) and volArr[i] > todayVolume and volArr[i] != 0
            arrow = ' ⬆️'
            if openArr[i] > closeArr[i]
                arrow := ' ⬇️'
            t = str.tostring(year(timeArr[i])) + "-" + (month(timeArr[i]) < 10 ? "0" + str.tostring(month(timeArr[i])) : str.tostring(month(timeArr[i]))) + "-" + (dayofmonth(timeArr[i]) < 10 ? "0" + str.tostring(dayofmonth(timeArr[i])) : str.tostring(dayofmonth(timeArr[i])))
            allDates := allDates + (allDates == "" ? "" : ", ") + t
            // if openArr[i] > close
            line_open = line.new(x1=bar_index - 290, y1=openArr[i], x2=bar_index + 2, y2=openArr[i], xloc=xloc.bar_index, color=color.white, width=1, style=line.style_dotted)
            label_open = label.new(bar_index + 2, openArr[i], str.tostring(openArr[i]) + '开盘价' + t + arrow, color=color.blue, style=label.style_label_left, textcolor=color.white, size=size.tiny)
            array.push(myLines, line_open)
            array.push(myLabels, label_open)
            // if closeArr[i] > close
            line_close = line.new(x1=bar_index - 290, y1=closeArr[i], x2=bar_index + 2, y2=closeArr[i], xloc=xloc.bar_index, color=color.white, width=1, style=line.style_dotted)
            label_close = label.new(bar_index + 2, closeArr[i], str.tostring(closeArr[i]) + "收盘价" + t + arrow, color=color.red, style=label.style_label_left, textcolor=color.white, size=size.tiny)
            array.push(myLines, line_close)
            array.push(myLabels, label_close)
// var tbl = table.new(position.top_center, 1, 1)
// table.cell(tbl, 0, 0, str.tostring(fuck) + ' ' +str.tostring(todayVolume), text_color=color.white, bgcolor=color.blue)
