//@version=6
indicator("金色10秒K线-修正版", overlay=true, max_bars_back=500)

// 1. 获取当前10秒K线属性
high10  = high
close10 = close
open10  = open

// 2. 获取前1分钟K线数据
prev_1m_open = 0.0
prev_1m_close = 0.0
prev_1m_high = 0.0
prev_1m_time_close = 0
prev_1m_time = 0

// 仅当K线是实时更新的最新K线时为true，在回放模式中始终为false
if barstate.isrealtime
    [prev2_1m_o, prev2_1m_c] = request.security(syminfo.tickerid, "1", [open[1], close[1]], lookahead=barmerge.lookahead_off)
    prev_1m_open := prev2_1m_o
    prev_1m_close := prev2_1m_c
else
    if second(time_close) == 0
    // 最后10秒获取的当前1分钟k线数据(此时10秒周期获取的秒数为0)
        [prev2_1m_o, prev2_1m_c] = request.security(syminfo.tickerid, "1", [open[1], close[1]], lookahead=barmerge.lookahead_off)
        prev_1m_open := prev2_1m_o
        prev_1m_close := prev2_1m_c
        // prev_1m_high := prev2_1m_h
// 测试用
// prev_1m_time_close := prev2_1m_tc
// prev_1m_time := prev2_1m_t
    else
//     // 获取前一根1分钟K线的开盘价、收盘价、最高价
        [prev_1m_o, prev_1m_c] = request.security(syminfo.tickerid, "1", [open[0], close[0]], lookahead=barmerge.lookahead_off)
        prev_1m_open := prev_1m_o
        prev_1m_close := prev_1m_c
        // prev_1m_high := prev_1m_h
//     // 测试用
//     prev_1m_time_close := prev_1m_tc
//     prev_1m_time := prev_1m_t

// 3. 仅在K线确认时判断
var bool useGold = false

// 10秒K线 阳线
isBull10 = close10 > open10

// 前1分钟K线 阴线
isBear1 = prev_1m_close < prev_1m_open

// 10秒K线收盘价高于前1分钟K线开盘价
isHigher = close10 > prev_1m_open

// isBull10_flg = 0
// isBear1_flg = 0
// isHigher_flg = 0
// if isBull10
//     isBull10_flg := 1
// if isBear1
//     isBear1_flg := 1
// if isHigher
//     isHigher_flg := 1
// 直接提取秒数（int类型）
// secondsInt = second(oneMinTime)
// plot(minute(prev_1m_time_close), "1min time close", color=color.blue, linewidth=2)
// plot(minute(prev_1m_time), "1min time", color=color.rgb(33, 243, 79), linewidth=2)
// plot(prev_1m_open, "1min open", color=color.green, linewidth=2, display=display.pane)
// plot(close10, "1min open", color=color.red, linewidth=2, display=display.pane)
// plot(prev_1m_close, "1min close", color=color.red, linewidth=2)
// plot(isBull10_flg, "1min open", color=color.green, linewidth=2, display=display.pane)
// plot(isBear1_flg, "1min close", color=color.red, linewidth=2, display=display.pane)
// plot(isHigher_flg, "1min close", color=color.blue, linewidth=2, display=display.pane)

// 只有在当前10秒K线是阳线，且前1分钟K线是阴线，且当前收盘价高于前1分钟K线开盘价时，才显示金色
useGold := isBull10 and isBear1 and isHigher

// 设置当前K线颜色为金色
goldColor = color.rgb(255, 215, 0)
barcolor(useGold ? goldColor : na)

// ============ 紫色 K线 逻辑修正版（含十字线"等待下一根"功能） ============
var float goldClose = na
var int   goldMinTime = na
var bool  hasDippedBelow = false
var bool  waitingForNextPurple = false
var bool  usePurple = false
var bool  purpleLocked = false
// 删除未使用变量 waitingForNextPurple_flg
var bool  lastWasDojiPurple = false  // 记录是否为"十字紫色K"

isOpenCloseEqualish(_o, _c) =>
    math.abs(_o - _c) <= syminfo.mintick * 4

curMinTime = time("1")

// 每次出现金色K线就初始化所有状态
if useGold
    goldClose := close
    goldMinTime := curMinTime
    hasDippedBelow := false
    waitingForNextPurple := false
    purpleLocked := false
    lastWasDojiPurple := false
    usePurple := false
    // label.new(bar_index, high, "金", style=label.style_label_down, color=color.yellow)

else if not na(goldClose) and not purpleLocked
    if close < goldClose
        hasDippedBelow := true

    // 正在等待"十字紫色K线"的跟随上涨K线
    if waitingForNextPurple
        // 跟随K线：不能与金色K线在同一分钟，且上涨且非十字
        if close > goldClose and curMinTime != goldMinTime and not isOpenCloseEqualish(open, close)
            usePurple := true
            purpleLocked := true
            waitingForNextPurple := false
            lastWasDojiPurple := false  // 重置十字状态
            goldClose := na
            // label.new(bar_index, high, "紫-跟随", color=color.purple, textcolor=color.white)
        else
            usePurple := false
    // 当前满足紫色K线条件
    else if close > open and hasDippedBelow and close > goldClose and curMinTime != goldMinTime
        usePurple := true
        lastWasDojiPurple := isOpenCloseEqualish(open, close)
        purpleLocked := not lastWasDojiPurple  // 十字就不锁死
        waitingForNextPurple := lastWasDojiPurple  // 是十字则继续等下一根
        // label.new(bar_index, high, lastWasDojiPurple ? "紫-十字" : "紫", color=color.purple, textcolor=color.white)
    else
        usePurple := false
else
    usePurple := false
    // 保持waitingForNextPurple和lastWasDojiPurple状态不变，因为可能在等待跟随K线

// if waitingForNextPurple
//     waitingForNextPurple_flg := 1
// else
//     waitingForNextPurple_flg := 0
// plot(waitingForNextPurple_flg, "waitingForNextPurple_flg", color=color.blue, linewidth=2)

// 设置粉紫色K线颜色
purpleColor = color.rgb(191, 0, 255)
barcolor(usePurple ? purpleColor : na)

// ============ 成交量增量箭头标注功能 ============
// 预先计算过去20根K线（不含本根）的最大成交量
maxVol20 = ta.highest(volume[1], 20)
// 记录金色或紫色K线出现的bar_index
var int lastSpecialBar = na
if useGold or usePurple
    lastSpecialBar := bar_index

// 检查金色或紫色K线后3根K线（含本身）成交量增量
var bool showArrow = false
if not na(lastSpecialBar)
    // 包含本身在内的3根K线内检查
    barsSinceSpecial = bar_index - lastSpecialBar
    if barsSinceSpecial >= 0 and barsSinceSpecial <= 2
        // 判断当前K线成交量是否大于过去20根K线的最大成交量
        showArrow := volume > maxVol20
    else
        showArrow := false
else
    showArrow := false

// 标注向上小箭头
plotshape(showArrow, title="增量成交量", style=shape.arrowup, location=location.abovebar, color=color.lime, size=size.tiny, offset=0)