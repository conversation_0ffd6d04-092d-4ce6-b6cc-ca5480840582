// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © atoht9527

//@version=6
indicator("09:30后5根1分钟K线最高点 (考虑熔断)", overlay=true)

// 获取当前bar的年、月、日
currentYear  = year(time)
currentMonth = month(time)
currentDay   = dayofmonth(time)

// 生成当天09:30的时间戳，计算09:35的结束时间（5分钟后的时间）
sessionOpen = timestamp(currentYear, currentMonth, currentDay, 9, 30)
sessionEnd  = sessionOpen + 5 * 60 * 1000  // 5分钟后，即09:35

// 判断当前bar是否处于09:30至09:35之间
inFirst5 = time >= sessionOpen and time < sessionEnd

// 定义变量，用于累计前5根1分钟K线的最高价及计数
var float first5High = na
// var int count = 0
var line first5Line = na

// 判断新的一天，重置变量与删除前一天的线
isNewDay = ta.change(time("D")) != 0
if isNewDay
    first5High := na
    // count := 0
    if not na(first5Line)
        line.delete(first5Line)
        first5Line := na

// 在09:30后的前5分钟内累计数据
if inFirst5
    // count += 1
    first5High := na(first5High) ? high : math.max(first5High, high)

// 当达到09:35或是最后一根K线时，如果还没有画出线，则根据已有数据绘制水平线
if time >= sessionEnd and na(first5Line) and not na(first5High)
    first5Line := line.new(x1=bar_index, y1=first5High, x2=bar_index + 100, y2=first5High, extend=extend.right, color=color.red, width=2)

// 为方便观察，同时画出最高价的plot
plot(first5High, title="前5根1分钟K线最高价", color=color.red, style=plot.style_line, display=display.pane)