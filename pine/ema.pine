//@version=6
indicator(title="Moving Average Exponential", shorttitle="EMA", overlay=true, timeframe="", timeframe_gaps=false)
// len0 = input.int(9, minval=1, title="Length")
// src0 = input(open, title="Source")
// offset0 = input.int(title="Offset", defval=0, minval=-500, maxval=500)
// out0 = ta.ema(src0, len0)
// plot(out0, title="EMA9", color=color.orange, offset=offset0)

// 设置参数
len0 = 9  // 相当于富途中的 P1 值
src0 = open  // 使用开盘价计算
// 计算 EMA
ema_open = ta.ema(src0, len0)
// 绘制 EMA 线
plot(ema_open, title="EMA(OPEN, 9)", color=color.orange, linewidth=1, display=display.pane)

len = input.int(20, minval=1, title="Length")
src = input(open, title="Source")
offset = input.int(title="Offset", defval=0, minval=-500, maxval=500)
out = ta.ema(src, len)
plot(out, title="EMA20", color=#f321e5, offset=offset, display=display.pane)

len2 = input.int(50, minval=1, title="Length2")
src2 = input(open, title="Source2")
offset2 = input.int(title="Offset2", defval=0, minval=-500, maxval=500)
out2 = ta.ema(src2, len2)
plot(out2, title="EMA50", color=#264cd3, linewidth=2, offset=offset2, display=display.pane)

len3 = input.int(120, minval=1, title="Length3")
src3 = input(open, title="Source3")
offset3 = input.int(title="Offset3", defval=0, minval=-500, maxval=500)
out3 = ta.ema(src3, len3)
plot(out3, title="EMA120", color=#41f321, linewidth=3, offset=offset3, display=display.pane)

ma(source, length, type) =>
    switch type
        "SMA" => ta.sma(source, length)
        "EMA" => ta.ema(source, length)
        "SMMA (RMA)" => ta.rma(source, length)
        "WMA" => ta.wma(source, length)
        "VWMA" => ta.vwma(source, length)

typeMA = input.string(title = "Method", defval = "SMA", options=["SMA", "EMA", "SMMA (RMA)", "WMA", "VWMA"], group="Smoothing")
smoothingLength = input.int(title = "Length", defval = 5, minval = 1, maxval = 100, group="Smoothing")

smoothingLine = ma(out, smoothingLength, typeMA)
plot(smoothingLine, title="Smoothing Line", color=#f37f20, offset=offset, display=display.none)



// ma2(source, length, type) =>
//     switch type
//         "SMA" => ta.sma(source, length)
//         "EMA" => ta.ema(source, length)
//         "SMMA (RMA)" => ta.rma(source, length)
//         "WMA" => ta.wma(source, length)
//         "VWMA" => ta.vwma(source, length)

// typeMA2 = input.string(title = "Method2", defval = "SMA", options=["SMA", "EMA", "SMMA (RMA)", "WMA", "VWMA"], group="Smoothing2")
// smoothingLength2 = input.int(title = "Length2", defval = 5, minval = 1, maxval = 100, group="Smoothing2")

// smoothingLine2 = ma2(out2, smoothingLength2, typeMA2)
// plot(smoothingLine2, title="Smoothing Line2", color=#f37f20, offset=offset2, display=display.none)