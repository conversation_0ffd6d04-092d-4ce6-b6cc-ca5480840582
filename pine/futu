// ╔══════════════════════════════════════════════════════════════════════════════
// ║ 价格突破检测指标
// ║ 功能：检测5分钟K线涨幅超过10%后的形态
// 1. 在10%涨幅的阳线之后
// 2. 出现1-4根连续阴线
// 3. 然后出现阳线止跌
// 4. 从突破K线位置(surge_bar)到当前K线画一条蓝色水平线
// ║ 支持连续多根10%阳线，自动重置追踪
// ║ 作者：Claude AI（修改自）
// ║ 最后更新：2025-06-14 23:41:00
// ╚══════════════════════════════════════════════════════════════════════════════

// 定义指标名称和显示设置
indicator("价格突变检测器", overlay=true)

// 参数设置
lookback = input.int(60, "回溯天数", minval=1, maxval=250)
surge_percent = input.float(5.0, "突变百分比阈值", minval=0.5, maxval=20.0, step=0.5)
vol_factor = input.float(1.5, "成交量倍数阈值", minval=1.0, maxval=5.0, step=0.1)
show_labels = input.bool(true, "显示标签", inline="labels")
show_lines = input.bool(true, "显示价格线", inline="lines")

// 获取日线数据
[openArr, highArr, lowArr, closeArr, volArr, timeArr] = request.security(syminfo.tickerid, "1D", [open, high, low, close, volume, time])

// 获取当前K线的日期（年月日）
curYear = year(time)
curMonth = month(time)
curDay = dayofmonth(time)

// 判断是否为新的一天
isNewDay = na(curYear[1]) or curYear != curYear[1] or curMonth != curMonth[1] or curDay != curDay[1]

// 累加今日成交量
var float todayVolume = na
todayVolume := isNewDay ? volume : nz(todayVolume[1]) + volume

// 存储线条和标签
var line[] myLines = array.new_line()
var label[] myLabels = array.new_label()

// 计算突变事件
if barstate.islast
    // 删除所有旧的线条
    if array.size(myLines) > 0
        for i = 0 to array.size(myLines) - 1
            line.delete(array.get(myLines, i))
        array.clear(myLines)
    
    // 删除所有旧的标签
    if array.size(myLabels) > 0
        for i = 0 to array.size(myLabels) - 1
            label.delete(array.get(myLabels, i))
        array.clear(myLabels)

    for i = 1 to lookback - 1
        // 判断该条数据是否是"今天"的
        isToday = year(timeArr[i]) == year(time) and 
                month(timeArr[i]) == month(time) and 
                dayofmonth(timeArr[i]) == dayofmonth(time)

        if not isToday and not na(closeArr[i]) and not na(closeArr[i+1])
            // 计算价格变动百分比
            price_change_percent = math.abs((closeArr[i] - closeArr[i+1]) / closeArr[i+1] * 100)
            
            // 计算成交量比例
            vol_ratio = not na(volArr[i]) and not na(volArr[i+1]) and volArr[i+1] > 0 ? volArr[i] / volArr[i+1] : 0
            
            // 检测价格突变
            if price_change_percent >= surge_percent and vol_ratio >= vol_factor
                // 格式化日期
                t = str.tostring(year(timeArr[i])) + "-" + 
                    (month(timeArr[i]) < 10 ? "0" + str.tostring(month(timeArr[i])) : str.tostring(month(timeArr[i]))) + "-" + 
                    (dayofmonth(timeArr[i]) < 10 ? "0" + str.tostring(dayofmonth(timeArr[i])) : str.tostring(dayofmonth(timeArr[i])))
                
                // 确定涨跌方向
                is_up = closeArr[i] > closeArr[i+1]
                arrow = is_up ? ' ⬆️' : ' ⬇️'
                surge_color = is_up ? color.green : color.red
                
                // 价格变动信息
                price_info = str.tostring(price_change_percent, "#.##") + "% " + 
                             (is_up ? "上涨" : "下跌") + 
                             " 成交量: " + str.tostring(vol_ratio, "#.##") + "倍"
                
                // 绘制价格线和标签
                if show_lines
                    // 绘制收盘价线
                    line_close = line.new(
                        x1=bar_index - 290, 
                        y1=closeArr[i], 
                        x2=bar_index + 2, 
                        y2=closeArr[i], 
                        xloc=xloc.bar_index, 
                        color=surge_color, 
                        width=2, 
                        style=line.style_dotted
                    )
                    array.push(myLines, line_close)
                
                if show_labels
                    // 添加标签
                    label_info = label.new(
                        x=bar_index + 2, 
                        y=closeArr[i], 
                        text=t + "\n" + str.tostring(closeArr[i]) + arrow + "\n" + price_info, 
                        color=surge_color, 
                        style=label.style_label_left, 
                        textcolor=color.white, 
                        size=size.small
                    )
                    array.push(myLabels, label_info)

// 创建表格显示统计信息
if barstate.islast
    var table stats = table.new(position.top_right, 2, 2, color.new(color.black, 70), color.white, 2, color.white, 12)
    
    // 计算检测到的突变事件数量
    surge_count = array.size(myLabels)
    
    // 更新表格
    table.cell(stats, 0, 0, "检测到的突变事件:", bgcolor=color.new(color.blue, 90))
    table.cell(stats, 1, 0, str.tostring(surge_count), bgcolor=color.new(color.blue, 90))
    table.cell(stats, 0, 1, "突变阈值:", bgcolor=color.new(color.gray, 90))
    table.cell(stats, 1, 1, str.tostring(surge_percent, "#.#") + "% / " + str.tostring(vol_factor, "#.#") + "倍", bgcolor=color.new(color.gray, 90))

// 添加警报条件
alertcondition(barstate.islast and array.size(myLabels) > 0, "价格突变检测", "检测到{{ticker}}的价格突变事件")