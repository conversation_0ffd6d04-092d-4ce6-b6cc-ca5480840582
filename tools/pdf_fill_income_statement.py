import pdfplumber
import PyPDF2
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from datetime import datetime
import re
import os

# 路径配置
BANK_PDF = '/Users/<USER>/Downloads/auじぶん銀行取引明細.pdf'
TEMPLATE_PDF = '/Users/<USER>/Downloads/収支の明細書.pdf'
OUTPUT_DIR = '/Users/<USER>/Downloads/'

# 1. 解析银行明细PDF，统计每月入金/出金总额
def extract_monthly_income_expense(pdf_path):
    monthly = {}
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            table = page.extract_table()
            if not table:
                continue
            # 查找表头
            header_idx = None
            for i, row in enumerate(table):
                if row and '取引日付' in row[0]:
                    header_idx = i
                    break
            if header_idx is None:
                continue
            for row in table[header_idx+1:]:
                if not row or not row[0] or not re.match(r'\d{4}/\d{2}/\d{2}', row[0]):
                    continue
                date_str = row[0]
                year, month, _ = date_str.split('/')
                ym = f'{year}/{month}'
                out_amt = row[2]
                in_amt = row[3]
                # 金额处理
                def parse_amount(s):
                    try:
                        return int(s.replace(',', '').replace('円', '').strip()) if s else 0
                    except:
                        return 0
                out_amt = parse_amount(out_amt)
                in_amt = parse_amount(in_amt)
                if ym not in monthly:
                    monthly[ym] = {'入金': 0, '出金': 0}
                monthly[ym]['入金'] += in_amt
                monthly[ym]['出金'] += out_amt
    return monthly

# 2. 解析目标PDF表格区域，确定填写坐标（参数化）
def get_table_positions(base_y=420, row_h=22, x_offset=0):
    positions = []
    for i in range(12):
        y = base_y - i * row_h
        positions.append({
            '年月': (65 + x_offset, y),
            '収入': (150 + x_offset, y),
            '支出': (250 + x_offset, y),
            '差額': (350 + x_offset, y),
            '備考': (450 + x_offset, y)
        })
    return positions

from PyPDF2 import PdfReader, PdfWriter
from reportlab.pdfgen.canvas import Canvas
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics

def fill_pdf(monthly_data, template_pdf, output_pdf, base_y=420, x_offset=0, font_size=12):
    reader = PdfReader(template_pdf)
    page = reader.pages[0]
    width = float(page.mediabox.width)
    height = float(page.mediabox.height)

    overlay_path = 'overlay.pdf'
    c = canvas.Canvas(overlay_path, pagesize=(width, height))
    try:
        pdfmetrics.registerFont(TTFont('HeiseiMin-W3', '/System/Library/Fonts/ヒラギノ明朝 ProN.ttc'))
        font_name = 'HeiseiMin-W3'
    except:
        font_name = 'Helvetica'
    c.setFont(font_name, font_size)

    positions = get_table_positions(base_y=base_y, row_h=22, x_offset=x_offset)
    months = sorted(monthly_data.keys())[-12:]
    for i, ym in enumerate(months):
        pos = positions[i]
        year, month = ym.split('/')
        year = int(year)
        month = int(month)
        reiwa = year - 2018 if year >= 2019 else year
        ym_str = f'令和{reiwa}年{month}月' if year >= 2019 else f'{year}年{month}月'
        c.drawString(pos['年月'][0], pos['年月'][1], ym_str)
        c.drawRightString(pos['収入'][0]+60, pos['収入'][1], f"{monthly_data[ym]['入金']:,} 円")
        c.drawRightString(pos['支出'][0]+60, pos['支出'][1], f"{monthly_data[ym]['出金']:,} 円")
        diff = monthly_data[ym]['入金'] - monthly_data[ym]['出金']
        c.drawRightString(pos['差額'][0]+60, pos['差額'][1], f"{diff:,} 円")
    c.save()

    writer = PdfWriter()
    with open(overlay_path, 'rb') as f_overlay:
        overlay_pdf = PdfReader(f_overlay)
        for i, page in enumerate(reader.pages):
            if i == 0:
                page.merge_page(overlay_pdf.pages[0])
            writer.add_page(page)
    with open(output_pdf, 'wb') as f_out:
        writer.write(f_out)
    os.remove(overlay_path)

if __name__ == '__main__':
    monthly = extract_monthly_income_expense(BANK_PDF)
    # 参数组合
    base_y_list = [420, 430, 440]
    x_offset_list = [0, 10]
    font_size_list = [12, 11]
    for base_y in base_y_list:
        for x_offset in x_offset_list:
            for font_size in font_size_list:
                out_name = f'収支の明細書-已填写-{base_y}-{x_offset}-{font_size}.pdf'
                out_path = os.path.join(OUTPUT_DIR, out_name)
                fill_pdf(monthly, TEMPLATE_PDF, out_path, base_y=base_y, x_offset=x_offset, font_size=font_size)
                print(f'已生成: {out_path}') 