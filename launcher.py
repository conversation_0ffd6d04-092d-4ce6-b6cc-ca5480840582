#!/usr/bin/env python3
"""
TradingView Lightweight Charts 启动器
支持多种启动模式
"""

import sys
import subprocess
import argparse
from pathlib import Path

def start_full_app():
    """启动完整应用 (包含NPM集成)
    
    启动所有组件：
    - Python后端服务器 + 数据获取
    - NPM前端开发服务器
    - GUI桌面应用界面
    """
    print("🚀 启动完整应用 (Python后端 + NPM前端 + GUI)")
    subprocess.run([sys.executable, "src/main.py"])

def start_npm_only():
    """仅启动NPM开发服务器
    
    只启动Vite开发服务器，适用于：
    - 前端功能开发和调试
    - 静态页面测试
    - 不需要后端数据的场景
    """
    print("📦 仅启动NPM开发服务器")
    subprocess.run(["npm", "run", "dev"])

def start_python_only():
    """仅启动Python后端
    
    只启动Flask Web服务器，包含：
    - 股票数据获取和处理
    - WebSocket实时推送
    - Web页面服务
    - 不启动GUI界面
    """
    print("🐍 仅启动Python后端服务器")
    subprocess.run([sys.executable, "start_with_web.py"])

def show_menu():
    """显示启动菜单"""
    print("\n" + "=" * 60)
    print("📊 TradingView Lightweight Charts 启动器")
    print("=" * 60)
    print("请选择启动模式:")
    print()
    print("1. 完整应用 (推荐)")
    print("   - Python后端 + 数据获取")
    print("   - NPM前端开发服务器") 
    print("   - GUI应用界面")
    print("   - 访问: http://localhost:3000/templates/index-npm.html")
    print()
    print("2. 仅NPM前端")
    print("   - 仅启动Vite开发服务器")
    print("   - 适用于前端开发和测试")
    print("   - 访问: http://localhost:3000/test-npm.html")
    print()
    print("3. 仅Python后端")
    print("   - 仅启动Flask服务器")
    print("   - 适用于Web版本开发")
    print("   - NPM版本: http://localhost:5001/npm")
    print("   - 原始版本: http://localhost:5001/")
    print()
    print("0. 退出")
    print("=" * 60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TradingView Lightweight Charts 启动器')
    parser.add_argument('--mode', choices=['full', 'npm', 'python'], 
                       help='启动模式: full(完整应用), npm(仅NPM), python(仅Python)')
    
    args = parser.parse_args()
    
    if args.mode:
        # 命令行模式
        if args.mode == 'full':
            start_full_app()
        elif args.mode == 'npm':
            start_npm_only()
        elif args.mode == 'python':
            start_python_only()
    else:
        # 交互式菜单
        while True:
            show_menu()
            try:
                choice = input("请输入选择 (0-3): ").strip()
                
                if choice == '1':
                    start_full_app()
                    break
                elif choice == '2':
                    start_npm_only()
                    break
                elif choice == '3':
                    start_python_only()
                    break
                elif choice == '0':
                    print("👋 再见!")
                    break
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
