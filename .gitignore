# --------------------
# Python & Whisper 项目 ignore 规则
# --------------------

# Node.js 依赖包
node_modules/

# 模型缓存（如 faster-whisper 的下载）
whisper_models/
*.bin

# Python 缓存
__pycache__/
*.py[cod]

# 虚拟环境
venv/
.env/
*.env
*.venv
stock_env/
*.egg-info/

# Jupyter/IPython 等临时文件
.ipynb_checkpoints/
*.ipynb

# MacOS 系统文件
.DS_Store
.AppleDouble
.LSOverride

# 日志/调试/临时输出
*.log
*.tmp
*.bak
*.swp

# VSCode/IDE 配置文件（可选）
.vscode/
.idea/

# 视频转录临时输出（音频/字幕）
*.wav
*.srt
audio_optimized.*
audio_fixed.*

# OCR结果目录
ocr_results/