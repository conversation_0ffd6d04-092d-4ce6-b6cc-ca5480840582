WITH 
-- 原逻辑：计算 first_live_data_time 后的日内涨幅
first_live_data_time AS (
    SELECT
        stock_code,
        date_,
        MIN(date_time) AS first_live_data_time
    FROM stock_trand.stock_price_ibkr
    WHERE live_data = 1
    GROUP BY stock_code, date_
),
price_after_first_live_data AS (
    SELECT
        sp.stock_code,
        sp.date_,
        sp.date_time,
        sp.Open,
        sp.High,
        sp.Low,
        sp.Close,
        sp.Volume,
        fld.first_live_data_time
    FROM stock_trand.stock_price_ibkr sp
    JOIN first_live_data_time fld
        ON sp.stock_code = fld.stock_code
        AND sp.date_ = fld.date_
    WHERE sp.date_time >= fld.first_live_data_time
),
max_high_after_first_live_data AS (
    SELECT
        stock_code,
        date_,
        first_live_data_time,
        MIN(CASE WHEN date_time = first_live_data_time THEN Open END) AS open_price,
        MAX(High) AS max_high
    FROM price_after_first_live_data
    GROUP BY stock_code, date_, first_live_data_time
),
intraday_return_after_first_live_data AS (
    SELECT
        stock_code,
        date_,
        first_live_data_time,
        open_price,
        max_high,
        ((max_high - open_price) / open_price) * 100 AS intraday_return
    FROM max_high_after_first_live_data
    WHERE ((max_high - open_price) / open_price) * 100 > 70
),
daily_volume AS (
    SELECT
        stock_code,
        date_,
        SUM(volume) AS total_volume
    FROM stock_trand.stock_price_ibkr
    GROUP BY stock_code, date_
),
filtered_stocks AS (
    SELECT
        ir.stock_code,
        ir.date_,
        ir.first_live_data_time,
        ir.intraday_return
    FROM intraday_return_after_first_live_data ir
    JOIN daily_volume dv
        ON ir.stock_code = dv.stock_code
        AND ir.date_ = dv.date_
    WHERE dv.total_volume > 30000000
),

-- 新增逻辑：计算三个自定义窗口指标（含 VWAP 范围判断）
fixed_time_window AS (
    SELECT
        stock_code,
        date_,
        window_id,
        -- 开盘价（窗口内第一个 Open）
        MIN(CASE WHEN date_time = min_date THEN Open END) AS open_5min,
        -- 收盘价（窗口内最后一个 Close）
        MAX(CASE WHEN date_time = max_date THEN Close END) AS close_5min,
        -- 最低价
        MIN(Low) AS min_low_5min,
        -- VWAP（从04:01:00开始到窗口结束时间）
        SUM( ((High + Low + Close)/3 * Volume ) ) / NULLIF(SUM(Volume), 0) AS vwap_5min
    FROM (
        SELECT
            *,
            -- 定义窗口标识符（1:09:36~09:40, 2:09:41~09:45, 3:09:46~09:50）
            CASE 
                WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:36:00' AND '09:40:00' THEN 1
                WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:41:00' AND '09:45:00' THEN 2
                WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:46:00' AND '09:50:00' THEN 3
            END AS window_id,
            -- 窗口内最早时间（用于开盘价）
            MIN(date_time) OVER (
                PARTITION BY stock_code, date_, 
                CASE 
                    WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:36:00' AND '09:40:00' THEN 1
                    WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:41:00' AND '09:45:00' THEN 2
                    WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:46:00' AND '09:50:00' THEN 3
                END
            ) AS min_date,
            -- 窗口内最晚时间（用于收盘价）
            MAX(date_time) OVER (
                PARTITION BY stock_code, date_, 
                CASE 
                    WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:36:00' AND '09:40:00' THEN 1
                    WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:41:00' AND '09:45:00' THEN 2
                    WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:46:00' AND '09:50:00' THEN 3
                END
            ) AS max_date,
            -- 窗口结束时间（用于VWAP范围）
            CASE 
                WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:36:00' AND '09:40:00' THEN '09:40:00'
                WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:41:00' AND '09:45:00' THEN '09:45:00'
                WHEN DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '09:46:00' AND '09:50:00' THEN '09:50:00'
            END AS window_end_time
        FROM stock_trand.stock_price_ibkr
        WHERE 
            DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '04:01:00' AND '09:50:00'  -- VWAP计算从04:01开始
    ) t
    WHERE 
        window_id IS NOT NULL
        AND DATE_FORMAT(date_time, '%H:%i:%s') BETWEEN '04:01:00' AND window_end_time
    GROUP BY stock_code, date_, window_id
),

-- 横向展开三个窗口的指标为列
fixed_time_pivot AS (
    SELECT
        stock_code,
        date_,
        -- 窗口1（09:36~09:40）
        MAX(CASE WHEN window_id = 1 THEN open_5min END) AS open_5min_1,
        MAX(CASE WHEN window_id = 1 THEN close_5min END) AS close_5min_1,
        MAX(CASE WHEN window_id = 1 THEN min_low_5min END) AS min_low_5min_1,
        MAX(CASE WHEN window_id = 1 THEN vwap_5min END) AS vwap_5min_1,
        -- 窗口2（09:41~09:45）
        MAX(CASE WHEN window_id = 2 THEN open_5min END) AS open_5min_2,
        MAX(CASE WHEN window_id = 2 THEN close_5min END) AS close_5min_2,
        MAX(CASE WHEN window_id = 2 THEN min_low_5min END) AS min_low_5min_2,
        MAX(CASE WHEN window_id = 2 THEN vwap_5min END) AS vwap_5min_2,
        -- 窗口3（09:46~09:50）
        MAX(CASE WHEN window_id = 3 THEN open_5min END) AS open_5min_3,
        MAX(CASE WHEN window_id = 3 THEN close_5min END) AS close_5min_3,
        MAX(CASE WHEN window_id = 3 THEN min_low_5min END) AS min_low_5min_3,
        MAX(CASE WHEN window_id = 3 THEN vwap_5min END) AS vwap_5min_3
    FROM fixed_time_window
    GROUP BY stock_code, date_
)

-- 最终结果：合并原字段并添加VWAP范围判断
SELECT
    fs.stock_code,
    fs.date_,
    fs.first_live_data_time,
    fs.intraday_return,
    -- 窗口1指标
    ftp.open_5min_1,
    ftp.close_5min_1,
    ftp.min_low_5min_1,
    ftp.vwap_5min_1,
    -- 判断VWAP是否在最低价和收盘价之间（窗口1）
    CASE 
        WHEN ftp.vwap_5min_1 BETWEEN ftp.min_low_5min_1 AND ftp.close_5min_1 THEN 'True'
        ELSE 'False'
    END AS vwap_in_range_1,
    -- 窗口2指标
    ftp.open_5min_2,
    ftp.close_5min_2,
    ftp.min_low_5min_2,
    ftp.vwap_5min_2,
    -- 判断VWAP是否在最低价和收盘价之间（窗口2）
    CASE 
        WHEN ftp.vwap_5min_2 BETWEEN ftp.min_low_5min_2 AND ftp.close_5min_2 THEN 'True'
        ELSE 'False'
    END AS vwap_in_range_2,
    -- 窗口3指标
    ftp.open_5min_3,
    ftp.close_5min_3,
    ftp.min_low_5min_3,
    ftp.vwap_5min_3,
    -- 判断VWAP是否在最低价和收盘价之间（窗口3）
    CASE 
        WHEN ftp.vwap_5min_3 BETWEEN ftp.min_low_5min_3 AND ftp.close_5min_3 THEN 'True'
        ELSE 'False'
    END AS vwap_in_range_3
FROM filtered_stocks fs
LEFT JOIN fixed_time_pivot ftp
    ON fs.stock_code = ftp.stock_code
    AND fs.date_ = ftp.date_;