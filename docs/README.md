# Stock_XXX 项目文档

## 📚 文档目录

这里包含了Stock_XXX项目的完整技术文档，帮助开发者快速理解和上手项目。

### 🚨 开发规则（必读）
- **[开发进度和规则.md](./开发进度和规则.md)** - 开发规则和项目进度，新对话窗口必须遵守

### 🚀 新手必读
- **[快速上手指南.md](./快速上手指南.md)** - 新对话窗口开发者必读，快速了解项目核心

### 🏗️ 架构文档
- **[项目架构说明.md](./项目架构说明.md)** - 完整的项目结构和架构设计
- **[Web数据流说明.md](./Web数据流说明.md)** - 详细的数据流程和处理机制

### 📖 接口文档
- **[API接口文档.md](./API接口文档.md)** - Web API接口的完整说明

### 📦 迁移文档
- **[migration/项目完成报告.md](./migration/项目完成报告.md)** - NPM ES模块迁移完成报告
- **[migration/快速使用指南.md](./migration/快速使用指南.md)** - NPM版本使用指南

## 🎯 快速导航

### 我是新的对话窗口，想快速上手
👉 **必须先阅读** **[开发进度和规则.md](./开发进度和规则.md)**，然后阅读 **[快速上手指南.md](./快速上手指南.md)**

### 我想了解项目整体架构
👉 阅读 **[项目架构说明.md](./项目架构说明.md)**

### 我需要开发Web相关功能
👉 先看 **[Web数据流说明.md](./Web数据流说明.md)**，再看 **[API接口文档.md](./API接口文档.md)**

### 我想了解NPM ES模块版本
👉 查看 **[migration/项目完成报告.md](./migration/项目完成报告.md)** 和 **[migration/快速使用指南.md](./migration/快速使用指南.md)**

### 我遇到了具体问题
👉 查看 **[快速上手指南.md](./快速上手指南.md)** 的"常见问题和解决方案"部分

## 📋 项目概述

Stock_XXX是一个实时股票监控系统，具有以下特性：

### 🖥️ 多版本支持
- **NPM ES模块版本**: 使用官方NPM包的现代ES模块版本（推荐）
- **原始UMD版本**: 基于CDN的传统版本（备用）
- **桌面GUI**: 基于PyQt的传统桌面应用

### 📊 实时数据
- **数据源**: Interactive Brokers (IB) API
- **更新频率**: 实时推送（秒级）
- **数据格式**: 支持IB API的时间格式 `"20241223 09:30:00 US/Eastern"`

### 🎨 现代化界面
- **多股票支持**: 同时监控多只股票，可切换查看
- **专业图表**: TradingView Lightweight Charts v5.0.7 NPM版本
- **响应式设计**: 支持桌面、平板、手机访问
- **技术指标**: EMA、MACD、价格突破检测
- **形态识别**: 突破形态确认、跌破检测功能

## 🔧 技术栈

### 后端
- **Python 3.10+**
- **Flask**: Web服务器
- **IB API**: 实时数据获取
- **PyQt**: 桌面GUI

### 前端
- **Tailwind CSS**: 现代化样式框架
- **TradingView Lightweight Charts v5.0.7**: 专业金融图表 (NPM版本)
- **ES模块**: 现代JavaScript模块系统

## 🚀 快速启动

### 1. 使用启动器（推荐）
```bash
python launcher.py
# 选择启动模式：完整应用/仅NPM前端/仅Python后端
```

### 2. 直接启动Python后端
```bash
python launcher.py --mode python
# NPM版本: http://localhost:5001/npm
# 原始版本: http://localhost:5001/
```

### 3. 启动完整系统
```bash
python launcher.py --mode full
# 桌面GUI + Web服务器 + NPM开发服务器
```

## 📁 项目结构

```
src/
├── docs/                           # 📚 项目文档
│   ├── README.md                   # 文档导航（本文件）
│   ├── 快速上手指南.md              # 新手必读
│   ├── 项目架构说明.md              # 架构设计
│   ├── Web数据流说明.md             # 数据流程
│   ├── API接口文档.md               # 接口说明
│   ├── 当前系统状态.md              # 最新系统状态
│   ├── Launcher启动器使用说明.md    # 启动器详细说明
│   └── migration/                  # 迁移相关文档
│       ├── 项目完成报告.md          # NPM迁移完成报告
│       └── 快速使用指南.md          # NPM版本使用指南
├── launcher.py                     # 🚀 统一启动器（推荐使用）
├── main.py                         # 🚀 主程序入口
├── api/
│   └── get_stock_price_ibapi.py    # 📡 IB API数据获取
├── web/                            # 🌐 Web模块
│   ├── web_server.py               # Flask服务器
│   ├── templates/index.html        # Web界面
│   └── README.md                   # Web模块说明
├── utils/
│   └── config.py                   # ⚙️ 配置文件
└── gui/
    └── stock_watch_app.py          # 🖥️ 桌面GUI
```

## 🔄 数据流概览

```
┌─────────────┐    ┌──────────────────────┐    ┌─────────────────┐    ┌─────────────┐
│   IB API    │───▶│ get_stock_price_     │───▶│ web_stock_data  │───▶│ Flask Web   │
│             │    │ ibapi.py             │    │ (dict)          │    │ Server      │
└─────────────┘    └──────────────────────┘    └─────────────────┘    └─────────────┘
                            │                                                  │
                            ▼                                                  ▼
                   ┌─────────────────┐                                ┌─────────────┐
                   │ manager_res_dict│                                │   浏览器     │
                   │ (桌面GUI)       │                                │  (用户界面)  │
                   └─────────────────┘                                └─────────────┘
```

## ⚙️ 配置说明

### Web功能开关
```python
# src/utils/config.py
WEB_CONFIG = {
    'ENABLED': True,        # 启用/禁用Web功能
    'HOST': '0.0.0.0',     # 监听地址
    'PORT': 5000,          # 监听端口
    'DEBUG': False,        # 调试模式
    'THREADED': True,      # 多线程支持
}
```

## 🧪 测试方法

### 独立测试
```bash
# Web界面测试
python test_web_only.py

# 更新方法测试  
python test_update_method.py

# 完整集成测试
python test_web_integration.py
```

### 功能验证
- ✅ 多股票列表显示
- ✅ 股票切换功能
- ✅ 实时K线图更新
- ✅ 价格涨跌显示
- ✅ 响应式界面

## 🚨 常见问题

### 1. 蜡烛柱全是绿色
**解决**: 检查测试数据生成逻辑，确保有涨有跌

### 2. 时间格式错误
**解决**: 处理IB API的 `"US/Eastern"` 时区信息

### 3. 图表不显示
**解决**: 检查容器尺寸和数据格式

### 4. 端口被占用
**解决**: 修改配置文件中的端口号

详细解决方案请查看 **[快速上手指南.md](./快速上手指南.md)**

## 📞 开发支持

### 文档更新

当项目有重大变更时，请及时更新相关文档：
1. 架构变更 → 更新 `项目架构说明.md`
2. 数据流变更 → 更新 `Web数据流说明.md`  
3. API变更 → 更新 `API接口文档.md`
4. 新手指南 → 更新 `快速上手指南.md`
5. **日志规范变更 → 更新 `开发进度和规则.md` 日志规范章节**

### 版本记录
- **v1.0**: 基础桌面GUI功能
- **v2.0**: 新增Web界面支持，多股票监控
- **v2.1**: 优化蜡烛柱更新机制，修复时间格式问题

---

📝 **提示**: 这些文档是为了帮助新的对话窗口快速理解项目。如果你是新的AI助手，建议从 **[快速上手指南.md](./快速上手指南.md)** 开始阅读！
