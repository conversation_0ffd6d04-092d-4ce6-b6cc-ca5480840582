# Web数据流详细说明（2025年6月更新：职责分明版）

## 📊 数据流概览

```

┌─────────────┐    ┌──────────────────────────────┐    ┌─────────────────┐    ┌─────────────┐
│   IB API    │───▶│ get_stock_price_ibapi.py     │───▶│ web_stock_data  │───▶│ Flask Web   │
│             │    │ (采集+监控+数据处理+推送)    │    │ (Web专用)      │    │ Server      │
└─────────────┘    └──────────────────────────────┘    └─────────────────┘    └─────────────┘
                            │                                                  │
                            ▼                                                  ▼
                   ┌─────────────────┐                                ┌─────────────┐
                   │ manager_res_dict│                                │   浏览器     │
                   │ (桌面GUI)       │                                │  (用户界面)  │
                   └─────────────────┘                                └─────────────┘

> **说明：** 股票监控、数据处理、推送全部在 get_stock_price_ibapi.py 完成，stock_watch_app.py 只做桌面展示。
```

## 🔄 详细数据流程


### 1. IB API数据接收与监控、推送
**位置**: `src/api/get_stock_price_ibapi.py`
**方法**: `historicalDataUpdate()` 及相关监控/推送逻辑

```python
def historicalDataUpdate(self, reqId, bar):
    # 1. 接收IB API推送的实时数据
    # bar.date = "20241223 09:30:00 US/Eastern"
    # bar.open, bar.high, bar.low, bar.close, bar.volume
    
    # 2. 处理并存储到req_data字典
    req_data[bar.date] = {
        "date_time": bar.date,
        "Open": bar.open,
        "High": bar.high,
        "Low": bar.low,
        "Close": bar.close,
        "Volume": vol_float
    }
    
    # 3. 推送到Web数据字典（采集、监控、数据处理、推送全部在此完成）
    self.web_stock_data[stock_code] = req_data
```


### 2. Web数据结构
**位置**: `src/main.py` 创建，唯一由 get_stock_price_ibapi.py 维护和推送，传递给各模块

```python
web_stock_data = {
    "AAPL": {
        "20241223 09:30:00 US/Eastern": {
            "date_time": "20241223 09:30:00 US/Eastern",
            "Open": 150.0,
            "High": 152.0,
            "Low": 149.0,
            "Close": 151.0,
            "Volume": 1000000
        },
        "20241223 09:31:00 US/Eastern": {
            "date_time": "20241223 09:31:00 US/Eastern",
            "Open": 151.0,
            "High": 151.5,
            "Low": 150.5,
            "Close": 150.8,
            "Volume": 1050000
        }
        # ... 更多时间点的数据
    },
    "GOOGL": {
        # ... 类似结构
    }
}
```


### 3. Web服务器数据处理
**位置**: `src/web/web_server.py`（只负责读取 web_stock_data 并提供API，不做数据处理/监控）





### 4. 前端数据处理
**位置**: `src/web/templates/index.html`（主要通过WebSocket实时推送消费数据，不做监控/推送）


#### 4.1 数据获取和显示（WebSocket实时推送为主）
```javascript
// 通过WebSocket实时接收K线数据
socket.on('data', (msg) => {
    const { symbol, data } = msg;
    // data为最新K线数组，直接setData或update
    if (isInitialLoad || currentStock !== symbol) {
        candleSeries.setData(data);
        lastDataLength = data.length;
    } else {
        // 增量更新
        if (data.length > lastDataLength) {
            const newCandles = data.slice(lastDataLength);
            newCandles.forEach(candle => {
                candleSeries.update(candle);
            });
            lastDataLength = data.length;
        } else if (data.length === lastDataLength && data.length > 0) {
            const lastCandle = data[data.length - 1];
            candleSeries.update(lastCandle);
        }
    }
});

// 股票概览等信息建议通过WebSocket推送或后端主动推送机制获取
```

## ⏰ 时间格式处理

### IB API时间格式
- **输入格式**: `"20250620 08:16:00 US/Eastern"`
- **处理方式**: 去除时区信息 `" US/Eastern"`
- **最终格式**: `"20250620 08:16:00"`

### 时间转换流程
```python
# 1. 原始时间字符串
date_time_str = "20250620 08:16:00 US/Eastern"

# 2. 去除时区信息
if " US/Eastern" in date_time_str:
    date_time_str = date_time_str.replace(" US/Eastern", "")

# 3. 转换为时间戳
timestamp = int(time.mktime(time.strptime(date_time_str, "%Y%m%d %H:%M:%S")))

# 4. 用于TradingView图表
candle = {
    "time": timestamp,  # Unix时间戳
    "open": 150.0,
    "high": 152.0,
    "low": 149.0,
    "close": 151.0
}
```

## 🔄 实时更新机制

### 1. 数据推送频率
- **IB API**: 实时推送（秒级）
- **Web前端**: 通过WebSocket实时推送（无须轮询）
- **图表更新**: 智能增量更新

### 2. 更新策略
```javascript
// 检查数据变化（WebSocket推送）
if (data.length > lastDataLength) {
    // 情况1: 有新的蜡烛柱
    const newCandles = data.slice(lastDataLength);
    newCandles.forEach(candle => {
        candleSeries.update(candle);  // 添加新蜡烛柱
    });
} else if (data.length === lastDataLength) {
    // 情况2: 最后一根蜡烛柱的价格更新
    const lastCandle = data[data.length - 1];
    candleSeries.update(lastCandle);  // 更新最后一根
}
```

### 3. 性能优化
- **避免闪烁**: 使用`.update()`而不是`.setData()`
- **数据限制**: 每只股票最多保存100条记录
- **智能检测**: 只在数据真正变化时更新图表

## 🚨 错误处理

### 1. 时间格式错误
```python
try:
    timestamp = time.mktime(time.strptime(date_time_str, "%Y%m%d %H:%M:%S"))
except ValueError as e:
    logging.warning(f"跳过无效数据: {bar_data}, 错误: {e}")
    continue
```

### 2. 数据缺失处理
```python
if not _web_stock_data:
    return jsonify([])  # 返回空数组

if stock_code not in _web_stock_data:
    return jsonify([])  # 股票不存在
```

### 3. 前端错误处理
```javascript
try {
    const res = await fetch('/data');
    if (res.ok) {
        const json = await res.json();
        // 处理数据
    } else {
        throw new Error(`HTTP ${res.status}`);
    }
} catch (error) {
    console.error('获取数据失败:', error);
    updateStatus('error', 0);
}
```

## 📝 数据流总结

1. **IB API** 推送实时数据到 `get_stock_price_ibapi.py`
2. **数据处理** 将数据存储到 `web_stock_data` 字典
3. **Web服务器** 读取字典数据，转换为API格式
4. **前端实时推送（WebSocket）** 获取数据并智能更新图表
5. **用户界面** 显示实时股票K线图和价格信息


整个数据流是单向的、实时的、高效的，**股票监控、数据处理、推送全部在 get_stock_price_ibapi.py 完成，stock_watch_app.py 只做桌面展示**，Web前端通过WebSocket实时接收推送，确保用户能看到最新的股票价格变化。

## 数据聚合和图表更新机制

### 1. 数据聚合流程
- 原始数据为1分钟K线数据
- 通过`aggregateTo5MinData`函数聚合为5分钟K线数据
- 聚合函数优化：
  - 首次加载时处理所有数据
  - 更新时只处理最后两个5分钟周期的数据
  - 强制更新最后一根K线的收盘价为最新价格

### 2. 价格更新机制
- 价格显示元素（如`price-SONM`）使用原始1分钟数据的最新价格
- 图表蜡烛柱使用聚合后的5分钟K线数据
- 为确保价格一致性，在更新模式下：
  - 记录最新的1分钟数据价格
  - 强制更新最后一根5分钟K线的收盘价为最新价格
  - 无论数据长度是否变化，都确保更新最后一根K线

### 3. 图表更新条件
- 首次加载：使用`setData`设置所有数据
- 有新数据（长度增加）：使用`update`添加新数据点
- 数据长度相同：更新最后一个数据点
- 数据长度变化（由于聚合优化）：强制更新最后一个数据点
