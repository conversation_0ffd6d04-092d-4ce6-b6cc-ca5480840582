# Web API接口文档

## 📋 接口概览

Web服务器提供以下REST API接口，支持多股票实时数据查询和展示。

**基础URL**: `http://localhost:5001` (可配置)

## 🌐 页面接口

### GET /
**描述**: 主页，多股票监控界面  
**返回**: HTML页面  
**功能**: 
- 显示多股票实时K线图
- 股票列表和选择器
- 响应式Tailwind CSS界面

**示例**:
```
GET http://localhost:5001/
```

## 📊 数据接口

### GET /data
**描述**: 获取股票K线数据，支持指定股票参数  
**参数**:
- `symbol` (可选): 股票代码，如 `AAPL`

**返回格式**:
```json
[
  {
    "time": 1734913800,
    "open": 150.0,
    "high": 152.0,
    "low": 149.0,
    "close": 151.0
  },
  {
    "time": 1734913860,
    "open": 151.0,
    "high": 151.5,
    "low": 150.5,
    "close": 150.8
  }
]
```

**字段说明**:
- `time`: Unix时间戳（秒）
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价

**示例**:
```bash
# 获取AAPL的K线数据
GET http://localhost:5001/data?symbol=AAPL

# 获取默认股票的K线数据（第一只股票）
GET http://localhost:5001/data
```

### GET /overview
**描述**: 获取所有股票的概览信息  
**参数**: 无

**返回格式**:
```json
[
  {
    "symbol": "AAPL",
    "current_price": 151.0,
    "open_price": 150.0,
    "high_price": 152.0,
    "low_price": 149.0,
    "change": 1.0,
    "change_percent": 0.67,
    "volume": 1000000,
    "last_update": "20241223 09:30:00 US/Eastern",
    "data_count": 25
  },
  {
    "symbol": "GOOGL",
    "current_price": 2800.0,
    "open_price": 2790.0,
    "high_price": 2810.0,
    "low_price": 2785.0,
    "change": 10.0,
    "change_percent": 0.36,
    "volume": 500000,
    "last_update": "20241223 09:30:00 US/Eastern",
    "data_count": 25
  }
]
```

**字段说明**:
- `symbol`: 股票代码
- `current_price`: 当前价格（最新收盘价）
- `open_price`: 开盘价（第一条数据的开盘价）
- `high_price`: 最高价（最新数据的最高价）
- `low_price`: 最低价（最新数据的最低价）
- `change`: 涨跌额（当前价格 - 开盘价）
- `change_percent`: 涨跌幅百分比
- `volume`: 成交量（最新数据的成交量）
- `last_update`: 最后更新时间（原始IB API格式）
- `data_count`: 数据条数

**示例**:
```bash
GET http://localhost:5001/overview
```

### GET /stocks
**描述**: 获取所有股票代码列表  
**参数**: 无

**返回格式**:
```json
{
  "stock_codes": ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA"],
  "count": 5
}
```

**字段说明**:
- `stock_codes`: 股票代码数组
- `count`: 股票总数

**示例**:
```bash
GET http://localhost:5001/stocks
```

### GET /stock/<stock_code>
**描述**: 获取指定股票的K线数据（与/data?symbol=相同）  
**参数**:
- `stock_code`: 路径参数，股票代码

**返回格式**: 与 `/data` 接口相同

**示例**:
```bash
GET http://localhost:5001/stock/AAPL
```

### GET /status
**描述**: 获取系统状态信息  
**参数**: 无

**返回格式**:
```json
{
  "stock_count": 5,
  "last_update": 1734913800,
  "status": "running"
}
```

**字段说明**:
- `stock_count`: 当前监控的股票数量
- `last_update`: 最后更新时间（Unix时间戳）
- `status`: 系统状态

**示例**:
```bash
GET http://localhost:5001/status
```

## 🔄 数据更新机制

### 实时数据流
1. **IB API** 推送实时数据到后端
2. **后端处理** 数据存储到 `web_stock_data` 字典
3. **前端轮询** 每3秒调用 `/overview` 和 `/data` 接口
4. **智能更新** 前端使用增量更新机制

### 前端调用顺序
```javascript
// 1. 页面加载时
fetchStocksOverview()  // 获取股票列表
fetchStockData(symbol, true)  // 初始加载K线数据

// 2. 定时更新（每3秒）
setInterval(() => {
    fetchStocksOverview()  // 更新股票概览
    fetchStockData(currentStock, false)  // 更新当前股票K线
}, 3000)
```

## ⚠️ 错误处理

### HTTP状态码
- `200`: 成功
- `404`: 接口不存在
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "error": "错误描述",
  "code": "ERROR_CODE"
}
```

### 常见错误
1. **股票不存在**: 返回空数组 `[]`
2. **数据格式错误**: 跳过无效数据，记录警告日志
3. **时间解析失败**: 跳过该条数据，继续处理其他数据

## 🕐 时间格式说明

### IB API原始格式
```
"20241223 09:30:00 US/Eastern"
```

### API返回格式
```json
{
  "time": 1734913800  // Unix时间戳（秒）
}
```

### 时间转换
```python
# 输入: "20241223 09:30:00 US/Eastern"
# 处理: 去除 " US/Eastern"
# 解析: strptime("%Y%m%d %H:%M:%S")
# 输出: Unix时间戳
```

## 📈 性能特性

### 数据限制
- 每只股票最多返回100条K线数据
- 自动清理过期数据，保持内存使用稳定

### 缓存机制
- 数据存储在内存字典中，访问速度快
- 无需数据库，减少I/O开销

### 并发支持
- Flask多线程模式，支持多用户同时访问
- 数据读取无锁，性能优秀

## 🧪 测试示例

### 使用curl测试
```bash
# 获取股票概览
curl http://localhost:5001/overview

# 获取AAPL的K线数据
curl "http://localhost:5001/data?symbol=AAPL"

# 获取股票列表
curl http://localhost:5001/stocks

# 获取系统状态
curl http://localhost:5001/status
```

### 使用JavaScript测试
```javascript
// 获取股票概览
fetch('/overview')
  .then(res => res.json())
  .then(data => console.log(data));

// 获取K线数据
fetch('/data?symbol=AAPL')
  .then(res => res.json())
  .then(data => console.log(data));
```

## 🔧 配置说明

### Web服务器配置
```python
# src/utils/config.py
WEB_CONFIG = {
    'ENABLED': True,        # 启用Web服务器
    'HOST': '0.0.0.0',     # 监听地址
    'PORT': 5000,          # 监听端口
    'DEBUG': False,        # 调试模式
    'THREADED': True,      # 多线程支持
}
```

### 启动Web服务器
```python
# 在main.py中
if WEB_CONFIG['ENABLED']:
    from src.web.web_server import start_web_server_thread
    start_web_server_thread(
        host=WEB_CONFIG['HOST'],
        port=WEB_CONFIG['PORT'],
        debug=WEB_CONFIG['DEBUG'],
        web_stock_data=web_stock_data
    )
```

## 📝 开发注意事项

1. **数据格式**: 确保时间格式正确处理
2. **错误处理**: 优雅处理数据缺失和格式错误
3. **性能优化**: 限制返回数据量，避免内存泄漏
4. **线程安全**: Web服务器在独立线程运行
5. **向后兼容**: 保持API接口稳定性
