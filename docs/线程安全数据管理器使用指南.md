# 线程安全数据管理器使用指南

## 概述

本项目已实现了统一的线程安全数据管理系统，替代了原有的分散全局变量管理方式。新系统提供了更好的线程安全性、数据验证和统一的访问接口。

## 核心组件

### 1. ThreadSafeStockDataManager

位置：`src/core/thread_safe_stock_data_manager.py`

**主要功能：**
- 统一管理所有股票相关数据
- 提供线程安全的数据访问
- 支持数据类型验证
- 提供访问统计和监控

**核心方法：**
```python
# 股票数据操作
set_stock_data(key: str, value: Any) -> None
get_stock_data(key: str, default: Any = None) -> Any
batch_update_stock_data(updates: Dict[str, Any]) -> None

# 交易数据操作
set_transaction_data(stock_code: str, count: int) -> None
get_transaction_data(stock_code: str) -> int

# 共享数据操作
set_shared_data(key: Any, value: Any) -> None
get_shared_data(key: Any, default: Any = None) -> Any

# 数据验证
register_validator(data_type: str, validator: Callable) -> None

# 统计信息
get_stats() -> Dict[str, Any]
```

### 2. 数据管理器适配器

位置：`src/core/data_manager_adapter.py`

**功能：**
- 提供与原有全局变量相同的接口
- 确保向后兼容性
- 将操作重定向到ThreadSafeStockDataManager

**适配器类型：**
- `StockDataDictAdapter` - 替代 `manager_res_dict_test`
- `TransactionDataDictAdapter` - 替代 `number_of_transactions_dict`
- `SharedDataDictAdapter` - 替代 `manager_res_dict`

### 3. 重构的MarketDataManager

位置：`src/core/market_data_manager.py`

**重大改进：**
- 完全移除了重复的数据存储（self._data）
- 所有方法统一使用ThreadSafeStockDataManager
- 将所有静态方法改为实例方法，提供更好的一致性
- 移除了冗余的线程锁机制
- 新增批量操作功能
- 增强的错误处理和数据验证

**新增功能：**
- `get(stock, field, default)` - 简洁的单字段获取接口
- `set(stock, field, value)` - 简洁的单字段设置接口
- `batch_get_market_data()` - 批量获取多个股票的市场数据
- `batch_update_stock_data()` - 批量更新股票数据
- `get_data_manager_stats()` - 获取数据管理器统计信息
- `set_active_stocks()` - 设置活跃股票列表
- `set_market_data()` - 批量设置股票市场数据的接口
- `get_market_data()` 现在包含 `buy_price` 和 `program_status` 字段

**数据访问统一：**
- StockWatchApp中的数据访问已统一为使用 `market_data.get()` 方式
- 移除了直接使用 `data_manager.get_data()` 和 `data_manager.update_data()` 的混合模式
- 提供了更一致和可预测的数据访问接口

## 使用方式

### 1. 获取数据管理器实例

```python
from src.core.thread_safe_stock_data_manager import get_stock_data_manager

# 获取全局单例实例
data_manager = get_stock_data_manager()
```

### 2. 基本数据操作

```python
# 设置股票价格
keys = key_generator.get_keys_for_stock('AAPL')
data_manager.set_stock_data(keys[NOW_PRICE], 155.50)

# 获取股票价格
price = data_manager.get_stock_data(keys[NOW_PRICE], 0)

# 批量更新
updates = {
    keys[NOW_PRICE]: 156.00,
    keys[VOLUME]: 1200000
}
data_manager.batch_update_stock_data(updates)
```

### 3. 交易数据操作

```python
# 设置交易次数
data_manager.set_transaction_data('AAPL', 500)

# 获取交易次数
count = data_manager.get_transaction_data('AAPL')
```

### 4. 使用适配器（兼容原有代码）

```python
from src.api.get_stock_price_ibapi import manager_res_dict_test, number_of_transactions_dict

# 原有代码无需修改，直接使用
manager_res_dict_test['AAPL_price'] = 155.50
price = manager_res_dict_test.get('AAPL_price', 0)

number_of_transactions_dict['AAPL'] = 500
count = number_of_transactions_dict.get('AAPL', 0)
```

### 5. 通过MarketDataManager访问

```python
from src.core.market_data_manager import MarketDataManager
from src.utils.constants import *

# 创建实例
manager = MarketDataManager()

# 方式1: 简洁接口（推荐）
# 获取数据
price = manager.get('AAPL', NOW_PRICE, 0)
set_flag = manager.get('AAPL', SET_FLG, False)
buy_price = manager.get('AAPL', 'buy_price', 0)

# 设置数据
manager.set('AAPL', NOW_PRICE, 155.50)
manager.set('AAPL', SET_FLG, True)
manager.set('AAPL', 'buy_price', 150.0)

# 方式2: 批量接口
# 批量设置
data = {NOW_PRICE: 156.00, VOLUME: 1300000, SET_FLG: True}
manager.set_market_data('AAPL', data)

# 批量获取
market_data = manager.get_market_data('AAPL')
price = market_data.get(NOW_PRICE, 0)

# 其他功能
stocks = ['AAPL', 'GOOGL', 'MSFT']
batch_data = manager.batch_get_market_data(stocks)
stats = manager.get_data_manager_stats()

# 设置市场数据（新功能）
market_data_to_set = {
    NOW_PRICE: 156.00,
    VOLUME: 1300000,
    SET_FLG: True,
    'buy_price': 155.00,
    TRANSACTION_RANK: 600
}
manager.set_market_data('AAPL', market_data_to_set)
```

### 6. 统一的数据访问模式（推荐）

```python
# 在StockWatchApp或其他GUI组件中，统一使用market_data.get()
market_data = manager.get_market_data(stock_code)

# 获取所有数据都通过market_data
current_price = market_data.get(NOW_PRICE, 0)
volume = market_data.get(VOLUME, 0)
buy_price = market_data.get('buy_price', 0)
set_flag = market_data.get(SET_FLG, False)
transaction_rank = market_data.get(TRANSACTION_RANK, 0)

# 计算价格变化
if buy_price > 0 and current_price > 0:
    price_change = current_price - buy_price
    price_change_percent = (price_change / buy_price) * 100

# 检查条件
if not market_data.get(SET_FLG, False):
    # 执行条件检查逻辑
    pass
```

### 7. 设置市场数据的完整示例

```python
from src.core.market_data_manager import MarketDataManager
from src.utils.constants import *

# 创建管理器实例
manager = MarketDataManager()

# 准备完整的市场数据
complete_market_data = {
    # 基础价格和成交量数据
    NOW_PRICE: 155.50,
    VOLUME: 1000000,
    VOLUME_AVG: 800000,
    CHECK_VOLUME: 900000,

    # 股票基本信息
    STOCK_SHARESOUTSTANDING: 5000000,
    NEWS: ['重要新闻1', '重要新闻2'],

    # 状态标志
    IS_SPREAD_NORMAL: True,
    BUY_FLG: False,
    SET_FLG: True,
    BREAKOUT_FLG: True,
    BREAKOUT_RED_FLG: False,
    UPWARD_PRESSURE_DETECTED: True,

    # 交易和排名数据
    TRANSACTION_RANK: 500,
    NUMBER_FLG: 300,

    # 分析指标
    SIGNAL_STRENGTH: 'strong',
    CONFIDENCE: 0.85,
    BUYING_PRESSURE: 0.75,
    BEHAVIOR_SCORE: 0.90,
    BEHAVIOR_SCORE_15MIN: 0.88,

    # 自定义数据
    'buy_price': 150.0,
    'program_status': 1
}

# 一次性设置所有数据
manager.set_market_data('AAPL', complete_market_data)

# 验证数据设置
retrieved_data = manager.get_market_data('AAPL')
print(f"价格: {retrieved_data.get(NOW_PRICE)}")
print(f"成交量: {retrieved_data.get(VOLUME)}")
print(f"买入价格: {retrieved_data.get('buy_price')}")

# 部分更新示例
partial_update = {
    NOW_PRICE: 157.00,  # 只更新价格
    VOLUME: 1100000,    # 只更新成交量
    SET_FLG: False      # 只更新标志
}
manager.set_market_data('AAPL', partial_update)
```

## 数据验证

### 内置验证器

系统自动注册了以下验证器：

- **价格验证器** - 检查价格数据类型和非负性
- **成交量验证器** - 检查成交量数据类型和非负性
- **百分比验证器** - 检查百分比范围（-100到100）
- **布尔值验证器** - 检查标志位数据类型

### 自定义验证器

```python
def custom_validator(value):
    if not isinstance(value, str) or len(value) == 0:
        raise ValueError("值必须是非空字符串")

# 注册验证器
data_manager.register_validator('custom_key_pattern', custom_validator)
```

## 性能监控

### 获取统计信息

```python
stats = data_manager.get_stats()
print(f"股票数据项数: {stats['stock_data_count']}")
print(f"交易数据项数: {stats['transaction_data_count']}")
print(f"共享数据项数: {stats['shared_data_count']}")
print(f"总访问次数: {stats['access_count']}")
print(f"最后访问时间: {stats['last_access_time']}")
```

## 线程安全性

### 内置保护

- 使用`threading.RLock()`保护所有数据操作
- 支持并发读写操作
- 自动处理竞态条件

### 最佳实践

```python
# 批量操作比单个操作更高效
updates = {
    'key1': 'value1',
    'key2': 'value2',
    'key3': 'value3'
}
data_manager.batch_update_stock_data(updates)

# 而不是
# data_manager.set_stock_data('key1', 'value1')
# data_manager.set_stock_data('key2', 'value2')
# data_manager.set_stock_data('key3', 'value3')
```

## 测试

### 运行测试

```bash
# 测试核心数据管理器
python -m pytest tests/test_thread_safe_stock_data_manager.py -v

# 测试适配器
python -m pytest tests/test_data_manager_adapter.py -v

# 测试MarketDataManager
python -m pytest tests/test_market_data_manager.py -v
```

### 测试覆盖

- 单例模式测试
- 基本数据操作测试
- 线程安全性测试
- 数据验证测试
- 适配器兼容性测试
- 性能测试

## 迁移指南

### 从旧系统迁移

1. **无需修改现有代码** - 适配器确保完全兼容
2. **逐步迁移** - 可以逐步将代码改为使用新的API
3. **性能提升** - 新系统提供更好的性能和稳定性

### MarketDataManager重构迁移

**重要变更：**
- 所有静态方法已改为实例方法
- 必须创建MarketDataManager实例才能使用

**迁移步骤：**
```python
# 旧代码（不再支持）
price = MarketDataManager.get_current_price('AAPL')

# 新代码
manager = MarketDataManager()
price = manager.get_current_price('AAPL')
```

### 推荐的迁移步骤

1. 确保所有测试通过
2. 在开发环境中验证功能
3. 更新所有MarketDataManager的使用点
4. 逐步将关键路径改为使用新API
5. 监控性能和稳定性
6. 完全迁移到新系统

## 故障排除

### 常见问题

1. **数据验证失败** - 检查数据类型和范围
2. **性能问题** - 使用批量操作而非单个操作
3. **内存使用** - 定期检查统计信息

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.INFO)

# 检查统计信息
stats = data_manager.get_stats()
print(f"当前状态: {stats}")

# 重置数据（仅用于测试）
from src.core.thread_safe_stock_data_manager import reset_stock_data_manager
reset_stock_data_manager()
```

## 总结

新的线程安全数据管理系统提供了：

- ✅ 统一的数据管理接口
- ✅ 完整的线程安全保护
- ✅ 自动数据验证
- ✅ 向后兼容性
- ✅ 性能监控
- ✅ 易于测试和调试

这个系统解决了原有分散全局变量管理的问题，为项目提供了更稳定、更可维护的数据管理基础。
