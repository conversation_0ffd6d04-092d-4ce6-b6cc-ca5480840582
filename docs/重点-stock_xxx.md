# 重点-stock_xxx项目

## 用户说"记住"的内容

### 时间格式记录
- 从ib api获取的时间格式："20250620 08:16:00 US/Eastern"

### 时间处理规则
- **IB API数据**: 纽约时间（不需要减1分钟）
- **数据库数据**: 纽约时间+1分钟（需要减1分钟，既有逻辑保持不变）
    - `stock_trand.stock_price_ibkr`和`stock_trand.stock_price_ibkr_120`里的`date_time`字段的时间都是+1分钟，时间格式是 `2025-05-18 08:38:00`
- **前端显示**: 纽约时间
- **数据时区来源**: 只有ib api和数据库的stock_trand.stock_price_ibkr和stock_trand.stock_price_ibkr_120

### 测试要求
- 不要总是开新的终端
- 测试时需要仔细地检查数据从开始到最后，是不是真的正确
- 遇到这种技术问题时，首先仔细阅读官方文档，不要自己过度修改和测试
- 像蜡烛柱，十字线，时间轴这种相关联的处理，在测试和修改时需要仔细复核，不要出现遗漏
- get_stock_price_ibapi.py有if __name__ == "__main__":可以直接启动，进行测试，在节假日只有历史数据，发送完就不会在发送新的数据。


