# Flask + WebSocket + Protobuf + JS前端集成方案

## 项目概述

本方案旨在为现有的股票监控系统增强实时通信能力，通过集成WebSocket和Protobuf技术，实现高效的实时数据推送和前端展示。

## 技术架构

### 整体架构图

```
IB API ──→ 数据处理引擎 ──→ 数据管理器 ──→ WebSocket服务器 ──→ 浏览器前端
   ↓              ↓              ↓              ↓              ↓
股票数据     分析计算处理     共享内存存储     Protobuf序列化    实时图表更新
```

### 核心组件

1. **数据流架构**
   ```
   IB API → StockAnalyzer → DataManager → WebSocketServer → Frontend
        ↓          ↓           ↓            ↓             ↓
    原始数据   技术分析     内存缓存    二进制传输    实时图表
   ```

2. **通信协议栈**
   ```
   应用层: 股票数据模型 (StockUpdate, AllStocksUpdate)
   序列化层: Protocol Buffers 3.0
   传输层: WebSocket (over HTTP/1.1)
   会话层: Flask-SocketIO
   网络层: TCP/IP
   ```

## 系统性能规范和限制

### 数据传输优化

**单股票数据规范**:
- 预期数据包大小: 通常<58KB/股票
- K线数据点: 约500个数据点/股票 (基于5分钟K线)
- 历史数据深度: 约2天历史数据

**批量传输优化**:
- 最大并发股票数: 15只
- 批处理超时: 50ms (提高实时性)
- 自动批量推送优化

**压缩策略**:
- 压缩阈值: 512B (降低阈值提高效率)  
- 压缩级别: 4 (平衡压缩率和速度)
- 压缩率要求: <80% 才使用压缩版本

### 客户端连接限制

**订阅限制**:
```python
MAX_STOCKS_PER_CLIENT = 15      # 每客户端最多15只股票
MIN_UPDATE_INTERVAL = 0.5       # 最小更新间隔0.5秒
MAX_CLIENTS = 50                # 最大并发客户端数
MAX_TOTAL_SUBSCRIPTIONS = 500   # 系统总订阅数限制
```

**内存管理**:
```python
# 每只股票内存分配(预期值)
TYPICAL_MEMORY_PER_STOCK = 20 * 1024    # 约20KB/股票典型占用
MAX_HISTORY_POINTS = 500                 # 最多500个历史数据点
CLEANUP_THRESHOLD = 0.7                  # 70%内存占用时触发清理
```

### 实时性能指标

**延迟要求**:
- WebSocket连接延迟: <50ms
- 数据序列化时间: <10ms
- 批量推送延迟: <100ms
- 端到端延迟: <200ms

**吞吐量指标**:
- 每秒消息数: 最大300条/秒 (15股票×20更新/秒)
- 压缩后预期传输: 约3-5MB/秒

## 详细技术规范

### 1. WebSocket服务器实现

#### 1.1 Flask-SocketIO集成

**文件位置**: `src/web/websocket_server.py`

**核心功能**:
- 集成到现有Flask应用
- 管理WebSocket连接生命周期
- 处理客户端订阅/取消订阅
- 实时数据广播

**技术特点**:
```python
# 连接管理
- 支持多客户端并发连接
- 自动心跳检测和重连
- 连接状态监控和日志记录
- 优雅断开连接处理

# 数据推送策略
- 增量更新优先 (仅推送变化数据)
- 全量更新备选 (新连接或重连时)
- 数据压缩传输 (Protobuf + gzip)
- 推送频率控制 (防止过载)
```

#### 1.2 数据推送机制

**推送触发条件**:
1. IB API数据更新时
2. 技术分析结果变化时
3. 客户端主动请求时
4. 定时心跳推送 (30秒)

**推送数据类型**:
```protobuf
// 实时K线数据推送
message StockDataUpdate {
  string symbol = 1;
  int64 timestamp = 2;
  StockDataPoint latest_kline = 3;
  repeated StockDataPoint history_klines = 4; // 仅全量更新时
  bool is_incremental = 5;
}

// 技术分析结果推送
message AnalysisUpdate {
  string symbol = 1;
  TechnicalSignal signal = 2;
  float confidence = 3;
  repeated string features = 4;
  int64 analysis_time = 5;
}

// 系统状态推送
message SystemStatus {
  bool ib_connected = 1;
  int32 active_stocks = 2;
  int64 server_time = 3;
  string status_message = 4;
}
```

### 2. Protobuf数据模型设计

#### 2.1 扩展现有Proto定义

**文件位置**: `src/web/proto/stock_data_v2.proto`

**新增消息类型**:
```protobuf
syntax = "proto3";
package stock_xxx.v2;

// WebSocket消息包装器
message WebSocketMessage {
  MessageType type = 1;
  bytes payload = 2;
  int64 timestamp = 3;
  string request_id = 4; // 用于请求-响应匹配
}

enum MessageType {
  UNKNOWN = 0;
  STOCK_UPDATE = 1;
  ANALYSIS_UPDATE = 2;
  SYSTEM_STATUS = 3;
  HEARTBEAT = 4;
  SUBSCRIPTION = 5;
  ERROR = 6;
}

// 客户端订阅请求
message SubscriptionRequest {
  repeated string symbols = 1; // 订阅的股票代码
  bool include_analysis = 2;   // 是否包含技术分析
  int32 update_interval = 3;   // 更新间隔(秒)
}

// 技术分析信号
message TechnicalSignal {
  SignalStrength strength = 1;
  float confidence = 2;
  string description = 3;
  repeated AnalysisFeature features = 4;
}

enum SignalStrength {
  AVOID = 0;
  WEAK = 1;
  WATCH = 2;
  BUY = 3;
  STRONG_BUY = 4;
}

message AnalysisFeature {
  string name = 1;
  float value = 2;
  string description = 3;
}

// 错误消息
message ErrorMessage {
  int32 code = 1;
  string message = 2;
  string details = 3;
}
```

#### 2.2 序列化性能优化

**压缩策略**:
```python
# 数据压缩配置
COMPRESSION_CONFIG = {
    'method': 'gzip',
    'level': 6,  # 平衡压缩率和速度
    'threshold': 1024,  # 超过1KB才压缩
}

# 序列化缓存
SERIALIZATION_CACHE = {
    'enable': True,
    'max_size': 1000,  # 最大缓存条目
    'ttl': 60,  # 缓存有效期(秒)
}
```

### 3. 前端JavaScript客户端

#### 3.1 WebSocket客户端实现

**文件位置**: `src/web/static/js/websocket_client.js`

**核心功能**:
```javascript
class StockWebSocketClient {
  constructor(options = {}) {
    this.url = options.url || `ws://${window.location.host}/socket.io/`;
    this.reconnectInterval = options.reconnectInterval || 5000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.protobufRoot = null;
    this.messageTypes = null;
    this.subscriptions = new Set();
    this.eventHandlers = new Map();
    
    // 初始化Protobuf
    this.initProtobuf();
  }

  // Protobuf初始化
  async initProtobuf() {
    try {
      // 加载protobuf.js库
      const protobuf = await import('/static/js/protobuf.min.js');
      
      // 加载.proto定义
      this.protobufRoot = await protobuf.load('/static/proto/stock_data_v2.proto');
      this.messageTypes = {
        WebSocketMessage: this.protobufRoot.lookupType('stock_xxx.v2.WebSocketMessage'),
        StockUpdate: this.protobufRoot.lookupType('stock_xxx.v2.StockUpdate'),
        AnalysisUpdate: this.protobufRoot.lookupType('stock_xxx.v2.AnalysisUpdate'),
        SystemStatus: this.protobufRoot.lookupType('stock_xxx.v2.SystemStatus'),
      };
    } catch (error) {
      console.error('Protobuf初始化失败:', error);
      throw error;
    }
  }

  // 连接WebSocket
  connect() {
    try {
      this.socket = io(this.url, {
        transports: ['websocket'],
        upgrade: false,
        rememberUpgrade: false,
      });

      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      this.scheduleReconnect();
    }
  }

  // 消息处理
  handleMessage(data) {
    try {
      // 解码Protobuf消息
      const message = this.messageTypes.WebSocketMessage.decode(data);
      
      switch (message.type) {
        case 1: // STOCK_UPDATE
          this.handleStockUpdate(message);
          break;
        case 2: // ANALYSIS_UPDATE
          this.handleAnalysisUpdate(message);
          break;
        case 3: // SYSTEM_STATUS
          this.handleSystemStatus(message);
          break;
        default:
          console.warn('未知消息类型:', message.type);
      }
    } catch (error) {
      console.error('消息解码失败:', error);
    }
  }

  // 订阅股票数据
  subscribe(symbols, includeAnalysis = true) {
    const subscription = {
      symbols: Array.isArray(symbols) ? symbols : [symbols],
      include_analysis: includeAnalysis,
      update_interval: 1, // 1秒更新间隔
    };

    this.socket.emit('subscribe', subscription);
    symbols.forEach(symbol => this.subscriptions.add(symbol));
  }

  // 取消订阅
  unsubscribe(symbols) {
    const symbolArray = Array.isArray(symbols) ? symbols : [symbols];
    this.socket.emit('unsubscribe', { symbols: symbolArray });
    symbolArray.forEach(symbol => this.subscriptions.delete(symbol));
  }
}
```

#### 3.2 图表数据更新机制

**集成TradingView Lightweight Charts**:
```javascript
class RealTimeChartManager {
  constructor(wsClient) {
    this.wsClient = wsClient;
    this.charts = new Map(); // symbol -> chart instance
    this.candlestickSeries = new Map(); // symbol -> series
    this.lastUpdateTime = new Map(); // symbol -> timestamp
    
    // 绑定WebSocket事件
    this.wsClient.on('stockUpdate', this.handleStockUpdate.bind(this));
    this.wsClient.on('analysisUpdate', this.handleAnalysisUpdate.bind(this));
  }

  // 创建图表
  createChart(containerId, symbol) {
    const chart = LightweightCharts.createChart(document.getElementById(containerId), {
      width: 600,
      height: 350,
      layout: {
        backgroundColor: '#1f2937',
        textColor: '#e5e7eb',
      },
      grid: {
        vertLines: { color: '#374151' },
        horzLines: { color: '#374151' },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderColor: '#4b5563',
      },
      rightPriceScale: {
        borderColor: '#4b5563',
      },
    });

    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#10b981',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#10b981',
      wickDownColor: '#ef4444',
      wickUpColor: '#10b981',
    });

    this.charts.set(symbol, chart);
    this.candlestickSeries.set(symbol, candlestickSeries);
    
    return { chart, candlestickSeries };
  }

  // 处理股票数据更新
  handleStockUpdate(update) {
    const { symbol, kline_data, is_incremental } = update;
    const series = this.candlestickSeries.get(symbol);
    
    if (!series) return;

    if (is_incremental && kline_data.length > 0) {
      // 增量更新：只添加最新数据点
      const latestData = this.convertToChartData(kline_data[kline_data.length - 1]);
      series.update(latestData);
    } else {
      // 全量更新：替换所有数据
      const chartData = kline_data.map(this.convertToChartData);
      series.setData(chartData);
    }

    this.lastUpdateTime.set(symbol, Date.now());
    this.updateUI(symbol, update);
  }

  // 数据格式转换
  convertToChartData(protobufData) {
    return {
      time: protobufData.time,
      open: protobufData.open,
      high: protobufData.high,
      low: protobufData.low,
      close: protobufData.close,
    };
  }

  // 更新UI元素
  updateUI(symbol, update) {
    // 更新价格显示
    const priceElement = document.getElementById(`price-${symbol}`);
    if (priceElement && update.kline_data.length > 0) {
      const latestData = update.kline_data[update.kline_data.length - 1];
      priceElement.textContent = latestData.close.toFixed(2);
      
      // 更新价格颜色
      if (latestData.close > latestData.open) {
        priceElement.className = 'price-up';
      } else if (latestData.close < latestData.open) {
        priceElement.className = 'price-down';
      } else {
        priceElement.className = 'price-neutral';
      }
    }

    // 更新成交量
    const volumeElement = document.getElementById(`volume-${symbol}`);
    if (volumeElement && update.transaction_count) {
      volumeElement.textContent = update.transaction_count;
    }

    // 更新最后更新时间
    document.getElementById('last-update').textContent = 
      new Date().toLocaleTimeString('zh-CN');
  }
}
```

### 4. 系统集成实现

#### 4.1 WebSocket服务器集成

**文件位置**: `src/web/websocket_server.py`

```python
"""
WebSocket服务器模块
集成Flask-SocketIO实现实时数据推送
"""

from flask import Flask
from flask_socketio import SocketIO, emit, join_room, leave_room
import threading
import time
import logging
import gzip
from typing import Dict, Set, Optional, List
from dataclasses import dataclass
from enum import Enum

from src.web.proto.stock_data_v2_pb2 import (
    WebSocketMessage, MessageType, StockUpdate, AnalysisUpdate, 
    SystemStatus, SubscriptionRequest, ErrorMessage
)

@dataclass
class ClientSubscription:
    """客户端订阅信息"""
    session_id: str
    symbols: Set[str]
    include_analysis: bool
    update_interval: int
    last_update: float

class WebSocketServer:
    """WebSocket服务器类"""
    
    def __init__(self, flask_app: Flask, data_manager):
        self.app = flask_app
        self.data_manager = data_manager
        self.socketio = SocketIO(
            app=flask_app,
            cors_allowed_origins="*",
            async_mode='threading',
            logger=False,
            engineio_logger=False
        )
        
        # 客户端管理
        self.clients: Dict[str, ClientSubscription] = {}
        self.active_symbols: Set[str] = set()
        
        # 推送控制
        self.push_thread: Optional[threading.Thread] = None
        self.push_enabled = False
        self.push_interval = 1.0  # 秒
        
        self._setup_event_handlers()
        
    def _setup_event_handlers(self):
        """设置事件处理器"""
        
        @self.socketio.on('connect')
        def handle_connect():
            session_id = request.sid
            logging.info(f'客户端连接: {session_id}')
            
            # 发送系统状态
            self._send_system_status(session_id)
            
        @self.socketio.on('disconnect')
        def handle_disconnect():
            session_id = request.sid
            if session_id in self.clients:
                del self.clients[session_id]
                logging.info(f'客户端断开: {session_id}')
                
        @self.socketio.on('subscribe')
        def handle_subscribe(data):
            session_id = request.sid
            try:
                # 解析订阅请求
                symbols = set(data.get('symbols', []))
                
                # 限制每个客户端最多订阅15只股票
                if len(symbols) > 15:
                    symbols = set(list(symbols)[:15])
                    logging.warning(f'客户端 {session_id} 订阅股票数超限，已限制为前15只')
                
                include_analysis = data.get('include_analysis', True)
                update_interval = max(data.get('update_interval', 1), 0.5)  # 最小0.5秒间隔
                
                # 更新客户端订阅信息
                self.clients[session_id] = ClientSubscription(
                    session_id=session_id,
                    symbols=symbols,
                    include_analysis=include_analysis,
                    update_interval=update_interval,
                    last_update=0
                )
                
                # 更新活跃股票列表
                self.active_symbols.update(symbols)
                
                # 立即发送全量数据
                self._send_full_update(session_id, symbols)
                
                logging.info(f'客户端 {session_id} 订阅: {len(symbols)}只股票')
                
            except Exception as e:
                self._send_error(session_id, f'订阅失败: {str(e)}')
                
        @self.socketio.on('unsubscribe')
        def handle_unsubscribe(data):
            session_id = request.sid
            if session_id in self.clients:
                symbols_to_remove = set(data.get('symbols', []))
                self.clients[session_id].symbols -= symbols_to_remove
                logging.info(f'客户端 {session_id} 取消订阅: {symbols_to_remove}')
    
    def start_push_thread(self):
        """启动数据推送线程"""
        if self.push_thread and self.push_thread.is_alive():
            return
            
        self.push_enabled = True
        self.push_thread = threading.Thread(
            target=self._push_loop, 
            daemon=True,
            name='WebSocketPushThread'
        )
        self.push_thread.start()
        logging.info('WebSocket推送线程已启动')
    
    def stop_push_thread(self):
        """停止数据推送线程"""
        self.push_enabled = False
        if self.push_thread:
            self.push_thread.join(timeout=5)
        logging.info('WebSocket推送线程已停止')
    
    def _push_loop(self):
        """数据推送循环"""
        while self.push_enabled:
            try:
                current_time = time.time()
                
                for session_id, subscription in list(self.clients.items()):
                    # 检查是否需要推送
                    if (current_time - subscription.last_update) >= subscription.update_interval:
                        self._push_incremental_update(subscription)
                        subscription.last_update = current_time
                
                time.sleep(self.push_interval)
                
            except Exception as e:
                logging.error(f'推送循环异常: {e}')
                time.sleep(1)
    
    def _push_incremental_update(self, subscription: ClientSubscription):
        """推送增量更新 - 优化批量推送"""
        try:
            updates = []
            
            for symbol in subscription.symbols:
                # 获取最新数据
                stock_data = self.data_manager.get_stock_data(symbol)
                if not stock_data:
                    continue
                
                # 构建更新消息
                update = self._build_stock_update(symbol, stock_data, incremental=True)
                if update:
                    updates.append(update)
            
            # 发送批量更新
            if updates:
                self._send_batch_updates(subscription.session_id, updates)
                
        except Exception as e:
            logging.error(f'增量更新失败: {e}')
    
    def _build_stock_update(self, symbol: str, stock_data: dict, incremental: bool = True) -> Optional[StockUpdate]:
        """构建股票更新消息"""
        try:
            update = StockUpdate()
            update.symbol = symbol
            update.is_incremental = incremental
            
            # 转换K线数据
            if incremental and len(stock_data.get('kline_data', [])) > 0:
                # 只发送最新数据点
                latest_data = stock_data['kline_data'][-1]
                kline_point = update.kline_data.add()
                kline_point.time = int(latest_data['time'])
                kline_point.open = float(latest_data['open'])
                kline_point.high = float(latest_data['high'])
                kline_point.low = float(latest_data['low'])
                kline_point.close = float(latest_data['close'])
            else:
                # 发送所有历史数据
                for data_point in stock_data.get('kline_data', []):
                    kline_point = update.kline_data.add()
                    kline_point.time = int(data_point['time'])
                    kline_point.open = float(data_point['open'])
                    kline_point.high = float(data_point['high'])
                    kline_point.low = float(data_point['low'])
                    kline_point.close = float(data_point['close'])
            
            # 设置其他字段
            update.transaction_count = stock_data.get('transaction_count', '0')
            update.historical_data_complete = stock_data.get('historical_data_complete', False)
            
            return update
            
        except Exception as e:
            logging.error(f'构建股票更新消息失败 {symbol}: {e}')
            return None
    
    def _send_message(self, session_id: str, message_type: MessageType, payload: bytes):
        """发送WebSocket消息 - 优化压缩策略"""
        try:
            # 构建包装消息
            wrapper = WebSocketMessage()
            wrapper.type = message_type
            wrapper.payload = payload
            wrapper.timestamp = int(time.time() * 1000)
            
            # 序列化
            data = wrapper.SerializeToString()
            
            # 压缩策略优化(降低阈值到512B)
            data_size = len(data)
            if data_size > 512:
                compressed_data = gzip.compress(data, compresslevel=4)  # 降低压缩级别提高速度
                compression_ratio = len(compressed_data) / data_size
                
                # 只有压缩效果好才使用压缩版本
                if compression_ratio < 0.8:
                    data = compressed_data
                    logging.debug(f'数据已压缩: {data_size}B -> {len(data)}B (比率: {compression_ratio:.2f})')
            
            # 发送
            self.socketio.emit('message', data, room=session_id)
            
            # 更新统计
            self.total_messages_sent += 1
            self.total_bytes_sent += len(data)
            
        except Exception as e:
            logging.error(f'发送消息失败 {session_id}: {e}')
            self.error_count += 1
    
    def _send_error(self, session_id: str, error_message: str):
        """发送错误消息"""
        error = ErrorMessage()
        error.code = 1
        error.message = error_message
        
        self._send_message(session_id, MessageType.ERROR, error.SerializeToString())
    
    def _send_system_status(self, session_id: str):
        """发送系统状态"""
        status = SystemStatus()
        status.ib_connected = self.data_manager.is_connected()
        status.active_stocks = len(self.active_symbols)
        status.server_time = int(time.time() * 1000)
        status.status_message = "系统运行正常"
        
        self._send_message(session_id, MessageType.SYSTEM_STATUS, status.SerializeToString())

# 全局WebSocket服务器实例
websocket_server: Optional[WebSocketServer] = None

def init_websocket_server(flask_app: Flask, data_manager) -> WebSocketServer:
    """初始化WebSocket服务器"""
    global websocket_server
    
    if websocket_server is None:
        websocket_server = WebSocketServer(flask_app, data_manager)
        websocket_server.start_push_thread()
        logging.info('WebSocket服务器初始化完成')
    
    return websocket_server

def get_websocket_server() -> Optional[WebSocketServer]:
    """获取WebSocket服务器实例"""
    return websocket_server
```

#### 4.2 Flask应用集成

**修改文件**: `src/web/web_server.py`

```python
# 在文件顶部添加导入
from src.web.websocket_server import init_websocket_server, get_websocket_server

# 在create_flask_app函数中添加WebSocket集成
def create_flask_app(web_stock_data):
    """创建Flask应用并集成WebSocket"""
    app = Flask(__name__, 
                template_folder='templates',
                static_folder='static')
    
    # 现有路由保持不变...
    
    # 初始化WebSocket服务器
    websocket_server = init_websocket_server(app, web_stock_data)
    
    # 添加WebSocket状态路由
    @app.route('/ws-status')
    def websocket_status():
        """WebSocket服务器状态"""
        ws_server = get_websocket_server()
        if ws_server:
            return jsonify({
                'websocket_enabled': True,
                'active_clients': len(ws_server.clients),
                'active_symbols': list(ws_server.active_symbols),
                'push_enabled': ws_server.push_enabled
            })
        else:
            return jsonify({'websocket_enabled': False})
    
    return app, websocket_server
```

### 5. 部署和配置

#### 5.1 依赖包更新

**更新文件**: `requirements.txt`

```pip-requirements
# 添加WebSocket支持
Flask-SocketIO==5.3.6
python-socketio==5.8.0
eventlet==0.33.3
# protobuf和grpcio-tools已安装，无需重复添加

# 添加性能优化
msgpack==1.0.5  # 可选的序列化格式
ujson==5.8.0     # 更快的JSON处理
```

#### 5.2 Protobuf编译脚本

**文件位置**: `scripts/compile_proto.py`

```python
#!/usr/bin/env python3
"""
编译Protobuf文件的脚本
"""

import subprocess
import sys
from pathlib import Path

def compile_proto_files():
    """编译所有.proto文件"""
    
    proto_dir = Path('src/web/proto')
    output_dir = proto_dir
    
    # 查找所有.proto文件
    proto_files = list(proto_dir.glob('*.proto'))
    
    if not proto_files:
        print("未找到.proto文件")
        return
    
    for proto_file in proto_files:
        print(f"编译 {proto_file}...")
        
        cmd = [
            'python', '-m', 'grpc_tools.protoc',  # 使用已安装的grpcio-tools
            f'--proto_path={proto_dir}',
            f'--python_out={output_dir}',
            str(proto_file)
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"✓ {proto_file.name} 编译成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {proto_file.name} 编译失败: {e.stderr}")
            sys.exit(1)
    
    print("所有Protobuf文件编译完成")

if __name__ == '__main__':
    compile_proto_files()
```

#### 5.3 启动脚本更新

**修改文件**: `src/main.py`

```python
# 在main函数中添加WebSocket支持
def main():
    """主函数"""
    
    # 现有初始化代码...
    
    try:
        # 启动Web服务器(包含WebSocket)
        web_thread = start_web_server_thread(
            manager_res_dict, 
            enable_websocket=True  # 启用WebSocket
        )
        
        # 现有代码...
        
    except KeyboardInterrupt:
        logging.info('用户中断程序')
        
        # 清理WebSocket服务器
        from src.web.websocket_server import get_websocket_server
        ws_server = get_websocket_server()
        if ws_server:
            ws_server.stop_push_thread()
            
    except Exception as e:
        logging.error(f'程序异常: {e}')
        sys.exit(1)
```

### 6. 性能优化和监控

#### 6.1 性能优化策略

**数据传输优化**:
```python
# 数据压缩配置 - 针对最多15只股票优化
WEBSOCKET_CONFIG = {
    'max_stocks_per_client': 15,    # 每个客户端最多订阅15只股票
    'compression': {
        'enabled': True,
        'threshold': 512,   # 超过512B启用压缩(降低阈值)
        'level': 4,         # 压缩级别(1-9，降低压缩级别提高速度)
    },
    'batching': {
        'enabled': True,
        'max_batch_size': 15,        # 最大批处理条目(匹配股票数量)
        'batch_timeout': 50,         # 批处理超时(ms，降低延迟)
    },
    'throttling': {
        'enabled': True,
        'max_updates_per_second': 20,  # 每秒最大更新数(提高频率)
        'per_stock_limit': True,       # 按股票限制更新频率
    }
}
```

**内存优化**:
```python
# 数据缓存策略 - 针对小规模股票池优化
CACHE_CONFIG = {
    'max_stocks': 15,              # 最多支持15只股票
    'max_history_points': 500,     # 最大历史数据点(减少内存占用)
    'cleanup_interval': 120,       # 清理间隔(秒，更频繁清理)
    'memory_threshold': 0.7,       # 内存阈值(降低阈值)
}

# K线数据点优化
# 5分钟K线，约500个数据点可存储约2天的历史数据
MAX_KLINE_POINTS_PER_STOCK = 500
```

#### 6.2 监控和日志

**WebSocket监控指标**:
```python
class WebSocketMetrics:
    """WebSocket性能指标"""
    
    def __init__(self):
        self.connection_count = 0
        self.total_messages_sent = 0
        self.total_bytes_sent = 0
        self.average_latency = 0.0
        self.error_count = 0
        
    def to_dict(self):
        return {
            'connections': self.connection_count,
            'messages_sent': self.total_messages_sent,
            'bytes_sent': self.total_bytes_sent,
            'avg_latency_ms': self.average_latency,
            'errors': self.error_count
        }
```

**性能监控端点**:
```python
@app.route('/metrics')
def get_metrics():
    """获取系统性能指标"""
    ws_server = get_websocket_server()
    
    metrics = {
        'websocket': ws_server.get_metrics() if ws_server else {},
        'system': {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'active_threads': threading.active_count()
        },
        'data_manager': {
            'active_stocks': len(manager_res_dict),
            'total_data_points': sum(len(data) for data in manager_res_dict.values())
        }
    }
    
    return jsonify(metrics)
```

### 7. 测试和验证

#### 7.1 单元测试

**文件位置**: `tests/test_websocket_server.py`

```python
import unittest
import asyncio
from unittest.mock import Mock, patch
from src.web.websocket_server import WebSocketServer
from src.web.proto.stock_data_v2_pb2 import StockUpdate

class TestWebSocketServer(unittest.TestCase):
    """WebSocket服务器测试"""
    
    def setUp(self):
        self.app = Mock()
        self.data_manager = Mock()
        self.ws_server = WebSocketServer(self.app, self.data_manager)
    
    def test_client_subscription(self):
        """测试客户端订阅"""
        session_id = 'test_session'
        symbols = ['AAPL', 'GOOGL']
        
        # 模拟订阅
        subscription = self.ws_server._create_subscription(
            session_id, symbols, True, 1
        )
        
        self.assertEqual(subscription.symbols, set(symbols))
        self.assertTrue(subscription.include_analysis)
    
    def test_protobuf_serialization(self):
        """测试Protobuf序列化"""
        update = StockUpdate()
        update.symbol = 'AAPL'
        update.is_incremental = True
        
        # 序列化
        data = update.SerializeToString()
        self.assertIsInstance(data, bytes)
        
        # 反序列化
        parsed_update = StockUpdate()
        parsed_update.ParseFromString(data)
        
        self.assertEqual(parsed_update.symbol, 'AAPL')
        self.assertTrue(parsed_update.is_incremental)

if __name__ == '__main__':
    unittest.main()
```

#### 7.2 集成测试

**文件位置**: `tests/test_websocket_integration.py`

```python
import unittest
import socketio
import time
from threading import Thread
from src.web.web_server import create_flask_app

class TestWebSocketIntegration(unittest.TestCase):
    """WebSocket集成测试"""
    
    def setUp(self):
        # 创建测试应用
        self.test_data = {'AAPL': {'kline_data': []}}
        self.app, self.ws_server = create_flask_app(self.test_data)
        self.app.config['TESTING'] = True
        
        # 启动测试服务器
        self.server_thread = Thread(
            target=lambda: self.app.run(port=5001, debug=False),
            daemon=True
        )
        self.server_thread.start()
        time.sleep(1)  # 等待服务器启动
    
    def test_websocket_connection(self):
        """测试WebSocket连接"""
        client = socketio.SimpleClient()
        
        try:
            # 连接到服务器
            client.connect('http://localhost:5001')
            
            # 发送订阅请求
            client.emit('subscribe', {
                'symbols': ['AAPL'],
                'include_analysis': True,
                'update_interval': 1
            })
            
            # 等待响应
            event = client.receive(timeout=5)
            self.assertIsNotNone(event)
            
        finally:
            client.disconnect()

if __name__ == '__main__':
    unittest.main()
```

### 8. 文档和使用指南

#### 8.1 API文档

**WebSocket事件API**:

| 事件名称 | 方向 | 描述 | 数据格式 |
|---------|------|------|---------|
| connect | 客户端→服务器 | 建立连接 | 无 |
| disconnect | 客户端→服务器 | 断开连接 | 无 |
| subscribe | 客户端→服务器 | 订阅股票数据 | `{symbols: string[], include_analysis: boolean, update_interval: number}` |
| unsubscribe | 客户端→服务器 | 取消订阅 | `{symbols: string[]}` |
| message | 服务器→客户端 | 数据推送 | Protobuf二进制数据 |

#### 8.2 前端使用示例

```html
<!-- 完整的HTML使用示例 -->
<!DOCTYPE html>
<html>
<head>
    <script src="/socket.io/socket.io.js"></script>
    <script src="/static/js/protobuf.min.js"></script>
    <script src="/static/js/websocket_client.js"></script>
</head>
<body>
    <div id="stock-charts"></div>
    
    <script>
        // 初始化WebSocket客户端
        const wsClient = new StockWebSocketClient({
            url: `ws://${window.location.host}`,
            reconnectInterval: 5000,
            maxReconnectAttempts: 10
        });

        // 初始化图表管理器
        const chartManager = new RealTimeChartManager(wsClient);

        // 连接并订阅数据
        wsClient.connect().then(() => {
            console.log('WebSocket连接成功');
            
            // 订阅股票数据 - 最多支持15只股票
            wsClient.subscribe(['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'], true);
            
            // 创建图表
            ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'].forEach(symbol => {  // 示例5只股票
                const container = document.createElement('div');
                container.id = `chart-${symbol}`;
                container.style.height = '400px';
                document.getElementById('stock-charts').appendChild(container);
                
                chartManager.createChart(`chart-${symbol}`, symbol);
            });
        });

        // 处理连接状态
        wsClient.on('connect', () => {
            console.log('WebSocket已连接');
        });

        wsClient.on('disconnect', () => {
            console.log('WebSocket已断开');
        });

        wsClient.on('error', (error) => {
            console.error('WebSocket错误:', error);
        });
    </script>
</body>
</html>
```

## 总结

本方案提供了完整的Flask + WebSocket + Protobuf + JS前端集成解决方案，具有以下特点：

### 核心优势

1. **实时性能**: WebSocket提供低延迟的双向通信
2. **数据效率**: Protobuf二进制序列化减少传输开销
3. **可扩展性**: 模块化设计支持功能扩展
4. **可靠性**: 完整的错误处理和重连机制
5. **易维护性**: 清晰的代码结构和文档

### 技术亮点

- **增量更新机制**: 只传输变化的数据，减少带宽使用
- **数据压缩**: 自动压缩大数据包，优化传输性能
- **连接管理**: 智能的客户端连接生命周期管理
- **性能监控**: 内置性能指标和监控端点
- **类型安全**: Protobuf提供强类型数据契约

### 部署建议

1. **开发环境**: 使用Flask开发服务器，启用调试模式
2. **生产环境**: 使用Gunicorn + Nginx，启用SSL/TLS
3. **监控**: 集成Prometheus或其他监控系统
4. **日志**: 使用结构化日志，便于问题排查

本方案已充分考虑现有系统架构，确保平滑集成和向后兼容性。通过分阶段实施，可以逐步验证各组件功能，降低部署风险。
