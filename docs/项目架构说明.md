# Stock_XXX 项目架构说明

## 📋 项目概述

这是一个实时股票监控系统，支持桌面GUI和Web界面双重访问，使用IB API获取实时股票数据。系统核心特性包括：

**最新职责说明：**
- `get_stock_price_ibapi.py` 负责通过 IB API 获取股票价格数据，并承担股票监控、数据处理、推送到 web_stock_data 的全部核心逻辑。
- `stock_watch_app.py` 仅负责桌面端的结果展示和交互。
- Web 端和 GUI 端数据结构完全隔离，`web_stock_data` 只由 IBAPI 采集线程维护和推送。

- **5分钟K线聚合**：将1分钟原始数据智能聚合为5分钟K线
- **技术指标计算**：基于5分钟数据计算EMA(9/20/120)和MACD指标
- **WebSocket实时推送**：替代轮询机制，提供低延迟数据更新
- **连接失败监控**：自动监控WebSocket连接状态，3分钟超时保护
- **形态跌破检测**：NPM版本支持形态完成后的跌破检测功能

## 🏗️ 项目结构

```
项目根目录/
├── docs/                           # 项目文档
│   ├── 项目架构说明.md              # 本文件
│   ├── Web数据流说明.md             # Web模块数据流详解
│   ├── API接口文档.md               # Web API接口说明
│   ├── 当前系统状态.md              # 最新系统状态文档
│   ├── 快速上手指南.md              # 项目快速上手指南
│   ├── Launcher启动器使用说明.md    # 启动器详细说明
│   └── migration/                  # 迁移相关文档
│       ├── 项目完成报告.md          # NPM迁移完成报告
│       └── 快速使用指南.md          # NPM版本使用指南
├── launcher.py                     # 启动器（推荐使用）
├── simple_backend.py               # 简化后端启动器
├── package.json                    # NPM依赖配置
├── vite.config.js                  # Vite构建配置
├── src/
│   ├── main.py                     # 主程序入口
│   ├── api/
│   │   └── get_stock_price_ibapi.py    # IB API数据获取
│   ├── web/                        # Web模块
│   │   ├── __init__.py
│   │   ├── web_server.py           # Flask Web服务器
│   │   ├── websocket_server.py     # WebSocket服务器 (SocketIO)
│   │   ├── templates/
│   │   │   ├── index-npm.html      # NPM ES模块版本（主版本）
│   │   │   └── index.html          # 原始UMD版本（备用）
│   │   ├── static/
│   │   │   └── js/
│   │   │       ├── main-npm.js     # NPM版本主入口
│   │   │       ├── chart_manager.js    # 原始版本图表管理
│   │   │       ├── websocket_client.js # WebSocket客户端
│   │   │       └── modules/
│   │   │           ├── chart-manager-npm.js  # NPM版本图表管理（含跌破检测）
│   │   │           ├── utils.js              # 工具模块
│   │   │           └── websocket-client.js   # WebSocket客户端模块
│   │   └── README.md               # Web模块说明
│   ├── config/
│   │   └── config.py               # 配置文件
│   └── gui/
│       └── stock_watch_app.py      # 桌面GUI应用
└── tests/                          # 测试目录
    └── ...                         # 各种测试文件
```

## 🔄 数据流架构

### 核心数据流（5分钟K线系统）
```
IB API (1分钟)
   ↓
get_stock_price_ibapi.py（采集+监控+数据处理+推送）
   ↓
web_stock_data（Web专用，独立于GUI）
   ↓
Flask Web Server / WebSocket Server
   ↓
Web前端（两个版本）
   ├── NPM ES模块版本 (推荐)
   │   └── Chart Manager NPM → 5分钟聚合 → EMA/MACD计算 → 跌破检测 → 前端渲染
   └── 原始UMD版本 (备用)
       └── Chart Manager → 5分钟聚合 → EMA/MACD计算 → 前端渲染
   ↑
REST API (向下兼容)
```

> **说明：** 股票监控、数据处理、推送全部在 get_stock_price_ibapi.py 完成，stock_watch_app.py 只做桌面展示。

### 详细处理链路
1. **数据获取**：IB API推送1分钟K线数据
2. **WebSocket推送**：服务器实时推送到前端
3. **前端聚合**：chart_manager.js执行5分钟聚合
4. **技术指标**：基于聚合后数据计算EMA和MACD
5. **图表渲染**：使用LightweightCharts展示5分钟K线
6. **连接监控**：检测失败连接，3分钟超时保护

### 关键数据结构

#### 1. web_stock_data（Web专用，唯一来源于 get_stock_price_ibapi.py）
```python
web_stock_data = {
    "AAPL": {
        "20241223 09:30:00 US/Eastern": {
            "date_time": "20241223 09:30:00 US/Eastern",
            "Open": 150.0,
            "High": 152.0,
            "Low": 149.0,
            "Close": 151.0,
            "Volume": 1000000
        },
        "20241223 09:31:00 US/Eastern": {
            # ... 更多数据
        }
    },
    "GOOGL": {
        # ... 类似结构
    }
}
```

#### 3. WebSocket推送数据格式
```javascript
// WebSocket 'data' 事件
{
    "symbol": "AAPL",
    "data": [
        {
            "time": 1640995200,         // Unix时间戳
            "open": 150.0,
            "high": 152.0,
            "low": 149.0,
            "close": 151.0,
            "volume": 1000000,
            "transactions": "1.2K"      // 格式化的交易次数
        }
    ],
    "timestamp": 1640995260.123,
    "source": "websocket_push"
}
```

#### 4. 5分钟聚合后数据格式
```javascript
// chart_manager.js aggregateTo5MinData() 输出
[
    {
        "time": 1640995200,     // 5分钟边界对齐时间戳
        "open": 150.0,          // 期间第一个1分钟的开盘价
        "high": 152.5,          // 期间所有1分钟的最高价
        "low": 149.0,           // 期间所有1分钟的最低价
        "close": 151.8,         // 期间最后一个1分钟的收盘价
        "volume": 5000000       // 期间所有1分钟成交量之和
    }
]
```


## 🚀 启动流程（职责分明版）

### 1. main.py 启动顺序
```python
def main():
    # 1. 初始化配置和日志
    # 2. 创建共享数据结构
    manager_res_dict = {0: 1, 1: []}
    web_stock_data = {}  # 新增：Web数据字典
    
    # 3. 启动IB API数据获取线程（负责采集、监控、数据处理、推送web_stock_data）
    thread_executor.submit(get_stock_data, manager_res_dict, quote_ctx, sound_warn, sound_signal, web_stock_data)

    # 4. 启动Web服务器线程（如果启用）
    if WEB_CONFIG['ENABLED']:
        start_web_server_thread(
            web_stock_data=web_stock_data,
            enable_websocket=True  # 启用WebSocket支持
        )

    # 5. 启动桌面GUI（仅做展示）
    app = StockWatchApp(sound_warn, manager_res_dict)
    app.run()

> **注意：** web_stock_data 只由 IBAPI 采集线程维护和推送，stock_watch_app.py 只消费 manager_res_dict。
```

### 2. WebSocket数据推送与5分钟聚合
```python
# websocket_server.py 推送逻辑
def _push_symbol_data(self, symbol: str):
    # 获取原始1分钟数据
    stock_data_dict = self.data_manager.get_stock_data(symbol)
    
    # 格式化为前端期望格式
    candles = []
    for date_str in sorted_dates[-800:]:  # 最近800个数据点
        candle = {
            "time": parse_datetime_string(bar_data["date_time"]),
            "open": round(float(bar_data["Open"]), 2),
            "high": round(float(bar_data["High"]), 2),
            "low": round(float(bar_data["Low"]), 2),
            "close": round(float(bar_data["Close"]), 2),
            "volume": round(float(bar_data.get("Volume", 0)), 0)
        }
        candles.append(candle)
    
    # 推送到前端
    self.socketio.emit('data', {
        'symbol': symbol,
        'data': candles,
        'timestamp': time.time()
    }, room=f"symbol_{symbol}")
```

```javascript
// chart_manager.js 5分钟聚合逻辑
doUpdateChart(symbol, newData) {
    // 1. 格式化数据（包含volume字段）
    const formattedData = this.formatChartData(newData);
    
    // 2. 执行5分钟聚合
    const aggregatedData = this.aggregateTo5MinData(formattedData);
    
    // 3. 检查数据变化（避免无效重绘）
    if (this.isDataEqual(currentData, aggregatedData)) return;
    
    // 4. 更新K线图表
    chartInfo.series.setData(aggregatedData);
    
    // 5. 计算技术指标（基于5分钟数据）
    const ema9 = this.calculateEMA(aggregatedData, 9);
    const ema20 = this.calculateEMA(aggregatedData, 20);
    const ema120 = this.calculateEMA(aggregatedData, 120);
    const macdResult = this.calculateMACD(aggregatedData);
    
    // 6. 更新技术指标系列
    chartInfo.ema9Series.setData(ema9);
    chartInfo.ema20Series.setData(ema20);
    chartInfo.ema120Series.setData(ema120);
    chartInfo.macdLineSeries.setData(macdResult.macdLine);
    // ...
}
```

## ⚙️ 配置说明

### Web配置（config.py）
```python
WEB_CONFIG = {
    'ENABLED': True,        # 是否启用Web服务器
    'HOST': '0.0.0.0',     # 监听地址
    'PORT': 5001,          # 监听端口
    'DEBUG': False,        # 调试模式
    'THREADED': True,      # 多线程模式
}
```

### WebSocket配置
```python
# websocket_server.py 配置
WEBSOCKET_CONFIG = {
    'cors_allowed_origins': "*",    # CORS设置
    'async_mode': 'threading',      # 异步模式
    'ping_timeout': 60,             # ping超时
    'ping_interval': 25             # ping间隔
}
```

## 🌐 Web模块特性

### 1. 多股票支持
- 左侧股票列表：显示所有股票及价格、涨跌幅
- 股票选择器：下拉菜单快速切换
- 实时价格更新：每3秒自动刷新

### 2. 现代化界面
- 使用Tailwind CSS构建
- 深色主题，专业金融界面
- 响应式设计，支持多设备

### 3. 高性能图表
- TradingView Lightweight Charts（官方API最佳实践，详见下方用法示例）
- 使用`.update()`方法避免闪烁，`.setData()`用于全量加载
- 支持缩放、滚动、十字线

#### chart_manager.js 主要 Lightweight Charts 用法（与实际代码一致）

```javascript
// 1. 创建图表
const chart = LightweightCharts.createChart(container, {
    ...this.chartOptions,
    width: container.offsetWidth,
    height: container.offsetHeight || 350,
});

// 2. 创建K线系列（v5推荐写法）
const candlestickSeries = chart.addSeries(LightweightCharts.CandlestickSeries, this.seriesOptions);

// 3. 创建EMA等技术指标系列
const ema9Series = chart.addSeries(LightweightCharts.LineSeries, { color: '#ff9800', lineWidth: 1 });
const ema20Series = chart.addSeries(LightweightCharts.LineSeries, { color: '#f321e5', lineWidth: 2 });
const ema120Series = chart.addSeries(LightweightCharts.LineSeries, { color: '#41f321', lineWidth: 3 });

// 4. 创建MACD系列（添加到窗格1）
const macdLineSeries = chart.addSeries(LightweightCharts.LineSeries, { color: '#2196F3', lineWidth: 2 }, 1);
const signalLineSeries = chart.addSeries(LightweightCharts.LineSeries, { color: '#FF9800', lineWidth: 1 }, 1);
const macdHistSeries = chart.addSeries(LightweightCharts.HistogramSeries, { color: '#26a69a' }, 1);

// 5. 设置主价格轴边距
chart.priceScale('right').applyOptions({ scaleMargins: { top: 0.1, bottom: 0.25 } });

// 6. 响应式调整图表尺寸
const resizeObserver = new ResizeObserver(entries => {
    for (let entry of entries) {
        const { width, height } = entry.contentRect;
        chart.applyOptions({ width, height });
    }
});
resizeObserver.observe(container);

// 7. 全量加载K线数据
candlestickSeries.setData(dataArray);

// 8. 实时增量更新K线
candlestickSeries.update(latestCandle);

// 9. 设置技术指标数据
ema9Series.setData(ema9Array);
ema20Series.setData(ema20Array);
ema120Series.setData(ema120Array);
macdLineSeries.setData(macdLineArray);
signalLineSeries.setData(signalLineArray);
macdHistSeries.setData(macdHistArray);
```

> **注：** 所有用法均与 chart_manager.js 现有实现完全一致，严格遵循 v5 官方推荐写法。

## 🔧 开发要点

### 1. 时间格式处理
IB API返回时间格式：`"20250620 08:16:00 US/Eastern"`
需要去除时区信息：`"20250620 08:16:00"`

### 2. 数据更新机制
- 初始加载：使用`candleSeries.setData()`
- 实时更新：使用`candleSeries.update()`
- 智能检测新数据和最后一根蜡烛柱更新

### 3. 蜡烛柱颜色
- 上涨：亮绿色 `#00ff00`
- 下跌：亮红色 `#ff0000`
- 判断：收盘价 vs 开盘价

## 🧪 测试方法

### 1. 独立Web测试
```bash
python test_web_only.py     # http://localhost:5004
```

### 2. 完整功能测试
```bash
python test_web_integration.py
```

### 3. 主程序启动
```bash
python src/main.py          # 桌面GUI + Web服务器
```

## 📝 重要注意事项

1. **数据隔离说明**：web_stock_data 和 manager_res_dict 实际上共同指向同一份保存股价数据的内存区域（如同一 dict 或共享内存），只是 manager_res_dict 除了股价还包含分析数据、信号等更多内容，web_stock_data 仅用于 Web 展示和推送。
2. **所有页面展示的时间均为纽约时区（US/Eastern）**：无论后端推送还是前端渲染，所有K线、价格、时间戳等展示内容，均需以纽约时区为准，禁止使用本地浏览器时区或UTC时间。
2. **线程安全**：Web服务器在独立线程运行，不影响桌面GUI
3. **向后兼容**：保持所有原有功能不变
4. **配置化**：可通过WEB_CONFIG启用/禁用Web功能
5. **错误隔离**：Web服务器错误不会影响桌面应用

## 🔗 相关文档

- [Web数据流说明.md](./Web数据流说明.md) - 详细的数据流程
- [API接口文档.md](./API接口文档.md) - Web API接口说明
- [../web/README.md](../web/README.md) - Web模块详细说明

## 🚨 快速上手指南

### 新对话窗口开发者必读

1. **理解数据结构**：重点关注`web_stock_data`的格式
2. **查看配置文件**：`src/utils/config.py`中的`WEB_CONFIG`
3. **测试环境**：先运行`python test_web_only.py`验证Web功能
4. **代码位置**：Web相关代码主要在`src/web/`目录
5. **数据推送**：关键在`get_stock_price_ibapi.py`的`historicalDataUpdate`方法
