# Launcher 启动器使用说明

## 📋 概述

`launcher.py` 是Stock_XXX项目的统一启动器，提供多种启动模式，让用户可以根据需要选择不同的组件组合。

## 🚀 基本使用

### 交互式菜单模式（推荐）
```bash
python launcher.py
```

启动后会显示菜单：
```
============================================================
📊 TradingView Lightweight Charts 启动器
============================================================
请选择启动模式:

1. 完整应用 (推荐)
   - Python后端 + 数据获取
   - NPM前端开发服务器
   - GUI应用界面
   - 访问: http://localhost:3000/templates/index-npm.html

2. 仅NPM前端
   - 仅启动Vite开发服务器
   - 适用于前端开发和测试
   - 访问: http://localhost:3000/test-npm.html

3. 仅Python后端
   - 仅启动Flask服务器
   - 适用于Web版本开发
   - NPM版本: http://localhost:5001/npm
   - 原始版本: http://localhost:5001/

0. 退出
============================================================
```

### 命令行模式
```bash
# 启动完整应用
python launcher.py --mode full

# 仅启动NPM开发服务器
python launcher.py --mode npm

# 仅启动Python后端
python launcher.py --mode python
```

## 🔧 启动模式详解

### 1. 完整应用模式 (--mode full)

**启动组件：**
- Python后端服务器（数据获取和处理）
- NPM前端开发服务器（Vite）
- GUI桌面应用界面

**适用场景：**
- 完整的开发和测试环境
- 需要同时使用桌面和Web界面
- 完整功能演示

**访问地址：**
- Web界面：http://localhost:3000/templates/index-npm.html
- 桌面GUI：自动弹出窗口

**执行命令：**
```python
subprocess.run([sys.executable, "src/main.py"])
```

### 2. 仅NPM前端模式 (--mode npm)

**启动组件：**
- Vite开发服务器

**适用场景：**
- 前端功能开发和调试
- 静态页面测试
- 不需要后端数据的场景
- UI/UX开发

**访问地址：**
- 开发服务器：http://localhost:3000/test-npm.html

**执行命令：**
```bash
npm run dev
```

### 3. 仅Python后端模式 (--mode python)

**启动组件：**
- Flask Web服务器
- 股票数据获取和处理服务
- WebSocket实时推送服务

**不启动：**
- GUI桌面应用界面
- NPM开发服务器

**适用场景：**
- Web版本开发
- API开发和测试
- 生产环境部署
- 资源节省（不需要GUI）

**访问地址：**
- NPM版本：http://localhost:5001/npm （推荐，功能完整）
- 原始版本：http://localhost:5001/ （备用）

**执行命令：**
```python
subprocess.run([sys.executable, "start_with_web.py"])
```

## 📊 版本比较

| 访问地址 | 版本类型 | 功能特性 | 推荐场景 |
|----------|----------|----------|----------|
| http://localhost:5001/npm | NPM ES模块版本 | ✅ 所有功能<br/>✅ 跌破检测<br/>✅ 官方NPM包 | 生产使用 |
| http://localhost:5001/ | 原始UMD版本 | ✅ 基本功能<br/>❌ 无跌破检测 | 备用/兼容 |
| http://localhost:3000/ | NPM开发环境 | ✅ 热重载<br/>✅ 开发工具 | 前端开发 |

## 🛠️ 开发工作流

### 前端开发工作流
```bash
# 1. 启动NPM开发服务器
python launcher.py --mode npm

# 2. 访问开发环境
# http://localhost:3000/test-npm.html

# 3. 修改代码，自动热重载
```

### 后端开发工作流
```bash
# 1. 启动Python后端
python launcher.py --mode python

# 2. 访问Web界面测试
# http://localhost:5001/npm

# 3. 修改后端代码，重启服务器
```

### 完整测试工作流
```bash
# 1. 启动完整应用
python launcher.py --mode full

# 2. 测试所有功能
# - 桌面GUI功能
# - Web界面功能
# - 数据同步功能
```

## ⚙️ 配置说明

### 端口配置
- **Flask服务器**: 5001 (避免Mac AirPlay冲突)
- **NPM开发服务器**: 3000
- **配置文件**: `src/config/config.py`

### 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :5001
   lsof -i :3000
   
   # 终止占用进程
   kill -9 <PID>
   ```

2. **NPM依赖问题**
   ```bash
   # 重新安装依赖
   npm install
   
   # 清除缓存
   npm cache clean --force
   ```

3. **Python依赖问题**
   ```bash
   # 检查虚拟环境
   which python
   
   # 安装依赖
   pip install -r requirements.txt
   ```

### 启动失败处理

1. **检查启动模式参数**
   ```bash
   python launcher.py --help
   ```

2. **查看错误日志**
   - 查看终端输出
   - 检查相关进程是否启动成功

3. **分步调试**
   ```bash
   # 单独测试各组件
   python src/main.py              # 测试主程序
   python start_with_web.py        # 测试Web服务器
   npm run dev                     # 测试开发服务器
   ```

## 📝 更新日志

### 2025-06-29
- ✅ 更新端口配置为5001（避免Mac冲突）
- ✅ 增强函数文档说明
- ✅ 支持NPM ES模块版本
- ✅ 删除ES模块版本支持
- ✅ 完善命令行参数支持
