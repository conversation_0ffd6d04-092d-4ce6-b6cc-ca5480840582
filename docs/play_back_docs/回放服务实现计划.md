# 回放服务实现计划

## 项目概述
基于现有的 `src/play_back/data_service.py` 和 `src/play_back/app.py`，创建独立的股票数据回放服务，实现历史数据的实时模拟推送功能。

---

## 1. 现有架构分析

### 1.1 核心组件
- **app.py**: Flask + SocketIO 服务入口，处理WebSocket连接和事件
- **data_service.py**: 股票数据服务核心，负责数据获取、处理和推送
- **db.py**: 数据库连接管理，提供SQLAlchemy引擎

### 1.2 数据流程
```
数据库(stock_price_ibkr) → data_service.load_daily_data() → 1分钟数据缓存
                                    ↓
前端WebSocket连接 ← socketio.emit() ← _simulation_loop() ← resample_to_5min()
```

### 1.3 已实现功能
- ✅ 数据库连接和查询
- ✅ 1分钟数据加载和缓存
- ✅ 5分钟K线重采样
- ✅ Top5涨幅股票计算
- ✅ WebSocket实时推送
- ✅ 模拟时间推进机制
- ✅ 线程安全的启动/停止控制

---

## 2. 服务特性

### 2.1 独立性
- **端口隔离**: 默认端口5001，与主系统分离
- **资源复用**: 复用主系统的静态资源和模板
- **数据库共享**: 使用相同的数据库，但查询历史数据(live_data=0)

### 2.2 兼容性
- **前端兼容**: 支持npm版本和UMD版本的前端页面
- **数据格式**: 与主系统保持一致的数据结构
- **WebSocket协议**: 使用相同的事件名称和数据格式

### 2.3 可控性
- **时间控制**: 支持指定回放日期和起始时间
- **速度控制**: 可调节推送间隔(默认1000ms)
- **实时控制**: 支持启动/停止操作

---

## 3. 技术实现详解

### 3.1 数据服务 (StockDataService)

#### 核心属性
```python
self.engine = get_engine()                    # 数据库连接
self.current_simulation_time = None           # 当前模拟时间
self.simulation_date = None                   # 模拟日期
self.simulation_speed = 1000                  # 推送间隔(ms)
self.is_running = False                       # 运行状态
self.thread = None                            # 推送线程
self.socketio = None                          # WebSocket实例
self.daily_data = {}                          # 当日数据缓存
self.current_loaded_date = None               # 已加载日期
```

#### 关键方法

**load_daily_data(date_str)**
- 功能: 一次性加载指定日期的全部1分钟数据
- 查询: `stock_price_ibkr` 表, `live_data = 0`
- 缓存: 按股票代码分组存储到 `daily_data`
- 优化: 避免重复加载相同日期数据

**get_top5_stocks(target_time)**
- 功能: 计算指定时间点的涨幅前5股票
- 算法: `(close_price - open_price) / open_price`
- 排序: 按涨幅降序排列
- 返回: 股票代码列表

**resample_to_5min(stock_code, target_time)**
- 功能: 将1分钟数据重采样为5分钟K线
- 工具: pandas.resample('5min')
- 聚合: Open(first), High(max), Low(min), Close(last), Volume(sum)
- 格式: 转换为Unix时间戳格式

**start_simulation(date_str, start_time, speed)**
- 功能: 启动回放模拟
- 检查: 停止之前的模拟
- 加载: 检查并加载所需日期数据
- 启动: 创建守护线程执行推送循环

**_simulation_loop()**
- 功能: 核心推送循环
- 频率: 每分钟推进一次时间
- 推送: 通过WebSocket发送market_update事件
- 结束: 到达16:00自动停止

### 3.2 Web服务 (app.py)

#### Flask配置
```python
STATIC_FOLDER = '../web/static'     # 复用主系统静态资源
TEMPLATE_FOLDER = '../web/templates' # 复用主系统模板
app = Flask(__name__, ...)
socketio = SocketIO(app, cors_allowed_origins="*")
```

#### 集成Vite启动功能
```python
# 集成NPM/Vite管理器
from src.utils.npm_integration import NPMIntegration

class PlaybackApp:
    def __init__(self):
        self.npm_integration = NPMIntegration()
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """设置信号处理器，确保优雅退出"""
        def signal_handler(sig, frame):
            print("\n🛑 正在停止回放服务...")
            self.cleanup()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start_services(self):
        """启动所有服务"""
        # 1. 启动Vite开发服务器（如果启用）
        if not os.getenv('VITE_DISABLED'):
            print("🚀 启动集成开发环境...")
            self.npm_integration.start_vite_server()
        else:
            print("📦 Vite已禁用，使用静态文件服务...")
        
        # 2. 启动Flask回放服务
        port = int(os.environ.get('PORT', 5001))
        print(f"🚀 启动回放服务: http://localhost:{port}")
        
        try:
            socketio.run(app, host='0.0.0.0', port=port, debug=True)
        except KeyboardInterrupt:
            pass
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'npm_integration'):
            self.npm_integration.stop_vite_server()

# 使用示例
if __name__ == '__main__':
    playback_app = PlaybackApp()
    playback_app.start_services()
```

#### 路由配置
- `/`: 返回 `index-npm.html` (npm版本)
- `/old`: 返回 `index.html` (UMD版本)

#### WebSocket事件
- `start_simulation`: 处理回放启动请求
- `stop_simulation`: 处理回放停止请求
- `market_update`: 向前端推送数据更新

---

## 4. 数据格式规范

### 4.1 WebSocket推送格式
```json
{
  "timestamp": "2024-12-23T09:31:00",
  "top5_stocks": ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA"],
  "stocks_data": {
    "AAPL": [
      {
        "time": 1703264700,
        "open": 195.89,
        "high": 196.95,
        "low": 195.89,
        "close": 196.95,
        "volume": 423800
      }
    ]
  }
}
```

### 4.2 前端控制格式
```json
// 启动回放
{
  "date": "2024-12-23",
  "start_time": "09:30",
  "speed": 1000
}

// 响应格式
{
  "status": "started|stopped|error",
  "msg": "错误信息(如有)"
}
```

---

## 5. 部署和使用

### 5.1 启动方式

#### 一键启动（推荐）
```bash
# 自动启动回放服务 + Vite开发服务器
cd src/play_back
python app.py

# 指定端口
PORT=5002 python app.py
```

**功能特性**：
- ✅ 自动启动Flask回放服务（端口5001）
- ✅ 自动启动Vite开发服务器（端口3000）
- ✅ 自动检测NPM可用性，优雅降级
- ✅ 进程管理和优雅退出
- ✅ 统一的错误处理和日志记录

#### 分离启动
```bash
# 方式1: 仅回放服务
cd src/play_back
VITE_DISABLED=true python app.py

# 方式2: 手动启动Vite
cd src/play_back
python app.py  # 启动回放服务
npm run dev    # 单独启动Vite（另一个终端）
```

### 5.2 访问地址
- npm版本: `http://localhost:5001/`
- UMD版本: `http://localhost:5001/old`

### 5.3 前端集成

#### 一键启动模式（推荐）
```bash
# 启动回放服务（自动包含Vite）
cd src/play_back
python app.py

# 访问地址
# - 开发模式：http://localhost:3000 (Vite热重载)
# - 生产模式：http://localhost:5001 (Flask静态服务)
```

**技术实现**：
- `app.py` 集成 `NPMIntegration` 类
- 自动检测NPM可用性
- 自动安装依赖（如需要）
- 后台启动Vite开发服务器
- 提供统一的进程管理

#### 传统分离模式
```bash
# 开发环境
cd src/play_back
VITE_DISABLED=true python app.py  # 仅启动回放服务
npm run dev                       # 手动启动Vite

# 生产环境
cd src/play_back
python app.py  # 包含静态文件服务
```

#### WebSocket连接示例
```javascript
// 开发模式下，Vite会自动代理WebSocket连接
const socket = io(); // 自动连接到当前域名

// 手动指定回放服务地址
const socket = io('http://localhost:5001');

// 启动回放
socket.emit('start_simulation', {
  date: '2024-12-23',
  start_time: '09:30',
  speed: 1000
});
```

#### 环境变量配置
```bash
# 禁用Vite自动启动
VITE_DISABLED=true python app.py

# 指定端口
PORT=5002 python app.py

# 开发模式（详细日志）
DEBUG=true python app.py
```

---

## 6. 优化建议

### 6.1 性能优化
- **数据预加载**: 后台预加载常用日期数据
- **内存管理**: 限制缓存数据量，及时清理
- **连接池**: 优化数据库连接池配置

### 6.2 功能增强
- **暂停/恢复**: 添加回放暂停功能
- **跳转时间**: 支持跳转到指定时间点
- **多速率**: 提供预设的回放速度选项

### 6.3 监控和调试
- **健康检查**: 添加服务状态检查接口
- **性能监控**: 记录推送延迟和数据处理时间
- **错误告警**: 完善错误处理和通知机制

---

## 7. 技术依赖

### 7.1 Python包
- Flask: Web框架
- Flask-SocketIO: WebSocket支持
- SQLAlchemy: 数据库ORM
- pandas: 数据处理
- logging: 日志记录

### 7.2 数据库
- 表: `stock_price_ibkr`
- 字段: `stock_code`, `date_time`, `Open`, `High`, `Low`, `Close`, `Volume`, `live_data`
- 索引: 建议在 `date_time` 和 `stock_code` 上建立索引

### 7.3 前端兼容
- Socket.IO客户端库
- TradingView Lightweight Charts
- 支持npm和UMD两种引入方式

### 7.4 前端开发环境（集成式）
- **集成启动**: `app.py` 自动启动Vite开发服务器
- **NPM集成器**: 复用 `src/utils/npm_integration.py`
- **进程管理**: 自动管理Vite进程生命周期
- **优雅降级**: NPM不可用时自动禁用Vite功能

#### 集成架构
```python
# 回放服务架构
app.py (Flask + SocketIO)
    ├── data_service.py (数据处理)
    ├── NPMIntegration (Vite管理)
    └── 静态资源服务 (备用方案)
```

#### 自动启动流程
1. **环境检测**: 检查NPM和Node.js可用性
2. **依赖安装**: 自动运行 `npm install`（如需要）
3. **Vite启动**: 后台启动Vite开发服务器
4. **代理配置**: Vite自动代理WebSocket到Flask
5. **状态监控**: 监控Vite进程状态
6. **优雅退出**: 应用退出时自动停止Vite

#### Vite配置详解
```javascript
// vite.config.js（回放服务专用配置）
export default defineConfig({
  root: 'src/web',                    // 设置根目录为src/web
  publicDir: 'static',                // 静态资源目录
  build: {
    outDir: '../../dist',             // 构建输出目录
    rollupOptions: {
      input: {
        main: 'templates/index-npm.html'  // 入口HTML文件
      }
    }
  },
  server: {
    port: 3000,                       // 开发服务器端口
    proxy: {
      '/api': {
        target: 'http://localhost:5001',     // API代理到回放服务
        changeOrigin: true
      },
      '/socket.io': {
        target: 'http://localhost:5001',     // WebSocket代理到回放服务
        changeOrigin: true,
        ws: true
      }
    }
  }
})
```

#### 启动方式对比
```bash
# 集成模式（推荐）
python app.py  # 自动启动Flask + Vite

# 分离模式
VITE_DISABLED=true python app.py  # 仅Flask
npm run dev                        # 手动Vite

# 传统模式
python app.py  # 仅Flask静态服务
```

#### 访问地址对比
- **开发模式**: `http://localhost:3000` (Vite热重载 + 代理)
- **生产模式**: `http://localhost:5001` (Flask静态服务)
- **备用模式**: `http://localhost:5001/old` (UMD版本)

---

## 8. 测试验证

### 8.1 数据验证
- 验证数据库中历史数据的完整性
- 检查不同日期的数据可用性
- 确认数据格式与主系统一致

### 8.2 功能测试
- 测试WebSocket连接和断开
- 验证启动/停止控制
- 检查数据推送的准确性和时效性
- **开发环境测试**: 验证Vite代理配置正常工作
- **热重载测试**: 确认前端代码修改能实时生效

### 8.3 性能测试
- 测试多客户端并发连接
- 验证长时间运行的稳定性
- 监控内存和CPU使用情况
- **开发工具**: 验证Vite构建性能和热重载速度

---

## 9. 开发工作流

### 9.1 推荐开发流程（集成模式）
1. **一键启动**: `cd src/play_back && python app.py`
   - 自动启动Flask回放服务（端口5001）
   - 自动启动Vite开发服务器（端口3000）
   - 自动处理依赖安装和代理配置
2. **访问开发页面**: `http://localhost:3000`
3. **修改前端代码**: 享受Vite热重载带来的快速反馈
4. **测试回放功能**: 在开发页面测试各种回放场景
5. **优雅退出**: Ctrl+C 自动停止所有服务

#### 分离模式开发流程
1. **启动回放服务**: `cd src/play_back && VITE_DISABLED=true python app.py`
2. **手动启动Vite**: `npm run dev`（另一个终端）
3. **访问开发页面**: `http://localhost:3000`

### 9.2 部署流程
1. **构建前端**: `npm run build`
2. **启动生产服务**: `cd src/play_back && python app.py`
3. **访问生产页面**: `http://localhost:5001`

#### 生产环境优化
```bash
# 禁用Vite（生产环境）
export VITE_DISABLED=true
python app.py

# 或者在代码中判断环境
if os.getenv('NODE_ENV') == 'production':
    VITE_DISABLED = True
```

### 9.3 代码结构
```
src/play_back/
├── app.py                    # 回放服务主入口（集成Vite启动）
├── data_service.py           # 数据服务核心
├── db.py                     # 数据库连接
└── config.py                 # 配置管理

src/web/
├── templates/
│   ├── index-npm.html        # npm版本页面 (Vite入口)
│   └── index.html            # UMD版本页面 (传统方式)
├── static/
│   ├── js/
│   │   ├── main-npm.js       # npm版本主入口
│   │   ├── chart-manager-npm.js  # 图表管理 (ES模块)
│   │   └── playback-control.js   # 回放控制
│   └── css/
│       └── main.css          # 样式文件

src/utils/
└── npm_integration.py        # NPM集成管理器（复用主系统）

vite.config.js                # Vite配置 (项目根目录)
package.json                  # NPM依赖配置
```

#### 集成架构优势
- ✅ **一键启动**: 单个命令启动完整开发环境
- ✅ **进程管理**: 自动管理Vite进程生命周期
- ✅ **优雅降级**: NPM不可用时回退到静态服务
- ✅ **统一配置**: 通过环境变量控制行为
- ✅ **错误处理**: 完善的错误处理和状态反馈
- ✅ **代码复用**: 复用主系统的NPM集成模块

---

## 10. 集成实现指南

### 10.1 app.py 集成Vite的实现方案

基于主系统中已有的 `src/utils/npm_integration.py`，回放服务可以轻松集成Vite启动功能：

#### 实现步骤
```python
# 在 app.py 中添加
import os
import signal
from src.utils.npm_integration import NPMIntegration

class PlaybackApp:
    def __init__(self):
        self.npm_integration = NPMIntegration()
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """设置信号处理器，确保优雅退出"""
        def signal_handler(sig, frame):
            print("\n🛑 正在停止回放服务...")
            self.cleanup()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start_services(self):
        """启动所有服务"""
        # 1. 启动Vite开发服务器（如果启用）
        if not os.getenv('VITE_DISABLED'):
            print("🚀 启动集成开发环境...")
            self.npm_integration.start_vite_server()
        else:
            print("📦 Vite已禁用，使用静态文件服务...")
        
        # 2. 启动Flask回放服务
        port = int(os.environ.get('PORT', 5001))
        print(f"🚀 启动回放服务: http://localhost:{port}")
        
        try:
            socketio.run(app, host='0.0.0.0', port=port, debug=True)
        except KeyboardInterrupt:
            pass
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'npm_integration'):
            self.npm_integration.stop_vite_server()

# 使用示例
if __name__ == '__main__':
    playback_app = PlaybackApp()
    playback_app.start_services()
```

### 10.2 环境变量配置

```bash
# 控制Vite启动
export VITE_DISABLED=true    # 禁用Vite自动启动
export PORT=5002             # 指定回放服务端口
export DEBUG=true            # 启用详细日志

# Node.js环境配置
export NODE_ENV=development  # 开发环境
export NODE_ENV=production   # 生产环境
```

### 10.3 配置文件增强

```python
# src/play_back/config.py
import os

class Config:
    # Flask配置
    PORT = int(os.getenv('PORT', 5001))
    DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'
    
    # Vite集成配置
    VITE_ENABLED = not os.getenv('VITE_DISABLED', 'false').lower() == 'true'
    VITE_PORT = int(os.getenv('VITE_PORT', 3000))
    
    # 数据库配置
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///playback.db')
    
    @classmethod
    def get_vite_target_url(cls):
        """获取Vite代理目标URL"""
        return f"http://localhost:{cls.PORT}"
```

### 10.4 错误处理和日志

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO if not Config.DEBUG else logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class PlaybackApp:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # ...其他初始化代码
    
    def start_vite_with_retry(self, max_retries=3):
        """带重试机制的Vite启动"""
        for attempt in range(max_retries):
            try:
                if self.npm_integration.start_vite_server():
                    self.logger.info("✅ Vite开发服务器启动成功")
                    return True
                else:
                    self.logger.warning(f"⚠️ Vite启动失败，尝试 {attempt + 1}/{max_retries}")
            except Exception as e:
                self.logger.error(f"❌ Vite启动异常: {e}")
        
        self.logger.warning("🔄 Vite启动失败，回退到静态文件服务")
        return False
```

### 10.5 优势总结

#### 开发体验提升
- **一键启动**: `python app.py` 启动完整开发环境
- **热重载**: 前端代码修改实时生效
- **统一管理**: 单一入口管理所有服务
- **智能降级**: NPM不可用时自动禁用

#### 部署简化
- **环境隔离**: 通过环境变量控制行为
- **生产优化**: 生产环境自动禁用Vite
- **容器友好**: 支持Docker等容器化部署
- **配置灵活**: 支持多种部署模式

#### 维护友好
- **代码复用**: 复用主系统的NPM集成模块
- **统一架构**: 与主系统保持一致的集成模式
- **文档完善**: 详细的使用指南和最佳实践
- **错误处理**: 完善的错误处理和日志记录
