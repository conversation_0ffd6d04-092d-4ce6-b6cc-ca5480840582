# 回放服务 WebSocket 数据推送实现详解

## 目标
让 `src/play_back/data_service.py` 获取到的股票数据，能够通过 WebSocket 实时推送到前端页面，实现回放/模拟行情的前后端联动。

---

## 1. 技术选型
- 后端：Flask + Flask-SocketIO
- 前端：原有页面无需改动，直接监听 `socket.io` 事件即可

---

## 2. 后端实现步骤

### 2.1 安装依赖
```bash
pip install flask-socketio
```

### 2.2 修改 app.py 启用 SocketIO
```python
from flask_socketio import SocketIO
# ...existing code...
socketio = SocketIO(app, cors_allowed_origins="*")

# 替换 app.run 为 socketio.run
if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5002))
    print(f"[INFO] 启动独立回放前端服务: http://localhost:{port}")
    socketio.run(app, host='0.0.0.0', port=port, debug=True)
```

### 2.3 data_service.py 绑定 socketio 实例
```python
# app.py
from src.play_back import data_service
# ...existing code...
data_service.set_socketio(socketio)
```

### 2.4 在 data_service.py 中推送数据
- 在 `_simulation_loop` 内部，调用：
```python
self.socketio.emit('market_update', {
    'timestamp': self.current_simulation_time.isoformat(),
    'top5_stocks': top5_stocks,
    'stocks_data': stocks_data
}, namespace='/')
```

---

## 3. 前端接收数据
前端无需改动，原有 socket.io 客户端会自动收到 `market_update` 事件。

---

## 4. 完整流程示意
1. 启动 Flask + SocketIO 服务
2. 前端页面连接 WebSocket
3. 后端 data_service 启动模拟推送
4. 每分钟通过 socketio.emit 向前端推送最新行情数据
5. 前端收到 `market_update` 事件后自动刷新页面

---

## 5. 参考代码片段

### app.py 关键部分
```python
from flask_socketio import SocketIO
from src.play_back import data_service
# ...existing code...
socketio = SocketIO(app, cors_allowed_origins="*")
data_service.set_socketio(socketio)

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5002, debug=True)
```

### data_service.py 推送部分
```python
if self.socketio and stocks_data:
    self.socketio.emit('market_update', {
        'timestamp': self.current_simulation_time.isoformat(),
        'top5_stocks': top5_stocks,
        'stocks_data': stocks_data
    }, namespace='/')
```

---

## 6. 注意事项
- 确保 Flask-SocketIO 版本与前端 socket.io 客户端兼容
- 若端口被占用，修改 `PORT` 环境变量
- 如需支持多客户端，Flask-SocketIO 会自动广播

---

> 本文档适用于回放/模拟行情的 WebSocket 数据推送场景，后续如有架构调整请及时同步更新。
