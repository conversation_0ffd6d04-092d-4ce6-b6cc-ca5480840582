# 📊 股票回放服务使用指南

## 🎯 功能概述

股票回放服务是一个独立的股票数据回放系统，支持历史股票数据的实时模拟推送，为前端图表提供逼真的历史数据回放体验。

## ✨ 核心功能

- ✅ **数据库连接**: 自动连接主系统数据库，读取历史数据
- ✅ **一次性数据加载**: 后端加载完整日期数据，一次推送给前端
- ✅ **前端时间模拟**: 前端接收完整数据后模拟时间流逝
- ✅ **数据重采样**: 将1分钟数据重采样为5分钟K线
- ✅ **Top5计算**: 计算指定日期涨幅前5股票
- ✅ **WebSocket推送**: 通过WebSocket推送完整数据包
- ✅ **真实数据**: 推送数据库中的真实历史股票数据（非模拟数据）
- ✅ **高效架构**: 减少网络通信，前端控制回放节奏
- ✅ **多页面支持**: 提供控制面板、测试页面等
- ✅ **开发集成**: 支持Vite热重载开发模式
- ✅ **优雅退出**: 信号处理，确保资源正确释放

## 🚀 快速启动

### 方式1: 使用快速启动脚本（推荐）
```bash
cd /Users/<USER>/git/stock_xxx
python start_playback.py
```

### 方式2: 直接启动
```bash
cd /Users/<USER>/git/stock_xxx
python src/play_back/app.py
```

### 方式3: 开发模式（包含Vite热重载）
```bash
cd /Users/<USER>/git/stock_xxx
VITE_DISABLED=false python src/play_back/app.py
```

## 🌐 访问地址

启动后可访问以下页面：

| 页面 | 地址 | 说明 |
|------|------|------|
| 🎮 **回放控制** | http://localhost:5001/playback | **推荐使用** - 完整的回放控制面板 |
| 🏠 主页 | http://localhost:5001/ | 标准图表页面 (npm版本) |
| 🧪 WebSocket测试 | http://localhost:5001/test-ws | WebSocket连接和事件测试 |
| 🔄 传统页面 | http://localhost:5001/old | 传统UMD版本页面 |

## 🔍 数据类型说明

### 🎯 真实历史数据回放
- **用途**: 主要功能，一次性加载数据库中的真实历史股票数据
- **触发**: 通过 `start_simulation` WebSocket事件
- **数据源**: `stock_price_ibkr` 表中 `live_data = 0` 的历史数据
- **特点**: 后端一次推送完整数据，前端模拟时间流逝

### 🧪 演示数据推送  
- **用途**: 仅用于测试WebSocket连接和前端图表功能
- **触发**: 通过 `start_live_data` WebSocket事件
- **数据源**: 程序生成的简单递增数据
- **特点**: 固定模式，便于验证连接正常

**重要**: 生产使用请选择"真实历史数据回放"模式！

## 🎮 使用步骤

1. **启动服务**
   ```bash
   python start_playback.py
   ```

2. **打开控制面板**
   - 访问: http://localhost:5001/playback

3. **配置回放参数**
   - 选择日期 (例如: 2024-06-20)
   - 选择开始时间 (09:30, 10:00, 11:00, 14:00, 15:00)
   - 选择推送速度 (500ms=快速, 1000ms=正常, 2000ms=慢速)

4. **加载完整数据**
   - 点击 "🚀 开始回放" 按钮
   - 后端一次性加载并推送该日期的完整数据
   - 前端接收数据包，准备开始时间模拟

5. **前端时间模拟**
   - 前端基于接收的完整数据模拟时间流逝
   - 按照设定的速度逐步显示K线图表
   - 用户可在前端控制暂停/继续/速度调节

## 📊 数据格式

### WebSocket事件

#### 客户端发送事件
- `start_simulation`: **加载完整历史数据**
  ```json
  {
    "date": "2024-06-20",
    "start_time": "09:30", 
    "speed": 1000
  }
  ```
- `stop_simulation`: 重置加载状态
- `start_live_data`: 启动演示数据推送（仅用于测试）
- `stop_live_data`: 停止演示数据推送
- `ping`: 连接测试

#### 服务器推送事件
- `complete_daily_data`: **完整日期数据包**（主要事件）
  ```json
  {
    "type": "complete_daily_data",
    "date": "2024-06-20",
    "start_time": "09:30",
    "speed_ms": 1000,
    "top5_stocks": ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA"],
    "stocks_data": {
      "AAPL": [
        {
          "time": 1703264700,
          "open": 195.89,
          "high": 196.95,
          "low": 195.89,
          "close": 196.95,
          "volume": 423800
        }
      ]
    },
    "data_info": {
      "total_stocks": 5,
      "total_candles": 250,
      "time_range": {
        "start": 1703264400,
        "end": 1703285400
      }
    },
    "instructions": {
      "message": "前端可基于此数据模拟时间流逝",
      "usage": "使用speed_ms控制回放速度，按时间戳顺序显示K线"
    }
  }
  ```
- `simulation_status`: 数据加载状态更新
- `stocks_list`: 真实股票列表（从数据库获取）
- `stock_data`: 演示数据（仅用于测试连接）

## 🔧 配置选项

### 环境变量

| 变量 | 默认值 | 说明 |
|------|--------|------|
| `PORT` | 5001 | 服务端口 |
| `VITE_DISABLED` | false | 是否禁用Vite开发服务器 |
| `DEBUG` | false | 是否启用调试模式 |

### 使用示例
```bash
# 指定端口
PORT=5002 python start_playback.py

# 禁用Vite（生产模式）
VITE_DISABLED=true python start_playback.py

# 启用调试模式
DEBUG=true python start_playback.py
```

## 🏗️ 架构说明

```
回放服务架构（一次性推送模式）
├── app.py                 # Flask + SocketIO 主入口
├── data_service.py        # 数据加载、处理、一次性推送核心
├── db.py                  # 数据库连接管理
├── config.py              # 配置管理
└── NPMIntegration         # Vite开发服务器集成

数据流:
数据库 → load_daily_data() → resample_to_5min() → 一次性WebSocket推送 → 前端时间模拟
```

## 🧪 功能测试

运行功能验证脚本：
```bash
python test_playback_service.py
```

## 📝 开发说明

### 添加新的WebSocket事件
在 `app.py` 中添加：
```python
@socketio.on('your_event')
def handle_your_event(data):
    session_id = request.sid
    # 处理逻辑
    socketio.emit('response_event', response_data, room=session_id)
```

### 修改数据处理逻辑
在 `data_service.py` 中的相关方法：
- `load_daily_data()`: 数据加载
- `get_top5_stocks()`: Top5计算
- `resample_to_5min()`: 数据重采样
- `_push_complete_daily_data()`: 一次性完整数据推送

## 🐛 常见问题

### 1. 数据库连接失败
- 检查数据库配置
- 确保主系统数据库可访问

### 2. 没有历史数据
- 检查 `stock_price_ibkr` 表中 `live_data = 0` 的数据
- 使用 `python check_playback_data.py` 检查可用日期

### 3. WebSocket连接失败
- 检查防火墙设置
- 确认端口没有被占用
- 使用 http://localhost:5001/test-ws 测试连接

### 4. Vite启动失败
- 确保已安装 Node.js 和 npm
- 运行 `npm install` 安装依赖
- 或设置 `VITE_DISABLED=true` 禁用Vite

## 📈 性能优化

- **一次性数据传输**: 后端加载完整日期数据，减少网络往返
- **前端控制**: 前端模拟时间流逝，响应更快速
- **内存管理**: 自动清理无用的历史数据
- **数据压缩**: 只推送Top5股票数据，减少网络负载
- **缓存机制**: 相同日期的重复请求使用缓存数据

## 🔄 更新日志

- ✅ 实现核心数据加载和推送功能
- ✅ 添加WebSocket事件处理
- ✅ 集成Vite开发环境支持
- ✅ 创建回放控制面板
- ✅ 添加ping-pong响应修复
- ✅ 完善错误处理和日志记录
- ✅ 优化进程管理和优雅退出

---

💡 **提示**: 首次使用建议访问回放控制页面 (http://localhost:5001/playback) 进行测试

## 最新优化：回放模式前端数据处理

### 优化内容

回放模式的数据处理流程已经进行了全面优化：

1. **后端数据推送**：
   - 后端现在推送的是原始1分钟K线数据，而不是5分钟重采样数据
   - 这为前端提供了更精确的数据控制能力

2. **前端数据管理**：
   - 新增了专门的回放数据结构`completePlaybackData`：存储完整的1分钟数据
   - 新增了`currentPlaybackTime`：跟踪每只股票的当前回放时间点
   - 新增了`playbackSettings`：控制回放速度和状态

3. **回放控制流程**：
   - 页面加载时获取页面设置的日期，获取数据并推送到前端
   - 初始化图表时，只显示开始时间之前的数据
   - 点击"开始回放"后，根据速度设置动态控制数据更新频率

4. **核心改进函数**：
   - `initPlaybackData`：初始化回放数据，只加载开始时间前的数据
   - `advancePlayback`：推进回放到下一时间点，动态控制数据更新
   - `startFrontendPlayback`：根据速度设置控制回放节奏

### 使用方法

1. 在回放页面选择日期和开始时间
2. 选择回放速度（500ms-5000ms）
3. 点击"开始回放"按钮
4. 系统会首先加载并显示开始时间之前的数据
5. 然后按照设定的速度逐步更新图表

### 技术实现

回放模式的数据流程如下：

1. 用户点击"开始回放"按钮
2. 前端发送`start_simulation`事件到后端
3. 后端通过`data_service.py`加载指定日期的原始1分钟数据
4. 后端通过WebSocket推送`complete_daily_data`事件和完整数据包
5. 前端`handleCompleteDailyData`函数接收数据：
   - 存储完整数据到`completePlaybackData`
   - 初始化图表，只显示开始时间之前的数据
6. 前端`startFrontendPlayback`函数开始回放：
   - 根据设定速度创建定时器
   - 按时间顺序逐步推送数据到图表
   - 使用`advancePlayback`函数更新每只股票的图表

系统会根据回放速度自动调整每次更新的数据点数量，保证回放流畅性。
