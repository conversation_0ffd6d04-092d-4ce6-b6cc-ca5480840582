# 回放模式前端数据处理优化方案

## 当前问题

目前回放模式存在以下问题：

1. 后端推送的是重采样后的5分钟K线数据，而不是原始1分钟数据
2. 前端数据处理逻辑未针对回放模式做优化，可能导致更新不顺畅
3. 回放模式下的数据变化检查可能导致部分更新被跳过
4. 无法精确控制回放速度和起始点

## 优化目标

1. 后端直接推送原始1分钟数据，不进行重采样
2. 前端根据设定的开始时间筛选数据，只初始化显示开始时间之前的数据
3. 回放过程中根据速度设置灵活控制数据更新频率
4. 确保回放模式下K线更新不会被数据变化检查跳过

## 详细实现方案

### 1. 数据结构设计

在chart-manager-npm.js中添加新的数据结构：

```javascript
// 在ChartManager构造函数中添加
this.completePlaybackData = new Map(); // 存储后端推送的全部1分钟数据
this.currentPlaybackTime = new Map(); // 存储当前回放时间点
this.playbackSettings = {
    startTime: null,      // 回放开始时间（时间戳）
    speed: 1000,          // 回放速度（毫秒）
    isPlaying: false      // 是否正在回放
};
```

### 2. 数据接收与处理流程

当接收到后端推送的完整数据时：

1. 存储完整数据到新增的completePlaybackData字典
2. 根据设定的开始时间筛选数据
3. 只将开始时间之前的数据存入oneMinData并初始化图表
4. 其余数据在回放过程中逐步使用

### 3. 修改doUpdateChart函数

确保在回放模式下跳过数据变化检查：

```javascript
doUpdateChart(symbol, newData) {
    // ...existing code...
    
    // 转换数据格式
    const formattedData = this.formatChartData(newData);
    
    // 回放模式下不聚合数据
    let aggregatedData = formattedData;
    if (!window.isPlaybackMode) {
        aggregatedData = this.aggregateTo5MinData(formattedData);
    }
    
    // 检查数据是否有变化 (回放模式下跳过此检查)
    const currentData = this.chartData.get(symbol);
    if (!window.isPlaybackMode && this.isDataEqual(currentData, aggregatedData)) {
        return; // 数据无变化，跳过更新
    }

    // ...remaining code...
}
```

### 4. 新增回放控制函数

添加专门的回放控制函数，用于处理回放过程：

```javascript
// 初始化回放数据
initPlaybackData(symbol, completeData, startTime) {
    this.completePlaybackData.set(symbol, completeData);
    this.currentPlaybackTime.set(symbol, startTime);
    
    // 筛选开始时间之前的数据
    const initialData = completeData.filter(d => d.time <= startTime);
    
    // 只存储初始数据到oneMinData
    this.oneMinData.set(symbol, initialData);
    
    // 更新图表显示初始数据
    this.updateChartIncremental(symbol, initialData);
}

// 推进回放到下一个时间点
advancePlayback(symbol, nextTime, dataPoints) {
    if (!this.completePlaybackData.has(symbol)) return;
    
    const allData = this.completePlaybackData.get(symbol);
    const currentTime = this.currentPlaybackTime.get(symbol);
    
    // 获取下一个时间点的数据
    const nextData = allData.filter(d => d.time > currentTime && d.time <= nextTime);
    
    // 限制数据点数量
    const limitedData = nextData.slice(0, dataPoints);
    
    if (limitedData.length > 0) {
        // 更新当前时间
        this.currentPlaybackTime.set(symbol, nextTime);
        
        // 更新图表
        this.updateChart(symbol, limitedData, true);
    }
}
```

### 5. 修改updateChartIncremental函数

确保在回放模式下对增量更新的处理是正确的：

```javascript
updateChartIncremental(symbol, newOneMinData) {
    // ...existing code...
    
    // 回放模式下处理
    if (window.isPlaybackMode) {
        // 在回放模式下，直接使用提供的数据更新图表
        // 不进行数据变化检测
        try {
            const validData = this.validateAndCleanData(newOneMinData);
            const oldFiveMin = this.fiveMinData.get(symbol) || [];
            
            // 聚合为5分钟数据
            const periods = new Set(validData.map(d => Math.floor(d.time / 300) * 300));
            const newFiveMinCandles = [];
            
            for (const period of periods) {
                const oneMinInPeriod = validData.filter(d => 
                    Math.floor(d.time / 300) * 300 === period
                );
                const aggregated = this.aggregateOnePeriodEnhanced(oneMinInPeriod);
                if (aggregated) {
                    newFiveMinCandles.push(aggregated);
                }
            }
            
            const updatedFiveMin = this.batchUpdate5MinCandles(oldFiveMin, newFiveMinCandles);
            this.updateChartSmart(symbol, updatedFiveMin, periods);
            
            // 更新缓存
            const oldOneMin = this.oneMinData.get(symbol) || [];
            const mergedOneMin = [...oldOneMin];
            
            // 合并新数据，避免重复
            for (const newPoint of validData) {
                const existingIndex = mergedOneMin.findIndex(p => p.time === newPoint.time);
                if (existingIndex >= 0) {
                    mergedOneMin[existingIndex] = newPoint; // 替换
                } else {
                    mergedOneMin.push(newPoint); // 添加
                }
            }
            
            // 按时间排序
            mergedOneMin.sort((a, b) => a.time - b.time);
            
            // 更新数据缓存
            this.oneMinData.set(symbol, mergedOneMin);
            this.fiveMinData.set(symbol, updatedFiveMin);
            this.chartData.set(symbol, updatedFiveMin);
            
        } catch (error) {
            console.error(`回放模式增量更新失败: ${symbol}`, error);
        }
        return;
    }
    
    // 非回放模式下的原有处理逻辑
    // ...existing code...
}
```

### 6. 前端回放控制函数优化

在playback-control.html中优化回放控制函数：

```javascript
function startFrontendPlayback() {
    if (!completeDataCache) {
        log('❌ 没有缓存的数据可以回放');
        return;
    }
    
    const speed = parseInt(document.getElementById('speedInput').value);
    log(`▶️ 开始前端时间模拟回放，速度: ${speed}ms`);
    
    // 清除之前的定时器
    if (playbackTimer) {
        clearInterval(playbackTimer);
    }
    
    // 获取所有时间点
    const allTimestamps = new Set();
    Object.values(completeDataCache.stocks_data || {}).forEach(stockData => {
        stockData.forEach(point => allTimestamps.add(point.time));
    });
    const sortedTimestamps = Array.from(allTimestamps).sort((a, b) => a - b);
    
    log(`📊 总共 ${sortedTimestamps.length} 个时间点需要回放`);
    
    // 计算每次应推送的数据点数量
    // 根据速度动态调整，速度越快，每次推送的数据点越少
    const dataPointsPerUpdate = Math.max(1, Math.ceil(5000 / speed));
    log(`📈 每次更新 ${dataPointsPerUpdate} 个数据点`);
    
    // 开始逐步推送数据
    playbackTimer = setInterval(() => {
        if (playbackIndex >= sortedTimestamps.length) {
            // 回放结束
            log('✅ 前端回放结束');
            clearInterval(playbackTimer);
            playbackTimer = null;
            updateSimulationStatus('stopped', '回放完成');
            return;
        }
        
        // 计算本次要推送的时间范围
        const startIdx = playbackIndex;
        const endIdx = Math.min(playbackIndex + dataPointsPerUpdate, sortedTimestamps.length);
        
        if (startIdx >= endIdx) return;
        
        const currentTimestamp = sortedTimestamps[startIdx];
        const endTimestamp = sortedTimestamps[endIdx - 1];
        
        // 模拟market_update事件数据
        const marketData = {
            timestamp: new Date(endTimestamp * 1000).toISOString(),
            top5_stocks: Object.keys(completeDataCache.stocks_data || {}),
            stocks_data: {}
        };
        
        // 为每只股票筛选当前时间范围的数据
        Object.entries(completeDataCache.stocks_data || {}).forEach(([symbol, stockData]) => {
            const rangePoints = stockData.filter(point => 
                point.time >= currentTimestamp && point.time <= endTimestamp
            );
            
            if (rangePoints.length > 0) {
                marketData.stocks_data[symbol] = rangePoints;
            }
        });
        
        // 更新界面
        updateMarketData(marketData);
        document.getElementById('currentTime').textContent = new Date(endTimestamp * 1000).toLocaleString();
        
        playbackIndex = endIdx;
    }, speed);
}
```

## 实施顺序

1. 修改后端data_service.py，确保直接推送1分钟数据
2. 在chart-manager-npm.js中添加新的数据结构
3. 修改doUpdateChart函数，在回放模式下跳过数据变化检查
4. 添加专用回放控制函数
5. 修改updateChartIncremental函数
6. 优化前端回放控制函数

## 预期效果

1. 页面加载后，图表只显示到设定的开始时间
2. 点击"开始回放"后，根据速度设置动态控制数据更新频率
3. 回放过程流畅，没有因数据变化检查导致的更新跳过
4. 灵活控制回放起始点和速度 