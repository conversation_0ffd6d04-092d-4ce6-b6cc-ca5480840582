# 回放服务 WebSocket 事件说明

## 1. 客户端 -> 服务端

### start_simulation
- 说明：请求启动回放模拟
- 参数：
  - date: 日期（YYYY-MM-DD）
  - start_time: 起始时间（HH:MM），默认09:30
  - speed: 步进速度（毫秒/步），默认1000
- 示例：
```js
socket.emit('start_simulation', { date: '2024-06-28', start_time: '09:30', speed: 1000 });
```
- 返回：
  - { status: 'started' } 或 { status: 'error', msg: '启动失败' }

### stop_simulation
- 说明：请求停止回放模拟
- 参数：无
- 示例：
```js
socket.emit('stop_simulation');
```
- 返回：
  - { status: 'stopped' }

---

## 2. 服务端 -> 客户端

### market_update
- 说明：推送当前回放行情数据
- 数据结构：
```json
{
  "timestamp": "2024-06-28T09:35:00",
  "top5_stocks": ["AAPL", "TSLA", ...],
  "stocks_data": {
    "AAPL": [ {"time":..., "open":..., ...}, ...],
    ...
  }
}
```
- 推送频率：每步（如每分钟/每秒，取决于 speed 参数）

---

> 本文档描述了回放服务的 WebSocket 事件协议，便于前后端联调和扩展。
