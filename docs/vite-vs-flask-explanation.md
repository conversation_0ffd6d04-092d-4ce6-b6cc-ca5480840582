# 🔧 Vite vs Flask：技术架构详解

## 📊 概述对比

| 特性 | Flask | Vite |
|------|-------|------|
| **类型** | Python Web框架 | 前端构建工具 |
| **主要用途** | 后端服务器 | 前端开发服务器 |
| **语言** | Python | JavaScript/TypeScript |
| **端口** | 5000 | 3000 |
| **角色** | 数据处理、API服务 | 静态资源服务、开发体验 |

## 🐍 Flask - Python Web框架

### 什么是Flask？
Flask是一个轻量级的Python Web框架，用于构建Web应用程序和API服务。

### 在你的项目中的作用：
```python
# src/web/web_server.py
@app.route('/api/stocks')
def get_stocks():
    return jsonify(web_stock_data)

@socketio.on('connect')
def handle_connect():
    print('客户端已连接')
```

### Flask负责的功能：
1. **🔌 WebSocket服务**：实时推送股票数据
2. **📡 REST API**：提供股票数据接口
3. **📊 数据处理**：处理富途API返回的股票数据
4. **🗃️ 数据存储**：管理内存中的股票数据
5. **🌐 静态文件服务**：为CDN版本提供HTML/CSS/JS文件

### 启动方式：
```python
# 在main.py中启动
start_web_server_thread(
    host='127.0.0.1',
    port=5000,
    web_stock_data=web_stock_data
)
```

## ⚡ Vite - 前端构建工具

### 什么是Vite？
Vite是一个现代化的前端构建工具，专门为JavaScript/TypeScript项目提供快速的开发体验。

### 在你的项目中的作用：
```javascript
// vite.config.js
export default defineConfig({
  root: 'src/web',
  server: {
    port: 3000,
    proxy: {
      '/api': 'http://localhost:5000',        // API代理到Flask
      '/socket.io': {                         // WebSocket代理到Flask
        target: 'http://localhost:5000',
        ws: true
      }
    }
  }
})
```

### Vite负责的功能：
1. **🚀 热重载**：代码修改后立即在浏览器中更新
2. **📦 模块管理**：处理ES模块导入/导出
3. **🔄 代理服务**：将API请求转发到Flask后端
4. **🏗️ 依赖预构建**：优化npm包的加载速度
5. **⚡ 快速启动**：毫秒级的开发服务器启动

### 启动方式：
```bash
npm run dev  # 启动Vite开发服务器
```

## 🔄 它们如何协作？

### 数据流向：
```
浏览器 → Vite(3000) → 代理 → Flask(5000) → 数据处理 → 返回给浏览器
```

### 具体流程：
1. **用户访问** `http://localhost:3000/templates/index-npm.html`
2. **Vite服务器** 提供HTML页面和静态资源
3. **页面加载** NPM版本的JavaScript代码
4. **WebSocket连接** 通过Vite代理连接到Flask的WebSocket服务
5. **API请求** 通过Vite代理转发到Flask的REST API
6. **数据获取** Flask从富途API获取股票数据
7. **实时推送** Flask通过WebSocket推送数据到前端
8. **图表更新** 前端使用TradingView Lightweight Charts显示数据

## 🎯 双版本架构

### CDN版本（纯Flask）：
```
浏览器 → Flask(5000) → 直接服务静态文件 + WebSocket + API
```

### NPM版本（Vite + Flask）：
```
浏览器 → Vite(3000) → 代理 → Flask(5000) → WebSocket + API
```

## 💡 为什么需要两个服务器？

### Flask的优势：
- ✅ 处理复杂的业务逻辑
- ✅ 与Python生态系统集成（pandas、numpy等）
- ✅ 稳定的WebSocket服务
- ✅ 数据库操作和数据持久化

### Vite的优势：
- ✅ 极快的热重载开发体验
- ✅ 现代化的ES模块支持
- ✅ 优秀的TypeScript支持
- ✅ 生产环境构建优化

## 🚀 实际运行示例

### 启动过程：
```bash
# 1. Flask后端启动
=== [Main] 准备启动Web服务器，时间: 1751118890.648701 ===
Web服务器已启动: http://localhost:5000

# 2. NPM集成启动  
=== [Main] 准备启动NPM集成，时间: 1751118890.814657 ===
🚀 启动Vite开发服务器...
✅ Vite开发服务器启动成功
📊 NPM版本: http://localhost:3000/templates/index-npm.html
```

### 访问方式：
- **开发推荐**：http://localhost:3000/templates/index-npm.html（NPM版本）
- **快速测试**：http://localhost:5000/index-es.html（CDN版本）

## 🔧 故障排除

### 常见问题：
1. **端口冲突**：确保5000和3000端口都未被占用
2. **代理失败**：检查Vite配置中的代理设置
3. **WebSocket连接失败**：确认Flask后端的WebSocket服务正常

### 调试方法：
```bash
# 检查Flask状态
curl http://localhost:5000/api/health

# 检查Vite状态  
curl http://localhost:3000/

# 查看进程
ps aux | grep -E "python.*flask|npm.*dev"
```

## 🎯 总结

**Flask** = 🏠 后端房子（数据处理、业务逻辑）
**Vite** = 🚪 前门（开发体验、资源优化）

它们各司其职，共同为你的股票监控应用提供：
- 📊 实时股票数据处理（Flask）
- ⚡ 优秀的前端开发体验（Vite）
- 🔄 无缝的前后端数据交互
- 🎨 现代化的用户界面
