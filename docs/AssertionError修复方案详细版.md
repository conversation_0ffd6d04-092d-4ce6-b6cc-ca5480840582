# 🔧 AssertionError: write() before start_response 修复方案

## 🎯 问题分析

`AssertionError: write() before start_response` 错误表明 Flask/Werkzeug 在调用 `start_response()` 之前就尝试写入响应体。这通常发生在以下情况：

1. ✅ **已修复：** 静态文件 404 错误 (`performance_test.js`)
2. 🔧 **正在修复：** 中间件异常处理问题
3. 🔧 **正在修复：** WebSocket 事件处理器异常
4. 🔧 **正在修复：** 缺少全局错误处理器

## 📋 修复措施

### 1. 静态文件路径修复 ✅
```bash
# 移动文件到正确位置
cp docs/migration/performance_test.js src/web/static/js/performance_test.js

# 修正 HTML 引用
# 从: <script src="/docs/migration/performance_test.js"></script>
# 到: <script src="/static/js/performance_test.js"></script>
```

### 2. Flask 中间件错误处理增强 🔧
**文件：** `src/web/web_server.py`

```python
# 增强 after_request 装饰器
@app.after_request
def after_request(response):
    try:
        # 为ES模块文件设置正确的MIME类型
        if response.content_type and response.content_type.startswith('text/html'):
            response.headers['X-Version'] = 'ES-Module-Support'
        # 只有在 request 上下文可用时才处理 JS 文件
        if hasattr(request, 'path') and request.path and request.path.endswith('.js') and '/modules/' in request.path:
            response.headers['Content-Type'] = 'application/javascript; charset=utf-8'
    except Exception as e:
        # 如果出现任何错误，记录但不中断响应
        logging.warning(f"after_request 处理失败: {e}")
    finally:
        # 确保总是返回响应对象
        return response
```

### 3. 添加全局错误处理器 🔧
```python
# 添加错误处理器
@app.errorhandler(Exception)
def handle_exception(e):
    """处理所有未捕获的异常"""
    logging.error(f"未处理的异常: {e}", exc_info=True)
    try:
        return jsonify({"error": "服务器内部错误", "message": str(e)}), 500
    except Exception as fallback_error:
        # 如果连 JSON 响应都失败，返回最简单的响应
        logging.error(f"错误处理器也失败了: {fallback_error}")
        return "Internal Server Error", 500

@app.errorhandler(404)
def handle_404(e):
    """处理404错误"""
    logging.warning(f"404错误: {request.url if hasattr(request, 'url') else 'unknown URL'}")
    return jsonify({"error": "页面未找到", "status": 404}), 404

@app.errorhandler(500)
def handle_500(e):
    """处理500错误"""
    logging.error(f"500错误: {e}")
    return jsonify({"error": "服务器内部错误", "status": 500}), 500
```

### 4. WebSocket 事件处理器错误保护 🔧
**文件：** `src/web/websocket_server.py`

为所有 WebSocket 事件处理器添加 try-catch 保护：

```python
@self.socketio.on('connect')
def handle_connect(auth):
    try:
        session_id = self._get_session_id()
        self.session_subscriptions[session_id] = set()
        emit('status', {
            'type': 'connected',
            'message': 'WebSocket连接成功',
            'timestamp': time.time()
        })
    except Exception as e:
        logging.error(f"WebSocket连接处理失败: {e}")

@self.socketio.on('subscribe')
def handle_subscribe(data):
    try:
        session_id = self._get_session_id()
        symbol = data.get('symbol')
        if not symbol:
            emit('error', {'message': '股票代码不能为空'})
            return
        # ... 订阅逻辑 ...
    except Exception as e:
        logging.error(f"WebSocket订阅处理失败: {e}")
        emit('error', {'message': f'订阅失败: {str(e)}'})
```

## 🚀 验证步骤

### 1. 重启服务器
```bash
# 停止当前服务器
# 重新运行
python test_web_only.py
```

### 2. 检查日志
- ✅ 不再有 `performance_test.js 404` 错误
- ✅ 不再有 `AssertionError: write() before start_response`
- ✅ WebSocket 连接正常
- ✅ 页面加载正常

### 3. 测试场景
1. **刷新页面** - 应该不再出现 AssertionError
2. **WebSocket 连接** - 应该正常建立连接
3. **数据订阅** - 应该正常接收数据
4. **错误场景** - 错误应该被正确捕获和处理

## 🔍 故障排查

如果问题仍然存在，检查以下方面：

### 1. 检查服务器端口
```bash
# Flask 后端: http://localhost:5001
# Vite 开发服务器: http://localhost:3000
lsof -i :5001
lsof -i :3000
```

### 2. 检查访问URL
- ✅ `http://localhost:5001/npm` (Flask 服务器)
- ✅ `http://localhost:3000/templates/index-npm.html` (Vite 服务器)

### 3. 浏览器开发者工具
- **Network 面板：** 检查是否还有 404 错误
- **Console 面板：** 检查 JavaScript 错误
- **WebSocket 连接：** 检查 Socket.IO 连接状态

### 4. 服务器日志
监控服务器输出，查看是否还有：
- `AssertionError: write() before start_response`
- `WebSocket处理失败`
- `after_request 处理失败`

## 📊 修复原理

### AssertionError 的根本原因
这个错误发生在 WSGI 协议层面：
1. **正常流程：** `start_response()` → 设置响应头 → `write()` 写入响应体
2. **错误流程：** 直接 `write()` → 触发 AssertionError

### 修复策略
1. **预防异常：** 在可能出错的地方添加 try-catch
2. **正确处理：** 确保所有响应都正确设置头部
3. **兜底机制：** 添加全局错误处理器作为最后防线

## 🎉 预期结果

修复后应该实现：
- ✅ 页面正常加载，无 AssertionError
- ✅ WebSocket 连接稳定
- ✅ 实时数据正常推送
- ✅ 图表正常显示和更新
- ✅ 错误得到优雅处理，不影响用户体验

**这些修复应该彻底解决 AssertionError 问题！** 🎯
