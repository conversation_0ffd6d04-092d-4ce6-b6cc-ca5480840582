{"timestamp": "2025-06-28 22:09:13", "results": {"server_tests": {"server_health": {"status": "passed", "status_code": 200, "response_time": 0.006751060485839844, "content_length": 13910}}, "page_tests": {"umd_page": {"status": "passed", "checks": {"tradingview_script": true, "chart_container": true, "websocket_client": true, "chart_manager": true, "version_indicator": true, "version_switch": true}, "content_length": 12698}, "es_page": {"status": "passed", "checks": {"tradingview_script": true, "chart_container": true, "importmap": true, "main_es_script": true, "version_indicator": true, "version_switch": true}, "content_length": 3058}}, "static_tests": {"static_resources": {"status": "passed", "accessible_count": 8, "total_count": 8, "resources": {"/static/js/chart_manager.js": {"status_code": 200, "size": 46725, "accessible": true}, "/static/js/websocket_client.js": {"status_code": 200, "size": 8863, "accessible": true}, "/static/js/main-es.js": {"status_code": 200, "size": 9962, "accessible": true}, "/static/js/modules/websocket-client.js": {"status_code": 200, "size": 5303, "accessible": true}, "/static/js/modules/chart-manager.js": {"status_code": 200, "size": 21566, "accessible": true}, "/static/js/modules/utils.js": {"status_code": 200, "size": 7409, "accessible": true}, "/static/js/version-preference.js": {"status_code": 200, "size": 5722, "accessible": true}, "/static/css/main.css": {"status_code": 200, "size": 839, "accessible": true}}}}, "api_tests": {"api_endpoints": {"status": "passed", "endpoints": {"stock_data_post": {"status_code": 404, "accessible": true}, "health_check": {"status_code": 404, "accessible": true}}}}, "errors": []}}