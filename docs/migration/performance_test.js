/**
 * 性能和兼容性测试脚本
 * 对比UMD版本和ES模块版本的性能表现
 */

class PerformanceComparator {
    constructor() {
        this.results = {
            umd: {},
            es: {}
        };
        this.testStartTime = Date.now();
    }
    
    // 测试页面加载性能
    testPageLoadPerformance() {
        console.log('🚀 开始页面加载性能测试...');
        
        // 获取Performance API数据
        const perfData = performance.getEntriesByType('navigation')[0];
        const paintEntries = performance.getEntriesByType('paint');
        
        const version = this.getCurrentVersion();
        
        this.results[version].loadingPerformance = {
            // DNS查询时间
            dnsTime: perfData.domainLookupEnd - perfData.domainLookupStart,
            // TCP连接时间
            connectTime: perfData.connectEnd - perfData.connectStart,
            // 请求响应时间
            responseTime: perfData.responseEnd - perfData.requestStart,
            // DOM解析时间
            domParseTime: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            // 页面完全加载时间
            totalLoadTime: perfData.loadEventEnd - perfData.navigationStart,
            // 首次渲染时间
            firstPaint: paintEntries.find(entry => entry.name === 'first-paint')?.startTime || 0,
            // 首次内容渲染时间
            firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
        };
        
        console.log(`${version.toUpperCase()}版本性能数据:`, this.results[version].loadingPerformance);
    }
    
    // 测试JavaScript模块加载性能
    testModuleLoadPerformance() {
        console.log('📦 测试模块加载性能...');
        
        const version = this.getCurrentVersion();
        const resourceEntries = performance.getEntriesByType('resource');
        
        // 筛选JavaScript相关资源
        const jsResources = resourceEntries.filter(entry => 
            entry.name.includes('.js') && 
            (entry.name.includes('static') || entry.name.includes('lightweight-charts') || entry.name.includes('socket.io'))
        );
        
        const moduleStats = jsResources.map(resource => ({
            name: resource.name.split('/').pop(),
            size: resource.transferSize || 0,
            loadTime: resource.responseEnd - resource.requestStart,
            cacheHit: resource.transferSize === 0
        }));
        
        this.results[version].modulePerformance = {
            totalModules: jsResources.length,
            totalSize: moduleStats.reduce((sum, mod) => sum + mod.size, 0),
            totalLoadTime: Math.max(...moduleStats.map(mod => mod.loadTime)),
            modules: moduleStats
        };
        
        console.log(`${version.toUpperCase()}版本模块性能:`, this.results[version].modulePerformance);
    }
    
    // 测试内存使用情况
    testMemoryUsage() {
        console.log('🧠 测试内存使用情况...');
        
        if ('memory' in performance) {
            const version = this.getCurrentVersion();
            const memInfo = performance.memory;
            
            this.results[version].memoryUsage = {
                usedJSHeapSize: memInfo.usedJSHeapSize,
                totalJSHeapSize: memInfo.totalJSHeapSize,
                jsHeapSizeLimit: memInfo.jsHeapSizeLimit,
                memoryEfficiency: (memInfo.usedJSHeapSize / memInfo.totalJSHeapSize * 100).toFixed(2) + '%'
            };
            
            console.log(`${version.toUpperCase()}版本内存使用:`, this.results[version].memoryUsage);
        } else {
            console.warn('浏览器不支持Memory API');
        }
    }
    
    // 测试运行时性能
    testRuntimePerformance() {
        console.log('⚡ 测试运行时性能...');
        
        const version = this.getCurrentVersion();
        const startTime = performance.now();
        
        // 模拟一些计算密集型操作
        let testOperations = 0;
        for (let i = 0; i < 100000; i++) {
            testOperations += Math.random() * Math.PI;
        }
        
        const endTime = performance.now();
        
        this.results[version].runtimePerformance = {
            computationTime: endTime - startTime,
            operationsPerSecond: Math.round(100000 / (endTime - startTime) * 1000)
        };
        
        console.log(`${version.toUpperCase()}版本运行时性能:`, this.results[version].runtimePerformance);
    }
    
    // 测试浏览器兼容性
    testBrowserCompatibility() {
        console.log('🌐 测试浏览器兼容性...');
        
        const version = this.getCurrentVersion();
        
        const compatibility = {
            // ES6模块支持
            esModules: 'import' in document.createElement('script'),
            // 动态导入支持
            dynamicImport: false,
            // Promise支持
            promises: typeof Promise !== 'undefined',
            // Fetch API支持
            fetch: typeof fetch !== 'undefined',
            // WebSocket支持
            webSocket: typeof WebSocket !== 'undefined',
            // Local Storage支持
            localStorage: typeof localStorage !== 'undefined',
            // Performance API支持
            performanceAPI: typeof performance !== 'undefined',
            // ResizeObserver支持
            resizeObserver: typeof ResizeObserver !== 'undefined'
        };
        
        // 测试动态导入
        try {
            eval('import("data:text/javascript,export default 1")').then(() => {
                compatibility.dynamicImport = true;
            }).catch(() => {
                compatibility.dynamicImport = false;
            });
        } catch (e) {
            compatibility.dynamicImport = false;
        }
        
        this.results[version].browserCompatibility = compatibility;
        
        console.log(`${version.toUpperCase()}版本浏览器兼容性:`, compatibility);
    }
    
    // 获取当前版本
    getCurrentVersion() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('es') === '1' ? 'es' : 'umd';
    }
    
    // 运行所有测试
    runAllTests() {
        console.log('🔍 开始性能和兼容性测试...');
        console.log('=====================================');
        
        // 延迟执行，确保页面完全加载
        setTimeout(() => {
            this.testPageLoadPerformance();
            this.testModuleLoadPerformance();
            this.testMemoryUsage();
            this.testRuntimePerformance();
            this.testBrowserCompatibility();
            
            this.generateComparisonReport();
        }, 2000);
    }
    
    // 生成对比报告
    generateComparisonReport() {
        console.log('\n📊 性能对比报告');
        console.log('=====================================');
        
        const version = this.getCurrentVersion();
        const data = this.results[version];
        
        if (data.loadingPerformance) {
            console.log(`\n⏱️ ${version.toUpperCase()}版本加载性能:`);
            console.log(`- 总加载时间: ${data.loadingPerformance.totalLoadTime.toFixed(2)}ms`);
            console.log(`- 首次渲染: ${data.loadingPerformance.firstPaint.toFixed(2)}ms`);
            console.log(`- 首次内容渲染: ${data.loadingPerformance.firstContentfulPaint.toFixed(2)}ms`);
            console.log(`- DOM解析时间: ${data.loadingPerformance.domParseTime.toFixed(2)}ms`);
        }
        
        if (data.modulePerformance) {
            console.log(`\n📦 ${version.toUpperCase()}版本模块性能:`);
            console.log(`- 模块数量: ${data.modulePerformance.totalModules}`);
            console.log(`- 总大小: ${(data.modulePerformance.totalSize / 1024).toFixed(2)}KB`);
            console.log(`- 最大加载时间: ${data.modulePerformance.totalLoadTime.toFixed(2)}ms`);
        }
        
        if (data.memoryUsage) {
            console.log(`\n🧠 ${version.toUpperCase()}版本内存使用:`);
            console.log(`- 已用内存: ${(data.memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
            console.log(`- 内存效率: ${data.memoryUsage.memoryEfficiency}`);
        }
        
        if (data.runtimePerformance) {
            console.log(`\n⚡ ${version.toUpperCase()}版本运行时性能:`);
            console.log(`- 计算速度: ${data.runtimePerformance.operationsPerSecond.toLocaleString()} ops/sec`);
        }
        
        if (data.browserCompatibility) {
            console.log(`\n🌐 ${version.toUpperCase()}版本浏览器兼容性:`);
            Object.entries(data.browserCompatibility).forEach(([feature, supported]) => {
                console.log(`- ${feature}: ${supported ? '✅' : '❌'}`);
            });
        }
        
        // 保存结果到sessionStorage供对比使用
        sessionStorage.setItem(`performance_${version}`, JSON.stringify(data));
        
        console.log('\n💡 建议:');
        if (version === 'es') {
            console.log('- ES模块版本具有更好的代码组织和潜在的性能优势');
            console.log('- 确保目标用户的浏览器支持ES模块');
        } else {
            console.log('- UMD版本具有更广泛的浏览器兼容性');
            console.log('- 适合需要支持旧版浏览器的场景');
        }
    }
    
    // 静态方法：比较两个版本的性能数据
    static compareVersions() {
        const umdData = JSON.parse(sessionStorage.getItem('performance_umd') || '{}');
        const esData = JSON.parse(sessionStorage.getItem('performance_es') || '{}');
        
        if (Object.keys(umdData).length === 0 || Object.keys(esData).length === 0) {
            console.warn('⚠️ 需要在两个版本中都运行测试才能进行对比');
            return;
        }
        
        console.log('\n🆚 版本对比分析');
        console.log('=====================================');
        
        // 加载性能对比
        if (umdData.loadingPerformance && esData.loadingPerformance) {
            console.log('\n⏱️ 加载性能对比:');
            const umdLoad = umdData.loadingPerformance.totalLoadTime;
            const esLoad = esData.loadingPerformance.totalLoadTime;
            const loadDiff = ((esLoad - umdLoad) / umdLoad * 100).toFixed(2);
            
            console.log(`- UMD总加载时间: ${umdLoad.toFixed(2)}ms`);
            console.log(`- ES总加载时间: ${esLoad.toFixed(2)}ms`);
            console.log(`- 性能差异: ${loadDiff > 0 ? '+' : ''}${loadDiff}%`);
        }
        
        // 内存使用对比
        if (umdData.memoryUsage && esData.memoryUsage) {
            console.log('\n🧠 内存使用对比:');
            const umdMem = umdData.memoryUsage.usedJSHeapSize;
            const esMem = esData.memoryUsage.usedJSHeapSize;
            const memDiff = ((esMem - umdMem) / umdMem * 100).toFixed(2);
            
            console.log(`- UMD内存使用: ${(umdMem / 1024 / 1024).toFixed(2)}MB`);
            console.log(`- ES内存使用: ${(esMem / 1024 / 1024).toFixed(2)}MB`);
            console.log(`- 内存差异: ${memDiff > 0 ? '+' : ''}${memDiff}%`);
        }
    }
}

// 页面加载完成后自动运行测试
document.addEventListener('DOMContentLoaded', () => {
    // 延迟3秒开始测试，确保所有模块都已加载
    setTimeout(() => {
        const tester = new PerformanceComparator();
        tester.runAllTests();
        
        // 将比较方法添加到全局作用域
        window.compareVersionPerformance = PerformanceComparator.compareVersions;
    }, 3000);
});

// 提供手动测试方法
window.runPerformanceTest = () => {
    const tester = new PerformanceComparator();
    tester.runAllTests();
};
