/**
 * ES模块版本自动验证脚本
 * 在浏览器控制台中运行，检查关键功能是否正常
 */

(function() {
    console.log('🔍 开始ES模块版本功能验证...');
    
    const results = {
        pageLoad: false,
        modulesLoad: false,
        websocketClient: false,
        chartManager: false,
        domElements: false,
        cssStyles: false
    };
    
    // 1. 检查页面基本元素
    function checkPageElements() {
        const requiredElements = [
            'status', 'stock-count', 'last-update', 
            'current-time', 'charts-grid', 'switch-version'
        ];
        
        let allFound = true;
        requiredElements.forEach(id => {
            const element = document.getElementById(id);
            if (!element) {
                console.error(`❌ 缺少元素: ${id}`);
                allFound = false;
            } else {
                console.log(`✅ 找到元素: ${id}`);
            }
        });
        
        results.domElements = allFound;
        return allFound;
    }
    
    // 2. 检查ES模块是否正确加载
    function checkModulesLoad() {
        const hasChartManager = window.chartManager && typeof window.chartManager.createChart === 'function';
        const hasWebSocketClient = window.wsClient && typeof window.wsClient.subscribe === 'function';
        const hasGlobalData = typeof window.globalTransactionData === 'object';
        
        results.chartManager = hasChartManager;
        results.websocketClient = hasWebSocketClient;
        results.modulesLoad = hasChartManager && hasWebSocketClient && hasGlobalData;
        
        console.log(`ChartManager 加载: ${hasChartManager ? '✅' : '❌'}`);
        console.log(`WebSocketClient 加载: ${hasWebSocketClient ? '✅' : '❌'}`);
        console.log(`全局数据对象: ${hasGlobalData ? '✅' : '❌'}`);
        
        return results.modulesLoad;
    }
    
    // 3. 检查CSS样式
    function checkCSSStyles() {
        const testElement = document.createElement('div');
        testElement.className = 'price-up';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement);
        const hasCorrectColor = computedStyle.color === 'rgb(16, 185, 129)'; // #10b981
        
        document.body.removeChild(testElement);
        
        results.cssStyles = hasCorrectColor;
        console.log(`CSS样式加载: ${hasCorrectColor ? '✅' : '❌'}`);
        
        return hasCorrectColor;
    }
    
    // 4. 检查版本指示器
    function checkVersionIndicator() {
        const versionIndicator = document.querySelector('.version-indicator');
        const isESVersion = versionIndicator && versionIndicator.textContent.includes('ES Module');
        
        console.log(`版本指示器: ${isESVersion ? '✅ ES Module' : '❌ 未找到或错误'}`);
        return isESVersion;
    }
    
    // 5. 检查WebSocket连接状态
    function checkWebSocketStatus() {
        const statusEl = document.getElementById('status');
        if (statusEl) {
            const status = statusEl.textContent;
            console.log(`WebSocket状态: ${status}`);
            
            if (window.wsClient) {
                const isConnected = window.wsClient.isConnected && window.wsClient.isConnected();
                console.log(`WebSocket连接检查: ${isConnected ? '✅ 已连接' : '🔄 连接中/未连接'}`);
                return isConnected;
            }
        }
        return false;
    }
    
    // 执行所有检查
    console.log('\n📋 执行功能检查...\n');
    
    setTimeout(() => {
        results.pageLoad = checkPageElements();
        results.modulesLoad = checkModulesLoad();
        results.cssStyles = checkCSSStyles();
        
        const versionCorrect = checkVersionIndicator();
        const websocketStatus = checkWebSocketStatus();
        
        // 生成报告
        console.log('\n📊 验证结果摘要:');
        console.log('================');
        
        Object.entries(results).forEach(([key, value]) => {
            const status = value ? '✅ 通过' : '❌ 失败';
            const label = {
                pageLoad: 'DOM元素加载',
                modulesLoad: 'ES模块加载', 
                websocketClient: 'WebSocket客户端',
                chartManager: '图表管理器',
                domElements: 'DOM元素',
                cssStyles: 'CSS样式'
            }[key] || key;
            
            console.log(`${label}: ${status}`);
        });
        
        console.log(`版本指示器: ${versionCorrect ? '✅ 正确' : '❌ 错误'}`);
        console.log(`WebSocket连接: ${websocketStatus ? '✅ 已连接' : '🔄 待连接'}`);
        
        const overallSuccess = Object.values(results).every(v => v) && versionCorrect;
        console.log(`\n🎯 总体状态: ${overallSuccess ? '✅ 验证成功' : '⚠️ 需要检查'}`);
        
        if (overallSuccess) {
            console.log('\n🎉 恭喜！ES模块版本基本功能验证通过！');
        } else {
            console.log('\n🔧 请检查失败的项目并进行修复。');
        }
        
    }, 2000); // 等待2秒让模块完全加载
    
})();
