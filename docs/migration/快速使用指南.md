# TradingView Lightweight Charts NPM版本快速使用指南

## 🚀 快速开始

### 启动系统
```bash
# 推荐：使用启动器
python launcher.py

# 选择模式3：仅Python后端
# 然后访问 http://localhost:5001/npm
```

### 访问不同版本

#### NPM ES模块版本（推荐）
```
http://localhost:5001/npm
```
- ✅ 官方NPM包
- ✅ 功能完整
- ✅ 包含跌破检测功能

#### 原始UMD版本（备用）
```
http://localhost:5001/
```
- 📦 传统CDN版本
- 🔄 基本功能

### 版本识别
- **NPM版本**: 右上角显示绿色"NPM ES Module"标识
- **原始版本**: 右上角显示传统标识

## 🔄 版本切换

### 从原始版本切换到NPM版本
- 访问: `http://localhost:5001/npm`
- 或点击页面上的"切换到NPM版本"按钮

### 从NPM版本切换到原始版本
- 访问: `http://localhost:5001/`
- 或点击页面上的"切换到原始版本"按钮

### 版本偏好记录
- 系统会自动记住您的版本选择偏好
- 下次访问时会提示是否切换到您偏好的版本
- 可以通过浏览器开发者工具的localStorage查看设置

## 🔧 功能对比

| 功能特性 | UMD版本 | ES模块版本 |
|----------|---------|------------|
| 基础图表功能 | ✅ | ✅ |
| WebSocket连接 | ✅ | ✅ |
| 技术指标计算 | ✅ | ✅ |
| 实时数据更新 | ✅ | ✅ |
| 版本指示器 | ✅ | ✅ |
| 版本切换 | ✅ | ✅ |
| 浏览器兼容性 | 更广泛 | 现代浏览器 |
| 代码结构 | 传统全局变量 | 现代模块化 |
| 加载性能 | 标准 | 潜在更优 |
| 开发调试 | 标准 | 更好支持 |

## 🛠️ 开发者信息

### ES模块文件结构
```
src/web/
├── templates/
│   ├── index.html              # UMD版本模板
│   └── index-es.html           # ES模块版本模板
└── static/
    ├── css/
    │   └── main.css            # 外部样式文件
    └── js/
        ├── modules/            # ES模块目录
        │   ├── websocket-client.js
        │   ├── chart-manager.js
        │   └── utils.js
        ├── main-es.js          # ES模块主入口
        └── version-preference.js # 版本偏好管理
```

### CDN依赖
```html
<!-- ES模块版本使用的CDN -->
<script type="importmap">
{
  "imports": {
    "lightweight-charts": "https://unpkg.com/lightweight-charts@5.0.7/dist/lightweight-charts.esm.production.js",
    "socket.io-client": "https://cdn.socket.io/4.7.2/socket.io.esm.min.js"
  }
}
</script>
```

## 🔍 调试和测试

### 浏览器控制台命令

#### 检查当前版本
```javascript
// 在浏览器控制台中运行
console.log('当前版本:', new URLSearchParams(window.location.search).get('es') === '1' ? 'ES模块' : 'UMD');
```

#### 手动切换版本
```javascript
// 切换到ES模块版本
window.location.href = window.location.pathname + '?es=1';

// 切换到UMD版本
window.location.href = window.location.pathname;
```

#### 运行性能测试
```javascript
// 运行性能测试（如果加载了测试脚本）
window.runPerformanceTest();

// 比较两版本性能（需要在两个版本中都运行过测试）
window.compareVersionPerformance();
```

### 版本偏好管理
```javascript
// 查看当前版本偏好
localStorage.getItem('stock_monitor_version_preference');

// 设置版本偏好
localStorage.setItem('stock_monitor_version_preference', 'es'); // 或 'umd'

// 清除版本偏好
localStorage.removeItem('stock_monitor_version_preference');
```

## ⚠️ 注意事项

### ES模块版本要求
- **浏览器支持**: 需要支持ES2015+模块的现代浏览器
- **网络环境**: 需要能够访问unpkg.com和cdn.socket.io
- **JavaScript启用**: 必须启用JavaScript

### 浏览器兼容性
- **推荐浏览器**: Chrome 61+, Firefox 60+, Safari 10.1+, Edge 16+
- **不支持**: Internet Explorer
- **移动端**: iOS Safari 10.3+, Chrome Mobile 61+

### 功能限制
- ES模块版本在不支持ES模块的浏览器中无法运行
- 某些企业网络可能阻止ES模块的CDN加载
- 离线环境下ES模块依赖可能无法加载

## 🆘 故障排除

### 常见问题

#### ES模块版本无法加载
1. 检查浏览器是否支持ES模块
2. 检查网络是否能访问CDN资源
3. 查看浏览器控制台是否有错误信息

#### 版本切换失败
1. 清除浏览器缓存
2. 检查JavaScript是否正常执行
3. 查看是否有网络连接问题

#### 功能表现不一致
1. 刷新页面重新加载
2. 清除localStorage中的版本偏好设置
3. 检查WebSocket连接状态

### 获取帮助
- 查看浏览器控制台错误信息
- 检查网络请求是否成功
- 验证服务器端是否正常运行

## 📈 性能优化建议

### 对于ES模块版本
- 使用现代浏览器以获得最佳性能
- 确保稳定的网络连接以加载CDN资源
- 利用浏览器缓存机制

### 对于UMD版本
- 适用于需要广泛浏览器兼容性的场景
- 在低带宽环境下可能加载更快
- 适合企业内网环境

---

**最后更新**: 2025年6月28日  
**版本**: v1.0  
**维护者**: 开发团队
