# 端到端验证报告

生成时间: 2025-06-28 21:55:25

## 测试结果总览

- **UMD版本**: 1/5 测试通过 (20.0%)
- **ES版本**: 1/4 测试通过 (25.0%)
- **版本切换**: 0/2 测试通过 (0.0%)

## 详细测试数据

```json
{
  "umd_tests": {
    "umd_page_load": {
      "status": "failed",
      "error": "页面标题不正确: 多股票实时监控 - 5分钟K线 - IB API数据"
    },
    "umd_chart_init": {
      "status": "failed",
      "error": "Message: \nStacktrace:\n0   chromedriver                        0x0000000101394eb4 cxxbridge1$str$ptr + 2722840\n1   chromedriver                        0x000000010138cdbc cxxbridge1$str$ptr + 2689824\n2   chromedriver                        0x0000000100ede3ec cxxbridge1$string$len + 90648\n3   chromedriver                        0x0000000100f25544 cxxbridge1$string$len + 381808\n4   chromedriver                        0x0000000100f66934 cxxbridge1$string$len + 649056\n5   chromedriver                        0x0000000100f19834 cxxbridge1$string$len + 333408\n6   chromedriver                        0x0000000101357fd0 cxxbridge1$str$ptr + 2473268\n7   chromedriver                        0x000000010135b23c cxxbridge1$str$ptr + 2486176\n8   chromedriver                        0x0000000101339a18 cxxbridge1$str$ptr + 2348924\n9   chromedriver                        0x000000010135baf8 cxxbridge1$str$ptr + 2488412\n10  chromedriver                        0x000000010132aaa8 cxxbridge1$str$ptr + 2287628\n11  chromedriver                        0x000000010137b9e8 cxxbridge1$str$ptr + 2619212\n12  chromedriver                        0x000000010137bb74 cxxbridge1$str$ptr + 2619608\n13  chromedriver                        0x000000010138c9f8 cxxbridge1$str$ptr + 2688860\n14  libsystem_pthread.dylib             0x0000000180f6cc0c _pthread_start + 136\n15  libsystem_pthread.dylib             0x0000000180f67b80 thread_start + 8\n"
    },
    "umd_version_indicator": {
      "status": "failed",
      "error": "Message: \nStacktrace:\n0   chromedriver                        0x0000000101394eb4 cxxbridge1$str$ptr + 2722840\n1   chromedriver                        0x000000010138cdbc cxxbridge1$str$ptr + 2689824\n2   chromedriver                        0x0000000100ede3ec cxxbridge1$string$len + 90648\n3   chromedriver                        0x0000000100f25544 cxxbridge1$string$len + 381808\n4   chromedriver                        0x0000000100f66934 cxxbridge1$string$len + 649056\n5   chromedriver                        0x0000000100f19834 cxxbridge1$string$len + 333408\n6   chromedriver                        0x0000000101357fd0 cxxbridge1$str$ptr + 2473268\n7   chromedriver                        0x000000010135b23c cxxbridge1$str$ptr + 2486176\n8   chromedriver                        0x0000000101339a18 cxxbridge1$str$ptr + 2348924\n9   chromedriver                        0x000000010135baf8 cxxbridge1$str$ptr + 2488412\n10  chromedriver                        0x000000010132aaa8 cxxbridge1$str$ptr + 2287628\n11  chromedriver                        0x000000010137b9e8 cxxbridge1$str$ptr + 2619212\n12  chromedriver                        0x000000010137bb74 cxxbridge1$str$ptr + 2619608\n13  chromedriver                        0x000000010138c9f8 cxxbridge1$str$ptr + 2688860\n14  libsystem_pthread.dylib             0x0000000180f6cc0c _pthread_start + 136\n15  libsystem_pthread.dylib             0x0000000180f67b80 thread_start + 8\n"
    },
    "umd_performance": {
      "status": "passed",
      "dom_content_loaded": 222.0,
      "load_complete": 222.30000001192093,
      "memory_info": {
        "jsHeapSizeLimit": 4294705152,
        "totalJSHeapSize": 9307789,
        "usedJSHeapSize": 7334545
      }
    },
    "umd_js_errors": {
      "status": "failed",
      "error_count": 26,
      "warning_count": 1,
      "errors": [
        {
          "level": "SEVERE",
          "message": "http://127.0.0.1:5000/docs/migration/performance_test.js - Failed to load resource: the server responded with a status of 404 (NOT FOUND)",
          "source": "network",
          "timestamp": 1751115243883
        },
        {
          "level": "SEVERE",
          "message": "https://cdn.socket.io/4.7.2/socket.io.min.js 5 WebSocket connection to 'ws://127.0.0.1:5000/socket.io/?EIO=4&transport=websocket' failed: Error during WebSocket handshake: Unexpected response code: 404",
          "source": "network",
          "timestamp": 1751115244125
        },
        {
          "level": "SEVERE",
          "message": "http://127.0.0.1:5000/static/js/websocket_client.js 80:20 \"WebSocket连接失败:\" i: websocket error\n    at i.value (https://cdn.socket.io/4.7.2/socket.io.min.js:6:9704)\n    at ws.onerror (https://cdn.socket.io/4.7.2/socket.io.min.js:6:17874)",
          "source": "console-api",
          "timestamp": 1751115244126
        },
        {
          "level": "SEVERE",
          "message": "http://127.0.0.1:5000/ 285:20 \"WebSocket连接错误:\" i: websocket error\n    at i.value (https://cdn.socket.io/4.7.2/socket.io.min.js:6:9704)\n    at ws.onerror (https://cdn.socket.io/4.7.2/socket.io.min.js:6:17874)",
          "source": "console-api",
          "timestamp": 1751115244126
        },
        {
          "level": "SEVERE",
          "message": "http://127.0.0.1:5000/favicon.ico - Failed to load resource: the server responded with a status of 404 (NOT FOUND)",
          "source": "network",
          "timestamp": 1751115244134
        }
      ],
      "warnings": [
        {
          "level": "WARNING",
          "message": "https://cdn.tailwindcss.com/ 63:1710 \"cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation\"",
          "source": "console-api",
          "timestamp": 1751115243990
        }
      ]
    }
  },
  "es_tests": {
    "es_chart_init": {
      "status": "failed",
      "error": "Message: \nStacktrace:\n0   chromedriver                        0x0000000101394eb4 cxxbridge1$str$ptr + 2722840\n1   chromedriver                        0x000000010138cdbc cxxbridge1$str$ptr + 2689824\n2   chromedriver                        0x0000000100ede3ec cxxbridge1$string$len + 90648\n3   chromedriver                        0x0000000100f25544 cxxbridge1$string$len + 381808\n4   chromedriver                        0x0000000100f66934 cxxbridge1$string$len + 649056\n5   chromedriver                        0x0000000100f19834 cxxbridge1$string$len + 333408\n6   chromedriver                        0x0000000101357fd0 cxxbridge1$str$ptr + 2473268\n7   chromedriver                        0x000000010135b23c cxxbridge1$str$ptr + 2486176\n8   chromedriver                        0x0000000101339a18 cxxbridge1$str$ptr + 2348924\n9   chromedriver                        0x000000010135baf8 cxxbridge1$str$ptr + 2488412\n10  chromedriver                        0x000000010132aaa8 cxxbridge1$str$ptr + 2287628\n11  chromedriver                        0x000000010137b9e8 cxxbridge1$str$ptr + 2619212\n12  chromedriver                        0x000000010137bb74 cxxbridge1$str$ptr + 2619608\n13  chromedriver                        0x000000010138c9f8 cxxbridge1$str$ptr + 2688860\n14  libsystem_pthread.dylib             0x0000000180f6cc0c _pthread_start + 136\n15  libsystem_pthread.dylib             0x0000000180f67b80 thread_start + 8\n"
    },
    "es_version_indicator": {
      "status": "failed",
      "error": "Message: \nStacktrace:\n0   chromedriver                        0x0000000101394eb4 cxxbridge1$str$ptr + 2722840\n1   chromedriver                        0x000000010138cdbc cxxbridge1$str$ptr + 2689824\n2   chromedriver                        0x0000000100ede3ec cxxbridge1$string$len + 90648\n3   chromedriver                        0x0000000100f25544 cxxbridge1$string$len + 381808\n4   chromedriver                        0x0000000100f66934 cxxbridge1$string$len + 649056\n5   chromedriver                        0x0000000100f19834 cxxbridge1$string$len + 333408\n6   chromedriver                        0x0000000101357fd0 cxxbridge1$str$ptr + 2473268\n7   chromedriver                        0x000000010135b23c cxxbridge1$str$ptr + 2486176\n8   chromedriver                        0x0000000101339a18 cxxbridge1$str$ptr + 2348924\n9   chromedriver                        0x000000010135baf8 cxxbridge1$str$ptr + 2488412\n10  chromedriver                        0x000000010132aaa8 cxxbridge1$str$ptr + 2287628\n11  chromedriver                        0x000000010137b9e8 cxxbridge1$str$ptr + 2619212\n12  chromedriver                        0x000000010137bb74 cxxbridge1$str$ptr + 2619608\n13  chromedriver                        0x000000010138c9f8 cxxbridge1$str$ptr + 2688860\n14  libsystem_pthread.dylib             0x0000000180f6cc0c _pthread_start + 136\n15  libsystem_pthread.dylib             0x0000000180f67b80 thread_start + 8\n"
    },
    "es_performance": {
      "status": "passed",
      "dom_content_loaded": 222.0,
      "load_complete": 222.30000001192093,
      "memory_info": {
        "jsHeapSizeLimit": 4294705152,
        "totalJSHeapSize": 37389884,
        "usedJSHeapSize": 18049244
      }
    },
    "es_js_errors": {
      "status": "failed",
      "error_count": 21,
      "warning_count": 0,
      "errors": [
        {
          "level": "SEVERE",
          "message": "https://cdn.socket.io/4.7.2/socket.io.min.js 5 WebSocket connection to 'ws://127.0.0.1:5000/socket.io/?EIO=4&transport=websocket' failed: Error during WebSocket handshake: Unexpected response code: 404",
          "source": "network",
          "timestamp": 1751115275065
        },
        {
          "level": "SEVERE",
          "message": "http://127.0.0.1:5000/static/js/websocket_client.js 80:20 \"WebSocket连接失败:\" i: websocket error\n    at i.value (https://cdn.socket.io/4.7.2/socket.io.min.js:6:9704)\n    at ws.onerror (https://cdn.socket.io/4.7.2/socket.io.min.js:6:17874)",
          "source": "console-api",
          "timestamp": 1751115275065
        },
        {
          "level": "SEVERE",
          "message": "http://127.0.0.1:5000/ 285:20 \"WebSocket连接错误:\" i: websocket error\n    at i.value (https://cdn.socket.io/4.7.2/socket.io.min.js:6:9704)\n    at ws.onerror (https://cdn.socket.io/4.7.2/socket.io.min.js:6:17874)",
          "source": "console-api",
          "timestamp": 1751115275065
        },
        {
          "level": "SEVERE",
          "message": "https://cdn.socket.io/4.7.2/socket.io.min.js 5 WebSocket connection to 'ws://127.0.0.1:5000/socket.io/?EIO=4&transport=websocket' failed: Error during WebSocket handshake: Unexpected response code: 404",
          "source": "network",
          "timestamp": 1751115280083
        },
        {
          "level": "SEVERE",
          "message": "http://127.0.0.1:5000/static/js/websocket_client.js 80:20 \"WebSocket连接失败:\" i: websocket error\n    at i.value (https://cdn.socket.io/4.7.2/socket.io.min.js:6:9704)\n    at ws.onerror (https://cdn.socket.io/4.7.2/socket.io.min.js:6:17874)",
          "source": "console-api",
          "timestamp": 1751115280085
        }
      ],
      "warnings": []
    }
  },
  "comparison": {
    "switch_umd_to_es": {
      "status": "failed",
      "error": "Message: \nStacktrace:\n0   chromedriver                        0x0000000101394eb4 cxxbridge1$str$ptr + 2722840\n1   chromedriver                        0x000000010138cdbc cxxbridge1$str$ptr + 2689824\n2   chromedriver                        0x0000000100ede3ec cxxbridge1$string$len + 90648\n3   chromedriver                        0x0000000100f25544 cxxbridge1$string$len + 381808\n4   chromedriver                        0x0000000100f66934 cxxbridge1$string$len + 649056\n5   chromedriver                        0x0000000100f19834 cxxbridge1$string$len + 333408\n6   chromedriver                        0x0000000101357fd0 cxxbridge1$str$ptr + 2473268\n7   chromedriver                        0x000000010135b23c cxxbridge1$str$ptr + 2486176\n8   chromedriver                        0x0000000101339a18 cxxbridge1$str$ptr + 2348924\n9   chromedriver                        0x000000010135baf8 cxxbridge1$str$ptr + 2488412\n10  chromedriver                        0x000000010132aaa8 cxxbridge1$str$ptr + 2287628\n11  chromedriver                        0x000000010137b9e8 cxxbridge1$str$ptr + 2619212\n12  chromedriver                        0x000000010137bb74 cxxbridge1$str$ptr + 2619608\n13  chromedriver                        0x000000010138c9f8 cxxbridge1$str$ptr + 2688860\n14  libsystem_pthread.dylib             0x0000000180f6cc0c _pthread_start + 136\n15  libsystem_pthread.dylib             0x0000000180f67b80 thread_start + 8\n"
    },
    "switch_es_to_umd": {
      "status": "failed",
      "error": "Message: \nStacktrace:\n0   chromedriver                        0x0000000101394eb4 cxxbridge1$str$ptr + 2722840\n1   chromedriver                        0x000000010138cdbc cxxbridge1$str$ptr + 2689824\n2   chromedriver                        0x0000000100ede3ec cxxbridge1$string$len + 90648\n3   chromedriver                        0x0000000100f25544 cxxbridge1$string$len + 381808\n4   chromedriver                        0x0000000100f66934 cxxbridge1$string$len + 649056\n5   chromedriver                        0x0000000100f19834 cxxbridge1$string$len + 333408\n6   chromedriver                        0x0000000101357fd0 cxxbridge1$str$ptr + 2473268\n7   chromedriver                        0x000000010135b23c cxxbridge1$str$ptr + 2486176\n8   chromedriver                        0x0000000101339a18 cxxbridge1$str$ptr + 2348924\n9   chromedriver                        0x000000010135baf8 cxxbridge1$str$ptr + 2488412\n10  chromedriver                        0x000000010132aaa8 cxxbridge1$str$ptr + 2287628\n11  chromedriver                        0x000000010137b9e8 cxxbridge1$str$ptr + 2619212\n12  chromedriver                        0x000000010137bb74 cxxbridge1$str$ptr + 2619608\n13  chromedriver                        0x000000010138c9f8 cxxbridge1$str$ptr + 2688860\n14  libsystem_pthread.dylib             0x0000000180f6cc0c _pthread_start + 136\n15  libsystem_pthread.dylib             0x0000000180f67b80 thread_start + 8\n"
    }
  },
  "errors": []
}
```
