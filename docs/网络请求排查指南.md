## 🔍 浏览器开发者工具网络请求排查指南

### 📋 排查步骤

#### 1. 打开 Network (网络) 面板
- 按 F12 打开开发者工具
- 点击 "Network" 或"网络" 选项卡
- 确保 "Preserve log" (保留日志) 已勾选

#### 2. 重现错误
- 刷新页面或执行导致错误的操作
- 观察网络请求列表

#### 3. 识别问题请求
查找以下特征的请求：

**🔴 失败请求标识：**
- Status Code: 500 (服务器内部错误)
- Status Code: 403 (权限错误) 
- 红色显示的请求
- 请求时间过长(超时)

**🎯 重点检查的端点：**
```
/data                    - 主要数据端点
/stocks                  - 股票列表
/stock/<symbol>          - 特定股票数据
/overview               - 概览数据
/status                 - 状态检查
/debug                  - 调试信息
/transactions           - 交易数据
/historical-data-end/*  - 历史数据状态
```

**🔌 WebSocket 连接：**
```
socket.io/?EIO=4&transport=websocket
socket.io/?EIO=4&transport=polling
```

#### 4. 查看请求详情
点击有问题的请求，查看：

**Headers 选项卡：**
- Request Method (GET/POST等)
- Request URL
- Status Code
- Response Headers

**Response 选项卡：**
- 查看服务器返回的错误信息
- 寻找 "AssertionError" 或 "write() before start_response"

**Console 选项卡：**
- 查看 JavaScript 错误
- 查看网络相关的错误信息

#### 5. 分析错误原因

**AssertionError: write() before start_response 的常见原因：**

1. **响应体在设置响应头之前被写入**
   - 某个 Flask 路由没有正确设置 Content-Type
   - 中间件或装饰器提前写入了响应

2. **WebSocket 相关问题**
   - Socket.IO 初始化失败
   - WebSocket 升级请求失败

3. **数据处理异常**
   - JSON 序列化失败
   - 数据为空或格式错误导致中间件异常

#### 6. 定位具体问题

**如果是 REST API 请求失败：**
1. 检查请求的 URL 和参数
2. 查看 Response 中的错误详情
3. 对比正常工作的请求格式

**如果是 WebSocket 连接失败：**
1. 查看 WebSocket 握手请求
2. 检查 Socket.IO 连接事件
3. 在 Console 中查看连接错误信息

#### 7. 前端代码对应关系

**主要发起请求的位置：**
```javascript
// main-npm.js 中的 WebSocket 事件
socket.emit('request_stocks_list')
socket.emit('subscribe', { symbol: symbol })

// 可能的 HTTP 请求（如果有的话）
// 目前代码中主要使用 WebSocket，较少直接HTTP请求
```

#### 8. 排查建议

**立即检查：**
1. 刷新页面后第一个失败的请求
2. Socket.IO 的初始连接请求
3. `request_stocks_list` 事件的响应

**如果找到失败请求：**
1. 记录请求的完整URL
2. 记录 Status Code 和错误信息
3. 记录请求发生的时间戳
4. 检查是否有重复的失败请求

**下一步行动：**
1. 将失败请求的详细信息提供给开发者
2. 检查对应的后端 Python 代码
3. 如果是 WebSocket 问题，检查 websocket_server.py
4. 如果是 REST API 问题，检查 web_server.py 对应路由

### 🔧 快速诊断命令

在浏览器 Console 中输入：
```javascript
// 检查当前连接状态
console.log('Socket状态:', socket?.connected);

// 检查图表管理器状态  
console.log('ChartManager状态:', window.chartManagerDebug?.chartManager);

// 手动内存监控
window.chartManagerDebug?.monitorMemoryUsage();
```

### ⚠️ 注意事项

1. **保持 Network 面板打开** - 在错误发生时才能捕获到请求
2. **清除缓存** - 有时缓存会隐藏真实的网络问题
3. **检查多个选项卡** - Headers, Response, Console 都要查看
4. **注意时间戳** - 确认错误和请求的时间对应关系

找到问题请求后，请提供具体的 URL、状态码和错误信息，这样可以更精确地定位后端代码中的问题。
