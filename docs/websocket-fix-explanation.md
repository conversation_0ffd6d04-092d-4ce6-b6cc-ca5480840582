## 🔧 NPM版本WebSocket连接修复说明

### 问题原因
之前NPM版本的WebSocket客户端试图连接到 `ws://localhost:3000/socket.io/`，但WebSocket服务器实际运行在Flask后端的5000端口，导致连接失败。

### 🚀 技术架构说明

#### Flask后端 (端口5000)
- **作用**: Python Web服务器，提供数据API和WebSocket服务
- **功能**: 
  - 股票数据处理和存储
  - WebSocket实时数据推送  
  - RESTful API接口
- **访问**: http://localhost:5000

#### Vite开发服务器 (端口3000) 
- **作用**: 前端开发服务器，专门为NPM版本提供服务
- **功能**:
  - ES模块热重载
  - 依赖预构建
  - 代理API请求到Flask后端
  - 静态资源服务
- **访问**: http://localhost:3000

### 🔧 修复方案

#### 修复前
```javascript
// ❌ 错误：直接连接到Vite服务器
socket = io('/', {
    transports: ['websocket'],
    // ...
});
```

#### 修复后  
```javascript
// ✅ 正确：通过Vite代理连接到Flask后端
const isViteDev = window.location.port === '3000';

if (isViteDev) {
    // 开发环境：使用相对路径，让Vite代理处理
    socket = io('/', {
        transports: ['websocket', 'polling'],
        timeout: 5000,
        forceNew: true
    });
} else {
    // 生产环境：直接连接到当前地址
    socket = io(window.location.origin, {
        transports: ['websocket', 'polling'],
        timeout: 5000
    });
}
```

### ⚙️ Vite代理配置
```javascript
// vite.config.js
server: {
    port: 3000,
    proxy: {
        '/api': {
            target: 'http://localhost:5000',
            changeOrigin: true
        },
        '/socket.io': {
            target: 'http://localhost:5000',
            changeOrigin: true,
            ws: true  // 启用WebSocket代理
        }
    }
}
```

### 🧪 验证步骤

1. **启动Flask后端** (5000端口)
   ```bash
   python simple_backend.py
   ```

2. **启动Vite开发服务器** (3000端口)  
   ```bash
   npm run dev
   ```

3. **测试连接**
   - 打开: http://localhost:3000/templates/index-npm.html
   - 打开浏览器开发者工具
   - 查看Console是否显示: "NPM版本 - 使用Vite代理连接WebSocket"
   - 确认显示: "WebSocket连接成功 (NPM版本)"

### 📊 双版本对比

| 特性 | CDN版本 (5000端口) | NPM版本 (3000端口) |
|------|-------------------|-------------------|
| 服务器 | Flask直接服务 | Vite + Flask代理 |
| 依赖管理 | CDN导入 | NPM包管理 |
| 开发体验 | 简单直接 | 热重载+类型支持 |
| 生产部署 | 静态文件 | 构建优化 |
| WebSocket | 直连Flask | 通过Vite代理 |

### 🎯 修复验证
修复后，NPM版本应该能够：
1. ✅ 正确连接WebSocket (无错误信息)
2. ✅ 接收实时股票数据
3. ✅ 显示TradingView图表
4. ✅ 与Flask后端完整交互

修复完成后，用户可以选择：
- **开发阶段**: 使用NPM版本 (http://localhost:3000)
- **快速测试**: 使用CDN版本 (http://localhost:5000)
- **生产环境**: 构建NPM版本为静态文件部署
