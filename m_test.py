import math

total = 429900
months = 12
monthly = math.floor(total / months)
last_month = total - monthly * (months - 1)
rates = [0.024, 0.087]  # 年利率

remain = total
penalty = 0.0

for i in range(months):
    pay = monthly if i < months - 1 else last_month
    rate = rates[0] if i == 0 else rates[1]
    month_penalty = remain * rate / 12
    penalty += month_penalty
    remain -= pay

# 只在总和时舍去1,000日元以下
penalty = math.floor(penalty / 1000) * 1000

print(f"分12期总延滞金约: {int(penalty)} 日元")