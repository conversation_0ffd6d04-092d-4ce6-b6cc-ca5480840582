#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行所有测试的脚本
"""

import unittest
import sys
import os
from unittest.mock import MagicMock, patch
from importlib import import_module

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 模拟导入依赖模块，避免导入错误
def mock_modules():
    """模拟导入依赖模块"""
    modules = [
        'core', 'core.check_the_signal', 
        'analysis', 'analysis.pre_stock_signal', 'analysis.macd_check', 
        'analysis.check_breakout_level', 'analysis.number_of_transactions',
        'analysis.stock_analyzer', 'analysis.tradingview_screener_q',
        'utils', 'utils.futu_utils', 'utils.constants', 'utils.stock_utils',
        'utils.yfinance_fun', 'utils.db_utils', 'utils.restart_IB_Gateway',
        'utils.config', 'utils.key_generator',
        'services', 'services.globenewswire_rss', 'services.stocktitan_rss',
        'futu',
        'ibapi', 'ibapi.client', 'ibapi.wrapper', 'ibapi.scanner',
        'ibapi.contract', 'ibapi.utils', 'ibapi.common'
    ]
    
    for module in modules:
        sys.modules[module] = MagicMock()
    
    # 设置一些特定的模拟值
    sys.modules['utils.config'].IB_CONFIG = {
        'MAX_RETRY_ATTEMPTS': 3,
        'HOST': '127.0.0.1',
        'PORT': 7497,
        'CLIENT_ID': 1000,
        'RETRY_INTERVAL': 5
    }
    sys.modules['utils.key_generator'].key_generator = MagicMock()
    sys.modules['utils.key_generator'].key_generator.get_keys_for_stock = MagicMock(return_value={})
    
    # 定义ibapi中的类型
    class TickerId(int): pass
    class ListOfHistoricalTickLast(list): pass
    class TickAttribLast: 
        def __init__(self):
            self.pastLimit = False
            self.unreported = False
    class TickAttribBidAsk:
        def __init__(self):
            self.bidPastLow = False
            self.askPastHigh = False
    class Decimal(float): pass
    
    # 创建EWrapper和EClient类，避免元类冲突
    class EWrapper:
        def __init__(self):
            pass

    class EClient:
        def __init__(self, wrapper):
            self.wrapper = wrapper
    
    # 将类型添加到ibapi模块中
    sys.modules['ibapi.common'].TickerId = TickerId
    sys.modules['ibapi.common'].ListOfHistoricalTickLast = ListOfHistoricalTickLast
    sys.modules['ibapi.common'].TickAttribLast = TickAttribLast
    sys.modules['ibapi.common'].TickAttribBidAsk = TickAttribBidAsk
    sys.modules['ibapi.common'].Decimal = Decimal
    sys.modules['ibapi.wrapper'].EWrapper = EWrapper
    sys.modules['ibapi.client'].EClient = EClient
    
    # 创建测试用的MyWrapper和MyClient类
    class MyWrapper(EWrapper):
        def __init__(self):
            super().__init__()
            
    class MyClient(EClient):
        def __init__(self, wrapper):
            super().__init__(wrapper)
    
    class IBApp(MyWrapper, MyClient):
        def __init__(self):
            MyWrapper.__init__(self)
            MyClient.__init__(self, wrapper=self)
            
            # 必要的属性
            self.top5_tick_stocks = set()
            self.watch_dict = {}
            self.active_tick_requests = {}
            self.reqId_dict = {}
            self.reqId_dict_wrapper = {}
            self.contractDetails_dict = {}
            
        def update_top5_tick_stocks(self):
            # 获取所有股票的rank
            ranked_stocks = sorted(self.watch_dict.items(), key=lambda x: x[0])
            # 只保留前5名
            new_top5 = set(stock for rank, stock in ranked_stocks[:5])
            
            # 找出需要添加和移除的股票
            to_add = new_top5 - self.top5_tick_stocks
            to_remove = self.top5_tick_stocks - new_top5
            
            # 更新集合
            self.top5_tick_stocks = new_top5
            
            # 对于被移除的股票，取消逐笔数据请求
            for stock_code in to_remove:
                self.cancel_tick_by_tick_data(stock_code)
            
            # 对于新添加的股票，检查是否需要立即请求Tick-by-tick数据
            for stock_code in to_add:
                if stock_code in self.reqId_dict:
                    # 如果股票不在接收Tick-by-tick数据，立即请求
                    if not self.is_receiving_tick_data(stock_code):
                        stock_count = self.reqId_dict[stock_code]
                        if stock_count in self.contractDetails_dict:
                            self.get_tick_by_tick_data(stock_count)
        
        def is_receiving_tick_data(self, stock_code):
            """检查股票是否已经在接收Tick-by-tick数据"""
            if stock_code in self.reqId_dict:
                reqid_ = self.reqId_dict[stock_code]
                if reqid_ in self.contractDetails_dict:
                    contract = self.contractDetails_dict[reqid_]
                    contract_key = f"{contract.symbol}_{contract.secType}_{contract.exchange}"
                    return contract_key in self.active_tick_requests
            return False
        
        def cancel_tick_by_tick_data(self, stock_code):
            """取消股票的逐笔数据请求"""
            if stock_code in self.reqId_dict:
                reqid_ = self.reqId_dict[stock_code]
                if reqid_ in self.contractDetails_dict:
                    contract = self.contractDetails_dict[reqid_]
                    contract_key = f"{contract.symbol}_{contract.secType}_{contract.exchange}"
                    
                    if contract_key in self.active_tick_requests:
                        active_reqid = self.active_tick_requests[contract_key]
                        self.cancelTickByTickData(active_reqid)
                        self.active_tick_requests.pop(contract_key)
        
        def get_tick_by_tick_data(self, reqid_):
            """请求逐笔交易数据"""
            pass
            
        def scannerData(self, reqId, rank, contractDetails, distance, benchmark, projection, legsStr):
            """处理市场扫描仪返回的数据"""
            stock_code = contractDetails.contract.localSymbol
            self.watch_dict[rank] = stock_code
            
            # 如果rank变化，更新前5名股票的Tick-by-tick数据跟踪集合
            if rank <= 4:  # rank从0开始，所以前5名是0-4
                # 更新前5名股票集合
                self.update_top5_tick_stocks()
    
    # 使用我们自定义的类替代实际导入
    sys.modules['src.api.get_stock_price_ibapi'] = MagicMock()
    sys.modules['src.api.get_stock_price_ibapi'].IBApp = IBApp
    sys.modules['src.api.get_stock_price_ibapi'].MyWrapper = MyWrapper
    sys.modules['src.api.get_stock_price_ibapi'].MyClient = MyClient

def run_mock_tests():
    """运行模拟测试"""
    # 导入测试模块
    from tests.test_tick_by_tick_rank_change import TestTickByTickRankChange
    from tests.test_tick_by_tick_integration import TestTickByTickIntegration
    from tests.test_stock_analyzer import TestStockAnalyzer
    
    # 创建测试套件
    suite = unittest.TestSuite()
    loader = unittest.TestLoader()
    
    # 添加测试用例
    suite.addTest(loader.loadTestsFromTestCase(TestTickByTickRankChange))
    suite.addTest(loader.loadTestsFromTestCase(TestTickByTickIntegration))
    suite.addTest(loader.loadTestsFromTestCase(TestStockAnalyzer))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def run_real_code_tests():
    """运行真实代码测试"""
    # 导入测试模块
    from tests.test_tick_by_tick_real_code import TestTickByTickRealCode
    
    # 创建测试套件
    suite = unittest.TestSuite()
    loader = unittest.TestLoader()
    
    # 添加测试用例
    suite.addTest(loader.loadTestsFromTestCase(TestTickByTickRealCode))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def run_all_tests():
    """运行所有测试"""
    # 运行模拟测试
    print("\n=== 运行模拟测试 ===")
    mock_success = run_mock_tests()
    
    # 运行真实代码测试
    print("\n=== 运行真实代码测试 ===")
    real_success = run_real_code_tests()
    
    # 返回整体结果
    return mock_success and real_success

if __name__ == "__main__":
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='运行测试')
    parser.add_argument('--mock-only', action='store_true', help='只运行模拟测试')
    parser.add_argument('--real-only', action='store_true', help='只运行真实代码测试')
    parser.add_argument('--stock-analyzer-only', action='store_true', help='只运行股票分析器测试')
    args = parser.parse_args()
    
    success = True
    
    if args.mock_only:
        print("\n=== 只运行模拟测试 ===")
        success = run_mock_tests()
    elif args.real_only:
        print("\n=== 只运行真实代码测试 ===")
        success = run_real_code_tests()
    elif args.stock_analyzer_only:
        print("\n=== 只运行股票分析器测试 ===")
        from tests.test_stock_analyzer import TestStockAnalyzer
        suite = unittest.TestSuite()
        loader = unittest.TestLoader()
        suite.addTest(loader.loadTestsFromTestCase(TestStockAnalyzer))
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        success = result.wasSuccessful()
    else:
        print("\n=== 运行所有测试 ===")
        success = run_all_tests()
    
    # 设置退出代码
    sys.exit(0 if success else 1) 