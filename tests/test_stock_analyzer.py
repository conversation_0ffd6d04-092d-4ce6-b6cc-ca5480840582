import unittest
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from unittest.mock import patch, MagicMock
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.analysis.stock_analyzer import (
    StockAnalyzer, 
    analyze_stock_data, 
    SignalStrength, 
    VolumeAnalysis, 
    OrderBookAnalysis, 
    TechnicalSignal
)

class TestStockAnalyzer(unittest.TestCase):
    """测试股票分析器"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.analyzer = StockAnalyzer()
        
        # 创建测试数据目录
        self.test_data_dir = os.path.join(os.path.dirname(__file__), 'test_data')
        os.makedirs(self.test_data_dir, exist_ok=True)
        
    def tearDown(self):
        """测试后的清理工作"""
        # 清理测试数据
        if os.path.exists(self.test_data_dir):
            for f in os.listdir(self.test_data_dir):
                os.remove(os.path.join(self.test_data_dir, f))
            os.rmdir(self.test_data_dir)
    
    def test_create_default_objects(self):
        """测试创建默认对象的方法"""
        volume_analysis = self.analyzer._create_default_volume_analysis()
        self.assertEqual(volume_analysis.buy_volume, 0)
        self.assertEqual(volume_analysis.sell_volume, 0)
        self.assertEqual(volume_analysis.volume_ratio, 0)
        
        orderbook_analysis = self.analyzer._create_default_orderbook_analysis()
        self.assertEqual(orderbook_analysis.ask_price, 0)
        self.assertEqual(orderbook_analysis.bid_price, 0)
        self.assertFalse(orderbook_analysis.is_ask_withdrawn)
        
        tech_indicators = self.analyzer._create_default_technical_indicators()
        self.assertEqual(tech_indicators['macd'], 0)
        self.assertEqual(tech_indicators['rsi'], 50)
    
    def test_calculate_volume_metrics(self):
        """测试计算成交量指标"""
        # 创建测试数据
        window_data = pd.DataFrame({
            'ticker_direction': ['BUY', 'SELL', 'NEUTRAL', 'BUY'],
            'price': [10.0, 10.1, 10.2, 10.3],
            'size': [100, 50, 30, 200]
        })
        
        history_data = pd.DataFrame({
            'price': np.linspace(9.5, 10.5, 30),
            'size': np.ones(30) * 100
        })
        
        # 计算成交量指标
        result = self.analyzer._calculate_volume_metrics(window_data, history_data)
        
        # 验证结果
        self.assertEqual(result.buy_volume, 300)  # 100 + 200
        self.assertEqual(result.sell_volume, 50)
        self.assertEqual(result.neutral_volume, 30)
        self.assertEqual(result.volume_ratio, 300 / 50)
        
    def test_analyze_orderbook(self):
        """测试分析买卖盘"""
        # 创建测试数据
        orderbook_window = pd.DataFrame({
            'ask_price': [10.1, 10.2],
            'ask_size': [500, 600],
            'bid_price': [10.0, 9.9],
            'bid_size': [300, 200]
        })
        
        # 分析买卖盘
        result = self.analyzer._analyze_orderbook(orderbook_window)
        
        # 验证结果
        self.assertEqual(result.ask_price, 10.2)
        self.assertEqual(result.ask_size, 600)
        self.assertEqual(result.ask_size_change, 100)  # 600 - 500
        self.assertEqual(result.bid_price, 9.9)
        self.assertEqual(result.bid_size, 200)
        self.assertEqual(result.bid_size_change, -100)  # 200 - 300
    
    def test_calculate_technical_indicators(self):
        """测试计算技术指标"""
        # 创建测试数据 - 价格呈上升趋势
        prices = np.linspace(10, 15, 50)
        history_data = pd.DataFrame({
            'price': prices,
            'size': np.ones(50) * 100
        })
        
        window_data = pd.DataFrame({
            'price': [14.8, 14.9, 15.0],
            'size': [100, 100, 100]
        })
        
        # 计算技术指标
        result = self.analyzer._calculate_technical_indicators(window_data, history_data)
        
        # 验证结果 - 上升趋势应该有正的MACD值
        self.assertGreater(result['macd'], 0)
        self.assertGreater(result['macd_hist'], 0)
        self.assertLess(result['rsi'], 100)
        self.assertGreater(result['rsi'], 0)
    
    def test_evaluate_signal(self):
        """测试评估信号强度"""
        # 创建测试数据 - 强烈买入信号
        volume_analysis = VolumeAnalysis(
            buy_volume=1000,
            sell_volume=200,
            volume_ratio=5.0,  # 高于阈值
            is_volume_surge=True,
            avg_price=10.0,
            neutral_volume=50,
            neutral_bias=0.8  # 偏向买入
        )
        
        orderbook_analysis = OrderBookAnalysis(
            ask_price=10.1,
            ask_size=500,
            ask_size_change=-2000,  # 大量撤单
            is_ask_withdrawn=True,
            is_ask_suppressed=False,
            bid_price=10.0,
            bid_size=2000,
            bid_size_change=1500,  # 大量增加
            is_bid_supported=True,
            is_bid_withdrawn=False
        )
        
        tech_indicators = {
            'macd': 0.5,
            'macd_signal': 0.2,  # MACD > Signal
            'macd_hist': 0.3,
            'rsi': 65  # 未超买
        }
        
        price_change_ratio = 0.01  # 上涨
        
        # 评估信号
        result = self.analyzer._evaluate_signal(
            volume_analysis,
            orderbook_analysis,
            tech_indicators,
            price_change_ratio
        )
        
        # 验证结果 - 应该是强烈买入信号
        self.assertEqual(result, SignalStrength.STRONG_BUY)
    
    def test_analyze_window(self):
        """测试分析时间窗口"""
        # 创建测试数据
        ticker_window = pd.DataFrame({
            'ticker_direction': ['BUY', 'BUY', 'SELL', 'NEUTRAL', 'BUY'],
            'price': [10.0, 10.1, 10.0, 10.2, 10.3],
            'size': [100, 200, 50, 30, 300]
        })
        
        orderbook_window = pd.DataFrame({
            'ask_price': [10.1, 10.2, 10.3, 10.3, 10.4],
            'ask_size': [500, 500, 600, 600, 500],
            'bid_price': [10.0, 10.0, 10.0, 10.1, 10.1],
            'bid_size': [300, 300, 400, 500, 600]
        })
        
        # 创建足够的历史数据
        prices = np.linspace(9.5, 10.3, 50)
        history_data = pd.DataFrame({
            'ticker_direction': ['BUY'] * 50,
            'price': prices,
            'size': np.ones(50) * 100
        })
        
        # 分析窗口
        result = self.analyzer.analyze_window(ticker_window, orderbook_window, history_data)
        
        # 验证结果
        self.assertIsInstance(result, TechnicalSignal)
        self.assertEqual(result.price_trend, '上涨')  # 价格从10.0上涨到10.3
        self.assertGreater(result.buying_pressure, 1.0)  # 买量大于卖量
        
    def _create_test_csv_files(self):
        """创建测试用的CSV文件"""
        # 创建ticker数据
        ticker_data = []
        base_time = datetime(2023, 1, 6, 10, 0, 0)
        
        for i in range(100):
            time = base_time + timedelta(seconds=i*10)
            direction = 'BUY' if i % 3 == 0 else ('SELL' if i % 3 == 1 else 'NEUTRAL')
            price = 10.0 + (i % 10) * 0.01
            size = 100 + i
            
            ticker_data.append({
                'id': i,
                'stock_code': 'US.TEST',
                'date_': '2023-01-06',
                'sequence': i,
                'date_time': time,
                'price': price,
                'volume': size,  # 注意这里是volume而不是size
                'turnover': price * size,
                'ticker_direction': direction,
                'type_': 'AUTO_MATCH',
                'creat_time': time,
                'update_time': time
            })
        
        ticker_df = pd.DataFrame(ticker_data)
        ticker_file = os.path.join(self.test_data_dir, 'test_ticker.csv')
        ticker_df.to_csv(ticker_file, index=False)
        
        # 创建orderbook数据
        orderbook_data = []
        
        for i in range(100):
            time = base_time + timedelta(seconds=i*10)
            bid_price = 10.0 - (i % 5) * 0.01
            ask_price = 10.1 + (i % 5) * 0.01
            bid_size = 100 + i * 2
            ask_size = 100 + i
            
            orderbook_data.append({
                'id': i,
                'stock_code': 'US.TEST',
                'date_': '2023-01-06',
                'svr_recv_time_bid': time,
                'svr_recv_time_ask': time,
                'bid': f'[({bid_price}, {bid_size}, 1, {{}})]',
                'ask': f'[({ask_price}, {ask_size}, 1, {{}})]',
                'creat_time': time,
                'update_time': time
            })
        
        orderbook_df = pd.DataFrame(orderbook_data)
        orderbook_file = os.path.join(self.test_data_dir, 'test_orderbook.csv')
        orderbook_df.to_csv(orderbook_file, index=False)
        
        return ticker_file, orderbook_file
    
    def test_read_csv_with_column_mapping(self):
        """测试读取CSV文件并修改列名"""
        ticker_file, orderbook_file = self._create_test_csv_files()
        
        # 读取ticker数据并修改列名
        ticker_df = pd.read_csv(ticker_file)
        # 将volume列重命名为size
        ticker_df.rename(columns={'volume': 'size'}, inplace=True)
        
        # 读取orderbook数据并解析bid/ask列
        orderbook_df = pd.read_csv(orderbook_file)
        
        # 解析bid/ask列并提取价格和数量
        def extract_price_size(row, field):
            # 示例: "[(0.158, 100, 1, {})]" -> (0.158, 100)
            try:
                s = row[field].strip('[]')
                parts = s.split(',')
                price = float(parts[0].strip('('))
                size = int(parts[1].strip())
                return price, size
            except:
                return 0, 0
        
        # 提取bid价格和数量
        orderbook_df['bid_price'] = orderbook_df.apply(lambda row: extract_price_size(row, 'bid')[0], axis=1)
        orderbook_df['bid_size'] = orderbook_df.apply(lambda row: extract_price_size(row, 'bid')[1], axis=1)
        
        # 提取ask价格和数量
        orderbook_df['ask_price'] = orderbook_df.apply(lambda row: extract_price_size(row, 'ask')[0], axis=1)
        orderbook_df['ask_size'] = orderbook_df.apply(lambda row: extract_price_size(row, 'ask')[1], axis=1)
        
        # 验证数据转换是否正确
        self.assertEqual(ticker_df.shape[0], 100)
        self.assertIn('size', ticker_df.columns)
        self.assertIn('bid_price', orderbook_df.columns)
        self.assertIn('ask_price', orderbook_df.columns)
        
        # 保存转换后的数据用于后续测试
        ticker_converted_file = os.path.join(self.test_data_dir, 'ticker_converted.csv')
        orderbook_converted_file = os.path.join(self.test_data_dir, 'orderbook_converted.csv')
        
        ticker_df.to_csv(ticker_converted_file, index=False)
        orderbook_df.to_csv(orderbook_converted_file, index=False)
        
        return ticker_converted_file, orderbook_converted_file
    
    def test_analyze_stock_data_with_converted_files(self):
        """测试使用转换后的文件分析股票数据"""
        # 准备转换后的文件
        ticker_file, orderbook_file = self.test_read_csv_with_column_mapping()
        
        # 读取转换后的文件并确保date_time列是日期时间类型
        ticker_df = pd.read_csv(ticker_file)
        orderbook_df = pd.read_csv(orderbook_file)
        
        # 确保date_time列存在
        if 'date_time' not in ticker_df.columns:
            # 如果没有date_time列，但有其他时间相关列，使用它们创建date_time列
            for col in ['creat_time', 'update_time']:
                if col in ticker_df.columns:
                    ticker_df['date_time'] = ticker_df[col]
                    break
        
        if 'date_time' not in orderbook_df.columns:
            # 对于orderbook，可能需要从其他列创建date_time
            for col_pair in [('svr_recv_time_bid', 'svr_recv_time_ask'), ('creat_time', 'update_time')]:
                if col_pair[0] in orderbook_df.columns:
                    orderbook_df['date_time'] = orderbook_df[col_pair[0]]
                    break
        
        # 确保date_time列是日期时间类型
        ticker_df['date_time'] = pd.to_datetime(ticker_df['date_time'])
        orderbook_df['date_time'] = pd.to_datetime(orderbook_df['date_time'])
        
        # 保存修改后的文件
        ticker_df.to_csv(ticker_file, index=False)
        orderbook_df.to_csv(orderbook_file, index=False)
        
        # 设置分析时间范围
        start_time = '2023-01-06 10:00:00'
        end_time = '2023-01-06 10:15:00'
        
        # 分析股票数据
        results = analyze_stock_data(
            ticker_file,
            orderbook_file,
            start_time=start_time,
            end_time=end_time,
            window_size='1min'
        )
        
        # 验证结果
        self.assertIsInstance(results, list)
        
    @patch('src.analysis.stock_analyzer.pd.read_csv')
    def test_analyze_stock_data_with_real_files(self, mock_read_csv):
        """测试使用真实文件路径但模拟读取过程"""
        # 模拟ticker数据
        mock_ticker_df = pd.DataFrame({
            'date_time': pd.date_range(start='2023-01-06 10:00:00', periods=100, freq='10s'),
            'ticker_direction': ['BUY', 'SELL', 'NEUTRAL'] * 33 + ['BUY'],
            'price': np.linspace(10.0, 11.0, 100),
            'size': np.ones(100) * 100
        })
        
        # 模拟orderbook数据
        mock_orderbook_df = pd.DataFrame({
            'date_time': pd.date_range(start='2023-01-06 10:00:00', periods=100, freq='10s'),
            'ask_price': np.linspace(10.1, 11.1, 100),
            'ask_size': np.ones(100) * 500,
            'bid_price': np.linspace(10.0, 10.9, 100),
            'bid_size': np.ones(100) * 300
        })
        
        # 设置mock的返回值
        mock_read_csv.side_effect = [mock_ticker_df, mock_orderbook_df]
        
        # 使用真实文件路径
        ticker_file = '/Users/<USER>/git/test_resources/20230118_swvl_ticker.csv'
        orderbook_file = '/Users/<USER>/git/test_resources/20230118_swvl_order_book.csv'
        
        # 分析股票数据
        results = analyze_stock_data(
            ticker_file,
            orderbook_file,
            start_time='2023-01-06 10:00:00',
            end_time='2023-01-06 10:15:00',
            window_size='1min'
        )
        
        # 验证结果
        self.assertIsInstance(results, list)
        
    def test_process_real_csv_files(self):
        """测试处理真实的CSV文件"""
        # 使用临时文件来存储转换后的数据
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as temp_ticker_file, \
             tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as temp_orderbook_file:
            
            temp_ticker_path = temp_ticker_file.name
            temp_orderbook_path = temp_orderbook_file.name
            
            # 读取原始文件
            try:
                ticker_df = pd.read_csv('/Users/<USER>/git/test_resources/20230118_swvl_ticker.csv')
                orderbook_df = pd.read_csv('/Users/<USER>/git/test_resources/20230118_swvl_order_book.csv')
                
                # 转换ticker数据
                if 'volume' in ticker_df.columns and 'size' not in ticker_df.columns:
                    ticker_df.rename(columns={'volume': 'size'}, inplace=True)
                
                # 确保date_time列是日期时间类型
                if 'date_time' in ticker_df.columns:
                    ticker_df['date_time'] = pd.to_datetime(ticker_df['date_time'])
                else:
                    # 如果没有date_time列，尝试从其他列创建
                    for col in ['creat_time', 'update_time']:
                        if col in ticker_df.columns:
                            ticker_df['date_time'] = pd.to_datetime(ticker_df[col])
                            break
                
                # 转换orderbook数据
                if 'bid_price' not in orderbook_df.columns:
                    # 解析bid/ask列并提取价格和数量
                    def extract_price_size(s, index=0):
                        try:
                            # 移除字符串中的括号等字符，只保留数值
                            s = s.strip('[]')
                            # 提取第一个元组
                            tuple_str = s.split('),')[0] + ')'
                            # 解析元组中的值
                            parts = tuple_str.strip('()').split(',')
                            return float(parts[index].strip())
                        except Exception as e:
                            print(f"解析错误: {str(e)}, 数据: {s}")
                            return 0
                    
                    # 提取bid价格和数量
                    orderbook_df['bid_price'] = orderbook_df['bid'].apply(lambda x: extract_price_size(x, 0))
                    orderbook_df['bid_size'] = orderbook_df['bid'].apply(lambda x: extract_price_size(x, 1))
                    
                    # 提取ask价格和数量
                    orderbook_df['ask_price'] = orderbook_df['ask'].apply(lambda x: extract_price_size(x, 0))
                    orderbook_df['ask_size'] = orderbook_df['ask'].apply(lambda x: extract_price_size(x, 1))
                
                # 确保orderbook有date_time列
                if 'date_time' not in orderbook_df.columns:
                    # 尝试从其他列创建date_time列
                    for col_pair in [('svr_recv_time_bid', 'svr_recv_time_ask'), ('creat_time', 'update_time')]:
                        if col_pair[0] in orderbook_df.columns:
                            orderbook_df['date_time'] = pd.to_datetime(orderbook_df[col_pair[0]])
                            break
                else:
                    orderbook_df['date_time'] = pd.to_datetime(orderbook_df['date_time'])
                
                # 保存转换后的数据
                ticker_df.to_csv(temp_ticker_path, index=False)
                orderbook_df.to_csv(temp_orderbook_path, index=False)
                
                # 使用转换后的文件进行分析
                results = analyze_stock_data(
                    temp_ticker_path,
                    temp_orderbook_path,
                    start_time='2023-01-06 10:00:00',
                    end_time='2023-01-06 10:30:00',
                    window_size='5min'
                )
                
                # 验证结果
                self.assertIsInstance(results, list)
                
            except FileNotFoundError:
                # 如果文件不存在，跳过测试
                self.skipTest("测试文件不存在")
            finally:
                # 清理临时文件
                if os.path.exists(temp_ticker_path):
                    os.unlink(temp_ticker_path)
                if os.path.exists(temp_orderbook_path):
                    os.unlink(temp_orderbook_path)

if __name__ == '__main__':
    unittest.main() 