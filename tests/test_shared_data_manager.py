#!/usr/bin/env python3
"""
测试共享数据管理器基础功能
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.shared_data_manager import get_shared_data_manager, SharedDataManager
from src.config.communication_config import communication_config


def test_basic_functionality():
    """测试基础功能"""
    print("=== 测试共享数据管理器基础功能 ===")
    
    # 获取管理器实例
    manager = get_shared_data_manager(use_shared_memory=False)  # 先使用本地内存测试
    
    print(f"管理器状态: {manager.get_status()}")
    
    # 测试股票列表
    print("\n--- 测试股票列表功能 ---")
    test_stocks = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
    manager.update_stock_list(test_stocks)
    
    result = manager.get_stock_list()
    print(f"股票列表: {result['stock_list']}")
    print(f"时间戳: {result['timestamp']}")
    
    # 测试实时Bar数据
    print("\n--- 测试实时Bar数据功能 ---")
    bar_data = {
        'time': '20250622 09:30:00',
        'open': 150.0,
        'high': 152.0,
        'low': 149.0,
        'close': 151.5,
        'volume': 1000000
    }
    
    manager.update_realtime_bar('AAPL', bar_data, 'realtime')
    
    result = manager.get_realtime_bars('AAPL')
    print(f"AAPL实时数据: {len(result['bars'])} 条记录")
    if result['bars']:
        print(f"最新数据: {result['bars'][-1]}")
    
    # 测试历史数据完成状态
    print("\n--- 测试历史数据完成状态功能 ---")
    manager.update_historical_data_end(
        'AAPL', 
        'historical_1min', 
        '20250622 09:30:00', 
        '20250622 16:00:00'
    )
    
    result = manager.get_historical_data_end('AAPL')
    print(f"AAPL历史数据状态: {result}")
    
    # 测试IB API状态
    print("\n--- 测试IB API状态功能 ---")
    manager.update_ib_api_status(
        is_connected=True,
        server_version="10.19",
        connection_time="2025-06-22 09:30:00",
        active_stocks_count=4
    )
    
    result = manager.get_ib_api_status()
    print(f"IB API状态: {result}")
    
    # 最终状态
    print(f"\n最终管理器状态: {manager.get_status()}")
    
    print("=== 基础功能测试完成 ===")


def test_callback_functionality():
    """测试回调功能"""
    print("\n=== 测试回调功能 ===")
    
    manager = get_shared_data_manager()
    
    # 回调函数
    callback_results = []
    
    def stock_list_callback(data_type, data):
        callback_results.append(f"股票列表回调: {data_type}, 股票数量: {len(data['stock_list'])}")
    
    def realtime_bar_callback(data_type, data):
        callback_results.append(f"实时数据回调: {data_type}, 股票: {data['stock_code']}")
    
    # 注册回调
    manager.register_callback('stock_list', stock_list_callback)
    manager.register_callback('realtime_bars', realtime_bar_callback)
    
    # 触发回调
    manager.update_stock_list(['AAPL', 'GOOGL'])
    manager.update_realtime_bar('AAPL', {
        'time': '20250622 09:31:00',
        'open': 151.5,
        'high': 152.5,
        'low': 151.0,
        'close': 152.0,
        'volume': 500000
    })
    
    # 等待回调执行
    time.sleep(0.1)
    
    print("回调结果:")
    for result in callback_results:
        print(f"  - {result}")
    
    print("=== 回调功能测试完成 ===")


def test_configuration():
    """测试配置功能"""
    print("\n=== 测试配置功能 ===")
    
    print(f"通信配置摘要: {communication_config.get_config_summary()}")
    
    # 测试不同数据类型的通信方式选择
    test_cases = [
        ('stock_list', 1),      # 低频
        ('realtime_bars', 200), # 高频
        ('ib_api_status', 1),   # 低频
        ('historical_data_end', 1)  # 低频
    ]
    
    for data_type, frequency in test_cases:
        use_shared = communication_config.should_use_shared_memory(data_type, frequency)
        print(f"{data_type} (频率: {frequency}/秒) -> {'共享内存' if use_shared else 'HTTP'}")
    
    print("=== 配置功能测试完成 ===")


def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    
    manager = get_shared_data_manager()
    
    # 测试大量数据更新
    start_time = time.time()
    
    for i in range(1000):
        bar_data = {
            'time': f'20250622 09:30:{i:02d}',
            'open': 150.0 + i * 0.01,
            'high': 152.0 + i * 0.01,
            'low': 149.0 + i * 0.01,
            'close': 151.5 + i * 0.01,
            'volume': 1000 + i
        }
        manager.update_realtime_bar('AAPL', bar_data)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"1000次数据更新耗时: {duration:.3f}秒")
    print(f"平均每次更新: {duration/1000*1000:.3f}毫秒")
    print(f"每秒处理能力: {1000/duration:.0f}次/秒")
    
    # 检查数据完整性
    result = manager.get_realtime_bars('AAPL')
    print(f"AAPL总数据条数: {len(result['bars'])}")
    
    print("=== 性能测试完成 ===")


if __name__ == "__main__":
    try:
        test_basic_functionality()
        test_callback_functionality()
        test_configuration()
        test_performance()
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
