


import { ChartManager } from '../src/web/static/js/modules/chart-manager-npm.js';

// 全局模拟 console，避免污染测试输出
global.console = {
    log: () => {},
    error: () => {},
    warn: () => {},
};

describe('ChartManager.getChanged1MinTimesOptimized', () => {
    let chartManager;

    beforeEach(() => {
        chartManager = new ChartManager();
    });

    // Test case 1: No changes
    test('当 newData 与 oldData 完全相同时应返回空数组', () => {
        const oldData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
            { time: 1060, open: 11, high: 13, low: 10, close: 12, volume: 120 },
        ];
        const newData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
            { time: 1060, open: 11, high: 13, low: 10, close: 12, volume: 120 },
        ];
        expect(chartManager.getChanged1MinTimesOptimized(newData, oldData)).toEqual([]);
    });

    // Test case 2: New data added
    test('应返回新添加数据点的时间戳', () => {
        const oldData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
        ];
        const newData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
            { time: 1060, open: 11, high: 13, low: 10, close: 12, volume: 120 },
            { time: 1120, open: 12, high: 14, low: 11, close: 13, volume: 130 },
        ];
        expect(chartManager.getChanged1MinTimesOptimized(newData, oldData)).toEqual([1060, 1120]);
    });

    // Test case 3: Data modified
    test('应返回被修改数据点的时间戳', () => {
        const oldData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
            { time: 1060, open: 11, high: 13, low: 10, close: 12, volume: 120 },
        ];
        const newData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
            { time: 1060, open: 11, high: 13.5, low: 10, close: 12.5, volume: 150 }, // Modified
        ];
        expect(chartManager.getChanged1MinTimesOptimized(newData, oldData)).toEqual([1060]);
    });

    // Test case 4: Data removed (The function is not designed to detect removals)
    test('不应把被移除的数据点视为变更', () => {
        const oldData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
            { time: 1060, open: 11, high: 13, low: 10, close: 12, volume: 120 },
        ];
        const newData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
        ];
        // The function iterates over newData, so it won't find the removed point.
        expect(chartManager.getChanged1MinTimesOptimized(newData, oldData)).toEqual([]);
    });

    // Test case 5: Mixed changes (add and modify)
    test('应返回新增和被修改数据点的时间戳', () => {
        const oldData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
            { time: 1060, open: 11, high: 13, low: 10, close: 12, volume: 120 },
        ];
        const newData = [
            { time: 1000, open: 10, high: 12.1, low: 9, close: 11.1, volume: 101 }, // Modified
            { time: 1060, open: 11, high: 13, low: 10, close: 12, volume: 120 },
            { time: 1120, open: 12, high: 14, low: 11, close: 13, volume: 130 }, // Added
        ];
        // The order might vary based on Map implementation, so we sort for stable testing
        const result = chartManager.getChanged1MinTimesOptimized(newData, oldData).sort((a, b) => a - b);
        expect(result).toEqual([1000, 1120]);
    });

    // Test case 6: Empty oldData
    test('当 oldData 为空时应返回所有时间戳', () => {
        const oldData = [];
        const newData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
            { time: 1060, open: 11, high: 13, low: 10, close: 12, volume: 120 },
        ];
        expect(chartManager.getChanged1MinTimesOptimized(newData, oldData).sort((a, b) => a - b)).toEqual([1000, 1060]);
    });

    // Test case 7: Empty newData
    test('当 newData 为空时应返回空数组', () => {
        const oldData = [
            { time: 1000, open: 10, high: 12, low: 9, close: 11, volume: 100 },
        ];
        const newData = [];
        expect(chartManager.getChanged1MinTimesOptimized(newData, oldData)).toEqual([]);
    });

    // Test case 8: Both arrays are empty
    test('当 oldData 和 newData 都为空时应返回空数组', () => {
        const oldData = [];
        const newData = [];
        expect(chartManager.getChanged1MinTimesOptimized(newData, oldData)).toEqual([]);
    });
});
