#!/usr/bin/env python3
"""
测试股票列表推送功能
"""
import sys
import os
import requests
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'api'))

def test_stock_list_push():
    """测试推送股票列表到前端"""
    test_stocks = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
    
    payload = {
        'stock_list': test_stocks,
        'timestamp': '2025-06-22T09:10:00'
    }
    
    try:
        print(f"=== 测试推送股票列表: {test_stocks} ===")
        
        response = requests.post(
            'http://localhost:5001/api/stock_list_update',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code == 200:
            print(f"=== 股票列表推送成功! ===")
            print(f"响应: {response.json()}")
        else:
            print(f"=== 股票列表推送失败: 状态码 {response.status_code} ===")
            print(f"响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"=== 网络错误: {e} ===")
    except Exception as e:
        print(f"=== 其他错误: {e} ===")

def test_realtime_bar_push():
    """测试推送实时bar数据到前端"""
    test_bar_data = {
        'time': '20250622 09:30:00',
        'open': 150.0,
        'high': 152.0,
        'low': 149.0,
        'close': 151.0,
        'volume': 10000
    }
    
    payload = {
        'stock_code': 'AAPL',
        'bar_data': test_bar_data,
        'data_type': 'realtime'
    }
    
    try:
        print(f"=== 测试推送实时bar数据: AAPL ===")
        
        response = requests.post(
            'http://localhost:5001/api/realtime_bar_update',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code == 200:
            print(f"=== 实时bar数据推送成功! ===")
            print(f"响应: {response.json()}")
        else:
            print(f"=== 实时bar数据推送失败: 状态码 {response.status_code} ===")
            print(f"响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"=== 网络错误: {e} ===")
    except Exception as e:
        print(f"=== 其他错误: {e} ===")

if __name__ == "__main__":
    print("=== 开始测试股票数据推送功能 ===")
    
    # 测试股票列表推送
    test_stock_list_push()
    
    print("\n" + "="*50 + "\n")
    
    # 等待一下
    time.sleep(2)
    
    # 测试实时bar数据推送
    test_realtime_bar_push()
    
    print("\n=== 测试完成 ===")
