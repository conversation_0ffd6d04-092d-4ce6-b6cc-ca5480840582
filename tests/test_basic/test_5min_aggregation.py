#!/usr/bin/env python3
"""
测试5分钟聚合效果
"""
import requests
import time
from datetime import datetime, timedelta

def test_5min_aggregation():
    """测试5分钟聚合效果"""
    
    print("=== 开始测试5分钟聚合效果 ===")
    
    # 模拟同一个5分钟周期内的多个1分钟数据
    # 时间周期：09:30-09:34 (5分钟周期)
    base_time = "20250622 09:30:00"
    
    # 5个1分钟的K线数据，模拟价格变化
    minute_bars = [
        {
            'time': '20250622 09:30:00',  # 第1分钟
            'open': 150.0,
            'high': 152.0,
            'low': 149.0,
            'close': 151.0,
            'volume': 10000
        },
        {
            'time': '20250622 09:31:00',  # 第2分钟
            'open': 151.0,
            'high': 153.5,
            'low': 150.5,
            'close': 152.5,
            'volume': 8000
        },
        {
            'time': '20250622 09:32:00',  # 第3分钟
            'open': 152.5,
            'high': 154.0,
            'low': 151.0,
            'close': 153.0,
            'volume': 12000
        },
        {
            'time': '20250622 09:33:00',  # 第4分钟
            'open': 153.0,
            'high': 155.0,
            'low': 152.0,
            'close': 154.5,
            'volume': 9000
        },
        {
            'time': '20250622 09:34:00',  # 第5分钟
            'open': 154.5,
            'high': 156.0,
            'low': 153.5,
            'close': 155.0,
            'volume': 11000
        }
    ]
    
    print(f"将发送5个1分钟K线数据，时间范围：09:30-09:34")
    print(f"期望的5分钟聚合结果：")
    print(f"  开盘价：150.0 (第1分钟的开盘价)")
    print(f"  最高价：156.0 (所有分钟中的最高价)")
    print(f"  最低价：149.0 (所有分钟中的最低价)")
    print(f"  收盘价：155.0 (第5分钟的收盘价)")
    print(f"  成交量：50000 (所有分钟成交量之和)")
    print()
    
    # 逐个发送1分钟数据
    for i, bar_data in enumerate(minute_bars, 1):
        print(f"发送第{i}分钟数据: {bar_data['time']}, 价格: {bar_data['open']}-{bar_data['close']}, 成交量: {bar_data['volume']}")
        
        payload = {
            'stock_code': 'AGG_TEST',
            'bar_data': bar_data,
            'data_type': 'realtime'
        }
        
        try:
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"  ✅ 第{i}分钟数据推送成功")
            else:
                print(f"  ❌ 第{i}分钟数据推送失败: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 第{i}分钟数据推送错误: {e}")
        
        # 间隔2秒，方便观察聚合过程
        if i < len(minute_bars):
            print(f"  等待2秒后发送下一分钟数据...")
            time.sleep(2)
    
    print()
    print("=== 5分钟聚合测试完成 ===")
    print("请检查前端图表，观察5分钟K线的聚合效果：")
    print("1. 图表应该显示一个5分钟K线")
    print("2. 开盘价应该是150.0")
    print("3. 最高价应该是156.0")
    print("4. 最低价应该是149.0")
    print("5. 收盘价应该是155.0")
    print("6. 成交量应该是50000")

def test_multiple_5min_periods():
    """测试多个5分钟周期"""
    
    print("\n=== 开始测试多个5分钟周期 ===")
    
    # 第一个5分钟周期：09:30-09:34
    period1_bars = [
        {
            'time': '20250622 09:30:00',
            'open': 150.0,
            'high': 152.0,
            'low': 149.0,
            'close': 151.0,
            'volume': 10000
        },
        {
            'time': '20250622 09:34:00',  # 最后一分钟
            'open': 151.0,
            'high': 153.0,
            'low': 150.0,
            'close': 152.0,
            'volume': 8000
        }
    ]
    
    # 第二个5分钟周期：09:35-09:39
    period2_bars = [
        {
            'time': '20250622 09:35:00',
            'open': 152.0,
            'high': 155.0,
            'low': 151.5,
            'close': 154.0,
            'volume': 12000
        },
        {
            'time': '20250622 09:39:00',  # 最后一分钟
            'open': 154.0,
            'high': 156.0,
            'low': 153.0,
            'close': 155.5,
            'volume': 9000
        }
    ]
    
    all_bars = period1_bars + period2_bars
    
    print(f"将发送2个5分钟周期的数据")
    print(f"第一个周期：09:30-09:34")
    print(f"第二个周期：09:35-09:39")
    print()
    
    for i, bar_data in enumerate(all_bars, 1):
        print(f"发送数据{i}: {bar_data['time']}, 价格: {bar_data['close']}")
        
        payload = {
            'stock_code': 'MULTI_PERIOD',
            'bar_data': bar_data,
            'data_type': 'realtime'
        }
        
        try:
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"  ✅ 数据{i}推送成功")
            else:
                print(f"  ❌ 数据{i}推送失败: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 数据{i}推送错误: {e}")
        
        time.sleep(1)
    
    print()
    print("=== 多周期测试完成 ===")
    print("请检查前端图表，应该显示2个5分钟K线")

if __name__ == "__main__":
    # 测试单个5分钟周期的聚合
    test_5min_aggregation()
    
    # 等待一下
    time.sleep(3)
    
    # 测试多个5分钟周期
    test_multiple_5min_periods()
    
    print("\n=== 所有测试完成 ===")
    print("请检查前端控制台和图表显示效果")
