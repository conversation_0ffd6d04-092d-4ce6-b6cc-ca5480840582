#!/usr/bin/env python3
"""
测试修复后的时间转换
"""
import requests
import time
from datetime import datetime
import pytz

def test_fixed_time_conversion():
    """测试修复后的时间转换"""
    
    # 纽约时区
    ny_tz = pytz.timezone('America/New_York')
    
    # 测试数据 - 模拟IB API返回的纽约时间
    test_bar_data = {
        'time': '20250622 09:30:00',  # 纽约时间 09:30
        'open': 150.0,
        'high': 152.0,
        'low': 149.0,
        'close': 151.0,
        'volume': 10000
    }
    
    payload = {
        'stock_code': 'AAPL',
        'bar_data': test_bar_data,
        'data_type': 'realtime'
    }
    
    try:
        print(f"=== 测试修复后的时间转换 ===")
        print(f"原始IB API时间: {test_bar_data['time']} (纽约时间)")
        
        # 计算期望的时间戳
        expected_ny_time = ny_tz.localize(datetime(2025, 6, 22, 9, 30, 0))
        expected_timestamp = int(expected_ny_time.timestamp())
        print(f"期望的时间戳: {expected_timestamp}")
        print(f"期望的UTC时间: {expected_ny_time.astimezone(pytz.UTC)}")
        
        response = requests.post(
            'http://localhost:5001/api/realtime_bar_update',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code == 200:
            print(f"=== 时间转换测试推送成功! ===")
            print(f"响应: {response.json()}")
            print(f"请检查前端控制台，确认时间戳是否正确")
        else:
            print(f"=== 时间转换测试推送失败: 状态码 {response.status_code} ===")
            print(f"响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"=== 网络错误: {e} ===")
    except Exception as e:
        print(f"=== 其他错误: {e} ===")

def test_multiple_time_formats():
    """测试多种时间格式"""
    
    test_cases = [
        {
            'name': 'IB API字符串格式',
            'time': '20250622 09:30:00',
            'description': '纽约时间 09:30'
        },
        {
            'name': 'IB API字符串格式 - 下午',
            'time': '20250622 15:45:00',
            'description': '纽约时间 15:45'
        },
        {
            'name': 'IB API字符串格式 - 盘前',
            'time': '20250622 08:00:00',
            'description': '纽约时间 08:00'
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n=== 测试案例 {i+1}: {test_case['name']} ===")
        
        test_bar_data = {
            'time': test_case['time'],
            'open': 150.0 + i,
            'high': 152.0 + i,
            'low': 149.0 + i,
            'close': 151.0 + i,
            'volume': 10000 * (i + 1)
        }
        
        payload = {
            'stock_code': f'TEST{i+1}',
            'bar_data': test_bar_data,
            'data_type': 'realtime'
        }
        
        try:
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"✅ {test_case['description']} - 推送成功")
            else:
                print(f"❌ {test_case['description']} - 推送失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {test_case['description']} - 错误: {e}")
        
        # 间隔一下
        time.sleep(1)

if __name__ == "__main__":
    print("=== 开始测试修复后的时间转换功能 ===")
    
    # 测试单个时间转换
    test_fixed_time_conversion()
    
    print("\n" + "="*60 + "\n")
    
    # 测试多种时间格式
    test_multiple_time_formats()
    
    print("\n=== 测试完成，请检查前端控制台的时间戳 ===")
