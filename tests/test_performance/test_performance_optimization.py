#!/usr/bin/env python3
"""
性能优化和错误处理增强测试

测试以下功能：
1. HTTP连接池性能
2. WebSocket批处理效果
3. 数据完整性检查
4. 性能监控指标
5. 错误处理和重连机制
"""
import time
import requests
import threading
import json
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

def test_http_connection_pool_performance():
    """测试HTTP连接池性能"""
    print("=== 测试HTTP连接池性能 ===")
    
    base_url = "http://localhost:5001"
    
    # 准备测试数据
    test_stocks = ['PERF_A', 'PERF_B', 'PERF_C', 'PERF_D', 'PERF_E']
    test_data = []
    
    for i, stock in enumerate(test_stocks):
        for j in range(10):  # 每个股票10个数据点
            bar_data = {
                'time': f'20250622 09:{30+j:02d}:00',
                'open': 100.0 + i*10 + j*0.5,
                'high': 101.0 + i*10 + j*0.5,
                'low': 99.0 + i*10 + j*0.5,
                'close': 100.5 + i*10 + j*0.5,
                'volume': 5000 + j*100
            }
            
            payload = {
                'stock_code': stock,
                'bar_data': bar_data,
                'data_type': 'realtime'
            }
            test_data.append(payload)
    
    print(f"准备推送 {len(test_data)} 个数据点")
    
    # 测试1: 串行推送（模拟无连接池）
    print("1. 串行推送测试...")
    start_time = time.time()
    success_count = 0
    
    for payload in test_data:
        try:
            response = requests.post(
                f"{base_url}/api/realtime_bar_update",
                json=payload,
                timeout=5.0
            )
            if response.status_code == 200:
                success_count += 1
        except Exception as e:
            print(f"   请求失败: {e}")
    
    serial_time = time.time() - start_time
    print(f"   串行推送完成: {success_count}/{len(test_data)} 成功")
    print(f"   总耗时: {serial_time:.2f} 秒")
    print(f"   平均延迟: {serial_time/len(test_data)*1000:.1f} ms/请求")
    
    time.sleep(2)  # 间隔
    
    # 测试2: 并发推送（模拟连接池）
    print("2. 并发推送测试...")
    start_time = time.time()
    success_count = 0
    
    def send_request(payload):
        try:
            response = requests.post(
                f"{base_url}/api/realtime_bar_update",
                json=payload,
                timeout=5.0
            )
            return response.status_code == 200
        except Exception:
            return False
    
    # 使用线程池并发发送
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(send_request, payload) for payload in test_data]
        
        for future in as_completed(futures):
            if future.result():
                success_count += 1
    
    concurrent_time = time.time() - start_time
    print(f"   并发推送完成: {success_count}/{len(test_data)} 成功")
    print(f"   总耗时: {concurrent_time:.2f} 秒")
    print(f"   平均延迟: {concurrent_time/len(test_data)*1000:.1f} ms/请求")
    
    # 性能对比
    improvement = (serial_time - concurrent_time) / serial_time * 100
    print(f"   性能提升: {improvement:.1f}%")
    
    return {
        'serial_time': serial_time,
        'concurrent_time': concurrent_time,
        'improvement_percent': improvement
    }

def test_websocket_batch_processing():
    """测试WebSocket批处理效果"""
    print("\n=== 测试WebSocket批处理效果 ===")
    
    # 快速连续推送多个数据，观察批处理效果
    test_stocks = ['BATCH_A', 'BATCH_B', 'BATCH_C']
    
    print("快速推送多个数据点，测试批处理...")
    
    start_time = time.time()
    
    for i in range(20):  # 快速推送20个数据点
        for stock in test_stocks:
            bar_data = {
                'time': f'20250622 10:{i:02d}:00',
                'open': 150.0 + i*0.1,
                'high': 151.0 + i*0.1,
                'low': 149.0 + i*0.1,
                'close': 150.5 + i*0.1,
                'volume': 6000 + i*50
            }
            
            payload = {
                'stock_code': stock,
                'bar_data': bar_data,
                'data_type': 'realtime'
            }
            
            try:
                response = requests.post(
                    'http://localhost:5001/api/realtime_bar_update',
                    json=payload,
                    timeout=2.0
                )
                if response.status_code == 200:
                    print(f"   ✅ {stock} 数据点 {i+1} 推送成功")
            except Exception as e:
                print(f"   ❌ {stock} 数据点 {i+1} 推送失败: {e}")
        
        # 非常短的间隔，触发批处理
        time.sleep(0.01)
    
    total_time = time.time() - start_time
    print(f"批处理测试完成，总耗时: {total_time:.2f} 秒")
    print("请检查前端控制台，观察是否收到批量消息")
    
    return total_time

def test_data_integrity_checking():
    """测试数据完整性检查"""
    print("\n=== 测试数据完整性检查 ===")
    
    # 推送正常数据
    print("1. 推送正常数据...")
    normal_stocks = ['INTEGRITY_A', 'INTEGRITY_B']
    
    for stock in normal_stocks:
        for i in range(5):
            bar_data = {
                'time': f'20250622 11:{i:02d}:00',
                'open': 200.0 + i,
                'high': 201.0 + i,
                'low': 199.0 + i,
                'close': 200.5 + i,
                'volume': 7000
            }
            
            payload = {
                'stock_code': stock,
                'bar_data': bar_data,
                'data_type': 'realtime'
            }
            
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"   ✅ {stock} 正常数据 {i+1} 推送成功")
            
            time.sleep(0.2)
    
    print("2. 模拟数据丢失（停止推送一段时间）...")
    print("   等待2分钟以触发数据丢失检测...")
    
    # 这里可以等待或者直接返回，让用户观察日志
    print("   请观察Flask控制台日志，查看是否有数据丢失警告")
    
    return True

def test_performance_monitoring():
    """测试性能监控功能"""
    print("\n=== 测试性能监控功能 ===")
    
    # 获取性能统计
    try:
        response = requests.get('http://localhost:5001/api/performance_stats', timeout=5.0)
        if response.status_code == 200:
            stats = response.json()
            print("✅ 性能统计API正常工作")
            print("📊 当前性能指标:")
            
            if stats.get('performance_optimization') == 'enabled':
                metrics = stats.get('performance_metrics', {})
                
                print(f"   HTTP平均延迟: {metrics.get('http_avg_time', 0)*1000:.1f} ms")
                print(f"   HTTP最大延迟: {metrics.get('http_max_time', 0)*1000:.1f} ms")
                print(f"   WebSocket平均延迟: {metrics.get('websocket_avg_time', 0)*1000:.1f} ms")
                print(f"   数据处理平均时间: {metrics.get('processing_avg_time', 0)*1000:.1f} ms")
                print(f"   总请求数: {metrics.get('total_requests', 0)}")
                print(f"   失败请求数: {metrics.get('failed_requests', 0)}")
                print(f"   成功率: {metrics.get('success_rate', 0)*100:.1f}%")
                print(f"   运行时间: {metrics.get('uptime_seconds', 0):.1f} 秒")
                print(f"   请求频率: {metrics.get('requests_per_second', 0):.1f} 请求/秒")
                
                # 数据完整性信息
                integrity = stats.get('data_integrity', {})
                print(f"   数据丢失次数: {integrity.get('missing_data_count', 0)}")
                print(f"   监控股票数: {integrity.get('monitored_stocks', 0)}")
                
                # 连接状态
                connection = stats.get('connection_status', {})
                print(f"   连接状态: {'已连接' if connection.get('is_connected') else '未连接'}")
                print(f"   重连次数: {connection.get('reconnect_count', 0)}")
                
            else:
                print("   性能优化功能未启用")
            
            return stats
        else:
            print(f"❌ 性能统计API异常: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取性能统计失败: {e}")
        return None

def test_error_handling():
    """测试错误处理机制"""
    print("\n=== 测试错误处理机制 ===")
    
    # 测试1: 发送无效数据
    print("1. 测试无效数据处理...")
    invalid_payloads = [
        {},  # 空数据
        {'stock_code': 'TEST'},  # 缺少bar_data
        {'bar_data': {'time': '20250622 12:00:00'}},  # 缺少stock_code
        {'stock_code': 'TEST', 'bar_data': {'invalid': 'data'}}  # 无效bar_data
    ]
    
    for i, payload in enumerate(invalid_payloads):
        try:
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            print(f"   测试 {i+1}: 状态码 {response.status_code}")
        except Exception as e:
            print(f"   测试 {i+1}: 异常 {e}")
    
    # 测试2: 网络超时处理
    print("2. 测试网络超时处理...")
    try:
        response = requests.post(
            'http://localhost:5001/api/realtime_bar_update',
            json={'stock_code': 'TIMEOUT_TEST', 'bar_data': {'time': '20250622 12:00:00'}},
            timeout=0.001  # 极短超时
        )
    except requests.exceptions.Timeout:
        print("   ✅ 超时处理正常")
    except Exception as e:
        print(f"   其他异常: {e}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 性能优化和错误处理增强测试")
    print("="*60)
    
    # 检查Flask服务
    try:
        response = requests.get('http://localhost:5001/', timeout=5.0)
        if response.status_code != 200:
            print("❌ Flask服务未运行，请先启动服务")
            return False
    except Exception:
        print("❌ 无法连接到Flask服务，请确保服务正在运行")
        return False
    
    print("✅ Flask服务运行正常，开始测试...\n")
    
    # 执行各项测试
    results = {}
    
    # 1. HTTP连接池性能测试
    results['http_performance'] = test_http_connection_pool_performance()
    
    # 2. WebSocket批处理测试
    results['batch_processing_time'] = test_websocket_batch_processing()
    
    # 3. 数据完整性检查测试
    results['integrity_check'] = test_data_integrity_checking()
    
    # 4. 性能监控测试
    results['performance_stats'] = test_performance_monitoring()
    
    # 5. 错误处理测试
    results['error_handling'] = test_error_handling()
    
    # 输出测试总结
    print("\n" + "="*60)
    print("🎉 性能优化和错误处理测试完成!")
    print("\n📊 测试结果总结:")
    
    if results['http_performance']:
        perf = results['http_performance']
        print(f"   HTTP性能提升: {perf['improvement_percent']:.1f}%")
    
    if results['performance_stats']:
        print("   性能监控: ✅ 正常工作")
    else:
        print("   性能监控: ❌ 需要检查")
    
    print(f"   批处理测试: ✅ 完成 ({results['batch_processing_time']:.2f}s)")
    print(f"   数据完整性: ✅ 完成")
    print(f"   错误处理: ✅ 完成")
    
    print("\n🔍 请检查:")
    print("1. Flask控制台日志中的性能指标")
    print("2. 前端页面的实时数据更新")
    print("3. 浏览器控制台的批量消息")
    print("4. 数据丢失检测警告（如有）")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 测试失败")
        exit(1)
    else:
        print("\n✅ 测试成功")
