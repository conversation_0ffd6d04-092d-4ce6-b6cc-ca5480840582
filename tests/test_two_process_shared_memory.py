#!/usr/bin/env python3
"""
测试两个进程间的共享内存通信
模拟IB API进程和Flask应用进程之间的数据共享
"""

import sys
import os
import time
import multiprocessing
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.api.shared_memory import (
    push_stock_list_to_frontend,
    push_realtime_bar_to_frontend,
    push_historical_data_end_to_frontend,
    push_ib_api_status_to_frontend,
    get_shared_data_status
)
from src.utils.shared_data_manager import get_shared_data_manager
from ibapi.common import BarData


def create_test_bar_data(stock_code: str, time_str: str, price: float) -> BarData:
    """创建测试用的BarData对象"""
    bar = BarData()
    bar.date = time_str
    bar.open = price
    bar.high = price + 1.0
    bar.low = price - 1.0
    bar.close = price + 0.5
    bar.volume = 1000000
    return bar


def ib_api_process():
    """模拟IB API进程 - 数据生产者"""
    print("🚀 IB API进程启动...")
    
    try:
        # 推送股票列表
        print("📋 推送股票列表...")
        push_stock_list_to_frontend(['AAPL', 'GOOGL', 'MSFT', 'TSLA'])
        
        # 推送IB API状态
        print("🔗 推送IB API连接状态...")
        push_ib_api_status_to_frontend(
            is_connected=True,
            server_version="10.19",
            connection_time="2025-06-22 09:30:00",
            active_stocks_count=4
        )
        
        # 模拟实时数据推送
        print("📊 开始推送实时数据...")
        for i in range(10):
            # 为每只股票推送数据
            for j, stock in enumerate(['AAPL', 'GOOGL', 'MSFT', 'TSLA']):
                bar_data = create_test_bar_data(
                    stock, 
                    f'20250622 09:3{i:01d}:00', 
                    150.0 + i + j * 100
                )
                push_realtime_bar_to_frontend(stock, bar_data)
            
            print(f"  📈 第{i+1}轮数据推送完成")
            time.sleep(0.5)  # 模拟实时数据间隔
        
        # 推送历史数据完成通知
        print("📚 推送历史数据完成通知...")
        for stock in ['AAPL', 'GOOGL', 'MSFT', 'TSLA']:
            push_historical_data_end_to_frontend(
                stock, 
                '20250622 09:30:00', 
                '20250622 16:00:00'
            )
        
        print("✅ IB API进程数据推送完成")
        
    except Exception as e:
        print(f"❌ IB API进程出错: {e}")
        import traceback
        traceback.print_exc()


def flask_app_process():
    """模拟Flask应用进程 - 数据消费者"""
    print("🌐 Flask应用进程启动...")
    
    try:
        # 等待一下让IB API进程先启动
        time.sleep(1)
        
        # 获取共享数据管理器
        manager = get_shared_data_manager(use_shared_memory=True)
        
        # 注册回调函数监听数据变化
        received_data = []
        
        def data_callback(data_type, data):
            received_data.append((data_type, data, datetime.now()))
            print(f"📨 Flask收到数据: {data_type}")
        
        manager.register_callback('stock_list', data_callback)
        manager.register_callback('realtime_bars', data_callback)
        manager.register_callback('historical_data_end', data_callback)
        manager.register_callback('ib_api_status', data_callback)
        
        print("👂 Flask应用开始监听数据变化...")
        
        # 监听数据变化
        start_time = time.time()
        while time.time() - start_time < 15:  # 监听15秒
            time.sleep(0.1)
            
            # 每2秒检查一次数据状态
            if int(time.time() - start_time) % 2 == 0:
                # 获取股票列表
                stock_data = manager.get_stock_list()
                if stock_data['stock_list']:
                    print(f"📋 当前股票列表: {stock_data['stock_list']}")
                
                # 获取实时数据统计
                all_bars = manager.get_realtime_bars()
                total_bars = sum(len(bars) for bars in all_bars['all_bars'].values())
                if total_bars > 0:
                    print(f"📊 实时数据总条数: {total_bars}")
                
                # 获取IB API状态
                api_status = manager.get_ib_api_status()
                if api_status:
                    print(f"🔗 IB API状态: 连接={api_status.get('is_connected', False)}")
        
        print(f"\n📈 Flask应用监听结束，共收到 {len(received_data)} 个数据变化通知")
        
        # 数据汇总
        print("\n📊 数据汇总:")
        stock_data = manager.get_stock_list()
        print(f"  股票列表: {stock_data['stock_list']}")
        
        all_bars = manager.get_realtime_bars()
        for stock, bars in all_bars['all_bars'].items():
            print(f"  {stock}: {len(bars)} 条实时数据")
        
        hist_data = manager.get_historical_data_end()
        print(f"  历史数据完成通知: {len(hist_data)} 只股票")
        
        api_status = manager.get_ib_api_status()
        print(f"  IB API状态: {api_status}")
        
        # 管理器状态
        status = manager.get_status()
        print(f"\n🔧 管理器状态: {status}")
        
        print("✅ Flask应用进程完成")
        
    except Exception as e:
        print(f"❌ Flask应用进程出错: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数 - 启动两个进程测试"""
    print("🧪 开始两个进程间共享内存通信测试")
    print("=" * 60)
    
    # 创建两个进程
    ib_process = multiprocessing.Process(target=ib_api_process, name="IB-API-Process")
    flask_process = multiprocessing.Process(target=flask_app_process, name="Flask-App-Process")
    
    try:
        # 启动进程
        print("🚀 启动进程...")
        ib_process.start()
        flask_process.start()
        
        # 等待进程完成
        print("⏳ 等待进程完成...")
        ib_process.join(timeout=20)
        flask_process.join(timeout=20)
        
        # 检查进程状态
        if ib_process.is_alive():
            print("⚠️  IB API进程超时，强制终止")
            ib_process.terminate()
            ib_process.join()
        
        if flask_process.is_alive():
            print("⚠️  Flask应用进程超时，强制终止")
            flask_process.terminate()
            flask_process.join()
        
        print("=" * 60)
        print("🎉 两个进程间共享内存通信测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理进程
        if ib_process.is_alive():
            ib_process.terminate()
        if flask_process.is_alive():
            flask_process.terminate()


if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)
    main()
