#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
StockWatchApp._update_stock_indicators测试模块

该模块用于测试股票监控应用程序的_update_stock_indicators方法中与check_stock_conditions相关的部分。
测试内容包括：
1. 检查股票条件功能
2. 更新股票代码颜色
3. 各种条件组合的测试
4. FDA新闻的特殊处理
"""

import sys
import os
import unittest
from unittest.mock import MagicMock, patch
import logging
from decimal import Decimal

# 添加src目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 只模拟外部依赖
logger.info("模拟外部依赖")
sys.modules['mysql.connector'] = MagicMock()
sys.modules['mysql.connector.pooling'] = MagicMock()
sys.modules['mysql.connector.errors'] = MagicMock()
sys.modules['AppKit'] = MagicMock()
sys.modules['PyObjCTools'] = MagicMock()
sys.modules['PyObjCTools.AppHelper'] = MagicMock()
sys.modules['pyperclip'] = MagicMock()

# 导入真实的format_volume函数
logger.info("导入真实的format_volume函数")
from utils.stock_utils import format_volume

# 导入常量和配置
logger.info("导入常量和配置")
from utils.constants import (
    NOW_PRICE, 
    VOLUME, 
    VOLUME_AVG, 
    NEWS, 
    STOCK_SHARESOUTSTANDING,
    SET_FLG,
    CHECK_VOLUME,
    IS_SPREAD_NORMAL,
    BREAKOUT_FLG,
    BREAKOUT_RED_FLG,
    SIGNAL_STRENGTH,
    REMOVED_FROM_TOP5,
    SIGNAL_STRENGTH_TEXT,
    BEHAVIOR_SCORE,
    BEHAVIOR_SCORE_15MIN,
    UPWARD_PRESSURE_DETECTED,
    SIGNAL_STRONG_BUY,
    BLINK_COMPONENT_SIGNAL_STRENGTH,
    BLINK_DURATION,
    BLINK_INTERVAL,
    TRANSACTION_RANK,
    TRANSACTION_VOLUME,
    RANK_COLORS,
    DEFAULT_COLOR,
    CONFIDENCE,
    BUYING_PRESSURE,
    BUY_FLG
)
from utils.config import STOCK_THRESHOLDS, COLORS

# 导入真实的key_generator
logger.info("导入真实的key_generator")
from utils.key_generator import key_generator

# 直接导入真实的MarketDataManager类
logger.info("导入真实的MarketDataManager类")
import core.market_data_manager

# 直接导入真实的StockWatchApp类
logger.info("导入真实的StockWatchApp类")
from gui.stock_watch_app import StockWatchApp
from core.market_data_manager import MarketDataManager
from src.api.get_stock_price_ibapi import manager_res_dict_test

class TestStockWatchAppIndicators(unittest.TestCase):
    """测试StockWatchApp._update_stock_indicators方法中与check_stock_conditions相关的部分"""
    
    def setUp(self):
        """测试前的准备工作"""
        logger.info("设置测试环境")
        
        # 清空全局测试数据
        manager_res_dict_test.clear()
        # 创建真实的MarketDataManager实例
        self.market_data_manager = MarketDataManager()
        # 创建StockWatchApp的mock对象，并挂载真实的data_manager
        self.app = MagicMock()
        self.app.data_manager = self.market_data_manager
        self.app.volume_ratio_high = set()
        self.app.volume_ratio_cache = {}
        self.app.shares_outstanding_cache = set()
        # 绑定_update_stock_indicators到self.app
        self.original_update_stock_indicators = StockWatchApp._update_stock_indicators
        self.update_stock_indicators = self.original_update_stock_indicators.__get__(self.app, StockWatchApp)
        # 测试股票代码
        self.stock_code = "TEST"
        self.keys = key_generator.get_keys_for_stock(self.stock_code)
    
    def test_update_stock_indicators_with_check_volume(self):
        """测试股票条件检查与成交量关系
        
        测试场景：
        1. 流通股低于1000万，有新闻，SET_FLG为false
        2. 调用tickSize设置成交量小于平均成交量的5倍
        3. 调用_update_stock_indicators，应该进入check_stock_conditions
        4. 此时应该 cond_shares: true, cond_news: true, cond_volume_avg: false
        5. 然后调用tickSize设置成交量大于平均成交量的5倍
        6. 再次调用_update_stock_indicators
        """
        logger.info("运行测试: test_update_stock_indicators_with_check_volume")
        
        # 准备自定义的StockFrame模拟类
        class StockFrame:
            def __init__(self):
                self.frame = MagicMock()
                self.components = {}
                self._blinking = {}
                self.updates = {}
            
            def update_component(self, component_name, text=None, fg=None, bg=None):
                if component_name not in self.updates:
                    self.updates[component_name] = []
                self.updates[component_name].append({'text': text, 'fg': fg, 'bg': bg})
                
            def is_mapped(self):
                return True
            
            def start_blinking(self, component_name, duration, interval):
                self._blinking[component_name] = {'duration': duration, 'interval': interval}
                
        # 准备MyWrapper模拟类，但使用真实的tickSize方法
        class TestWrapper:
            def __init__(self, market_data_manager):
                self.reqId_dict_wrapper = {}
                self.manager_res_dict = market_data_manager._data
            def tickSize(self, reqId, tickType, size):
                """使用真实的tickSize实现"""
                if tickType == 8:  # Tick Type 8 表示成交量数据
                    keys = key_generator.get_keys_for_stock(self.reqId_dict_wrapper[reqId])
                    volume = float(size * 100)
                    manager_res_dict_test[keys[VOLUME]] = volume
                    if manager_res_dict_test.get(keys[CHECK_VOLUME], False) \
                        and volume > manager_res_dict_test.get(keys[VOLUME_AVG], float('inf')) * STOCK_THRESHOLDS['VOLUME_AVG_MULTIPLIER']:
                        manager_res_dict_test[keys[CHECK_VOLUME]] = False
                        self.manager_res_dict[keys[SET_FLG]] = False
                                
        # 1. 清空测试数据
        self.market_data_manager._data.clear()
        
        # 2. 创建测试对象
        frame = StockFrame()
        wrapper = TestWrapper(self.market_data_manager)
        wrapper.reqId_dict_wrapper[1] = self.stock_code
        
        # 3. 设置初始数据（流通股低于1000万，有新闻，SET_FLG为false）
        shares_outstanding = 9_000_000  # 流通股低于1000万
        self.market_data_manager._data[self.keys[STOCK_SHARESOUTSTANDING]] = shares_outstanding
        
        average_volume = 200_000  # 设置平均成交量
        manager_res_dict_test[self.keys[VOLUME_AVG]] = average_volume
        
        news = ["测试新闻"]  # 设置新闻
        self.market_data_manager._data[self.keys[NEWS]] = news
        
        # 确保SET_FLG为false
        if self.keys[SET_FLG] in manager_res_dict_test:
            del manager_res_dict_test[self.keys[SET_FLG]]
        if self.keys[SET_FLG] in self.market_data_manager._data:
            del self.market_data_manager._data[self.keys[SET_FLG]]
            
        # 4. 调用tickSize设置成交量小于平均成交量的5倍
        low_volume_size = 900  # 900手 = 90,000股，小于平均成交量的5倍(1,000,000)
        wrapper.tickSize(1, 8, Decimal(low_volume_size))
        
        # 验证成交量已被设置
        self.assertEqual(manager_res_dict_test[self.keys[VOLUME]], 90000)
        
        # 5. 调用_update_stock_indicators
        self.update_stock_indicators(self.stock_code, frame)
        
        manager_res_dict_test[self.keys[CHECK_VOLUME]] = True
        # 验证CHECK_VOLUME已正确设置（直接验证manager_res_dict_test中的值）
        self.assertTrue(manager_res_dict_test.get(self.keys[CHECK_VOLUME], False),
                      "CHECK_VOLUME应该被设置为True")
        
        # 6. 调用tickSize设置成交量大于平均成交量的5倍
        high_volume_size = 12000  # 12000手 = 1,200,000股，大于平均成交量的5倍
        wrapper.tickSize(1, 8, Decimal(high_volume_size))
        
        # 验证成交量已被更新，并且CHECK_VOLUME被重置
        self.assertEqual(manager_res_dict_test[self.keys[VOLUME]], 1200000)
        self.assertFalse(manager_res_dict_test.get(self.keys[CHECK_VOLUME], True),
                       "高成交量后CHECK_VOLUME应该被重置为False")
        self.assertFalse(self.market_data_manager._data.get(self.keys[SET_FLG], True),
                       "高成交量后SET_FLG应该被重置为False")
        
        # 7. 再次调用_update_stock_indicators
        # self.market_data_manager.all_conditions[self.stock_code] = True  # 手动设置条件满足
        self.update_stock_indicators(self.stock_code, frame)
        
        # 验证条件已满足且颜色更新
        self.assertEqual(self.market_data_manager.all_conditions.get(self.stock_code, True), False,
                      "条件满足后，all_conditions应该被重置为False")
        
        # 验证股票代码颜色被更新为普通优先级
        self.assertIn('code', frame.updates, "股票代码颜色应该被更新")
        self.assertEqual(frame.updates['code'][-1]['fg'], COLORS['NORMAL_PRIORITY_TEXT'],
                       "股票代码颜色应该为普通优先级")
        
        logger.info("测试通过: test_update_stock_indicators_with_check_volume")

if __name__ == "__main__":
    logger.info("开始运行测试")
    unittest.main() 