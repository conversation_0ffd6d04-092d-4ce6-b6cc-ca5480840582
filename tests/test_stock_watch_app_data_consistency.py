"""
StockWatchApp数据访问一致性测试

验证StockWatchApp中的数据访问已统一为使用market_data.get()方式
"""

import unittest
import logging
from unittest.mock import MagicMock, patch
from src.data.market_data_manager import MarketDataManager
from src.data.thread_safe_stock_data_manager import reset_stock_data_manager, get_stock_data_manager
from src.utils.key_generator import key_generator
from src.utils.constants import *

# 配置日志
logging.basicConfig(level=logging.INFO)


class TestStockWatchAppDataConsistency(unittest.TestCase):
    """StockWatchApp数据访问一致性测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        reset_stock_data_manager()
        self.manager = MarketDataManager()
        self.data_manager = get_stock_data_manager()
        self.test_stock = 'AAPL'
        self.keys = key_generator.get_keys_for_stock(self.test_stock)
    
    def tearDown(self):
        """测试后的清理工作"""
        reset_stock_data_manager()
    
    def test_market_data_includes_buy_price(self):
        """测试market_data包含买入价格"""
        # 设置买入价格
        buy_price = 150.0
        self.data_manager.set_shared_data(self.test_stock, buy_price)
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 验证买入价格包含在market_data中
        self.assertIn('buy_price', market_data)
        self.assertEqual(market_data['buy_price'], buy_price)
    
    def test_market_data_includes_program_status(self):
        """测试market_data包含程序状态"""
        # 设置程序状态
        program_status = 1
        self.data_manager.set_shared_data(0, program_status)
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 验证程序状态包含在market_data中
        self.assertIn('program_status', market_data)
        self.assertEqual(market_data['program_status'], program_status)
    
    def test_market_data_includes_set_flag(self):
        """测试market_data包含SET_FLG"""
        # 设置SET_FLG
        self.data_manager.set_stock_data(self.keys[SET_FLG], True)
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 验证SET_FLG包含在market_data中
        self.assertIn(SET_FLG, market_data)
        self.assertTrue(market_data[SET_FLG])
    
    def test_market_data_comprehensive_data(self):
        """测试market_data包含所有必要的数据"""
        # 设置完整的测试数据
        test_data = {
            self.keys[NOW_PRICE]: 155.50,
            self.keys[VOLUME]: 1000000,
            self.keys[VOLUME_AVG]: 800000,
            self.keys[SET_FLG]: True,
            self.keys[BUY_FLG]: False,
            self.keys[IS_SPREAD_NORMAL]: True,
            self.keys[NUMBER_FLG]: 300,
            self.keys[SIGNAL_STRENGTH]: 'strong',
            self.keys[CONFIDENCE]: 0.85,
            self.keys[BEHAVIOR_SCORE]: 0.90,
            self.keys[BEHAVIOR_SCORE_15MIN]: 0.88,
            self.keys[UPWARD_PRESSURE_DETECTED]: True,
            self.keys[BREAKOUT_FLG]: True,
            self.keys[BREAKOUT_RED_FLG]: False,
            self.keys[NEWS]: ['新闻1', '新闻2'],
            self.keys[STOCK_SHARESOUTSTANDING]: 5000000
        }
        
        # 批量设置数据
        self.data_manager.batch_update_stock_data(test_data)
        self.data_manager.set_transaction_data(self.test_stock, 500)
        self.data_manager.set_shared_data(self.test_stock, 150.0)  # 买入价格
        self.data_manager.set_shared_data(0, 1)  # 程序状态
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 验证所有关键数据都可以通过market_data.get()获取
        required_keys = [
            NOW_PRICE, VOLUME, VOLUME_AVG, SET_FLG, BUY_FLG,
            IS_SPREAD_NORMAL, TRANSACTION_RANK, TRANSACTION_VOLUME,
            SIGNAL_STRENGTH, CONFIDENCE, BEHAVIOR_SCORE, BEHAVIOR_SCORE_15MIN,
            UPWARD_PRESSURE_DETECTED, BREAKOUT_FLG, BREAKOUT_RED_FLG,
            NEWS, STOCK_SHARESOUTSTANDING, 'buy_price', 'program_status'
        ]
        
        for key in required_keys:
            self.assertIn(key, market_data, f"market_data缺少关键字段: {key}")
        
        # 验证数据值的正确性
        self.assertEqual(market_data[NOW_PRICE], 155.50)
        self.assertEqual(market_data[VOLUME], 1000000.0)
        self.assertEqual(market_data[TRANSACTION_RANK], 500)
        self.assertEqual(market_data['buy_price'], 150.0)
        self.assertEqual(market_data['program_status'], 1)
        self.assertTrue(market_data[SET_FLG])
        self.assertTrue(market_data[BREAKOUT_FLG])
    
    def test_data_access_pattern_consistency(self):
        """测试数据访问模式的一致性"""
        # 设置测试数据
        self.data_manager.set_stock_data(self.keys[NOW_PRICE], 155.50)
        self.data_manager.set_stock_data(self.keys[SET_FLG], False)
        self.data_manager.set_shared_data(self.test_stock, 150.0)
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 模拟StockWatchApp中的数据访问模式
        # 1. 获取当前价格
        current_price = market_data.get(NOW_PRICE, 0)
        self.assertEqual(current_price, 155.50)
        
        # 2. 获取买入价格
        buy_price = market_data.get('buy_price', 0)
        self.assertEqual(buy_price, 150.0)
        
        # 3. 检查SET_FLG
        set_flag = market_data.get(SET_FLG, False)
        self.assertFalse(set_flag)
        
        # 4. 获取程序状态
        program_status = market_data.get('program_status', 1)
        self.assertEqual(program_status, 1)  # 默认值
    
    def test_price_change_calculation_with_market_data(self):
        """测试使用market_data进行价格变化计算"""
        # 设置测试数据
        current_price = 155.50
        buy_price = 150.0
        
        self.data_manager.set_stock_data(self.keys[NOW_PRICE], current_price)
        self.data_manager.set_shared_data(self.test_stock, buy_price)
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 模拟StockWatchApp中的价格变化计算
        current = market_data.get(NOW_PRICE, 0)
        buy = market_data.get('buy_price', 0)
        
        if buy > 0 and current > 0:
            price_change = current - buy
            price_change_percent = (price_change / buy) * 100
            
            self.assertEqual(price_change, 5.50)
            self.assertAlmostEqual(price_change_percent, 3.67, places=2)
    
    def test_volume_ratio_calculation_with_market_data(self):
        """测试使用market_data进行成交量比率计算"""
        # 设置测试数据
        volume = 1000000
        volume_avg = 800000
        
        self.data_manager.set_stock_data(self.keys[VOLUME], volume)
        self.data_manager.set_stock_data(self.keys[VOLUME_AVG], volume_avg)
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 模拟StockWatchApp中的成交量比率计算
        vol = market_data.get(VOLUME, 0)
        vol_avg = market_data.get(VOLUME_AVG, 0)
        
        if vol > 0 and vol_avg > 0:
            volume_ratio = int(vol / vol_avg)
            self.assertEqual(volume_ratio, 1)  # 1000000 / 800000 = 1.25 -> int(1.25) = 1
    
    def test_condition_checking_with_market_data(self):
        """测试使用market_data进行条件检查"""
        # 设置测试数据
        self.data_manager.set_stock_data(self.keys[SET_FLG], False)
        self.data_manager.set_stock_data(self.keys[IS_SPREAD_NORMAL], True)
        self.data_manager.set_stock_data(self.keys[BREAKOUT_FLG], True)
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 模拟StockWatchApp中的条件检查
        set_flag = market_data.get(SET_FLG, False)
        is_spread_normal = market_data.get(IS_SPREAD_NORMAL, False)
        breakout_normal = market_data.get(BREAKOUT_FLG, False)
        
        self.assertFalse(set_flag)
        self.assertTrue(is_spread_normal)
        self.assertTrue(breakout_normal)
        
        # 验证可以基于market_data进行条件判断
        if not set_flag:
            # 这里应该调用check_stock_conditions
            pass
        
        if is_spread_normal:
            # 这里应该设置背景色
            pass
        
        if breakout_normal:
            # 这里应该显示突破标志
            pass


if __name__ == '__main__':
    unittest.main()
