#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试股票排名变化时Tick-by-tick数据请求的逻辑
"""

import unittest
import logging
import sys
import os
from unittest.mock import MagicMock, patch
from datetime import datetime
import threading
import pytz
import pandas as pd

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 模拟导入依赖模块
sys.modules['core'] = MagicMock()
sys.modules['core.check_the_signal'] = MagicMock()
sys.modules['analysis'] = MagicMock()
sys.modules['analysis.pre_stock_signal'] = MagicMock()
sys.modules['utils'] = MagicMock()
sys.modules['utils.futu_utils'] = MagicMock()
sys.modules['utils.constants'] = MagicMock()
sys.modules['services'] = MagicMock()
sys.modules['services.globenewswire_rss'] = MagicMock()
sys.modules['services.stocktitan_rss'] = MagicMock()
sys.modules['utils.stock_utils'] = MagicMock()
sys.modules['utils.yfinance_fun'] = MagicMock()
sys.modules['analysis.tradingview_screener_q'] = MagicMock()
sys.modules['utils.db_utils'] = MagicMock()
sys.modules['analysis.macd_check'] = MagicMock()
sys.modules['analysis.check_breakout_level'] = MagicMock()
sys.modules['utils.restart_IB_Gateway'] = MagicMock()
sys.modules['analysis.number_of_transactions'] = MagicMock()
sys.modules['utils.config'] = MagicMock()
sys.modules['utils.config'].IB_CONFIG = {
    'MAX_RETRY_ATTEMPTS': 3,
    'HOST': '127.0.0.1',
    'PORT': 7497,
    'CLIENT_ID': 1000,
    'RETRY_INTERVAL': 5
}
sys.modules['utils.key_generator'] = MagicMock()
sys.modules['utils.key_generator'].key_generator = MagicMock()
sys.modules['utils.key_generator'].key_generator.get_keys_for_stock = MagicMock(return_value={})
sys.modules['analysis.stock_analyzer'] = MagicMock()
sys.modules['futu'] = MagicMock()

# 导入ibapi模块并定义必要的类型
sys.modules['ibapi'] = MagicMock()
sys.modules['ibapi.client'] = MagicMock()
sys.modules['ibapi.wrapper'] = MagicMock()
sys.modules['ibapi.scanner'] = MagicMock()
sys.modules['ibapi.contract'] = MagicMock()
sys.modules['ibapi.utils'] = MagicMock()
sys.modules['ibapi.common'] = MagicMock()

# 定义ibapi中的类型
class TickerId(int): pass
class ListOfHistoricalTickLast(list): pass
class TickAttribLast: 
    def __init__(self):
        self.pastLimit = False
        self.unreported = False
class TickAttribBidAsk:
    def __init__(self):
        self.bidPastLow = False
        self.askPastHigh = False
class Decimal(float): pass

# 创建EWrapper和EClient类，避免元类冲突
class EWrapper:
    def __init__(self):
        pass

class EClient:
    def __init__(self, wrapper):
        self.wrapper = wrapper

# 将类型添加到ibapi模块中
sys.modules['ibapi.common'].TickerId = TickerId
sys.modules['ibapi.common'].ListOfHistoricalTickLast = ListOfHistoricalTickLast
sys.modules['ibapi.common'].TickAttribLast = TickAttribLast
sys.modules['ibapi.common'].TickAttribBidAsk = TickAttribBidAsk
sys.modules['ibapi.common'].Decimal = Decimal
sys.modules['ibapi.wrapper'].EWrapper = EWrapper
sys.modules['ibapi.client'].EClient = EClient

# 创建测试用的MyWrapper和MyClient类
class MyWrapper(EWrapper):
    def __init__(self):
        super().__init__()
        
class MyClient(EClient):
    def __init__(self, wrapper):
        super().__init__(wrapper)

class IBApp(MyWrapper, MyClient):
    def __init__(self):
        MyWrapper.__init__(self)
        MyClient.__init__(self, wrapper=self)
        
        # 必要的属性
        self.top5_tick_stocks = set()
        self.watch_dict = {}
        self.active_tick_requests = {}
        self.reqId_dict = {}
        self.reqId_dict_wrapper = {}
        self.contractDetails_dict = {}
        
    def update_top5_tick_stocks(self):
        # 获取所有股票的rank
        ranked_stocks = sorted(self.watch_dict.items(), key=lambda x: x[0])
        # 只保留前5名
        new_top5 = set(stock for rank, stock in ranked_stocks[:5])
        
        # 找出需要添加和移除的股票
        to_add = new_top5 - self.top5_tick_stocks
        to_remove = self.top5_tick_stocks - new_top5
        
        # 更新集合
        self.top5_tick_stocks = new_top5
        
        # 对于被移除的股票，取消逐笔数据请求
        for stock_code in to_remove:
            self.cancel_tick_by_tick_data(stock_code)
        
        # 对于新添加的股票，检查是否需要立即请求Tick-by-tick数据
        for stock_code in to_add:
            if stock_code in self.reqId_dict:
                # 如果股票不在接收Tick-by-tick数据，立即请求
                if not self.is_receiving_tick_data(stock_code):
                    stock_count = self.reqId_dict[stock_code]
                    if stock_count in self.contractDetails_dict:
                        self.get_tick_by_tick_data(stock_count)
    
    def is_receiving_tick_data(self, stock_code):
        """检查股票是否已经在接收Tick-by-tick数据"""
        if stock_code in self.reqId_dict:
            reqid_ = self.reqId_dict[stock_code]
            if reqid_ in self.contractDetails_dict:
                contract = self.contractDetails_dict[reqid_]
                contract_key = f"{contract.symbol}_{contract.secType}_{contract.exchange}"
                return contract_key in self.active_tick_requests
        return False
    
    def cancel_tick_by_tick_data(self, stock_code):
        """取消股票的逐笔数据请求"""
        if stock_code in self.reqId_dict:
            reqid_ = self.reqId_dict[stock_code]
            if reqid_ in self.contractDetails_dict:
                contract = self.contractDetails_dict[reqid_]
                contract_key = f"{contract.symbol}_{contract.secType}_{contract.exchange}"
                
                if contract_key in self.active_tick_requests:
                    active_reqid = self.active_tick_requests[contract_key]
                    self.cancelTickByTickData(active_reqid)
                    self.active_tick_requests.pop(contract_key)
    
    def get_tick_by_tick_data(self, reqid_):
        """请求逐笔交易数据"""
        pass
        
    def scannerData(self, reqId, rank, contractDetails, distance, benchmark, projection, legsStr):
        """处理市场扫描仪返回的数据"""
        stock_code = contractDetails.contract.localSymbol
        self.watch_dict[rank] = stock_code
        
        # 如果rank变化，更新前5名股票的Tick-by-tick数据跟踪集合
        if rank <= 4:  # rank从0开始，所以前5名是0-4
            # 更新前5名股票集合
            self.update_top5_tick_stocks()

# 现在导入要测试的模块
with patch.dict('sys.modules'):
    # 使用我们自定义的类替代实际导入
    sys.modules['src.api.get_stock_price_ibapi'] = MagicMock()
    sys.modules['src.api.get_stock_price_ibapi'].IBApp = IBApp
    sys.modules['src.api.get_stock_price_ibapi'].MyWrapper = MyWrapper
    sys.modules['src.api.get_stock_price_ibapi'].MyClient = MyClient

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(filename)s - %(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

class TestTickByTickRankChange(unittest.TestCase):
    """测试股票排名变化时Tick-by-tick数据请求的逻辑"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建IBApp实例
        self.ib_app = IBApp()
        
        # 模拟必要的属性和方法
        self.ib_app.start = True
        self.ib_app.is_shutting_down = False
        self.ib_app.lock = threading.Lock()
        self.ib_app.tick_request_lock = threading.Lock()
        self.ib_app.error_lock = threading.Lock()
        self.ib_app.request_id_lock = threading.Lock()
        self.ib_app.active_request_ids = set()
        self.ib_app.last_request_id = 10000
        self.ib_app.active_tick_requests = {}
        self.ib_app.reqId_dict = {}
        self.ib_app.reqId_dict_day = {}
        self.ib_app.reqId_dict_wrapper = {}
        self.ib_app.contractDetails_dict = {}
        self.ib_app.data = {}
        self.ib_app.watch_dict = {}
        self.ib_app.top5_tick_stocks = set()
        self.ib_app.curr_date = datetime.now(tz=pytz.timezone('America/New_York')).strftime('%Y-%m-%d')
        
        # 模拟方法
        self.ib_app._get_next_request_id = MagicMock(return_value=10001)
        self.ib_app._release_request_id = MagicMock()
        self.ib_app.reqTickByTickData = MagicMock()
        self.ib_app.cancelTickByTickData = MagicMock()
        self.ib_app.reqHistoricalData = MagicMock()
        self.ib_app.reqMktData = MagicMock()
        self.ib_app.get_tick_by_tick_data = MagicMock()  # 使用MagicMock替代原始方法
        
    def test_update_top5_tick_stocks_add_stock(self):
        """测试当股票进入前5名时，是否正确请求Tick-by-tick数据"""
        # 准备测试数据
        # 模拟5个股票的合约
        for i in range(1, 6):
            stock_code = f"STOCK{i}"
            reqid = 10000 + i
            contract = MagicMock()
            contract.symbol = stock_code
            contract.secType = "STK"
            contract.exchange = "SMART"
            
            self.ib_app.reqId_dict[stock_code] = reqid
            self.ib_app.reqId_dict_wrapper[reqid] = stock_code
            self.ib_app.contractDetails_dict[reqid] = contract
            
        # 设置初始排名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK5"
        }
        
        # 模拟is_receiving_tick_data方法，让它返回True，这样就不会调用get_tick_by_tick_data
        self.ib_app.is_receiving_tick_data = MagicMock(return_value=True)
        
        # 初始化前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证是否正确请求了Tick-by-tick数据
        self.assertEqual(len(self.ib_app.top5_tick_stocks), 5)
        self.assertSetEqual(self.ib_app.top5_tick_stocks, {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5"})
        self.assertEqual(self.ib_app.get_tick_by_tick_data.call_count, 0)  # 初始化时没有调用，因为我们模拟的is_receiving_tick_data总是返回True
        
        # 修改is_receiving_tick_data的行为，让它返回False
        self.ib_app.is_receiving_tick_data = MagicMock(return_value=False)
        
        # 重新初始化，这次应该会请求数据
        self.ib_app.top5_tick_stocks = set()
        self.ib_app.update_top5_tick_stocks()
        
        # 验证是否正确请求了Tick-by-tick数据
        self.assertEqual(self.ib_app.get_tick_by_tick_data.call_count, 5)  # 应该为5个股票请求数据
        
    def test_update_top5_tick_stocks_remove_stock(self):
        """测试当股票离开前5名时，是否正确取消Tick-by-tick数据请求"""
        # 准备测试数据
        # 模拟6个股票的合约
        for i in range(1, 7):
            stock_code = f"STOCK{i}"
            reqid = 10000 + i
            contract = MagicMock()
            contract.symbol = stock_code
            contract.secType = "STK"
            contract.exchange = "SMART"
            
            self.ib_app.reqId_dict[stock_code] = reqid
            self.ib_app.reqId_dict_wrapper[reqid] = stock_code
            self.ib_app.contractDetails_dict[reqid] = contract
            
        # 设置初始排名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK5"
        }
        
        # 初始化前5名股票集合
        self.ib_app.top5_tick_stocks = {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5"}
        
        # 模拟cancel_tick_by_tick_data方法
        self.ib_app.cancel_tick_by_tick_data = MagicMock()
        
        # 修改排名，让STOCK5离开前5名，STOCK6进入前5名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK6"
        }
        
        # 更新前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证是否正确取消了STOCK5的Tick-by-tick数据请求
        self.ib_app.cancel_tick_by_tick_data.assert_called_once_with("STOCK5")
        
        # 验证前5名股票集合是否正确更新
        self.assertEqual(len(self.ib_app.top5_tick_stocks), 5)
        self.assertSetEqual(self.ib_app.top5_tick_stocks, {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK6"})
        
    def test_scanner_data_rank_change(self):
        """测试当scannerData返回新的排名时，是否正确更新前5名股票集合并请求Tick-by-tick数据"""
        # 准备测试数据
        # 模拟6个股票的合约
        for i in range(1, 7):
            stock_code = f"STOCK{i}"
            reqid = 10000 + i
            contract = MagicMock()
            contract.symbol = stock_code
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.localSymbol = stock_code
            
            self.ib_app.reqId_dict[stock_code] = reqid
            self.ib_app.reqId_dict_wrapper[reqid] = stock_code
            self.ib_app.contractDetails_dict[reqid] = contract
            
        # 设置初始排名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK5"
        }
        
        # 初始化前5名股票集合
        self.ib_app.top5_tick_stocks = {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5"}
        
        # 模拟update_top5_tick_stocks方法
        self.ib_app.update_top5_tick_stocks = MagicMock()
        
        # 模拟contractDetails
        contract_details = MagicMock()
        contract_details.contract = MagicMock()
        contract_details.contract.localSymbol = "STOCK6"
        
        # 调用scannerData方法，模拟STOCK6进入前5名
        self.ib_app.scannerData(reqId=1, rank=4, contractDetails=contract_details, 
                               distance="", benchmark="", projection="", legsStr="")
        
        # 验证是否调用了update_top5_tick_stocks方法
        self.ib_app.update_top5_tick_stocks.assert_called_once()
        
        # 验证watch_dict是否正确更新
        self.assertEqual(self.ib_app.watch_dict[4], "STOCK6")
        
    def test_is_receiving_tick_data(self):
        """测试is_receiving_tick_data方法是否正确检测股票是否在接收Tick-by-tick数据"""
        # 准备测试数据
        stock_code = "STOCK1"
        reqid = 10001
        contract = MagicMock()
        contract.symbol = stock_code
        contract.secType = "STK"
        contract.exchange = "SMART"
        
        self.ib_app.reqId_dict[stock_code] = reqid
        self.ib_app.contractDetails_dict[reqid] = contract
        
        # 测试股票不在接收数据的情况
        self.ib_app.active_tick_requests = {}
        self.assertFalse(self.ib_app.is_receiving_tick_data(stock_code))
        
        # 测试股票在接收数据的情况
        contract_key = f"{stock_code}_STK_SMART"
        self.ib_app.active_tick_requests[contract_key] = reqid
        self.assertTrue(self.ib_app.is_receiving_tick_data(stock_code))
        
    def test_cancel_tick_by_tick_data(self):
        """测试cancel_tick_by_tick_data方法是否正确取消Tick-by-tick数据请求"""
        # 准备测试数据
        stock_code = "STOCK1"
        reqid = 10001
        contract = MagicMock()
        contract.symbol = stock_code
        contract.secType = "STK"
        contract.exchange = "SMART"
        
        self.ib_app.reqId_dict[stock_code] = reqid
        self.ib_app.contractDetails_dict[reqid] = contract
        
        # 模拟股票在接收数据
        contract_key = f"{stock_code}_STK_SMART"
        self.ib_app.active_tick_requests[contract_key] = reqid
        
        # 调用cancel_tick_by_tick_data方法
        self.ib_app.cancel_tick_by_tick_data(stock_code)
        
        # 验证是否调用了cancelTickByTickData方法
        self.ib_app.cancelTickByTickData.assert_called_once_with(reqid)
        
        # 验证active_tick_requests是否正确更新
        self.assertNotIn(contract_key, self.ib_app.active_tick_requests)

if __name__ == "__main__":
    unittest.main() 