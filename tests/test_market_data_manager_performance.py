"""
MarketDataManager高负荷性能测试

本测试用于验证MarketDataManager在以下场景下的性能和稳定性：
1. 多线程并发读写测试
2. 大数据量读写性能测试
3. 批量操作与单独操作的性能对比
4. 模拟真实市场数据高频更新场景
"""

import unittest
import threading
import time
import logging
import random
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
from matplotlib.ticker import FuncFormatter
import numpy as np

from src.data.market_data_manager import MarketDataManager
from src.data.thread_safe_stock_data_manager import reset_stock_data_manager, get_stock_data_manager
from src.utils.key_generator import key_generator
from src.utils.constants import *

# 配置日志
logging.basicConfig(level=logging.INFO)


class TestMarketDataManagerPerformance(unittest.TestCase):
    """MarketDataManager性能测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        reset_stock_data_manager()
        self.manager = MarketDataManager()
        self.data_manager = get_stock_data_manager()
        
        # 测试参数
        self.num_stocks = 500  # 股票数量
        self.num_operations = 10000  # 操作次数
        self.num_threads = 8  # 线程数
        
        # 测试数据
        self.test_stocks = [f'STOCK{i:04d}' for i in range(self.num_stocks)]
        self.test_fields = [NOW_PRICE, VOLUME, CHECK_VOLUME, VOLUME_AVG, 
                           IS_SPREAD_NORMAL, BUY_FLG, SET_FLG, BREAKOUT_FLG, 
                           SIGNAL_STRENGTH, CONFIDENCE, BEHAVIOR_SCORE]
        
        # 性能记录
        self.performance_results = {
            'single_get': [],
            'single_set': [],
            'batch_get': [],
            'batch_set': [],
            'market_data_get': [],
            'market_data_set': [],
            'concurrent_reads': [],
            'concurrent_writes': [],
            'concurrent_mixed': []
        }
        
    def tearDown(self):
        """测试后的清理工作"""
        reset_stock_data_manager()
        
    def test_single_vs_batch_operations(self):
        """测试单个操作与批量操作的性能对比"""
        print("\n=== 测试单个操作与批量操作性能 ===")
        
        # 准备数据
        test_data = {}
        for field in self.test_fields:
            if field == NOW_PRICE:
                test_data[field] = 150.0
            elif field in [VOLUME, CHECK_VOLUME, VOLUME_AVG]:
                test_data[field] = 1000000
            elif field in [IS_SPREAD_NORMAL, BUY_FLG, SET_FLG, BREAKOUT_FLG]:
                test_data[field] = True
            elif field == SIGNAL_STRENGTH:
                test_data[field] = 'STRONG_BUY'
            elif field in [CONFIDENCE, BEHAVIOR_SCORE]:
                test_data[field] = 0.85
                
        stock_code = self.test_stocks[0]
        iterations = 1000
                
        # 1. 测试单个字段 set 操作
        start_time = time.time()
        for _ in range(iterations):
            for field, value in test_data.items():
                self.manager.set(stock_code, field, value)
        end_time = time.time()
        single_set_time = end_time - start_time
        self.performance_results['single_set'].append(single_set_time)
        
        # 2. 测试单个字段 get 操作
        start_time = time.time()
        for _ in range(iterations):
            for field in test_data:
                self.manager.get(stock_code, field)
        end_time = time.time()
        single_get_time = end_time - start_time
        self.performance_results['single_get'].append(single_get_time)
        
        # 3. 测试批量 set 操作
        start_time = time.time()
        for _ in range(iterations):
            self.manager.set_market_data(stock_code, test_data)
        end_time = time.time()
        batch_set_time = end_time - start_time
        self.performance_results['batch_set'].append(batch_set_time)
        
        # 4. 测试批量 get 操作
        start_time = time.time()
        for _ in range(iterations):
            self.manager.get_market_data(stock_code)
        end_time = time.time()
        batch_get_time = end_time - start_time
        self.performance_results['batch_get'].append(batch_get_time)
        
        # 打印结果
        print(f"单个字段 set {len(test_data)}个字段 x {iterations}次: {single_set_time:.3f}秒 ({len(test_data)*iterations/single_set_time:.0f}次/秒)")
        print(f"单个字段 get {len(test_data)}个字段 x {iterations}次: {single_get_time:.3f}秒 ({len(test_data)*iterations/single_get_time:.0f}次/秒)")
        print(f"批量 set_market_data x {iterations}次: {batch_set_time:.3f}秒 ({iterations/batch_set_time:.0f}次/秒)")
        print(f"批量 get_market_data x {iterations}次: {batch_get_time:.3f}秒 ({iterations/batch_get_time:.0f}次/秒)")
        
        # 验证数据一致性
        market_data = self.manager.get_market_data(stock_code)
        for field, value in test_data.items():
            self.assertEqual(market_data.get(field), value)
            
        # 验证性能优势
        self.assertLess(batch_set_time, single_set_time, "批量设置应该比单独设置更快")
        
    def test_large_data_performance(self):
        """测试大数据量场景下的性能"""
        print("\n=== 测试大数据量性能 ===")
        
        # 为大量股票设置数据
        start_time = time.time()
        for stock_code in self.test_stocks:
            data = {
                NOW_PRICE: random.uniform(10.0, 500.0),
                VOLUME: random.randint(100000, 10000000),
                VOLUME_AVG: random.randint(50000, 5000000),
                IS_SPREAD_NORMAL: random.choice([True, False]),
                BUY_FLG: random.choice([True, False]),
                SET_FLG: random.choice([True, False]),
                SIGNAL_STRENGTH: random.choice(['WEAK_BUY', 'NEUTRAL', 'STRONG_BUY', 'WATCH']),
                CONFIDENCE: random.uniform(0.0, 1.0),
                BEHAVIOR_SCORE: random.uniform(0.0, 10.0),
            }
            self.manager.set_market_data(stock_code, data)
            
            # 设置交易排名
            self.manager.set(stock_code, TRANSACTION_RANK, random.randint(0, 5))
        
        set_time = time.time() - start_time
        self.performance_results['market_data_set'].append(set_time)
        
        # 获取所有股票的数据
        start_time = time.time()
        all_data = self.manager.batch_get_market_data(self.test_stocks)
        get_time = time.time() - start_time
        self.performance_results['market_data_get'].append(get_time)
        
        # 打印结果
        print(f"设置{self.num_stocks}支股票数据耗时: {set_time:.3f}秒 ({self.num_stocks/set_time:.0f}支/秒)")
        print(f"获取{self.num_stocks}支股票数据耗时: {get_time:.3f}秒 ({self.num_stocks/get_time:.0f}支/秒)")
        print(f"数据管理器状态: {self.manager.get_data_manager_stats()}")
        
        # 验证数据完整性
        self.assertEqual(len(all_data), self.num_stocks)
        for stock_code in self.test_stocks:
            self.assertIn(stock_code, all_data)
            stock_data = all_data[stock_code]
            self.assertIn(NOW_PRICE, stock_data)
            self.assertIn(VOLUME, stock_data)
            
    def _concurrent_reader(self, stock_codes, iterations):
        """并发读取器函数"""
        results = []
        for _ in range(iterations):
            stock_code = random.choice(stock_codes)
            start_time = time.time()
            data = self.manager.get_market_data(stock_code)
            elapsed = time.time() - start_time
            results.append((elapsed, len(data) > 0))
        return results
        
    def _concurrent_writer(self, stock_codes, iterations):
        """并发写入器函数"""
        results = []
        for _ in range(iterations):
            stock_code = random.choice(stock_codes)
            data = {
                NOW_PRICE: random.uniform(10.0, 500.0),
                VOLUME: random.randint(100000, 10000000),
                SIGNAL_STRENGTH: random.choice(['WEAK_BUY', 'NEUTRAL', 'STRONG_BUY', 'WATCH']),
            }
            start_time = time.time()
            self.manager.set_market_data(stock_code, data)
            elapsed = time.time() - start_time
            results.append(elapsed)
        return results
        
    def _concurrent_mixed(self, stock_codes, iterations, read_ratio=0.8):
        """混合读写函数，模拟真实场景"""
        results = {'read': [], 'write': []}
        for _ in range(iterations):
            stock_code = random.choice(stock_codes)
            if random.random() < read_ratio:  # 大部分是读操作
                start_time = time.time()
                self.manager.get_market_data(stock_code)
                elapsed = time.time() - start_time
                results['read'].append(elapsed)
            else:  # 少部分是写操作
                data = {
                    NOW_PRICE: random.uniform(10.0, 500.0),
                    VOLUME: random.randint(100000, 10000000),
                }
                start_time = time.time()
                self.manager.set_market_data(stock_code, data)
                elapsed = time.time() - start_time
                results['write'].append(elapsed)
        return results
    
    def test_concurrent_access(self):
        """测试并发访问性能"""
        print("\n=== 测试并发访问性能 ===")
        
        # 准备测试数据
        for stock_code in self.test_stocks[:100]:  # 使用前100支股票
            data = {
                NOW_PRICE: random.uniform(10.0, 500.0),
                VOLUME: random.randint(100000, 10000000),
                VOLUME_AVG: random.randint(50000, 5000000),
                SIGNAL_STRENGTH: random.choice(['WEAK_BUY', 'NEUTRAL', 'STRONG_BUY', 'WATCH']),
                CONFIDENCE: random.uniform(0.0, 1.0),
            }
            self.manager.set_market_data(stock_code, data)
        
        test_stocks = self.test_stocks[:100]
        operations_per_thread = 1000
        
        # 1. 测试并发读取
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            read_futures = [
                executor.submit(self._concurrent_reader, test_stocks, operations_per_thread)
                for _ in range(self.num_threads)
            ]
            
        read_times = []
        for future in read_futures:
            read_times.extend([t for t, success in future.result()])
            
        avg_read_time = sum(read_times) / len(read_times)
        self.performance_results['concurrent_reads'].append(avg_read_time)
        
        # 2. 测试并发写入
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            write_futures = [
                executor.submit(self._concurrent_writer, test_stocks, operations_per_thread)
                for _ in range(self.num_threads)
            ]
            
        write_times = []
        for future in write_futures:
            write_times.extend(future.result())
            
        avg_write_time = sum(write_times) / len(write_times)
        self.performance_results['concurrent_writes'].append(avg_write_time)
        
        # 3. 测试混合读写
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            mixed_futures = [
                executor.submit(self._concurrent_mixed, test_stocks, operations_per_thread)
                for _ in range(self.num_threads)
            ]
            
        mixed_read_times = []
        mixed_write_times = []
        for future in mixed_futures:
            result = future.result()
            mixed_read_times.extend(result['read'])
            mixed_write_times.extend(result['write'])
            
        avg_mixed_read_time = sum(mixed_read_times) / len(mixed_read_times) if mixed_read_times else 0
        avg_mixed_write_time = sum(mixed_write_times) / len(mixed_write_times) if mixed_write_times else 0
        self.performance_results['concurrent_mixed'].append((avg_mixed_read_time, avg_mixed_write_time))
        
        # 打印结果
        total_reads = self.num_threads * operations_per_thread
        total_writes = self.num_threads * operations_per_thread
        total_mixed = self.num_threads * operations_per_thread
        
        print(f"并发读取: {self.num_threads}线程 x {operations_per_thread}操作, 平均耗时: {avg_read_time*1000:.3f}毫秒/操作")
        print(f"并发写入: {self.num_threads}线程 x {operations_per_thread}操作, 平均耗时: {avg_write_time*1000:.3f}毫秒/操作")
        print(f"混合读写: {self.num_threads}线程 x {operations_per_thread}操作")
        print(f"  - 读取平均耗时: {avg_mixed_read_time*1000:.3f}毫秒/操作")
        print(f"  - 写入平均耗时: {avg_mixed_write_time*1000:.3f}毫秒/操作")
        print(f"读取吞吐量: {1/avg_read_time:.0f}操作/秒")
        print(f"写入吞吐量: {1/avg_write_time:.0f}操作/秒")
        print(f"数据管理器状态: {self.manager.get_data_manager_stats()}")
        
    def test_high_frequency_updates(self):
        """测试高频更新场景，模拟股票实时数据快速变化"""
        print("\n=== 测试高频更新场景 ===")
        
        # 选择少量股票进行高频更新测试
        test_stocks = self.test_stocks[:10]
        updates_per_stock = 1000
        
        # 记录每支股票每次更新的时间
        update_times = {stock: [] for stock in test_stocks}
        
        # 为每支股票执行大量快速更新
        for stock in test_stocks:
            for i in range(updates_per_stock):
                # 模拟价格小幅波动
                price = 100 + random.uniform(-1, 1)
                volume = 100000 + i * 100
                
                start_time = time.time()
                self.manager.set(stock, NOW_PRICE, price)
                self.manager.set(stock, VOLUME, volume)
                elapsed = time.time() - start_time
                update_times[stock].append(elapsed)
                
        # 计算平均更新时间
        all_times = []
        for stock, times in update_times.items():
            all_times.extend(times)
            avg_time = sum(times) / len(times)
            print(f"股票 {stock} 平均更新时间: {avg_time*1000:.3f}毫秒/操作")
            
        overall_avg = sum(all_times) / len(all_times)
        print(f"所有股票平均更新时间: {overall_avg*1000:.3f}毫秒/操作")
        print(f"更新吞吐量: {1/overall_avg:.0f}操作/秒")
        
        # 检查数据一致性
        for stock in test_stocks:
            data = self.manager.get_market_data(stock)
            self.assertIn(NOW_PRICE, data)
            self.assertIn(VOLUME, data)
            
    def test_realistic_trading_day_simulation(self):
        """模拟真实交易日场景，测试系统在持续负载下的性能"""
        print("\n=== 模拟真实交易日场景 ===")
        
        # 模拟参数
        num_stocks = 50  # 模拟股票数量
        duration_seconds = 10  # 模拟持续时间（秒）
        update_interval = 0.01  # 更新间隔（秒）
        stocks = self.test_stocks[:num_stocks]
        
        # 记录每个时间点的系统负载
        timestamps = []
        operations_per_sec = []
        avg_response_times = []
        
        start_simulation = time.time()
        end_simulation = start_simulation + duration_seconds
        operation_count = 0
        
        # 模拟不同时间段的负载变化
        while time.time() < end_simulation:
            current_time = time.time()
            elapsed = current_time - start_simulation
            timestamps.append(elapsed)
            
            # 确定这个时间点的操作数量（模拟开盘、午盘和收盘的不同负载）
            if elapsed < duration_seconds * 0.2:  # 开盘
                ops_this_second = int(num_stocks * 0.8)  # 80%的股票更新
            elif duration_seconds * 0.45 < elapsed < duration_seconds * 0.55:  # 午盘
                ops_this_second = int(num_stocks * 0.4)  # 40%的股票更新
            elif elapsed > duration_seconds * 0.8:  # 收盘
                ops_this_second = int(num_stocks * 0.9)  # 90%的股票更新
            else:  # 常规交易
                ops_this_second = int(num_stocks * 0.6)  # 60%的股票更新
                
            # 执行操作并记录响应时间
            batch_times = []
            for _ in range(ops_this_second):
                stock = random.choice(stocks)
                
                # 80%概率是更新价格，20%概率是获取数据
                if random.random() < 0.8:
                    price = 100 + random.uniform(-2, 2)
                    volume = random.randint(10000, 1000000)
                    
                    start_op = time.time()
                    self.manager.set(stock, NOW_PRICE, price)
                    self.manager.set(stock, VOLUME, volume)
                    op_time = time.time() - start_op
                else:
                    start_op = time.time()
                    self.manager.get_market_data(stock)
                    op_time = time.time() - start_op
                    
                batch_times.append(op_time)
                operation_count += 1
            
            # 记录这个时间点的平均响应时间
            if batch_times:
                avg_response_times.append(sum(batch_times) / len(batch_times))
                operations_per_sec.append(len(batch_times) / update_interval)
            
            # 控制更新速率
            sleep_time = update_interval - (time.time() - current_time)
            if sleep_time > 0:
                time.sleep(sleep_time)
                
        # 计算总体统计
        total_time = time.time() - start_simulation
        avg_ops_per_sec = operation_count / total_time
        overall_avg_response = sum(avg_response_times) / len(avg_response_times) if avg_response_times else 0
        
        print(f"模拟总时长: {total_time:.2f}秒")
        print(f"总操作次数: {operation_count}")
        print(f"平均吞吐量: {avg_ops_per_sec:.1f}操作/秒")
        print(f"平均响应时间: {overall_avg_response*1000:.3f}毫秒")
        print(f"数据管理器状态: {self.manager.get_data_manager_stats()}")
        
        try:
            # 解决中文字体问题
            plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']  # 尝试使用这些字体支持中文
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            
            # 绘制模拟结果图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
            
            # 吞吐量图表
            ax1.plot(timestamps, operations_per_sec, 'b-')
            ax1.set_title('系统吞吐量')
            ax1.set_ylabel('操作/秒')
            ax1.grid(True)
            
            # 响应时间图表
            ax2.plot(timestamps, [t*1000 for t in avg_response_times], 'r-')  # 转换为毫秒
            ax2.set_title('平均响应时间')
            ax2.set_ylabel('响应时间(毫秒)')
            ax2.set_xlabel('时间(秒)')
            ax2.grid(True)
            
            plt.tight_layout()
            plt.savefig('market_data_manager_performance.png')
            print("性能测试结果图表已保存为 'market_data_manager_performance.png'")
        except Exception as e:
            print(f"无法生成图表: {e}")
            
    def _visualize_performance_results(self):
        """将性能测试结果可视化"""
        try:
            # 解决中文字体问题
            plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']  # 尝试使用这些字体支持中文
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            
            # 创建性能比较图表
            fig, axs = plt.subplots(2, 2, figsize=(12, 10))
            
            # 1. 单个操作与批量操作对比
            if self.performance_results['single_set'] and self.performance_results['batch_set']:
                labels = ['单独设置', '单独获取', '批量设置', '批量获取']
                times = [
                    np.mean(self.performance_results['single_set']),
                    np.mean(self.performance_results['single_get']),
                    np.mean(self.performance_results['batch_set']),
                    np.mean(self.performance_results['batch_get'])
                ]
                axs[0, 0].bar(labels, times)
                axs[0, 0].set_title('单个操作与批量操作耗时对比')
                axs[0, 0].set_ylabel('时间(秒)')
                axs[0, 0].grid(axis='y')
            
            # 2. 并发访问性能
            if self.performance_results['concurrent_reads'] and self.performance_results['concurrent_writes']:
                labels = ['并发读取', '并发写入']
                times = [
                    np.mean(self.performance_results['concurrent_reads']) * 1000,  # 转为毫秒
                    np.mean(self.performance_results['concurrent_writes']) * 1000  # 转为毫秒
                ]
                if self.performance_results['concurrent_mixed']:
                    labels.extend(['混合读取', '混合写入'])
                    mixed_read, mixed_write = self.performance_results['concurrent_mixed'][0]
                    times.extend([mixed_read * 1000, mixed_write * 1000])  # 转为毫秒
                    
                axs[0, 1].bar(labels, times)
                axs[0, 1].set_title('并发访问性能')
                axs[0, 1].set_ylabel('平均响应时间(毫秒)')
                axs[0, 1].grid(axis='y')
            
            # 3. 大数据量性能
            if self.performance_results['market_data_set'] and self.performance_results['market_data_get']:
                labels = ['设置大量数据', '获取大量数据']
                times = [
                    np.mean(self.performance_results['market_data_set']),
                    np.mean(self.performance_results['market_data_get'])
                ]
                axs[1, 0].bar(labels, times)
                axs[1, 0].set_title('大数据量操作性能')
                axs[1, 0].set_ylabel('时间(秒)')
                axs[1, 0].grid(axis='y')
                
            # 4. 吞吐量比较
            throughputs = []
            throughput_labels = []
            
            if self.performance_results['single_set']:
                throughputs.append(len(self.test_fields) * 1000 / np.mean(self.performance_results['single_set']))
                throughput_labels.append('单独设置')
                
            if self.performance_results['batch_set']:
                throughputs.append(1000 / np.mean(self.performance_results['batch_set']))
                throughput_labels.append('批量设置')
                
            if self.performance_results['concurrent_reads']:
                throughputs.append(1 / np.mean(self.performance_results['concurrent_reads']))
                throughput_labels.append('并发读取')
                
            if self.performance_results['concurrent_writes']:
                throughputs.append(1 / np.mean(self.performance_results['concurrent_writes']))
                throughput_labels.append('并发写入')
                
            axs[1, 1].bar(throughput_labels, throughputs)
            axs[1, 1].set_title('操作吞吐量比较')
            axs[1, 1].set_ylabel('操作/秒')
            axs[1, 1].grid(axis='y')
            
            plt.tight_layout()
            plt.savefig('market_data_manager_performance_comparison.png')
            print("性能对比图表已保存为 'market_data_manager_performance_comparison.png'")
            
        except Exception as e:
            print(f"无法生成性能比较图表: {e}")

    def test_performance_summary(self):
        """总结所有性能测试结果"""
        # 运行各项测试
        try:
            self.test_single_vs_batch_operations()
            self.test_large_data_performance()
            self.test_concurrent_access()
            self.test_high_frequency_updates()
            self.test_realistic_trading_day_simulation()
            
            # 可视化性能测试结果
            self._visualize_performance_results()
        except AssertionError as e:
            # 捕获测试中的断言错误但继续执行总结
            print(f"注意：测试断言失败但继续执行: {e}")
        
        print("\n=== 性能测试总结 ===")
        stats = self.manager.get_data_manager_stats()
        print(f"数据管理器最终状态: {stats}")
        
        # 给出综合建议
        print("\n建议:")
        print("1. 当需要获取或设置多个字段时，应使用批量操作方法")
        print("2. 高并发场景下应考虑减少不必要的锁竞争")
        print("3. 对于大量数据的处理，应当分批进行，避免单次操作过重")
        print("4. 考虑使用更细粒度的锁策略，进一步优化并发性能")
        print("5. 在高频更新场景下，可以考虑使用更高效的数据结构或缓存策略")


if __name__ == '__main__':
    unittest.main() 