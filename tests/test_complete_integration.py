#!/usr/bin/env python3
"""
完整的IB API + Flask应用集成测试

测试修复后的ThreadPoolExecutor在完整系统中的工作状态。
"""

import sys
import os
import time
import threading
import requests

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_flask_app():
    """检查Flask应用是否运行"""
    try:
        response = requests.get('http://localhost:5001', timeout=2)
        return True
    except:
        return False

def test_stock_list_push():
    """测试股票列表推送功能"""
    print("=== 测试股票列表推送功能 ===")
    
    from src.api.external_APIs import push_stock_list_to_frontend
    
    test_stock_list = ['AAPL', 'GOOGL', 'TSLA', 'MSFT']
    
    print(f"推送股票列表: {test_stock_list}")
    push_stock_list_to_frontend(test_stock_list)
    
    # 等待异步任务完成
    time.sleep(2)
    
    print("✅ 股票列表推送完成")

def test_realtime_bar_push():
    """测试实时bar数据推送功能"""
    print("\n=== 测试实时bar数据推送功能 ===")
    
    from src.api.external_APIs import push_realtime_bar_to_frontend, update_active_stock_list
    
    # 创建模拟的BarData
    class MockBarData:
        def __init__(self, stock_code, price_offset=0):
            self.date = "20250622 09:30:00 US/Eastern"
            self.open = 150.0 + price_offset
            self.high = 152.0 + price_offset
            self.low = 149.0 + price_offset
            self.close = 151.0 + price_offset
            self.volume = 10000 * (price_offset + 1)
    
    # 设置活跃股票列表
    stock_list = ['AAPL', 'GOOGL', 'TSLA']
    update_active_stock_list(stock_list)
    
    print(f"推送实时bar数据给股票: {stock_list}")
    
    # 推送多个bar数据
    for i, stock_code in enumerate(stock_list):
        mock_bar = MockBarData(stock_code, i * 10)
        push_realtime_bar_to_frontend(stock_code, mock_bar)
        print(f"  已推送 {stock_code} 的bar数据")
    
    # 等待异步任务完成
    time.sleep(3)
    
    print("✅ 实时bar数据推送完成")

def test_historical_bar_push():
    """测试历史bar数据推送功能"""
    print("\n=== 测试历史bar数据推送功能 ===")
    
    from src.api.external_APIs import push_historical_bar_to_frontend
    
    # 创建模拟的BarData
    class MockBarData:
        def __init__(self, time_offset=0):
            self.date = f"20250622 09:{30+time_offset}:00 US/Eastern"
            self.open = 150.0 + time_offset
            self.high = 152.0 + time_offset
            self.low = 149.0 + time_offset
            self.close = 151.0 + time_offset
            self.volume = 10000
    
    stock_code = 'AAPL'
    print(f"推送历史bar数据给股票: {stock_code}")
    
    # 推送多个历史bar数据
    for i in range(5):
        mock_bar = MockBarData(i)
        push_historical_bar_to_frontend(stock_code, mock_bar)
        print(f"  已推送历史bar {i+1}")
    
    # 等待异步任务完成
    time.sleep(3)
    
    print("✅ 历史bar数据推送完成")

def test_ib_api_status_push():
    """测试IB API状态推送功能"""
    print("\n=== 测试IB API状态推送功能 ===")
    
    from src.api.external_APIs import push_ib_api_status_to_frontend
    
    # 推送连接状态
    print("推送IB API连接状态...")
    push_ib_api_status_to_frontend(
        is_connected=True,
        server_version="TWS 10.19",
        connection_time="2025-06-22 09:30:00",
        active_stocks_count=5
    )
    
    # 等待异步任务完成
    time.sleep(2)
    
    print("✅ IB API状态推送完成")

def test_historical_data_end_push():
    """测试历史数据完成通知推送功能"""
    print("\n=== 测试历史数据完成通知推送功能 ===")
    
    from src.api.external_APIs import push_historical_data_end_to_frontend, update_active_stock_list
    
    # 设置活跃股票
    update_active_stock_list(['AAPL'])
    
    print("推送历史数据完成通知...")
    push_historical_data_end_to_frontend(
        stock_code='AAPL',
        start="20250622 09:30:00",
        end="20250622 16:00:00"
    )
    
    # 等待同步任务完成
    time.sleep(2)
    
    print("✅ 历史数据完成通知推送完成")

def test_concurrent_operations():
    """测试并发操作"""
    print("\n=== 测试并发操作 ===")
    
    from src.api.external_APIs import (
        push_stock_list_to_frontend, 
        push_realtime_bar_to_frontend,
        push_ib_api_status_to_frontend,
        update_active_stock_list
    )
    
    # 创建模拟数据
    class MockBarData:
        def __init__(self, stock_code):
            self.date = "20250622 09:30:00 US/Eastern"
            self.open = 150.0
            self.high = 152.0
            self.low = 149.0
            self.close = 151.0
            self.volume = 10000
    
    stock_list = ['AAPL', 'GOOGL', 'TSLA', 'MSFT', 'AMZN']
    update_active_stock_list(stock_list)
    
    print("执行并发操作...")
    
    # 同时执行多种操作
    push_stock_list_to_frontend(stock_list)
    
    for stock_code in stock_list:
        mock_bar = MockBarData(stock_code)
        push_realtime_bar_to_frontend(stock_code, mock_bar)
    
    push_ib_api_status_to_frontend(
        is_connected=True,
        server_version="TWS 10.19",
        active_stocks_count=len(stock_list)
    )
    
    print(f"已提交 {len(stock_list) + 2} 个并发任务")
    
    # 等待所有任务完成
    time.sleep(5)
    
    print("✅ 并发操作完成")

def check_thread_pool_status():
    """检查线程池状态"""
    print("\n=== 检查线程池状态 ===")
    
    from src.api.external_APIs import _network_thread_pool
    
    print(f"线程池状态:")
    print(f"  最大工作线程数: {_network_thread_pool._max_workers}")
    print(f"  是否关闭: {_network_thread_pool._shutdown}")
    print(f"  当前线程数: {len(_network_thread_pool._threads)}")
    print(f"  工作队列大小: {_network_thread_pool._work_queue.qsize()}")
    
    print(f"\n当前活跃线程数: {threading.active_count()}")
    print("当前线程列表:")
    for thread in threading.enumerate():
        print(f"  - {thread.name} (守护线程: {thread.daemon})")

if __name__ == '__main__':
    print("开始完整的IB API + Flask应用集成测试...")
    
    # 检查Flask应用状态
    if not check_flask_app():
        print("❌ Flask应用未运行，请先启动Flask应用")
        sys.exit(1)
    
    print("✅ Flask应用正在运行")
    
    # 记录初始状态
    initial_threads = threading.active_count()
    print(f"初始活跃线程数: {initial_threads}")
    
    # 运行集成测试
    test_stock_list_push()
    test_realtime_bar_push()
    test_historical_bar_push()
    test_ib_api_status_push()
    test_historical_data_end_push()
    test_concurrent_operations()
    
    # 检查最终状态
    check_thread_pool_status()
    
    print("\n🎉 完整集成测试完成！")
    print("✅ 所有功能正常工作")
    print("✅ ThreadPoolExecutor正常运行")
    print("✅ 网络请求成功发送")
    print("✅ Flask应用成功接收数据")
