#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成测试：模拟真实环境中的股票排名变化，测试Tick-by-tick数据请求的完整流程
"""

import unittest
import logging
import sys
import os
import time
from unittest.mock import MagicMock, patch
from datetime import datetime
import threading
import pytz
import pandas as pd
import queue

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 模拟导入依赖模块 - 只模拟外部API相关的模块
sys.modules['ibapi'] = MagicMock()
sys.modules['ibapi.client'] = MagicMock()
sys.modules['ibapi.wrapper'] = MagicMock()
sys.modules['ibapi.scanner'] = MagicMock()
sys.modules['ibapi.contract'] = MagicMock()
sys.modules['ibapi.utils'] = MagicMock()
sys.modules['ibapi.common'] = MagicMock()
sys.modules['futu'] = MagicMock()

# 定义ibapi中的类型
class TickerId(int): pass
class ListOfHistoricalTickLast(list): pass
class TickAttribLast: 
    def __init__(self):
        self.pastLimit = False
        self.unreported = False
class TickAttribBidAsk:
    def __init__(self):
        self.bidPastLow = False
        self.askPastHigh = False
class Decimal(float): pass

# 创建EWrapper和EClient类，避免元类冲突
class EWrapper:
    def __init__(self):
        pass

class EClient:
    def __init__(self, wrapper):
        self.wrapper = wrapper

# 将类型添加到ibapi模块中
sys.modules['ibapi.common'].TickerId = TickerId
sys.modules['ibapi.common'].ListOfHistoricalTickLast = ListOfHistoricalTickLast
sys.modules['ibapi.common'].TickAttribLast = TickAttribLast
sys.modules['ibapi.common'].TickAttribBidAsk = TickAttribBidAsk
sys.modules['ibapi.common'].Decimal = Decimal
sys.modules['ibapi.wrapper'].EWrapper = EWrapper
sys.modules['ibapi.client'].EClient = EClient

# 导入真实的配置
from src.config.config import IB_CONFIG
from src.utils.key_generator import key_generator

# 导入真实的IBApp类，但模拟其外部依赖
with patch('src.api.get_stock_price_ibapi.EWrapper', EWrapper), \
     patch('src.api.get_stock_price_ibapi.EClient', EClient):
    from src.api.get_stock_price_ibapi import IBApp

class MockContract:
    """模拟IB合约"""
    def __init__(self, symbol):
        self.symbol = symbol
        self.secType = "STK"
        self.exchange = "SMART"
        self.primaryExchange = "NASDAQ"
        self.currency = "USD"
        self.localSymbol = symbol

class MockContractDetails:
    """模拟IB合约详情"""
    def __init__(self, symbol):
        self.contract = MockContract(symbol)

class TestTickByTickIntegration(unittest.TestCase):
    """集成测试：模拟真实环境中的股票排名变化"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建一个事件队列，用于记录方法调用
        self.events = queue.Queue()
        
        # 创建IBApp实例
        self.ib_app = IBApp()
        
        # 模拟必要的属性
        self.ib_app.start = True
        self.ib_app.is_shutting_down = False
        self.ib_app.lock = threading.Lock()
        self.ib_app.tick_request_lock = threading.Lock()
        self.ib_app.error_lock = threading.Lock()
        self.ib_app.request_id_lock = threading.Lock()
        self.ib_app.news_stock_dict_lock = threading.Lock()
        self.ib_app.sound_lock = threading.Lock()
        self.ib_app.active_request_ids = set()
        self.ib_app.last_request_id = 10000
        self.ib_app.active_tick_requests = {}
        self.ib_app.reqId_dict = {}
        self.ib_app.reqId_dict_day = {}
        self.ib_app.reqId_dict_wrapper = {}
        self.ib_app.contractDetails_dict = {}
        self.ib_app.new_contractDetails_dict = {}
        self.ib_app.data = {}
        self.ib_app.watch_dict = {}
        self.ib_app.top5_tick_stocks = set()
        self.ib_app.pre_symbol_list_pre = [None] * 15
        self.ib_app.symbol_set = set()
        self.ib_app.pre_symbol_list_backup = set()
        self.ib_app.pre_historicalData = {}
        self.ib_app.filtered_list_backup = []
        self.ib_app.stock_count = 10000
        self.ib_app.stock_count_day = 1000000
        self.ib_app.min_low_amount = 170000
        self.ib_app.play_flg_dict = {}
        self.ib_app.news_stock_dict = {}
        self.ib_app.EXPIRE_SECONDS = 59 * 5
        self.ib_app.curr_date = datetime.now(tz=pytz.timezone('America/New_York')).strftime('%Y-%m-%d')
        
        # 模拟方法
        self.mock_methods()
        
        # 创建测试数据
        self.create_test_data()
        
    def mock_methods(self):
        """模拟IBApp的方法"""
        # 模拟基础方法
        self.ib_app._get_next_request_id = MagicMock(return_value=10001)
        self.ib_app._release_request_id = MagicMock()
        
        # 模拟IB API方法
        self.ib_app.reqTickByTickData = MagicMock(side_effect=lambda reqid, contract, tick_type, num_rows, ignore_size: 
                                                 self.events.put(f"reqTickByTickData_{contract.symbol}"))
        self.ib_app.cancelTickByTickData = MagicMock(side_effect=lambda reqid: 
                                                    self.events.put(f"cancelTickByTickData_{reqid}"))
        self.ib_app.reqHistoricalData = MagicMock()
        self.ib_app.reqMktData = MagicMock()
        
        # 保留原始方法的引用，以便在模拟方法中调用
        self.original_is_receiving_tick_data = self.ib_app.is_receiving_tick_data
        self.original_cancel_tick_by_tick_data = self.ib_app.cancel_tick_by_tick_data
        self.original_get_tick_by_tick_data = self.ib_app.get_tick_by_tick_data
        self.original_update_top5_tick_stocks = self.ib_app.update_top5_tick_stocks
        
        # 模拟方法，记录调用
        self.ib_app.is_receiving_tick_data = MagicMock(return_value=False)
        self.ib_app.cancel_tick_by_tick_data = MagicMock(side_effect=lambda stock_code: 
                                                        self.events.put(f"cancel_tick_by_tick_data_{stock_code}"))
        self.ib_app.get_tick_by_tick_data = MagicMock(side_effect=lambda reqid: 
                                                     self.events.put(f"get_tick_by_tick_data_{self.ib_app.reqId_dict_wrapper.get(reqid, reqid)}"))
        
        # 恢复update_top5_tick_stocks方法的原始实现
        self.ib_app.update_top5_tick_stocks = self.original_update_top5_tick_stocks
        
    def create_test_data(self):
        """创建测试数据"""
        # 创建10个股票的合约
        for i in range(1, 11):
            stock_code = f"STOCK{i}"
            reqid = 10000 + i
            contract = MockContract(stock_code)
            
            self.ib_app.reqId_dict[stock_code] = reqid
            self.ib_app.reqId_dict_day[stock_code] = reqid + 1000
            self.ib_app.reqId_dict_wrapper[reqid] = stock_code
            self.ib_app.reqId_dict_wrapper[reqid + 1000] = stock_code
            self.ib_app.contractDetails_dict[reqid] = contract
            
        # 设置初始排名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK5",
            5: "STOCK6",
            6: "STOCK7",
            7: "STOCK8",
            8: "STOCK9",
            9: "STOCK10"
        }
        
    def test_rank_change_flow(self):
        """测试排名变化时的完整流程"""
        # 初始化前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证前5名股票集合
        self.assertEqual(len(self.ib_app.top5_tick_stocks), 5)
        self.assertSetEqual(self.ib_app.top5_tick_stocks, {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5"})
        
        # 验证是否为前5名股票请求了Tick-by-tick数据
        events = []
        while not self.events.empty():
            events.append(self.events.get())
        
        # 应该有5个get_tick_by_tick_data调用，对应前5名股票
        self.assertEqual(len(events), 5)
        for i in range(1, 6):
            self.assertIn(f"get_tick_by_tick_data_STOCK{i}", events)
        
        # 清空事件队列
        while not self.events.empty():
            self.events.get()
        
        # 模拟STOCK6上升到排名第3
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK6",  # STOCK6上升到第3
            3: "STOCK3",
            4: "STOCK4",
            5: "STOCK5",  # STOCK5下降到第6
            6: "STOCK7",
            7: "STOCK8",
            8: "STOCK9",
            9: "STOCK10"
        }
        
        # 更新前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证前5名股票集合是否正确更新
        self.assertEqual(len(self.ib_app.top5_tick_stocks), 5)
        self.assertSetEqual(self.ib_app.top5_tick_stocks, {"STOCK1", "STOCK2", "STOCK6", "STOCK3", "STOCK4"})
        
        # 验证是否取消了STOCK5的Tick-by-tick数据请求，并为STOCK6请求了数据
        events = []
        while not self.events.empty():
            events.append(self.events.get())
        
        self.assertIn("cancel_tick_by_tick_data_STOCK5", events)
        self.assertIn("get_tick_by_tick_data_STOCK6", events)
        
        # 清空事件队列
        while not self.events.empty():
            self.events.get()
        
        # 模拟scannerData调用，STOCK9进入前5名
        contract_details = MockContractDetails("STOCK9")
        self.ib_app.scannerData(reqId=1, rank=2, contractDetails=contract_details, 
                               distance="", benchmark="", projection="", legsStr="")
        
        # 验证watch_dict是否正确更新
        self.assertEqual(self.ib_app.watch_dict[2], "STOCK9")
        
        # 验证前5名股票集合是否正确更新
        self.assertEqual(len(self.ib_app.top5_tick_stocks), 5)
        self.assertSetEqual(self.ib_app.top5_tick_stocks, {"STOCK1", "STOCK2", "STOCK9", "STOCK3", "STOCK4"})
        
        # 验证是否取消了STOCK6的Tick-by-tick数据请求，并为STOCK9请求了数据
        events = []
        while not self.events.empty():
            events.append(self.events.get())
        
        self.assertIn("cancel_tick_by_tick_data_STOCK6", events)
        self.assertIn("get_tick_by_tick_data_STOCK9", events)
        
    def test_is_receiving_tick_data_integration(self):
        """测试is_receiving_tick_data方法与其他方法的集成"""
        # 恢复is_receiving_tick_data方法的原始实现
        self.ib_app.is_receiving_tick_data = self.original_is_receiving_tick_data
        
        # 准备测试数据
        stock_code = "STOCK1"
        reqid = self.ib_app.reqId_dict[stock_code]
        
        # 模拟股票不在接收数据
        self.ib_app.active_tick_requests = {}
        
        # 初始化前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证是否为STOCK1请求了Tick-by-tick数据
        events = []
        while not self.events.empty():
            events.append(self.events.get())
        
        self.assertIn(f"get_tick_by_tick_data_{stock_code}", events)
        
        # 清空事件队列
        while not self.events.empty():
            self.events.get()
        
        # 模拟股票在接收数据
        contract = self.ib_app.contractDetails_dict[reqid]
        contract_key = f"{contract.symbol}_{contract.secType}_{contract.exchange}"
        self.ib_app.active_tick_requests[contract_key] = reqid
        
        # 再次更新前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证是否没有为STOCK1请求Tick-by-tick数据
        events = []
        while not self.events.empty():
            events.append(self.events.get())
        
        for event in events:
            self.assertNotEqual(event, f"get_tick_by_tick_data_{stock_code}")
            
    def test_cancel_tick_by_tick_data_integration(self):
        """测试cancel_tick_by_tick_data方法与其他方法的集成"""
        # 恢复cancel_tick_by_tick_data方法的原始实现
        self.ib_app.cancel_tick_by_tick_data = self.original_cancel_tick_by_tick_data
        
        # 准备测试数据
        stock_code = "STOCK5"
        reqid = self.ib_app.reqId_dict[stock_code]
        contract = self.ib_app.contractDetails_dict[reqid]
        contract_key = f"{contract.symbol}_{contract.secType}_{contract.exchange}"
        
        # 模拟股票在接收数据
        self.ib_app.active_tick_requests[contract_key] = reqid
        
        # 初始化前5名股票集合
        self.ib_app.top5_tick_stocks = {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5"}
        
        # 修改排名，让STOCK5离开前5名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK6",
            5: "STOCK5",
            6: "STOCK7",
            7: "STOCK8",
            8: "STOCK9",
            9: "STOCK10"
        }
        
        # 更新前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证是否取消了STOCK5的Tick-by-tick数据请求
        events = []
        while not self.events.empty():
            events.append(self.events.get())
        
        self.assertIn(f"cancelTickByTickData_{reqid}", events)
        
        # 验证active_tick_requests是否正确更新
        self.assertNotIn(contract_key, self.ib_app.active_tick_requests)

if __name__ == "__main__":
    unittest.main() 