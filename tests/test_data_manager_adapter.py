"""
数据管理器适配器测试

测试适配器是否能正确模拟原有全局变量的行为
"""

import unittest
import logging
from src.data.data_manager_adapter import (
    StockDataDictAdapter,
    TransactionDataDictAdapter,
    SharedDataDictAdapter,
    manager_res_dict_test_adapter,
    number_of_transactions_dict_adapter,
    manager_res_dict_adapter
)
from src.data.thread_safe_stock_data_manager import reset_stock_data_manager

# 配置日志
logging.basicConfig(level=logging.INFO)


class TestDataManagerAdapter(unittest.TestCase):
    """数据管理器适配器测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        reset_stock_data_manager()
    
    def tearDown(self):
        """测试后的清理工作"""
        reset_stock_data_manager()
    
    def test_stock_data_adapter_basic_operations(self):
        """测试股票数据适配器基本操作"""
        adapter = StockDataDictAdapter()
        
        # 测试设置和获取
        adapter['AAPL_price'] = 150.0
        adapter['AAPL_volume'] = 1000000
        
        self.assertEqual(adapter['AAPL_price'], 150.0)
        self.assertEqual(adapter['AAPL_volume'], 1000000)
        
        # 测试get方法
        self.assertEqual(adapter.get('AAPL_price'), 150.0)
        self.assertEqual(adapter.get('MISSING_KEY', 'default'), 'default')
        
        # 测试包含检查
        self.assertTrue('AAPL_price' in adapter)
        self.assertFalse('MISSING_KEY' in adapter)
    
    def test_stock_data_adapter_update_operations(self):
        """测试股票数据适配器更新操作"""
        adapter = StockDataDictAdapter()
        
        # 测试update方法
        updates = {
            'GOOGL_price': 2800.0,
            'GOOGL_volume': 800000,
            'MSFT_price': 300.0
        }
        adapter.update(updates)
        
        for key, expected_value in updates.items():
            self.assertEqual(adapter[key], expected_value)
        
        # 测试setdefault
        result = adapter.setdefault('NEW_KEY', 'new_value')
        self.assertEqual(result, 'new_value')
        self.assertEqual(adapter['NEW_KEY'], 'new_value')
        
        # 已存在的键不应该被覆盖
        result = adapter.setdefault('GOOGL_price', 9999.0)
        self.assertEqual(result, 2800.0)  # 应该返回原值
    
    def test_transaction_data_adapter_basic_operations(self):
        """测试交易数据适配器基本操作"""
        adapter = TransactionDataDictAdapter()
        
        # 测试设置和获取
        adapter['AAPL'] = 500
        adapter['GOOGL'] = 300
        
        self.assertEqual(adapter['AAPL'], 500)
        self.assertEqual(adapter['GOOGL'], 300)
        
        # 测试get方法
        self.assertEqual(adapter.get('AAPL'), 500)
        self.assertEqual(adapter.get('MISSING'), 0)  # 默认值应该是0
        
        # 测试包含检查
        self.assertTrue('AAPL' in adapter)
        self.assertFalse('MISSING' in adapter)
    
    def test_transaction_data_adapter_validation(self):
        """测试交易数据适配器数据验证"""
        adapter = TransactionDataDictAdapter()
        
        # 有效数据
        adapter['VALID'] = 100
        self.assertEqual(adapter['VALID'], 100)
        
        # 无效数据应该被拒绝（负数）
        adapter['INVALID'] = -10
        self.assertEqual(adapter.get('INVALID'), 0)  # 应该没有被设置
    
    def test_shared_data_adapter_basic_operations(self):
        """测试共享数据适配器基本操作"""
        adapter = SharedDataDictAdapter()
        
        # 测试不同类型的键
        adapter[1] = ['AAPL', 'GOOGL']
        adapter['status'] = 'running'
        adapter['config'] = {'debug': True}
        
        self.assertEqual(adapter[1], ['AAPL', 'GOOGL'])
        self.assertEqual(adapter['status'], 'running')
        self.assertEqual(adapter['config'], {'debug': True})
        
        # 测试get方法
        self.assertEqual(adapter.get(1), ['AAPL', 'GOOGL'])
        self.assertEqual(adapter.get('missing', 'default'), 'default')
    
    def test_global_adapter_instances(self):
        """测试全局适配器实例"""
        # 测试全局实例是否正常工作
        manager_res_dict_test_adapter['test_key'] = 'test_value'
        self.assertEqual(manager_res_dict_test_adapter['test_key'], 'test_value')
        
        number_of_transactions_dict_adapter['TEST_STOCK'] = 200
        self.assertEqual(number_of_transactions_dict_adapter['TEST_STOCK'], 200)
        
        manager_res_dict_adapter['shared_key'] = 'shared_value'
        self.assertEqual(manager_res_dict_adapter['shared_key'], 'shared_value')
    
    def test_adapter_clear_operations(self):
        """测试适配器清空操作"""
        # 设置一些数据
        manager_res_dict_test_adapter['key1'] = 'value1'
        manager_res_dict_test_adapter['key2'] = 'value2'
        
        number_of_transactions_dict_adapter['STOCK1'] = 100
        number_of_transactions_dict_adapter['STOCK2'] = 200
        
        manager_res_dict_adapter['shared1'] = 'data1'
        manager_res_dict_adapter['shared2'] = 'data2'
        
        # 清空股票数据
        manager_res_dict_test_adapter.clear()
        self.assertIsNone(manager_res_dict_test_adapter.get('key1'))
        self.assertIsNone(manager_res_dict_test_adapter.get('key2'))
        
        # 其他数据应该还在
        self.assertEqual(number_of_transactions_dict_adapter['STOCK1'], 100)
        self.assertEqual(manager_res_dict_adapter['shared1'], 'data1')
        
        # 清空交易数据
        number_of_transactions_dict_adapter.clear()
        self.assertEqual(number_of_transactions_dict_adapter.get('STOCK1'), 0)
        
        # 清空共享数据
        manager_res_dict_adapter.clear()
        self.assertIsNone(manager_res_dict_adapter.get('shared1'))
    
    def test_adapter_pop_operations(self):
        """测试适配器弹出操作"""
        # 股票数据适配器
        manager_res_dict_test_adapter['pop_key'] = 'pop_value'
        popped = manager_res_dict_test_adapter.pop('pop_key', 'default')
        self.assertEqual(popped, 'pop_value')
        self.assertIsNone(manager_res_dict_test_adapter.get('pop_key'))
        
        # 弹出不存在的键
        popped = manager_res_dict_test_adapter.pop('missing_key', 'default')
        self.assertEqual(popped, 'default')
        
        # 交易数据适配器
        number_of_transactions_dict_adapter['POP_STOCK'] = 150
        popped = number_of_transactions_dict_adapter.pop('POP_STOCK', 0)
        self.assertEqual(popped, 150)
        self.assertEqual(number_of_transactions_dict_adapter.get('POP_STOCK'), 0)
    
    def test_adapter_compatibility_with_original_usage(self):
        """测试适配器与原有使用方式的兼容性"""
        # 模拟原有代码的使用方式
        
        # 1. 直接赋值和读取
        manager_res_dict_test_adapter['AAPL_NOW_PRICE'] = 155.50
        price = manager_res_dict_test_adapter.get('AAPL_NOW_PRICE', 0)
        self.assertEqual(price, 155.50)
        
        # 2. 交易次数字典的使用
        number_of_transactions_dict_adapter['AAPL'] = 500
        rank = number_of_transactions_dict_adapter.get('AAPL', 0)
        self.assertEqual(rank, 500)
        
        # 3. 共享数据字典的使用
        manager_res_dict_adapter[1] = ['AAPL', 'GOOGL', 'MSFT']
        stock_list = manager_res_dict_adapter.get(1, [])
        self.assertEqual(stock_list, ['AAPL', 'GOOGL', 'MSFT'])
        
        # 4. 批量更新
        updates = {
            'GOOGL_NOW_PRICE': 2850.0,
            'MSFT_NOW_PRICE': 310.0
        }
        manager_res_dict_test_adapter.update(updates)
        
        self.assertEqual(manager_res_dict_test_adapter['GOOGL_NOW_PRICE'], 2850.0)
        self.assertEqual(manager_res_dict_test_adapter['MSFT_NOW_PRICE'], 310.0)
    
    def test_adapter_thread_safety(self):
        """测试适配器的线程安全性"""
        import threading
        from concurrent.futures import ThreadPoolExecutor
        
        def worker_thread(thread_id):
            """工作线程函数"""
            for i in range(50):
                # 股票数据
                key = f'thread_{thread_id}_key_{i}'
                value = f'thread_{thread_id}_value_{i}'
                manager_res_dict_test_adapter[key] = value
                retrieved = manager_res_dict_test_adapter[key]
                self.assertEqual(retrieved, value)
                
                # 交易数据
                stock = f'STOCK_{thread_id}_{i}'
                count = thread_id * 100 + i
                number_of_transactions_dict_adapter[stock] = count
                retrieved_count = number_of_transactions_dict_adapter[stock]
                self.assertEqual(retrieved_count, count)
        
        # 启动多个线程
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            for thread_id in range(5):
                future = executor.submit(worker_thread, thread_id)
                futures.append(future)
            
            # 等待所有线程完成
            for future in futures:
                future.result()


if __name__ == '__main__':
    unittest.main()
