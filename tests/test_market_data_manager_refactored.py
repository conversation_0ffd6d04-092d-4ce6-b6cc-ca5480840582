"""
重构后的MarketDataManager测试

测试重构后的MarketDataManager是否正常工作
"""

import unittest
import logging
from src.data.market_data_manager import MarketDataManager
from src.data.thread_safe_stock_data_manager import reset_stock_data_manager, get_stock_data_manager
from src.utils.key_generator import key_generator
from src.utils.constants import *

# 配置日志
logging.basicConfig(level=logging.INFO)


class TestMarketDataManagerRefactored(unittest.TestCase):
    """重构后的MarketDataManager测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        reset_stock_data_manager()
        self.manager = MarketDataManager()
        self.data_manager = get_stock_data_manager()
        self.test_stock = 'AAPL'
        self.keys = key_generator.get_keys_for_stock(self.test_stock)
    
    def tearDown(self):
        """测试后的清理工作"""
        reset_stock_data_manager()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.manager._data_manager)
        self.assertEqual(self.manager.all_conditions, {})
        self.assertEqual(self.manager.high_all_conditions, {})
    
    def test_basic_data_operations(self):
        """测试基本数据操作"""
        # 设置测试数据
        self.data_manager.set_stock_data(self.keys[NOW_PRICE], 155.50)
        self.data_manager.set_stock_data(self.keys[VOLUME], 1000000)
        self.data_manager.set_stock_data(self.keys[VOLUME_AVG], 800000)
        self.data_manager.set_transaction_data(self.test_stock, 500)
        
        # 测试获取数据
        price = self.manager.get_current_price(self.test_stock)
        volume = self.manager.get_volume(self.test_stock)
        volume_avg = self.manager.get_volume_avg(self.test_stock)
        rank = self.manager.get_transaction_rank(self.test_stock)
        
        self.assertEqual(price, 155.50)
        self.assertEqual(volume, 1000000.0)
        self.assertEqual(volume_avg, 800000.0)
        self.assertEqual(rank, 500)
    
    def test_flag_operations(self):
        """测试标志操作"""
        # 测试买入标志
        self.assertFalse(self.manager.get_buy_flag(self.test_stock))
        self.data_manager.set_stock_data(self.keys[BUY_FLG], True)
        self.assertTrue(self.manager.get_buy_flag(self.test_stock))
        
        # 测试设置标志
        self.assertFalse(self.manager.get_set_flag(self.test_stock))
        self.data_manager.set_stock_data(self.keys[SET_FLG], True)
        self.assertTrue(self.manager.get_set_flag(self.test_stock))
        
        # 测试价差状态
        self.assertFalse(self.manager.get_spread_status(self.test_stock))
        self.data_manager.set_stock_data(self.keys[IS_SPREAD_NORMAL], True)
        self.assertTrue(self.manager.get_spread_status(self.test_stock))
    
    def test_breakout_flags(self):
        """测试突破标志"""
        # 设置突破标志
        self.data_manager.set_stock_data(self.keys[BREAKOUT_FLG], True)
        self.data_manager.set_stock_data(self.keys[BREAKOUT_RED_FLG], False)
        
        flags = self.manager.get_breakout_flags(self.test_stock)
        self.assertEqual(flags['normal'], True)
        self.assertEqual(flags['red'], False)
    
    def test_score_operations(self):
        """测试评分操作"""
        # 设置评分数据
        self.data_manager.set_stock_data(self.keys[CONFIDENCE], 0.85)
        self.data_manager.set_stock_data(self.keys[BUYING_PRESSURE], 0.75)
        self.data_manager.set_stock_data(self.keys[BEHAVIOR_SCORE], 0.90)
        self.data_manager.set_stock_data(self.keys[BEHAVIOR_SCORE_15MIN], 0.88)
        
        # 测试获取评分
        confidence = self.manager.get_confidence(self.test_stock)
        buying_pressure = self.manager.get_buying_pressure(self.test_stock)
        behavior_score = self.manager.get_behavior_score(self.test_stock)
        behavior_score_15min = self.manager.get_behavior_score_15min(self.test_stock)
        
        self.assertEqual(confidence, 0.85)
        self.assertEqual(buying_pressure, 0.75)
        self.assertEqual(behavior_score, 0.90)
        self.assertEqual(behavior_score_15min, 0.88)
    
    def test_get_market_data(self):
        """测试获取市场数据"""
        # 设置完整的测试数据
        test_data = {
            self.keys[NOW_PRICE]: 155.50,
            self.keys[VOLUME]: 1000000,
            self.keys[CHECK_VOLUME]: 900000,
            self.keys[VOLUME_AVG]: 800000,
            self.keys[STOCK_SHARESOUTSTANDING]: 5000000,
            self.keys[NEWS]: ['新闻1', '新闻2'],
            self.keys[IS_SPREAD_NORMAL]: True,
            self.keys[BUY_FLG]: False,
            self.keys[SET_FLG]: True,
            self.keys[NUMBER_FLG]: 300,
            self.keys[SIGNAL_STRENGTH]: 'strong',
            self.keys[CONFIDENCE]: 0.85,
            self.keys[BUYING_PRESSURE]: 0.75,
            self.keys[BEHAVIOR_SCORE]: 0.90,
            self.keys[BEHAVIOR_SCORE_15MIN]: 0.88,
            self.keys[UPWARD_PRESSURE_DETECTED]: True,
            self.keys[BREAKOUT_FLG]: True,
            self.keys[BREAKOUT_RED_FLG]: False
        }
        
        # 批量设置数据
        self.data_manager.batch_update_stock_data(test_data)
        self.data_manager.set_transaction_data(self.test_stock, 500)
        
        # 获取市场数据
        market_data = self.manager.get_market_data(self.test_stock)
        
        # 验证数据
        self.assertEqual(market_data[NOW_PRICE], 155.50)
        self.assertEqual(market_data[VOLUME], 1000000.0)
        self.assertEqual(market_data[TRANSACTION_RANK], 500)
        self.assertEqual(market_data[SIGNAL_STRENGTH], 'strong')
        self.assertTrue(market_data[BREAKOUT_FLG])
        self.assertFalse(market_data[BREAKOUT_RED_FLG])
    
    def test_batch_operations(self):
        """测试批量操作"""
        # 准备多个股票的数据
        stocks = ['AAPL', 'GOOGL', 'MSFT']
        
        for i, stock in enumerate(stocks):
            keys = key_generator.get_keys_for_stock(stock)
            self.data_manager.set_stock_data(keys[NOW_PRICE], 100.0 + i * 10)
            self.data_manager.set_stock_data(keys[VOLUME], 1000000 + i * 100000)
            self.data_manager.set_transaction_data(stock, 100 + i * 50)
        
        # 批量获取市场数据
        batch_data = self.manager.batch_get_market_data(stocks)
        
        self.assertEqual(len(batch_data), 3)
        self.assertIn('AAPL', batch_data)
        self.assertIn('GOOGL', batch_data)
        self.assertIn('MSFT', batch_data)
        
        # 验证数据正确性
        self.assertEqual(batch_data['AAPL'][NOW_PRICE], 100.0)
        self.assertEqual(batch_data['GOOGL'][NOW_PRICE], 110.0)
        self.assertEqual(batch_data['MSFT'][NOW_PRICE], 120.0)
    
    def test_batch_update_stock_data(self):
        """测试批量更新股票数据"""
        updates = {
            self.keys[NOW_PRICE]: 160.0,
            self.keys[VOLUME]: 1200000,
            self.keys[CONFIDENCE]: 0.95
        }
        
        # 批量更新
        self.manager.batch_update_stock_data(updates)
        
        # 验证更新结果
        self.assertEqual(self.manager.get_current_price(self.test_stock), 160.0)
        self.assertEqual(self.manager.get_volume(self.test_stock), 1200000.0)
        self.assertEqual(self.manager.get_confidence(self.test_stock), 0.95)
    
    def test_active_stocks_management(self):
        """测试活跃股票管理"""
        stock_list = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
        
        # 设置活跃股票列表
        self.manager.set_active_stocks(stock_list)
        
        # 获取活跃股票列表
        active_stocks = self.manager.get_active_stocks()
        self.assertEqual(active_stocks, stock_list)
    
    def test_buy_flag_operations(self):
        """测试买入标志操作"""
        # 初始状态
        self.assertFalse(self.manager.is_buy_flag_set(self.test_stock))
        
        # 设置买入标志
        self.manager.set_buy_flag(self.test_stock, True)
        self.assertTrue(self.manager.is_buy_flag_set(self.test_stock))
        
        # 清除买入标志
        self.manager.set_buy_flag(self.test_stock, False)
        self.assertFalse(self.manager.is_buy_flag_set(self.test_stock))
    
    def test_buy_price_operations(self):
        """测试买入价格操作"""
        buy_price = 155.50
        
        # 更新买入价格
        self.manager.update_buy_price(self.test_stock, buy_price)
        
        # 验证价格已保存
        saved_price = self.data_manager.get_shared_data(self.test_stock)
        self.assertEqual(saved_price, buy_price)
    
    def test_statistics(self):
        """测试统计信息"""
        # 设置一些数据
        self.data_manager.set_stock_data(self.keys[NOW_PRICE], 155.50)
        self.data_manager.set_transaction_data(self.test_stock, 500)
        
        # 获取统计信息
        stats = self.manager.get_data_manager_stats()
        
        self.assertIn('stock_data_count', stats)
        self.assertIn('transaction_data_count', stats)
        self.assertIn('access_count', stats)
        self.assertGreater(stats['access_count'], 0)
    
    def test_set_market_data(self):
        """测试设置市场数据"""
        # 准备完整的市场数据
        market_data = {
            NOW_PRICE: 155.50,
            VOLUME: 1000000,
            VOLUME_AVG: 800000,
            CHECK_VOLUME: 900000,
            STOCK_SHARESOUTSTANDING: 5000000,
            NEWS: ['新闻1', '新闻2'],
            IS_SPREAD_NORMAL: True,
            BUY_FLG: False,
            SET_FLG: True,
            TRANSACTION_RANK: 500,
            SIGNAL_STRENGTH: 'strong',
            CONFIDENCE: 0.85,
            BUYING_PRESSURE: 0.75,
            BEHAVIOR_SCORE: 0.90,
            BEHAVIOR_SCORE_15MIN: 0.88,
            UPWARD_PRESSURE_DETECTED: True,
            BREAKOUT_FLG: True,
            BREAKOUT_RED_FLG: False,
            NUMBER_FLG: 300,
            'buy_price': 150.0,
            'program_status': 1
        }

        # 设置市场数据
        self.manager.set_market_data(self.test_stock, market_data)

        # 验证数据是否正确设置
        retrieved_data = self.manager.get_market_data(self.test_stock)

        # 验证所有设置的数据
        for key, expected_value in market_data.items():
            if key in retrieved_data:
                actual_value = retrieved_data[key]
                if isinstance(expected_value, float):
                    self.assertAlmostEqual(actual_value, expected_value, places=2)
                else:
                    self.assertEqual(actual_value, expected_value)

    def test_set_market_data_partial(self):
        """测试部分设置市场数据"""
        # 只设置部分数据
        partial_data = {
            NOW_PRICE: 160.0,
            VOLUME: 1200000,
            SET_FLG: True,
            'buy_price': 155.0
        }

        # 设置部分数据
        self.manager.set_market_data(self.test_stock, partial_data)

        # 验证设置的数据
        retrieved_data = self.manager.get_market_data(self.test_stock)
        self.assertEqual(retrieved_data[NOW_PRICE], 160.0)
        self.assertEqual(retrieved_data[VOLUME], 1200000.0)
        self.assertTrue(retrieved_data[SET_FLG])
        self.assertEqual(retrieved_data['buy_price'], 155.0)

        # 验证未设置的数据使用默认值
        self.assertEqual(retrieved_data[CONFIDENCE], 0.0)
        self.assertFalse(retrieved_data[BUY_FLG])

    def test_simple_get_set_interface(self):
        """测试简洁的get/set接口"""
        # 测试基本的get/set操作
        self.manager.set(self.test_stock, NOW_PRICE, 155.50)
        self.manager.set(self.test_stock, SET_FLG, True)
        self.manager.set(self.test_stock, 'buy_price', 150.0)

        # 验证数据
        price = self.manager.get(self.test_stock, NOW_PRICE, 0)
        set_flag = self.manager.get(self.test_stock, SET_FLG, False)
        buy_price = self.manager.get(self.test_stock, 'buy_price', 0)

        self.assertEqual(price, 155.50)
        self.assertTrue(set_flag)
        self.assertEqual(buy_price, 150.0)

        # 测试默认值
        unknown_value = self.manager.get(self.test_stock, 'unknown_field', 'default')
        self.assertEqual(unknown_value, 'default')

        # 测试特殊字段
        self.manager.set(self.test_stock, TRANSACTION_RANK, 500)
        rank = self.manager.get(self.test_stock, TRANSACTION_RANK, 0)
        self.assertEqual(rank, 500)

    def test_simple_interface_with_multiple_stocks(self):
        """测试简洁接口的多股票支持"""
        # 设置不同股票的数据
        self.manager.set('AAPL', NOW_PRICE, 155.50)
        self.manager.set('GOOGL', NOW_PRICE, 2800.0)
        self.manager.set('AAPL', SET_FLG, True)
        self.manager.set('GOOGL', SET_FLG, False)

        # 验证数据独立性
        aapl_price = self.manager.get('AAPL', NOW_PRICE, 0)
        googl_price = self.manager.get('GOOGL', NOW_PRICE, 0)
        aapl_flag = self.manager.get('AAPL', SET_FLG, False)
        googl_flag = self.manager.get('GOOGL', SET_FLG, True)

        self.assertEqual(aapl_price, 155.50)
        self.assertEqual(googl_price, 2800.0)
        self.assertTrue(aapl_flag)
        self.assertFalse(googl_flag)

    def test_interface_consistency(self):
        """测试两种接口的一致性"""
        # 使用简洁接口设置数据
        self.manager.set(self.test_stock, NOW_PRICE, 155.50)
        self.manager.set(self.test_stock, VOLUME, 1000000)

        # 使用复杂接口获取数据
        market_data = self.manager.get_market_data(self.test_stock)

        # 验证一致性
        self.assertEqual(market_data[NOW_PRICE], 155.50)
        self.assertEqual(market_data[VOLUME], 1000000.0)

        # 使用复杂接口设置数据
        complex_data = {CONFIDENCE: 0.85, BREAKOUT_FLG: True}
        self.manager.set_market_data(self.test_stock, complex_data)

        # 使用简洁接口获取数据
        confidence = self.manager.get(self.test_stock, CONFIDENCE, 0)
        breakout = self.manager.get(self.test_stock, BREAKOUT_FLG, False)

        # 验证一致性
        self.assertEqual(confidence, 0.85)
        self.assertTrue(breakout)

    def test_error_handling(self):
        """测试错误处理"""
        # 测试获取不存在的数据
        price = self.manager.get_current_price('NONEXISTENT')
        self.assertEqual(price, 0)

        volume = self.manager.get_volume('NONEXISTENT')
        self.assertEqual(volume, 0)

        rank = self.manager.get_transaction_rank('NONEXISTENT')
        self.assertEqual(rank, 0)

        # 测试简洁接口的错误处理
        unknown_price = self.manager.get('NONEXISTENT', NOW_PRICE, 999)
        self.assertEqual(unknown_price, 999)

        # 测试批量操作的错误处理
        batch_data = self.manager.batch_get_market_data(['NONEXISTENT'])
        self.assertIn('NONEXISTENT', batch_data)
        # get_market_data会返回默认值而不是空字典，这是正确的行为
        nonexistent_data = batch_data['NONEXISTENT']
        self.assertEqual(nonexistent_data[NOW_PRICE], 0.0)
        self.assertEqual(nonexistent_data[VOLUME], 0.0)


if __name__ == '__main__':
    unittest.main()
