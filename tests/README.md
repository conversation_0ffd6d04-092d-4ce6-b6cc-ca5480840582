# 回放服务图表渲染测试

## 图表渲染测试功能

### 测试目的
该测试旨在验证回放服务中的关键功能：页面是否能够正确生成并显示图表。这是回放服务的核心功能之一，确保用户可以通过回放功能查看历史股票数据。

### 测试方法
本测试直接在chart-manager-npm.js中实现，通过生成模拟数据并直接调用图表管理器的方法来渲染图表，不依赖WebSocket连接和后端数据推送。

### 使用方法

#### 方法1：使用测试按钮
1. 打开回放服务页面（例如 http://localhost:5001/）
2. 页面右下角会显示一个蓝色的"测试图表渲染"按钮
3. 点击该按钮执行测试
4. 在浏览器控制台（F12）中查看测试结果

#### 方法2：通过控制台手动执行
1. 打开回放服务页面（例如 http://localhost:5001/）
2. 打开浏览器开发者工具（F12）
3. 在控制台中输入以下代码：
```javascript
window.chartManager.testChartRendering();
```
4. 观察控制台输出的测试结果

### 测试内容
测试会执行以下步骤：
1. 生成符合LightweightCharts格式要求的模拟数据（5只股票，每只48根K线）
2. 清空现有图表
3. 为每只股票创建图表容器和图表实例
4. 将生成的数据应用到图表上
5. 验证图表是否正确渲染
6. 在控制台显示详细的测试过程和结果

### 预期结果
- 页面应显示5个股票图表
- 每个图表应包含48根K线
- 控制台应显示"测试完成: 5/5 个图表渲染成功"的消息

### 如何排查问题
如果测试失败，可以通过以下方式排查：
1. 查看控制台中的错误消息
2. 检查是否有JavaScript错误
3. 验证图表管理器是否正确初始化
4. 检查图表容器是否正确创建

### 与实际系统的关系
该测试使用与实际回放系统相同的图表管理器和数据格式，但不依赖WebSocket连接和后端数据推送，可以独立验证前端图表渲染功能是否正常工作。如果此测试成功但实际系统中图表不显示，则问题可能出在数据传输或处理环节，而非图表渲染本身。 