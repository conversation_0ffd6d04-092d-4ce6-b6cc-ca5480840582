#!/usr/bin/env python3
"""
测试实时数据按钮功能的脚本

这个脚本模拟IB API推送实时数据，用于测试前端"实时数据"按钮的功能。
"""

import sys
import os
import requests
import time
import logging
import threading
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def push_test_stock_list():
    """推送测试股票列表"""
    print("=== 推送测试股票列表 ===")
    
    test_stocks = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
    
    payload = {
        'stock_list': test_stocks,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        response = requests.post(
            'http://localhost:5001/api/stock_list_update',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code == 200:
            print(f"✅ 股票列表推送成功: {test_stocks}")
            return True
        else:
            print(f"❌ 股票列表推送失败: 状态码 {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 股票列表推送异常: {e}")
        return False

def push_realtime_data_continuously():
    """持续推送实时数据"""
    print("=== 开始持续推送实时数据 ===")
    
    stocks = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
    base_prices = {
        'AAPL': 150.0,
        'TSLA': 200.0,
        'NVDA': 400.0,
        'MSFT': 300.0,
        'GOOGL': 2500.0
    }
    
    count = 0
    
    while count < 20:  # 推送20次数据
        for i, stock_code in enumerate(stocks):
            # 生成模拟的价格变动
            base_price = base_prices[stock_code]
            price_change = (count % 10 - 5) * 0.5  # -2.5 到 +2.5 的变动
            
            current_price = base_price + price_change
            
            bar_data = {
                'time': int(time.time()) - (4 - i) * 60,  # 不同股票不同时间
                'open': current_price - 0.5,
                'high': current_price + 1.0,
                'low': current_price - 1.0,
                'close': current_price,
                'volume': 10000 + count * 1000
            }
            
            payload = {
                'stock_code': stock_code,
                'bar_data': bar_data,
                'data_type': 'realtime'
            }
            
            try:
                response = requests.post(
                    'http://localhost:5001/api/realtime_bar_update',
                    json=payload,
                    timeout=3.0
                )
                
                if response.status_code == 200:
                    print(f"✅ {stock_code} 实时数据推送成功 (第{count+1}次)")
                else:
                    print(f"❌ {stock_code} 实时数据推送失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {stock_code} 推送异常: {e}")
        
        count += 1
        time.sleep(2)  # 每2秒推送一轮数据
    
    print("=== 实时数据推送完成 ===")

def push_historical_data_end():
    """推送历史数据完成通知"""
    print("=== 推送历史数据完成通知 ===")
    
    stocks = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
    
    for stock_code in stocks:
        payload = {
            'stock_code': stock_code,
            'data_type': 'historical_end',
            'start_time': '20250622 09:30:00',
            'end_time': '20250622 16:00:00',
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            response = requests.post(
                'http://localhost:5001/api/historical_data_end',
                json=payload,
                timeout=3.0
            )
            
            if response.status_code == 200:
                print(f"✅ {stock_code} 历史数据完成通知成功")
            else:
                print(f"❌ {stock_code} 历史数据完成通知失败")
                
        except Exception as e:
            print(f"❌ {stock_code} 通知异常: {e}")
        
        time.sleep(0.5)

def simulate_ib_api_data_flow():
    """模拟完整的IB API数据流"""
    print("=== 模拟完整的IB API数据流 ===")
    
    # 1. 首先推送股票列表
    if not push_test_stock_list():
        print("❌ 股票列表推送失败，停止模拟")
        return False
    
    time.sleep(1)
    
    # 2. 推送一些历史数据完成通知
    push_historical_data_end()
    
    time.sleep(1)
    
    # 3. 开始持续推送实时数据
    push_realtime_data_continuously()
    
    return True

def test_frontend_response():
    """测试前端响应"""
    print("=== 测试前端响应 ===")
    
    try:
        # 检查前端服务是否运行
        response = requests.get('http://localhost:5001/health', timeout=5.0)
        
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            return True
        else:
            print(f"❌ 前端服务异常: 状态码 {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 前端服务未启动 (localhost:5001)")
        return False
    except Exception as e:
        print(f"❌ 前端服务检查异常: {e}")
        return False

if __name__ == '__main__':
    print("实时数据按钮功能测试")
    print("=" * 50)
    print("这个脚本将模拟IB API数据推送，用于测试前端'实时数据'按钮")
    print("请确保前端服务 (localhost:5001) 正在运行")
    print("=" * 50)
    
    # 检查前端服务
    if not test_frontend_response():
        print("\n❌ 前端服务检查失败，请先启动前端服务")
        sys.exit(1)
    
    print("\n✅ 前端服务检查通过")
    
    # 等待用户确认
    print("\n请按以下步骤测试：")
    print("1. 在浏览器中打开 http://localhost:5001")
    print("2. 点击'实时数据'按钮")
    print("3. 观察前端的响应和状态信息")
    print("4. 然后回到这里按回车键开始模拟数据推送")
    
    input("\n按回车键开始模拟IB API数据推送...")
    
    # 开始模拟数据流
    try:
        success = simulate_ib_api_data_flow()
        
        if success:
            print("\n🎉 模拟数据推送完成！")
            print("现在您应该能在前端看到实时更新的图表了。")
        else:
            print("\n❌ 模拟数据推送失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了数据推送")
    except Exception as e:
        print(f"\n❌ 模拟数据推送异常: {e}")
    
    print("\n测试完成！")
