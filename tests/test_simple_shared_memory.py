#!/usr/bin/env python3
"""
简单的共享内存测试
使用multiprocessing.Manager验证基本功能
"""

import sys
import os
import time
import multiprocessing

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.api.shared_memory import (
    push_stock_list_to_frontend,
    push_realtime_bar_to_frontend,
    get_shared_data_status
)
from ibapi.common import BarData


def create_test_bar_data(stock_code: str, time_str: str, price: float) -> BarData:
    """创建测试用的BarData对象"""
    bar = BarData()
    bar.date = time_str
    bar.open = price
    bar.high = price + 1.0
    bar.low = price - 1.0
    bar.close = price + 0.5
    bar.volume = 1000000
    return bar


def producer_process():
    """生产者进程 - 推送数据"""
    print("🚀 生产者进程启动...")
    
    try:
        # 推送股票列表
        print("📋 推送股票列表...")
        push_stock_list_to_frontend(['AAPL', 'GOOGL'])
        
        # 推送一些实时数据
        print("📊 推送实时数据...")
        for i in range(5):
            bar_data = create_test_bar_data('AAPL', f'20250622 09:3{i}:00', 150.0 + i)
            push_realtime_bar_to_frontend('AAPL', bar_data)
            print(f"  📈 推送第{i+1}条AAPL数据")
            time.sleep(0.2)
        
        print("✅ 生产者进程完成")
        
    except Exception as e:
        print(f"❌ 生产者进程出错: {e}")
        import traceback
        traceback.print_exc()


def consumer_process():
    """消费者进程 - 读取数据"""
    print("🌐 消费者进程启动...")
    
    try:
        # 等待生产者先启动
        time.sleep(1)
        
        from src.utils.shared_data_manager import get_shared_data_manager
        
        # 获取共享数据管理器
        manager = get_shared_data_manager(use_shared_memory=True)
        
        print("👂 开始读取共享数据...")
        
        # 读取数据
        for i in range(10):
            # 获取股票列表
            stock_data = manager.get_stock_list()
            print(f"📋 股票列表: {stock_data['stock_list']}")
            
            # 获取实时数据
            bars_data = manager.get_realtime_bars('AAPL')
            print(f"📊 AAPL数据条数: {len(bars_data['bars'])}")
            
            time.sleep(0.5)
        
        print("✅ 消费者进程完成")
        
    except Exception as e:
        print(f"❌ 消费者进程出错: {e}")
        import traceback
        traceback.print_exc()


def test_single_process():
    """单进程测试"""
    print("🧪 单进程测试开始...")
    
    try:
        # 推送数据
        push_stock_list_to_frontend(['AAPL', 'GOOGL', 'MSFT'])
        
        bar_data = create_test_bar_data('AAPL', '20250622 09:30:00', 150.0)
        push_realtime_bar_to_frontend('AAPL', bar_data)
        
        # 获取状态
        status = get_shared_data_status()
        print(f"📊 共享数据状态: {status}")
        
        print("✅ 单进程测试完成")
        
    except Exception as e:
        print(f"❌ 单进程测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_multiprocess():
    """多进程测试"""
    print("🧪 多进程测试开始...")
    
    # 创建进程
    producer = multiprocessing.Process(target=producer_process, name="Producer")
    consumer = multiprocessing.Process(target=consumer_process, name="Consumer")
    
    try:
        # 启动进程
        producer.start()
        consumer.start()
        
        # 等待完成
        producer.join(timeout=10)
        consumer.join(timeout=10)
        
        # 清理
        if producer.is_alive():
            producer.terminate()
        if consumer.is_alive():
            consumer.terminate()
        
        print("✅ 多进程测试完成")
        
    except Exception as e:
        print(f"❌ 多进程测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🧪 开始简单共享内存测试")
    print("=" * 50)
    
    # 先测试单进程
    test_single_process()
    
    print("\n" + "=" * 50)
    
    # 再测试多进程
    test_multiprocess()
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成！")


if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)
    main()
