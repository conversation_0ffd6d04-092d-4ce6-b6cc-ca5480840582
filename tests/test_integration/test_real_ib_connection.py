#!/usr/bin/env python3
"""
IB API实时数据推送测试脚本
"""
import sys
import os
sys.path.append('.')
sys.path.append('src/api')

from src.api.get_stock_price_ibapi import IBApp
from ibapi.contract import Contract
import time
import threading

def create_stock_contract(symbol):
    """创建股票合约"""
    contract = Contract()
    contract.symbol = symbol
    contract.secType = "STK"
    contract.exchange = "SMART"
    contract.currency = "USD"
    return contract

def test_real_ib_api():
    """测试真实IB API连接和数据推送"""
    print("=== 开始真实IB API测试 ===")
    
    # 创建IB应用实例
    app = IBApp()
    
    # 连接到IB Gateway
    print("连接到IB Gateway...")
    app.connect("127.0.0.1", 7497, 1000)  # Paper Trading端口
    
    # 等待连接建立
    time.sleep(2)
    
    if not app.isConnected():
        print("❌ IB Gateway连接失败")
        return False
    
    print("✅ IB Gateway连接成功")
    
    # 启动消息循环
    api_thread = threading.Thread(target=app.run, daemon=True)
    api_thread.start()
    
    # 等待nextValidId回调
    timeout = 10
    while not app.start and timeout > 0:
        time.sleep(1)
        timeout -= 1
    
    if not app.start:
        print("❌ 未收到nextValidId回调")
        return False
    
    print("✅ IB API初始化完成")
    
    # 测试股票列表
    test_stocks = ['AAPL', 'TSLA', 'MSFT']
    
    # 推送股票列表到前端
    try:
        app._push_stock_list_to_frontend(test_stocks)
        print(f"✅ 股票列表推送成功: {test_stocks}")
    except Exception as e:
        print(f"❌ 股票列表推送失败: {e}")
    
    # 为每个股票请求历史数据（带实时更新）
    req_id = 1
    for symbol in test_stocks:
        print(f"\n--- 请求 {symbol} 的历史数据（带实时更新）---")
        
        contract = create_stock_contract(symbol)
        app.reqId_dict_wrapper[req_id] = symbol
        
        # 关键：设置keepUpToDate=True启用实时更新
        app.reqHistoricalData(
            reqId=req_id,
            contract=contract,
            endDateTime="",  # 当前时间
            durationStr="1 D",  # 1天历史数据
            barSizeSetting="1 min",  # 1分钟K线
            whatToShow="TRADES",
            useRTH=0,  # 包含盘前盘后
            formatDate=1,
            keepUpToDate=True  # 🔑 关键：启用实时更新
        )
        
        print(f"✅ {symbol} 历史数据请求已发送 (reqId: {req_id})")
        req_id += 1
        time.sleep(1)  # 避免请求过快
    
    print("\n=== 实时数据监控开始 ===")
    print("系统将持续监控实时数据推送...")
    print("请检查前端页面 http://localhost:5001 查看图表更新")
    print("按 Ctrl+C 停止监控")
    
    try:
        # 持续运行，监控实时数据
        while True:
            time.sleep(10)
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 系统运行中，监控实时数据推送...")
    except KeyboardInterrupt:
        print("\n=== 停止监控 ===")
    finally:
        app.disconnect()
        print("✅ IB API连接已断开")
    
    return True

if __name__ == "__main__":
    test_real_ib_api()
