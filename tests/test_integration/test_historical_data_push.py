#!/usr/bin/env python3
"""
测试历史数据推送功能
验证 historicalData 和 historicalDataEnd 功能的正确性
"""
import sys
import os
import requests
import time
import json
from datetime import datetime, timedelta

def test_historical_data_push():
    """测试推送历史数据到前端"""
    print("=== 开始测试历史数据推送功能 ===")
    
    # 模拟历史数据 - 一天的1分钟K线数据
    base_time = datetime(2025, 6, 22, 9, 30)  # 2025-06-22 09:30:00
    historical_bars = []
    
    # 生成30个1分钟历史数据点
    for i in range(30):
        bar_time = base_time + timedelta(minutes=i)
        time_str = bar_time.strftime('%Y%m%d %H:%M:%S')
        
        # 模拟价格波动
        base_price = 150.0 + (i * 0.5)  # 价格逐渐上涨
        
        bar_data = {
            'time': time_str,
            'open': round(base_price, 2),
            'high': round(base_price + 0.8, 2),
            'low': round(base_price - 0.3, 2),
            'close': round(base_price + 0.5, 2),
            'volume': 8000 + (i * 200)  # 成交量逐渐增加
        }
        historical_bars.append(bar_data)
    
    # 测试股票代码
    test_stock = 'HIST_TEST'
    
    print(f"将推送 {len(historical_bars)} 个历史数据点给股票: {test_stock}")
    print(f"时间范围: {historical_bars[0]['time']} 到 {historical_bars[-1]['time']}")
    
    success_count = 0
    
    # 逐个推送历史数据
    for i, bar_data in enumerate(historical_bars):
        payload = {
            'stock_code': test_stock,
            'bar_data': bar_data,
            'data_type': 'historical'
        }
        
        try:
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                success_count += 1
                print(f"  ✅ 历史数据 {i+1}/{len(historical_bars)} 推送成功: {bar_data['time']}")
            else:
                print(f"  ❌ 历史数据 {i+1} 推送失败: 状态码 {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 网络错误: {e}")
        
        # 短暂延迟，避免过快推送
        time.sleep(0.1)
    
    print(f"\n=== 历史数据推送完成: {success_count}/{len(historical_bars)} 成功 ===")
    
    # 推送历史数据完成通知
    test_historical_data_end(test_stock)
    
    return success_count == len(historical_bars)

def test_historical_data_end(stock_code):
    """测试历史数据完成通知"""
    print(f"\n=== 测试历史数据完成通知: {stock_code} ===")
    
    payload = {
        'stock_code': stock_code,
        'start_time': '20250622 09:30:00',
        'end_time': '20250622 09:59:00',
        'message': '历史数据加载完成'
    }
    
    try:
        response = requests.post(
            'http://localhost:5001/api/historical_data_end',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code == 200:
            print(f"  ✅ 历史数据完成通知推送成功!")
            print(f"  响应: {response.json()}")
            return True
        else:
            print(f"  ❌ 历史数据完成通知推送失败: 状态码 {response.status_code}")
            print(f"  响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 网络错误: {e}")
        return False

def test_multiple_stocks_historical():
    """测试多股票历史数据推送"""
    print("\n=== 测试多股票历史数据推送 ===")
    
    test_stocks = ['HIST_A', 'HIST_B', 'HIST_C']
    
    for stock in test_stocks:
        print(f"\n--- 推送股票 {stock} 的历史数据 ---")
        
        # 为每个股票生成5个历史数据点
        base_time = datetime(2025, 6, 22, 10, 0)
        
        for i in range(5):
            bar_time = base_time + timedelta(minutes=i)
            time_str = bar_time.strftime('%Y%m%d %H:%M:%S')
            
            # 不同股票使用不同的基础价格
            base_price = 100.0 + (ord(stock[-1]) - ord('A')) * 20  # A=100, B=120, C=140
            
            bar_data = {
                'time': time_str,
                'open': round(base_price + i * 0.5, 2),
                'high': round(base_price + i * 0.5 + 1.0, 2),
                'low': round(base_price + i * 0.5 - 0.5, 2),
                'close': round(base_price + i * 0.5 + 0.3, 2),
                'volume': 5000 + i * 100
            }
            
            payload = {
                'stock_code': stock,
                'bar_data': bar_data,
                'data_type': 'historical'
            }
            
            try:
                response = requests.post(
                    'http://localhost:5001/api/realtime_bar_update',
                    json=payload,
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    print(f"  ✅ {stock} 数据点 {i+1} 推送成功")
                else:
                    print(f"  ❌ {stock} 数据点 {i+1} 推送失败")
                    
            except requests.exceptions.RequestException as e:
                print(f"  ❌ 网络错误: {e}")
            
            time.sleep(0.05)  # 短暂延迟
        
        # 发送完成通知
        test_historical_data_end(stock)
        time.sleep(0.5)  # 股票间延迟

def test_historical_to_realtime_transition():
    """测试历史数据到实时数据的过渡"""
    print("\n=== 测试历史数据到实时数据的过渡 ===")
    
    stock_code = 'TRANSITION_TEST'
    
    # 1. 先推送历史数据
    print("1. 推送历史数据...")
    base_time = datetime(2025, 6, 22, 14, 55)  # 14:55开始
    
    for i in range(3):  # 推送3个历史数据点
        bar_time = base_time + timedelta(minutes=i)
        time_str = bar_time.strftime('%Y%m%d %H:%M:%S')
        
        bar_data = {
            'time': time_str,
            'open': 200.0 + i,
            'high': 201.0 + i,
            'low': 199.5 + i,
            'close': 200.5 + i,
            'volume': 6000
        }
        
        payload = {
            'stock_code': stock_code,
            'bar_data': bar_data,
            'data_type': 'historical'
        }
        
        response = requests.post(
            'http://localhost:5001/api/realtime_bar_update',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code == 200:
            print(f"  ✅ 历史数据 {time_str} 推送成功")
        
        time.sleep(0.1)
    
    # 2. 发送历史数据完成通知
    print("2. 发送历史数据完成通知...")
    test_historical_data_end(stock_code)
    
    time.sleep(1)
    
    # 3. 推送实时数据
    print("3. 推送实时数据...")
    realtime_base = datetime(2025, 6, 22, 14, 58)  # 14:58开始实时数据
    
    for i in range(2):  # 推送2个实时数据点
        bar_time = realtime_base + timedelta(minutes=i)
        time_str = bar_time.strftime('%Y%m%d %H:%M:%S')
        
        bar_data = {
            'time': time_str,
            'open': 203.0 + i,
            'high': 204.0 + i,
            'low': 202.5 + i,
            'close': 203.5 + i,
            'volume': 7000
        }
        
        payload = {
            'stock_code': stock_code,
            'bar_data': bar_data,
            'data_type': 'realtime'
        }
        
        response = requests.post(
            'http://localhost:5001/api/realtime_bar_update',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code == 200:
            print(f"  ✅ 实时数据 {time_str} 推送成功")
        
        time.sleep(0.5)
    
    print("=== 历史到实时数据过渡测试完成 ===")

if __name__ == "__main__":
    print("=== 开始历史数据推送功能测试 ===")
    
    # 测试1: 单股票历史数据推送
    success1 = test_historical_data_push()
    
    print("\n" + "="*60 + "\n")
    
    # 测试2: 多股票历史数据推送
    test_multiple_stocks_historical()
    
    print("\n" + "="*60 + "\n")
    
    # 测试3: 历史数据到实时数据的过渡
    test_historical_to_realtime_transition()
    
    print("\n=== 所有历史数据推送测试完成 ===")
    print("请检查前端页面和控制台，确认:")
    print("1. 历史数据图表正确显示")
    print("2. 历史数据完成通知正确接收")
    print("3. 历史数据到实时数据的无缝过渡")
