#!/usr/bin/env python3
"""
简化的IB API集成测试

由于完整的IB API测试需要复杂的依赖，这里提供一个简化版本来验证集成准备情况。
"""
import requests
import time
from datetime import datetime

def test_flask_api_endpoints():
    """测试Flask API端点是否正常工作"""
    print("=== 测试Flask API端点 ===")
    
    base_url = "http://localhost:5001"
    
    # 测试主页
    try:
        response = requests.get(f"{base_url}/", timeout=5.0)
        if response.status_code == 200:
            print("✅ 主页访问正常")
        else:
            print(f"❌ 主页访问异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
        return False
    
    # 测试股票列表API
    try:
        test_payload = {
            'stock_list': ['AAPL', 'TSLA', 'MSFT'],
            'timestamp': datetime.now().isoformat()
        }
        response = requests.post(f"{base_url}/api/stock_list_update", json=test_payload, timeout=5.0)
        if response.status_code == 200:
            print("✅ 股票列表API正常")
        else:
            print(f"❌ 股票列表API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 股票列表API失败: {e}")
        return False
    
    # 测试实时数据API
    try:
        test_bar = {
            'time': '20250622 09:30:00',
            'open': 150.0,
            'high': 151.0,
            'low': 149.5,
            'close': 150.5,
            'volume': 10000
        }
        test_payload = {
            'stock_code': 'TEST_API',
            'bar_data': test_bar,
            'data_type': 'realtime'
        }
        response = requests.post(f"{base_url}/api/realtime_bar_update", json=test_payload, timeout=5.0)
        if response.status_code == 200:
            print("✅ 实时数据API正常")
        else:
            print(f"❌ 实时数据API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 实时数据API失败: {e}")
        return False
    
    # 测试历史数据完成API
    try:
        test_payload = {
            'stock_code': 'TEST_API',
            'start_time': '20250622 09:30:00',
            'end_time': '20250622 16:00:00',
            'message': '测试历史数据完成'
        }
        response = requests.post(f"{base_url}/api/historical_data_end", json=test_payload, timeout=5.0)
        if response.status_code == 200:
            print("✅ 历史数据完成API正常")
        else:
            print(f"❌ 历史数据完成API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 历史数据完成API失败: {e}")
        return False
    
    return True

def simulate_ib_api_data_flow():
    """模拟IB API数据流，测试完整的数据处理链路"""
    print("\n=== 模拟IB API数据流测试 ===")
    
    # 模拟股票列表推送
    print("1. 模拟股票列表推送...")
    stock_list = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL']
    payload = {
        'stock_list': stock_list,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        response = requests.post('http://localhost:5001/api/stock_list_update', json=payload, timeout=5.0)
        if response.status_code == 200:
            print(f"   ✅ 股票列表推送成功: {len(stock_list)} 只股票")
        else:
            print(f"   ❌ 股票列表推送失败")
            return False
    except Exception as e:
        print(f"   ❌ 股票列表推送异常: {e}")
        return False
    
    time.sleep(1)
    
    # 模拟历史数据推送
    print("2. 模拟历史数据推送...")
    for i, stock in enumerate(['AAPL', 'TSLA']):  # 测试2只股票
        print(f"   推送 {stock} 历史数据...")
        
        # 推送5个历史数据点
        for j in range(5):
            bar_data = {
                'time': f'20250622 09:{30+j:02d}:00',
                'open': 150.0 + i*10 + j*0.5,
                'high': 151.0 + i*10 + j*0.5,
                'low': 149.0 + i*10 + j*0.5,
                'close': 150.5 + i*10 + j*0.5,
                'volume': 8000 + j*200
            }
            
            payload = {
                'stock_code': stock,
                'bar_data': bar_data,
                'data_type': 'historical'
            }
            
            response = requests.post('http://localhost:5001/api/realtime_bar_update', json=payload, timeout=5.0)
            if response.status_code != 200:
                print(f"   ❌ {stock} 历史数据推送失败")
                return False
            
            time.sleep(0.1)
        
        # 发送历史数据完成通知
        payload = {
            'stock_code': stock,
            'start_time': '20250622 09:30:00',
            'end_time': '20250622 09:34:00',
            'message': f'{stock} 历史数据加载完成'
        }
        response = requests.post('http://localhost:5001/api/historical_data_end', json=payload, timeout=5.0)
        if response.status_code == 200:
            print(f"   ✅ {stock} 历史数据推送完成")
        else:
            print(f"   ❌ {stock} 历史数据完成通知失败")
            return False
        
        time.sleep(0.5)
    
    # 模拟实时数据推送
    print("3. 模拟实时数据推送...")
    for i in range(3):  # 推送3个实时数据点
        for stock in ['AAPL', 'TSLA']:
            bar_data = {
                'time': f'20250622 09:{35+i:02d}:00',
                'open': 155.0 + (ord(stock[0]) - ord('A')) + i*0.3,
                'high': 156.0 + (ord(stock[0]) - ord('A')) + i*0.3,
                'low': 154.0 + (ord(stock[0]) - ord('A')) + i*0.3,
                'close': 155.5 + (ord(stock[0]) - ord('A')) + i*0.3,
                'volume': 9000 + i*100
            }
            
            payload = {
                'stock_code': stock,
                'bar_data': bar_data,
                'data_type': 'realtime'
            }
            
            response = requests.post('http://localhost:5001/api/realtime_bar_update', json=payload, timeout=5.0)
            if response.status_code != 200:
                print(f"   ❌ {stock} 实时数据推送失败")
                return False
        
        print(f"   ✅ 第{i+1}轮实时数据推送完成")
        time.sleep(1)
    
    print("✅ 模拟IB API数据流测试完成")
    return True

def check_ib_api_integration_readiness():
    """检查IB API集成准备情况"""
    print("\n=== IB API集成准备情况检查 ===")
    
    # 检查关键文件是否存在
    import os
    
    key_files = [
        'src/api/get_stock_price_ibapi.py',
        'stock_gui_demo/app.py',
        'stock_gui_demo/data_service.py',
        'stock_gui_demo/static/realtime-charts.js'
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            return False
    
    # 检查关键方法是否已实现
    try:
        with open('src/api/get_stock_price_ibapi.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_methods = [
            '_push_realtime_bar_to_frontend',
            '_push_historical_bar_to_frontend',
            '_push_stock_list_to_frontend',
            '_push_historical_data_end_to_frontend'
        ]
        
        for method in required_methods:
            if method in content:
                print(f"✅ {method} 方法已实现")
            else:
                print(f"❌ {method} 方法未找到")
                return False
        
        # 检查关键调用是否已添加
        if 'self._push_realtime_bar_to_frontend(stock_code, bar)' in content:
            print("✅ historicalDataUpdate 中的推送调用已添加")
        else:
            print("❌ historicalDataUpdate 中的推送调用未找到")
            return False
        
        if 'self._push_historical_bar_to_frontend(stock_code, bar)' in content:
            print("✅ historicalData 中的推送调用已添加")
        else:
            print("❌ historicalData 中的推送调用未找到")
            return False
            
    except Exception as e:
        print(f"❌ 检查IB API文件时出错: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 IB API集成测试 - 简化版本")
    print("="*50)
    
    # 1. 检查Flask API端点
    if not test_flask_api_endpoints():
        print("\n❌ Flask API端点测试失败")
        return False
    
    # 2. 检查IB API集成准备情况
    if not check_ib_api_integration_readiness():
        print("\n❌ IB API集成准备检查失败")
        return False
    
    # 3. 模拟完整数据流
    if not simulate_ib_api_data_flow():
        print("\n❌ 数据流模拟测试失败")
        return False
    
    # 4. 输出成功信息和下一步指导
    print("\n" + "="*50)
    print("🎉 IB API集成测试 - 简化版本完成!")
    print("\n✅ 所有测试通过，系统已准备好进行真实IB API集成")
    
    print("\n📋 真实IB API集成步骤:")
    print("1. 启动IB Gateway或TWS")
    print("2. 配置API设置 (端口7497, 启用API)")
    print("3. 修改 get_stock_price_ibapi.py 中的连接参数")
    print("4. 运行IB API应用程序")
    print("5. 观察前端页面的实时数据更新")
    
    print("\n🔧 关键配置:")
    print("- keepUpToDate=True (启用实时更新)")
    print("- barSizeSetting='1 min' (1分钟K线)")
    print("- 推送方法已集成到所有回调函数中")
    
    print("\n📊 监控指标:")
    print("- 前端页面: http://localhost:5001")
    print("- 查看控制台日志确认数据推送")
    print("- 验证5分钟K线聚合效果")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 测试失败，请检查系统配置")
        exit(1)
    else:
        print("\n✅ 测试成功，系统准备就绪")
