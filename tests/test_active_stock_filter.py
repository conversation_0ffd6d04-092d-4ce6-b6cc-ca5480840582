#!/usr/bin/env python3
"""
测试活跃股票过滤功能的脚本

这个脚本测试修复后的活跃股票列表管理，确保只有在列表中的股票才会推送数据。
"""

import sys
import os
import time
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_active_stock_list_management():
    """测试活跃股票列表管理"""
    print("=== 测试活跃股票列表管理 ===")
    
    from src.api.external_APIs import (
        update_active_stock_list, 
        is_stock_active, 
        get_active_stock_count,
        push_stock_list_to_frontend
    )
    
    # 测试初始状态
    print(f"初始活跃股票数量: {get_active_stock_count()}")
    
    # 测试更新活跃股票列表
    test_stocks = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
    update_active_stock_list(test_stocks)
    
    print(f"更新后活跃股票数量: {get_active_stock_count()}")
    
    # 测试股票活跃状态检查
    for stock in test_stocks:
        is_active = is_stock_active(stock)
        print(f"  {stock}: {'✅ 活跃' if is_active else '❌ 非活跃'}")
    
    # 测试非活跃股票
    inactive_stocks = ['AMZN', 'META', 'NFLX']
    for stock in inactive_stocks:
        is_active = is_stock_active(stock)
        print(f"  {stock}: {'✅ 活跃' if is_active else '❌ 非活跃'}")
    
    # 测试通过push_stock_list_to_frontend更新
    print("\n--- 通过push_stock_list_to_frontend更新活跃列表 ---")
    new_stocks = ['AAPL', 'TSLA', 'AMD']  # 减少了一些股票
    
    try:
        push_stock_list_to_frontend(new_stocks)
        print(f"推送新股票列表: {new_stocks}")
        print(f"更新后活跃股票数量: {get_active_stock_count()}")
        
        # 验证更新结果
        for stock in ['AAPL', 'TSLA', 'AMD']:
            is_active = is_stock_active(stock)
            print(f"  {stock}: {'✅ 活跃' if is_active else '❌ 非活跃'}")
            
        for stock in ['NVDA', 'MSFT', 'GOOGL']:  # 这些应该不再活跃
            is_active = is_stock_active(stock)
            print(f"  {stock}: {'✅ 活跃' if is_active else '❌ 非活跃'}")
            
    except Exception as e:
        print(f"推送股票列表失败: {e}")

def test_data_push_filtering():
    """测试数据推送过滤功能"""
    print("\n=== 测试数据推送过滤功能 ===")
    
    from src.api.external_APIs import (
        push_realtime_bar_to_frontend,
        push_historical_bar_to_frontend,
        push_historical_data_end_to_frontend,
        update_active_stock_list
    )
    
    # 创建模拟的 BarData 对象
    class MockBarData:
        def __init__(self, date, open_price, high, low, close, volume):
            self.date = date
            self.open = open_price
            self.high = high
            self.low = low
            self.close = close
            self.volume = volume
    
    # 设置活跃股票列表
    active_stocks = ['AAPL', 'TSLA']
    update_active_stock_list(active_stocks)
    print(f"设置活跃股票: {active_stocks}")
    
    # 测试数据
    test_cases = [
        {'stock': 'AAPL', 'should_push': True, 'description': '活跃股票'},
        {'stock': 'TSLA', 'should_push': True, 'description': '活跃股票'},
        {'stock': 'NVDA', 'should_push': False, 'description': '非活跃股票'},
        {'stock': 'MSFT', 'should_push': False, 'description': '非活跃股票'}
    ]
    
    for case in test_cases:
        stock_code = case['stock']
        should_push = case['should_push']
        description = case['description']
        
        print(f"\n--- 测试 {stock_code} ({description}) ---")
        
        # 创建模拟数据
        mock_bar = MockBarData(
            date="20250622 10:30:00 US/Eastern",
            open_price=150.0,
            high=152.0,
            low=149.0,
            close=151.0,
            volume=10000
        )
        
        # 测试实时bar数据推送
        try:
            print(f"  测试实时bar数据推送...")
            push_realtime_bar_to_frontend(stock_code, mock_bar)
            print(f"  ✅ 实时bar数据推送{'成功' if should_push else '被过滤'}")
        except Exception as e:
            print(f"  ❌ 实时bar数据推送异常: {e}")
        
        # 测试历史bar数据推送
        try:
            print(f"  测试历史bar数据推送...")
            push_historical_bar_to_frontend(stock_code, mock_bar)
            print(f"  ✅ 历史bar数据推送{'成功' if should_push else '被过滤'}")
        except Exception as e:
            print(f"  ❌ 历史bar数据推送异常: {e}")
        
        # 测试历史数据完成通知
        try:
            print(f"  测试历史数据完成通知...")
            push_historical_data_end_to_frontend(
                stock_code, 
                "20250622 09:30:00", 
                "20250622 16:00:00"
            )
            print(f"  ✅ 历史数据完成通知{'成功' if should_push else '被过滤'}")
        except Exception as e:
            print(f"  ❌ 历史数据完成通知异常: {e}")
        
        time.sleep(0.5)  # 短暂间隔

def test_dynamic_list_updates():
    """测试动态列表更新"""
    print("\n=== 测试动态列表更新 ===")
    
    from src.api.external_APIs import (
        update_active_stock_list,
        is_stock_active,
        get_active_stock_count
    )
    
    # 模拟股票列表的动态变化
    scenarios = [
        {
            'name': '初始列表',
            'stocks': ['AAPL', 'TSLA', 'NVDA']
        },
        {
            'name': '添加股票',
            'stocks': ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
        },
        {
            'name': '移除部分股票',
            'stocks': ['AAPL', 'MSFT']
        },
        {
            'name': '完全替换',
            'stocks': ['AMD', 'INTC', 'CRM']
        },
        {
            'name': '清空列表',
            'stocks': []
        }
    ]
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        update_active_stock_list(scenario['stocks'])
        print(f"  更新列表: {scenario['stocks']}")
        print(f"  活跃股票数量: {get_active_stock_count()}")
        
        # 测试一些常见股票的状态
        test_stocks = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'AMD']
        for stock in test_stocks:
            is_active = is_stock_active(stock)
            status = '✅' if is_active else '❌'
            print(f"    {stock}: {status}")
        
        time.sleep(0.5)

if __name__ == '__main__':
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    print("开始测试活跃股票过滤功能...")
    print("注意：这个测试主要验证过滤逻辑，网络推送可能会失败（如果前端服务未启动）")
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 运行测试
    test_active_stock_list_management()
    test_data_push_filtering()
    test_dynamic_list_updates()
    
    print("\n=== 测试完成 ===")
    print("活跃股票过滤功能测试完成，请检查:")
    print("1. 活跃股票列表管理是否正常")
    print("2. 数据推送过滤是否生效")
    print("3. 动态列表更新是否正确")
    print("4. 日志中是否显示了正确的过滤信息")
