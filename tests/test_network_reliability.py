#!/usr/bin/env python3
"""
测试网络可靠性修复的脚本

这个脚本测试修复后的网络请求可靠性，特别是历史数据完成通知的重试机制。
"""

import sys
import os
import requests
import time
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_historical_data_end_reliability():
    """测试历史数据完成通知的可靠性"""
    print("=== 测试历史数据完成通知可靠性 ===")
    
    test_cases = [
        {
            'stock_code': 'AAPL',
            'start_time': '20250622 09:30:00',
            'end_time': '20250622 16:00:00'
        },
        {
            'stock_code': 'TSLA',
            'start_time': '20250622 09:30:00',
            'end_time': '20250622 16:00:00'
        },
        {
            'stock_code': 'NVDA',
            'start_time': '20250622 09:30:00',
            'end_time': '20250622 16:00:00'
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases):
        print(f"\n--- 测试案例 {i+1}: {test_case['stock_code']} ---")
        
        # 直接调用修复后的函数
        try:
            from src.api.external_APIs import push_historical_data_end_to_frontend
            
            start_time = time.time()
            push_historical_data_end_to_frontend(
                test_case['stock_code'],
                test_case['start_time'],
                test_case['end_time']
            )
            end_time = time.time()
            
            print(f"✅ {test_case['stock_code']} - 历史数据完成通知发送成功")
            print(f"   耗时: {end_time - start_time:.2f}秒")
            success_count += 1
            
        except Exception as e:
            print(f"❌ {test_case['stock_code']} - 发送失败: {e}")
        
        # 间隔一下
        time.sleep(1)
    
    print(f"\n=== 历史数据完成通知测试结果 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")

def test_concurrent_requests():
    """测试并发请求的处理能力"""
    print("\n=== 测试并发请求处理能力 ===")
    
    def send_test_request(stock_code, request_id):
        """发送测试请求"""
        try:
            payload = {
                'stock_code': stock_code,
                'bar_data': {
                    'time': 1719747360 + request_id,  # 不同的时间戳
                    'open': 150.0 + request_id,
                    'high': 152.0 + request_id,
                    'low': 149.0 + request_id,
                    'close': 151.0 + request_id,
                    'volume': 10000 * (request_id + 1)
                },
                'data_type': 'realtime'
            }
            
            start_time = time.time()
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            end_time = time.time()
            
            return {
                'request_id': request_id,
                'stock_code': stock_code,
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'response_time': end_time - start_time
            }
            
        except Exception as e:
            return {
                'request_id': request_id,
                'stock_code': stock_code,
                'success': False,
                'error': str(e),
                'response_time': None
            }
    
    # 并发测试参数
    num_concurrent = 10
    stocks = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
    
    print(f"发送 {num_concurrent} 个并发请求...")
    
    with ThreadPoolExecutor(max_workers=num_concurrent) as executor:
        futures = []
        for i in range(num_concurrent):
            stock_code = stocks[i % len(stocks)]
            future = executor.submit(send_test_request, stock_code, i)
            futures.append(future)
        
        results = []
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    
    # 分析结果
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"\n=== 并发请求测试结果 ===")
    print(f"总请求数: {len(results)}")
    print(f"成功: {len(successful)}")
    print(f"失败: {len(failed)}")
    print(f"成功率: {len(successful)/len(results)*100:.1f}%")
    
    if successful:
        response_times = [r['response_time'] for r in successful if r['response_time']]
        if response_times:
            print(f"平均响应时间: {sum(response_times)/len(response_times):.2f}秒")
            print(f"最快响应: {min(response_times):.2f}秒")
            print(f"最慢响应: {max(response_times):.2f}秒")
    
    if failed:
        print(f"\n失败的请求:")
        for r in failed[:5]:  # 只显示前5个失败的
            error_info = r.get('error', f"状态码: {r.get('status_code', 'unknown')}")
            print(f"  - {r['stock_code']} (请求{r['request_id']}): {error_info}")

def test_timeout_scenarios():
    """测试超时场景的处理"""
    print("\n=== 测试超时场景处理 ===")
    
    # 测试不同的超时情况
    timeout_tests = [
        {'timeout': 0.1, 'description': '极短超时(0.1秒)'},
        {'timeout': 1.0, 'description': '短超时(1秒)'},
        {'timeout': 3.0, 'description': '正常超时(3秒)'},
        {'timeout': 10.0, 'description': '长超时(10秒)'}
    ]
    
    for test in timeout_tests:
        print(f"\n--- {test['description']} ---")
        
        payload = {
            'stock_code': 'TEST_TIMEOUT',
            'bar_data': {
                'time': int(time.time()),
                'open': 150.0,
                'high': 152.0,
                'low': 149.0,
                'close': 151.0,
                'volume': 10000
            },
            'data_type': 'realtime'
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=test['timeout']
            )
            end_time = time.time()
            
            print(f"  ✅ 请求成功: 状态码 {response.status_code}, 耗时 {end_time - start_time:.2f}秒")
            
        except requests.exceptions.Timeout:
            print(f"  ⏰ 请求超时: {test['timeout']}秒")
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接错误（前端服务可能未启动）")
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")
        
        time.sleep(0.5)

if __name__ == '__main__':
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    print("开始测试网络可靠性修复...")
    print("注意：需要确保前端服务（localhost:5001）正在运行")
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 运行测试
    test_historical_data_end_reliability()
    test_concurrent_requests()
    test_timeout_scenarios()
    
    print("\n=== 测试完成 ===")
    print("请检查日志输出，确认:")
    print("1. 历史数据完成通知的重试机制是否正常工作")
    print("2. 网络错误日志是否已减少")
    print("3. 并发请求的处理能力是否满足需求")
