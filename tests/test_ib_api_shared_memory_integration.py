#!/usr/bin/env python3
"""
测试IB API进程与共享内存的集成
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.api.external_APIs import (
    push_stock_list_to_frontend,
    push_realtime_bar_to_frontend,
    push_historical_data_end_to_frontend,
    push_ib_api_status_to_frontend,
    update_active_stock_list,
    SHARED_MEMORY_AVAILABLE
)
from src.utils.shared_data_manager import get_shared_data_manager
from src.config.communication_config import communication_config
from ibapi.common import BarData


def create_test_bar_data(stock_code: str, time_str: str, price: float) -> BarData:
    """创建测试用的BarData对象"""
    bar = BarData()
    bar.date = time_str
    bar.open = price
    bar.high = price + 1.0
    bar.low = price - 1.0
    bar.close = price + 0.5
    bar.volume = 1000000
    return bar


def test_shared_memory_integration():
    """测试共享内存集成功能"""
    print("=== 测试IB API与共享内存集成 ===")
    
    print(f"共享内存可用性: {SHARED_MEMORY_AVAILABLE}")
    print(f"通信配置: {communication_config.get_config_summary()}")
    
    # 获取共享数据管理器
    manager = get_shared_data_manager(use_shared_memory=False)  # 使用本地内存测试
    
    # 注册回调函数来监控数据变化
    received_data = []
    
    def data_callback(data_type, data):
        received_data.append((data_type, data))
        print(f"📨 收到数据变化通知: {data_type}")
    
    manager.register_callback('stock_list', data_callback)
    manager.register_callback('realtime_bars', data_callback)
    manager.register_callback('historical_data_end', data_callback)
    manager.register_callback('ib_api_status', data_callback)
    
    print("\n--- 测试股票列表推送 ---")
    test_stocks = ['AAPL', 'GOOGL', 'MSFT']
    push_stock_list_to_frontend(test_stocks)
    
    # 等待回调执行
    time.sleep(0.1)
    
    # 验证数据
    stock_data = manager.get_stock_list()
    print(f"共享内存中的股票列表: {stock_data['stock_list']}")
    
    print("\n--- 测试实时Bar数据推送 ---")
    bar_data = create_test_bar_data('AAPL', '20250622 09:30:00', 150.0)
    push_realtime_bar_to_frontend('AAPL', bar_data)
    
    time.sleep(0.1)
    
    # 验证数据
    bar_result = manager.get_realtime_bars('AAPL')
    print(f"AAPL实时数据条数: {len(bar_result['bars'])}")
    if bar_result['bars']:
        print(f"最新Bar数据: {bar_result['bars'][-1]}")
    
    print("\n--- 测试历史数据完成通知 ---")
    push_historical_data_end_to_frontend('AAPL', '20250622 09:30:00', '20250622 16:00:00')
    
    time.sleep(0.1)
    
    # 验证数据
    hist_data = manager.get_historical_data_end('AAPL')
    print(f"AAPL历史数据状态: {hist_data}")
    
    print("\n--- 测试IB API状态推送 ---")
    push_ib_api_status_to_frontend(
        is_connected=True,
        server_version="10.19",
        connection_time="2025-06-22 09:30:00",
        active_stocks_count=3
    )
    
    time.sleep(0.1)
    
    # 验证数据
    api_status = manager.get_ib_api_status()
    print(f"IB API状态: {api_status}")
    
    print("\n--- 回调数据汇总 ---")
    print(f"总共收到 {len(received_data)} 个数据变化通知:")
    for i, (data_type, data) in enumerate(received_data, 1):
        print(f"  {i}. {data_type}: {type(data).__name__}")
    
    print("\n--- 管理器状态 ---")
    status = manager.get_status()
    print(f"管理器状态: {status}")
    
    print("=== 集成测试完成 ===")


def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 性能对比测试 ===")
    
    manager = get_shared_data_manager()
    
    # 测试共享内存性能
    print("测试共享内存推送性能...")
    start_time = time.time()
    
    for i in range(100):
        bar_data = create_test_bar_data('AAPL', f'20250622 09:30:{i:02d}', 150.0 + i * 0.01)
        push_realtime_bar_to_frontend('AAPL', bar_data)
    
    shared_memory_time = time.time() - start_time
    
    print(f"共享内存方式: 100次推送耗时 {shared_memory_time:.3f}秒")
    print(f"平均每次: {shared_memory_time/100*1000:.3f}毫秒")
    print(f"每秒处理能力: {100/shared_memory_time:.0f}次/秒")
    
    # 检查数据完整性
    result = manager.get_realtime_bars('AAPL')
    print(f"数据完整性检查: 期望101条，实际{len(result['bars'])}条")
    
    print("=== 性能对比测试完成 ===")


def test_communication_mode_switching():
    """测试通信模式切换"""
    print("\n=== 通信模式切换测试 ===")
    
    # 测试不同数据类型的通信方式选择
    test_cases = [
        ('stock_list', 1, '股票列表'),
        ('realtime_bars', 200, '实时Bar数据'),
        ('historical_data_end', 1, '历史数据完成'),
        ('ib_api_status', 1, 'IB API状态')
    ]
    
    for data_type, frequency, description in test_cases:
        use_shared = communication_config.should_use_shared_memory(data_type, frequency)
        method = '共享内存' if use_shared else 'HTTP'
        print(f"{description} (频率: {frequency}/秒) -> {method}")
    
    print("=== 通信模式切换测试完成 ===")


if __name__ == "__main__":
    try:
        test_shared_memory_integration()
        test_performance_comparison()
        test_communication_mode_switching()
        
        print("\n🎉 所有集成测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
