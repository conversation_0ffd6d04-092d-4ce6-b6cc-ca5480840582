#!/usr/bin/env python3
"""
测试 tvDatafeed 清理的脚本

这个脚本验证项目中是否已经完全清理了 tvDatafeed 相关的代码。
"""

import os
import sys
import re
import subprocess

def search_in_file(file_path, patterns):
    """在文件中搜索指定的模式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        matches = []
        for pattern in patterns:
            regex = re.compile(pattern, re.IGNORECASE)
            for line_num, line in enumerate(content.split('\n'), 1):
                if regex.search(line):
                    matches.append({
                        'pattern': pattern,
                        'line_num': line_num,
                        'line': line.strip()
                    })
        
        return matches
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return []

def scan_directory(directory, patterns, exclude_dirs=None, exclude_files=None):
    """扫描目录中的所有文件"""
    if exclude_dirs is None:
        exclude_dirs = {'.git', '__pycache__', 'node_modules', '.pytest_cache', 'venv', 'env'}
    
    if exclude_files is None:
        exclude_files = {'.pyc', '.pyo', '.pyd', '.so', '.dll'}
    
    results = {}
    
    for root, dirs, files in os.walk(directory):
        # 排除指定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            # 排除指定文件类型
            if any(file.endswith(ext) for ext in exclude_files):
                continue
                
            file_path = os.path.join(root, file)
            relative_path = os.path.relpath(file_path, directory)
            
            # 只检查文本文件
            if file.endswith(('.py', '.js', '.html', '.md', '.txt', '.yml', '.yaml', '.json')):
                matches = search_in_file(file_path, patterns)
                if matches:
                    results[relative_path] = matches
    
    return results

def test_tvdatafeed_cleanup():
    """测试 tvDatafeed 清理"""
    print("=== 测试 tvDatafeed 清理 ===")
    
    # 要搜索的模式
    patterns = [
        r'tvDatafeed',
        r'TvDatafeed',
        r'from tvDatafeed',
        r'import tvDatafeed',
        r'tradingview_service',
        r'getTradingViewData',
        r'handleTradingViewData',
        r'test_tv_data',
        r'check_data_time',
        r'test_timestamp_fix'
    ]
    
    # 项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    print(f"扫描目录: {project_root}")
    print(f"搜索模式: {patterns}")
    
    # 扫描项目
    results = scan_directory(project_root, patterns)
    
    if not results:
        print("✅ 未发现 tvDatafeed 相关代码，清理完成！")
        return True
    
    print(f"❌ 发现 {len(results)} 个文件仍包含 tvDatafeed 相关代码:")
    
    for file_path, matches in results.items():
        print(f"\n📁 {file_path}:")
        for match in matches:
            print(f"  第{match['line_num']}行: {match['pattern']} -> {match['line'][:100]}")
    
    return False

def test_import_cleanup():
    """测试导入清理"""
    print("\n=== 测试导入清理 ===")
    
    # 测试主要文件是否能正常导入
    test_files = [
        'stock_gui_demo.app',
        'stock_gui_demo.data_service',
        'src.api.external_APIs'
    ]
    
    success_count = 0
    
    for module_name in test_files:
        try:
            # 动态导入模块
            __import__(module_name)
            print(f"✅ {module_name} - 导入成功")
            success_count += 1
        except ImportError as e:
            if 'tvDatafeed' in str(e) or 'tradingview_service' in str(e):
                print(f"❌ {module_name} - 仍有 tvDatafeed 相关导入错误: {e}")
            else:
                print(f"⚠️ {module_name} - 其他导入错误: {e}")
        except Exception as e:
            print(f"⚠️ {module_name} - 其他错误: {e}")
    
    print(f"\n导入测试结果: {success_count}/{len(test_files)} 成功")
    return success_count == len(test_files)

def test_file_existence():
    """测试文件是否已删除"""
    print("\n=== 测试文件删除 ===")
    
    # 应该被删除的文件
    deleted_files = [
        'stock_gui_demo/test_tv_data.py',
        'stock_gui_demo/tradingview_service.py',
        'stock_gui_demo/test_timestamp_fix.py',
        'stock_gui_demo/check_data_time.py',
        'stock_gui_demo/templates/test_tradingview.html'
    ]
    
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    all_deleted = True
    
    for file_path in deleted_files:
        full_path = os.path.join(project_root, file_path)
        if os.path.exists(full_path):
            print(f"❌ {file_path} - 文件仍然存在")
            all_deleted = False
        else:
            print(f"✅ {file_path} - 文件已删除")
    
    return all_deleted

def test_requirements_cleanup():
    """测试 requirements.txt 清理"""
    print("\n=== 测试 requirements.txt 清理 ===")
    
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    requirements_files = [
        'requirements.txt',
        'stock_gui_demo/requirements.txt'
    ]
    
    tvdatafeed_found = False
    
    for req_file in requirements_files:
        full_path = os.path.join(project_root, req_file)
        if os.path.exists(full_path):
            try:
                with open(full_path, 'r') as f:
                    content = f.read()
                    if 'tvDatafeed' in content or 'tvdatafeed' in content:
                        print(f"❌ {req_file} - 仍包含 tvDatafeed 依赖")
                        tvdatafeed_found = True
                    else:
                        print(f"✅ {req_file} - 已清理 tvDatafeed 依赖")
            except Exception as e:
                print(f"⚠️ {req_file} - 读取失败: {e}")
    
    return not tvdatafeed_found

if __name__ == '__main__':
    print("开始测试 tvDatafeed 清理...")
    
    # 运行所有测试
    tests = [
        test_tvdatafeed_cleanup,
        test_file_existence,
        test_requirements_cleanup,
        test_import_cleanup
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试失败: {e}")
            results.append(False)
    
    # 汇总结果
    print("\n=== 测试结果汇总 ===")
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！tvDatafeed 清理完成。")
    else:
        print("❌ 部分测试失败，需要进一步清理。")
    
    sys.exit(0 if passed == total else 1)
