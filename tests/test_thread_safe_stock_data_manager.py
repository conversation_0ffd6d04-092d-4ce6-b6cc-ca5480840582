"""
ThreadSafeStockDataManager 单元测试

测试线程安全的股票数据管理器的功能和性能
"""

import unittest
import threading
import time
import logging
from concurrent.futures import ThreadPoolExecutor
from src.data.thread_safe_stock_data_manager import (
    ThreadSafeStockDataManager,
    get_stock_data_manager,
    reset_stock_data_manager
)

# 配置日志
logging.basicConfig(level=logging.INFO)


class TestThreadSafeStockDataManager(unittest.TestCase):
    """ThreadSafeStockDataManager 测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 重置全局管理器
        reset_stock_data_manager()
        self.manager = get_stock_data_manager()
    
    def tearDown(self):
        """测试后的清理工作"""
        reset_stock_data_manager()
    
    def test_singleton_pattern(self):
        """测试单例模式"""
        manager1 = get_stock_data_manager()
        manager2 = get_stock_data_manager()
        
        # 应该是同一个实例
        self.assertIs(manager1, manager2)
        
        # 直接创建实例也应该是同一个
        manager3 = ThreadSafeStockDataManager()
        self.assertIs(manager1, manager3)
    
    def test_stock_data_operations(self):
        """测试股票数据操作"""
        # 设置数据
        self.manager.set_stock_data('AAPL_price', 150.0)
        self.manager.set_stock_data('AAPL_volume', 1000000)
        
        # 获取数据
        price = self.manager.get_stock_data('AAPL_price')
        volume = self.manager.get_stock_data('AAPL_volume')
        
        self.assertEqual(price, 150.0)
        self.assertEqual(volume, 1000000)
        
        # 获取不存在的数据
        missing = self.manager.get_stock_data('MISSING_KEY', 'default')
        self.assertEqual(missing, 'default')
    
    def test_transaction_data_operations(self):
        """测试交易数据操作"""
        # 设置交易数据
        self.manager.set_transaction_data('AAPL', 500)
        self.manager.set_transaction_data('GOOGL', 300)
        
        # 获取交易数据
        aapl_count = self.manager.get_transaction_data('AAPL')
        googl_count = self.manager.get_transaction_data('GOOGL')
        missing_count = self.manager.get_transaction_data('MISSING')
        
        self.assertEqual(aapl_count, 500)
        self.assertEqual(googl_count, 300)
        self.assertEqual(missing_count, 0)
        
        # 测试无效数据
        self.manager.set_transaction_data('INVALID', -10)  # 应该被拒绝
        invalid_count = self.manager.get_transaction_data('INVALID')
        self.assertEqual(invalid_count, 0)
    
    def test_shared_data_operations(self):
        """测试共享数据操作"""
        # 设置共享数据
        self.manager.set_shared_data(1, ['AAPL', 'GOOGL'])
        self.manager.set_shared_data('status', 'running')
        
        # 获取共享数据
        stock_list = self.manager.get_shared_data(1)
        status = self.manager.get_shared_data('status')
        missing = self.manager.get_shared_data('missing', 'default')
        
        self.assertEqual(stock_list, ['AAPL', 'GOOGL'])
        self.assertEqual(status, 'running')
        self.assertEqual(missing, 'default')
    
    def test_batch_update(self):
        """测试批量更新"""
        updates = {
            'AAPL_price': 155.0,
            'AAPL_volume': 1200000,
            'GOOGL_price': 2800.0,
            'GOOGL_volume': 800000
        }
        
        self.manager.batch_update_stock_data(updates)
        
        # 验证所有数据都已更新
        for key, expected_value in updates.items():
            actual_value = self.manager.get_stock_data(key)
            self.assertEqual(actual_value, expected_value)
    
    def test_data_validators(self):
        """测试数据验证器"""
        # 注册价格验证器
        def price_validator(value):
            if not isinstance(value, (int, float)) or value < 0:
                raise ValueError("价格必须是非负数")
        
        self.manager.register_validator('price', price_validator)
        
        # 有效数据应该成功
        self.manager.set_stock_data('price', 100.0)
        self.assertEqual(self.manager.get_stock_data('price'), 100.0)
        
        # 无效数据应该被拒绝
        self.manager.set_stock_data('price', -50.0)
        # 数据不应该被更新
        self.assertEqual(self.manager.get_stock_data('price'), 100.0)
    
    def test_clear_operations(self):
        """测试清空操作"""
        # 设置一些数据
        self.manager.set_stock_data('test_key', 'test_value')
        self.manager.set_transaction_data('TEST', 100)
        self.manager.set_shared_data('test', 'data')
        
        # 清空股票数据
        self.manager.clear_stock_data()
        self.assertIsNone(self.manager.get_stock_data('test_key'))
        
        # 其他数据应该还在
        self.assertEqual(self.manager.get_transaction_data('TEST'), 100)
        self.assertEqual(self.manager.get_shared_data('test'), 'data')
        
        # 清空交易数据
        self.manager.clear_transaction_data()
        self.assertEqual(self.manager.get_transaction_data('TEST'), 0)
        
        # 清空共享数据
        self.manager.clear_shared_data()
        self.assertIsNone(self.manager.get_shared_data('test'))
    
    def test_statistics(self):
        """测试统计信息"""
        # 设置一些数据
        self.manager.set_stock_data('key1', 'value1')
        self.manager.set_stock_data('key2', 'value2')
        self.manager.set_transaction_data('STOCK1', 100)
        self.manager.set_shared_data('shared1', 'data1')
        
        # 获取统计信息
        stats = self.manager.get_stats()
        
        self.assertGreaterEqual(stats['stock_data_count'], 2)
        self.assertGreaterEqual(stats['transaction_data_count'], 1)
        self.assertGreaterEqual(stats['shared_data_count'], 1)
        self.assertGreater(stats['access_count'], 0)
        self.assertIn('last_access_time', stats)
    
    def test_thread_safety(self):
        """测试线程安全性"""
        num_threads = 10
        operations_per_thread = 100
        
        def worker_thread(thread_id):
            """工作线程函数"""
            for i in range(operations_per_thread):
                # 设置数据
                key = f'thread_{thread_id}_key_{i}'
                value = f'thread_{thread_id}_value_{i}'
                self.manager.set_stock_data(key, value)
                
                # 读取数据
                retrieved_value = self.manager.get_stock_data(key)
                self.assertEqual(retrieved_value, value)
                
                # 设置交易数据
                stock_code = f'STOCK_{thread_id}_{i}'
                count = thread_id * 100 + i
                self.manager.set_transaction_data(stock_code, count)
                
                # 读取交易数据
                retrieved_count = self.manager.get_transaction_data(stock_code)
                self.assertEqual(retrieved_count, count)
        
        # 创建并启动多个线程
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = []
            for thread_id in range(num_threads):
                future = executor.submit(worker_thread, thread_id)
                futures.append(future)
            
            # 等待所有线程完成
            for future in futures:
                future.result()  # 如果有异常会在这里抛出
        
        # 验证最终状态
        stats = self.manager.get_stats()
        expected_min_operations = num_threads * operations_per_thread * 4  # 每次循环4个操作
        self.assertGreaterEqual(stats['access_count'], expected_min_operations)
    
    def test_concurrent_access_consistency(self):
        """测试并发访问的数据一致性"""
        shared_counter_key = 'shared_counter'
        self.manager.set_stock_data(shared_counter_key, 0)
        
        def increment_counter():
            """增加计数器"""
            for _ in range(100):
                current = self.manager.get_stock_data(shared_counter_key, 0)
                self.manager.set_stock_data(shared_counter_key, current + 1)
                time.sleep(0.001)  # 模拟一些处理时间
        
        # 启动多个线程同时修改同一个数据
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=increment_counter)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 由于线程安全，最终结果应该是500
        final_count = self.manager.get_stock_data(shared_counter_key)
        self.assertEqual(final_count, 500)


if __name__ == '__main__':
    unittest.main()
