#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
使用真实代码的测试：测试股票排名变化时Tick-by-tick数据请求的逻辑
这个测试导入真实的IBApp类，但模拟其外部依赖
"""

import unittest
import logging
import sys
import os
from unittest.mock import MagicMock, patch
from datetime import datetime
import threading
import pytz
import pandas as pd

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(filename)s - %(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

# 定义模拟的合约类
class MockContract:
    """模拟IB合约"""
    def __init__(self, symbol):
        self.symbol = symbol
        self.secType = "STK"
        self.exchange = "SMART"
        self.primaryExchange = "NASDAQ"
        self.currency = "USD"
        self.localSymbol = symbol

class MockContractDetails:
    """模拟IB合约详情"""
    def __init__(self, symbol):
        self.contract = MockContract(symbol)

class TestTickByTickWithRealCode(unittest.TestCase):
    """使用真实代码测试股票排名变化时Tick-by-tick数据请求的逻辑"""

    def setUp(self):
        """测试前的准备工作"""
        # 模拟ibapi模块
        self.ibapi_patcher = patch.dict('sys.modules', {
            'ibapi': MagicMock(),
            'ibapi.client': MagicMock(),
            'ibapi.wrapper': MagicMock(),
            'ibapi.scanner': MagicMock(),
            'ibapi.contract': MagicMock(),
            'ibapi.utils': MagicMock(),
            'ibapi.common': MagicMock(),
            'futu': MagicMock(),
            'core': MagicMock(),
            'core.check_the_signal': MagicMock(),
            'analysis': MagicMock(),
            'analysis.pre_stock_signal': MagicMock(),
            'analysis.macd_check': MagicMock(),
            'analysis.check_breakout_level': MagicMock(),
            'analysis.number_of_transactions': MagicMock(),
            'analysis.stock_analyzer': MagicMock(),
            'analysis.tradingview_screener_q': MagicMock(),
            'utils': MagicMock(),
            'utils.futu_utils': MagicMock(),
            'utils.constants': MagicMock(),
            'utils.stock_utils': MagicMock(),
            'utils.yfinance_fun': MagicMock(),
            'utils.db_utils': MagicMock(),
            'utils.restart_IB_Gateway': MagicMock(),
            'utils.config': MagicMock(),
            'utils.key_generator': MagicMock(),
            'services': MagicMock(),
            'services.globenewswire_rss': MagicMock(),
            'services.stocktitan_rss': MagicMock(),
        })
        
        # 定义必要的类型和类
        class TickerId(int): pass
        class ListOfHistoricalTickLast(list): pass
        class TickAttribLast: 
            def __init__(self):
                self.pastLimit = False
                self.unreported = False
        class TickAttribBidAsk:
            def __init__(self):
                self.bidPastLow = False
                self.askPastHigh = False
        class Decimal(float): pass
        
        class EWrapper:
            def __init__(self):
                pass
        
        class EClient:
            def __init__(self, wrapper):
                self.wrapper = wrapper
        
        # 将类型添加到ibapi模块中
        sys.modules['ibapi.common'].TickerId = TickerId
        sys.modules['ibapi.common'].ListOfHistoricalTickLast = ListOfHistoricalTickLast
        sys.modules['ibapi.common'].TickAttribLast = TickAttribLast
        sys.modules['ibapi.common'].TickAttribBidAsk = TickAttribBidAsk
        sys.modules['ibapi.common'].Decimal = Decimal
        sys.modules['ibapi.wrapper'].EWrapper = EWrapper
        sys.modules['ibapi.client'].EClient = EClient
        
        # 设置utils.key_generator.key_generator
        key_generator_mock = MagicMock()
        key_generator_mock.get_keys_for_stock = MagicMock(return_value={})
        sys.modules['utils.key_generator'].key_generator = key_generator_mock
        
        # 设置utils.config.IB_CONFIG
        sys.modules['utils.config'].IB_CONFIG = {
            'MAX_RETRY_ATTEMPTS': 3,
            'HOST': '127.0.0.1',
            'PORT': 7497,
            'CLIENT_ID': 1000,
            'RETRY_INTERVAL': 5
        }
        
        # 启动模拟
        self.ibapi_patcher.start()
        
        # 导入真实的IBApp类
        from src.api.get_stock_price_ibapi import IBApp
        
        # 创建IBApp实例
        self.ib_app = IBApp()
        
        # 模拟必要的属性和方法
        self.ib_app.start = True
        self.ib_app.is_shutting_down = False
        self.ib_app.active_tick_requests = {}
        self.ib_app.reqId_dict = {}
        self.ib_app.reqId_dict_day = {}
        self.ib_app.reqId_dict_wrapper = {}
        self.ib_app.contractDetails_dict = {}
        self.ib_app.data = {}
        self.ib_app.watch_dict = {}
        self.ib_app.top5_tick_stocks = set()
        self.ib_app.curr_date = datetime.now(tz=pytz.timezone('America/New_York')).strftime('%Y-%m-%d')
        
        # 模拟方法
        self.ib_app.reqTickByTickData = MagicMock()
        self.ib_app.cancelTickByTickData = MagicMock()
        self.ib_app.reqHistoricalData = MagicMock()
        self.ib_app.reqMktData = MagicMock()
        
    def tearDown(self):
        """测试后的清理工作"""
        # 停止模拟
        self.ibapi_patcher.stop()
        
    def test_update_top5_tick_stocks_add_stock(self):
        """测试当股票进入前5名时，是否正确请求Tick-by-tick数据"""
        # 准备测试数据
        # 模拟5个股票的合约
        for i in range(1, 6):
            stock_code = f"STOCK{i}"
            reqid = 10000 + i
            contract = MockContract(stock_code)
            
            self.ib_app.reqId_dict[stock_code] = reqid
            self.ib_app.reqId_dict_wrapper[reqid] = stock_code
            self.ib_app.contractDetails_dict[reqid] = contract
            
        # 设置初始排名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK5"
        }
        
        # 模拟is_receiving_tick_data方法，让它返回True，这样就不会调用get_tick_by_tick_data
        self.ib_app.is_receiving_tick_data = MagicMock(return_value=True)
        
        # 初始化前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证前5名股票集合是否正确
        self.assertEqual(len(self.ib_app.top5_tick_stocks), 5)
        self.assertSetEqual(self.ib_app.top5_tick_stocks, {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5"})
        
        # 验证是否没有调用get_tick_by_tick_data
        self.ib_app.get_tick_by_tick_data = MagicMock()
        
        # 修改is_receiving_tick_data的行为，让它返回False
        self.ib_app.is_receiving_tick_data = MagicMock(return_value=False)
        
        # 重新初始化，这次应该会请求数据
        self.ib_app.top5_tick_stocks = set()
        self.ib_app.update_top5_tick_stocks()
        
        # 验证是否正确请求了Tick-by-tick数据
        self.assertEqual(self.ib_app.get_tick_by_tick_data.call_count, 5)  # 应该为5个股票请求数据
        
    def test_update_top5_tick_stocks_remove_stock(self):
        """测试当股票离开前5名时，是否正确取消Tick-by-tick数据请求"""
        # 准备测试数据
        # 模拟6个股票的合约
        for i in range(1, 7):
            stock_code = f"STOCK{i}"
            reqid = 10000 + i
            contract = MockContract(stock_code)
            
            self.ib_app.reqId_dict[stock_code] = reqid
            self.ib_app.reqId_dict_wrapper[reqid] = stock_code
            self.ib_app.contractDetails_dict[reqid] = contract
            
        # 设置初始排名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK5"
        }
        
        # 初始化前5名股票集合
        self.ib_app.top5_tick_stocks = {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5"}
        
        # 模拟cancel_tick_by_tick_data方法
        self.ib_app.cancel_tick_by_tick_data = MagicMock()
        
        # 修改排名，让STOCK5离开前5名，STOCK6进入前5名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK6"
        }
        
        # 更新前5名股票集合
        self.ib_app.update_top5_tick_stocks()
        
        # 验证是否正确取消了STOCK5的Tick-by-tick数据请求
        self.ib_app.cancel_tick_by_tick_data.assert_called_once_with("STOCK5")
        
        # 验证前5名股票集合是否正确更新
        self.assertEqual(len(self.ib_app.top5_tick_stocks), 5)
        self.assertSetEqual(self.ib_app.top5_tick_stocks, {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK6"})
        
    def test_scanner_data_rank_change(self):
        """测试当scannerData返回新的排名时，是否正确更新前5名股票集合并请求Tick-by-tick数据"""
        # 准备测试数据
        # 模拟6个股票的合约
        for i in range(1, 7):
            stock_code = f"STOCK{i}"
            reqid = 10000 + i
            contract = MockContract(stock_code)
            contract.localSymbol = stock_code
            
            self.ib_app.reqId_dict[stock_code] = reqid
            self.ib_app.reqId_dict_wrapper[reqid] = stock_code
            self.ib_app.contractDetails_dict[reqid] = contract
            
        # 设置初始排名
        self.ib_app.watch_dict = {
            0: "STOCK1",
            1: "STOCK2",
            2: "STOCK3",
            3: "STOCK4",
            4: "STOCK5"
        }
        
        # 初始化前5名股票集合
        self.ib_app.top5_tick_stocks = {"STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5"}
        
        # 模拟update_top5_tick_stocks方法
        self.ib_app.update_top5_tick_stocks = MagicMock()
        
        # 模拟contractDetails
        contract_details = MockContractDetails("STOCK6")
        
        # 调用scannerData方法，模拟STOCK6进入前5名
        self.ib_app.scannerData(reqId=1, rank=4, contractDetails=contract_details, 
                               distance="", benchmark="", projection="", legsStr="")
        
        # 验证是否调用了update_top5_tick_stocks方法
        self.ib_app.update_top5_tick_stocks.assert_called_once()
        
        # 验证watch_dict是否正确更新
        self.assertEqual(self.ib_app.watch_dict[4], "STOCK6")

if __name__ == "__main__":
    unittest.main() 