#!/usr/bin/env python3
"""
测试完整数据流程的脚本

这个脚本测试从 IB API 数据推送到前端图表展示的完整流程。
"""

import sys
import os
import requests
import time
import logging
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_stock_list_push():
    """测试股票列表推送"""
    print("=== 测试股票列表推送 ===")
    
    test_stock_list = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
    
    payload = {
        'stock_list': test_stock_list,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        response = requests.post(
            'http://localhost:5001/api/stock_list_update',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code == 200:
            print(f"✅ 股票列表推送成功: {test_stock_list}")
            return True
        else:
            print(f"❌ 股票列表推送失败: 状态码 {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：前端服务可能未启动 (localhost:5001)")
        return False
    except Exception as e:
        print(f"❌ 股票列表推送异常: {e}")
        return False

def test_realtime_bar_push():
    """测试实时bar数据推送"""
    print("\n=== 测试实时bar数据推送 ===")
    
    test_stocks = ['AAPL', 'TSLA', 'NVDA']
    success_count = 0
    
    for i, stock_code in enumerate(test_stocks):
        print(f"\n--- 测试 {stock_code} ---")
        
        # 构造测试数据
        bar_data = {
            'time': int(time.time()) - i * 60,  # 不同的时间戳
            'open': 150.0 + i,
            'high': 152.0 + i,
            'low': 149.0 + i,
            'close': 151.0 + i,
            'volume': 10000 * (i + 1)
        }
        
        payload = {
            'stock_code': stock_code,
            'bar_data': bar_data,
            'data_type': 'realtime'
        }
        
        try:
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"✅ {stock_code} 实时数据推送成功")
                success_count += 1
            else:
                print(f"❌ {stock_code} 实时数据推送失败: 状态码 {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {stock_code} 连接失败")
        except Exception as e:
            print(f"❌ {stock_code} 推送异常: {e}")
        
        time.sleep(0.5)  # 间隔一下
    
    print(f"\n实时数据推送结果: {success_count}/{len(test_stocks)} 成功")
    return success_count == len(test_stocks)

def test_historical_data_end():
    """测试历史数据完成通知"""
    print("\n=== 测试历史数据完成通知 ===")
    
    test_stocks = ['AAPL', 'TSLA']
    success_count = 0
    
    for stock_code in test_stocks:
        print(f"\n--- 测试 {stock_code} 历史数据完成通知 ---")
        
        payload = {
            'stock_code': stock_code,
            'data_type': 'historical_end',
            'start_time': '20250622 09:30:00',
            'end_time': '20250622 16:00:00',
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            response = requests.post(
                'http://localhost:5001/api/historical_data_end',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"✅ {stock_code} 历史数据完成通知成功")
                success_count += 1
            else:
                print(f"❌ {stock_code} 历史数据完成通知失败: 状态码 {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {stock_code} 连接失败")
        except Exception as e:
            print(f"❌ {stock_code} 通知异常: {e}")
        
        time.sleep(0.5)
    
    print(f"\n历史数据完成通知结果: {success_count}/{len(test_stocks)} 成功")
    return success_count == len(test_stocks)

def test_frontend_health():
    """测试前端服务健康状态"""
    print("\n=== 测试前端服务健康状态 ===")
    
    try:
        response = requests.get('http://localhost:5001/health', timeout=5.0)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 前端服务健康: {health_data}")
            return True
        else:
            print(f"❌ 前端服务不健康: 状态码 {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 前端服务未启动 (localhost:5001)")
        return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_active_stock_filtering():
    """测试活跃股票过滤功能"""
    print("\n=== 测试活跃股票过滤功能 ===")
    
    # 首先推送股票列表，激活过滤
    active_stocks = ['AAPL', 'TSLA']
    inactive_stocks = ['NVDA', 'MSFT']
    
    # 推送活跃股票列表
    payload = {
        'stock_list': active_stocks,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        response = requests.post(
            'http://localhost:5001/api/stock_list_update',
            json=payload,
            timeout=5.0
        )
        
        if response.status_code != 200:
            print("❌ 无法设置活跃股票列表")
            return False
        
        print(f"✅ 设置活跃股票列表: {active_stocks}")
        time.sleep(1)
        
        # 测试活跃股票推送（应该成功）
        success_count = 0
        for stock_code in active_stocks:
            bar_data = {
                'time': int(time.time()),
                'open': 150.0,
                'high': 152.0,
                'low': 149.0,
                'close': 151.0,
                'volume': 10000
            }
            
            payload = {
                'stock_code': stock_code,
                'bar_data': bar_data,
                'data_type': 'realtime'
            }
            
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"✅ {stock_code} (活跃股票) 推送成功")
                success_count += 1
            else:
                print(f"❌ {stock_code} (活跃股票) 推送失败")
        
        # 测试非活跃股票推送（应该被过滤）
        for stock_code in inactive_stocks:
            bar_data = {
                'time': int(time.time()),
                'open': 150.0,
                'high': 152.0,
                'low': 149.0,
                'close': 151.0,
                'volume': 10000
            }
            
            payload = {
                'stock_code': stock_code,
                'bar_data': bar_data,
                'data_type': 'realtime'
            }
            
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"✅ {stock_code} (非活跃股票) 推送成功（应该被过滤）")
            else:
                print(f"❌ {stock_code} (非活跃股票) 推送失败")
        
        return success_count == len(active_stocks)
        
    except Exception as e:
        print(f"❌ 活跃股票过滤测试异常: {e}")
        return False

if __name__ == '__main__':
    print("开始测试完整数据流程...")
    print("注意：需要确保前端服务（localhost:5001）正在运行")
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 运行所有测试
    tests = [
        ("前端服务健康检查", test_frontend_health),
        ("股票列表推送", test_stock_list_push),
        ("实时bar数据推送", test_realtime_bar_push),
        ("历史数据完成通知", test_historical_data_end),
        ("活跃股票过滤", test_active_stock_filtering)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            print(f"运行测试: {test_name}")
            print(f"{'='*50}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 通过")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！IB API 到前端图表的完整数据流程正常工作。")
    else:
        print(f"\n⚠️ {total-passed} 个测试失败，请检查相关组件。")
    
    print("\n测试完成！")
