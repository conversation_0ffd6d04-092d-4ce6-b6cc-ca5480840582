#!/usr/bin/env python3
"""
测试线程池管理的脚本

这个脚本测试修复后的线程池管理是否正常工作。
"""

import sys
import os
import time
import threading

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_thread_pool_management():
    """测试线程池管理"""
    print("=== 测试线程池管理 ===")
    
    # 导入模块，这会创建线程池
    from src.api.external_APIs import _network_thread_pool, push_realtime_bar_to_frontend
    
    print(f"线程池最大工作线程数: {_network_thread_pool._max_workers}")
    print(f"线程池线程名前缀: {_network_thread_pool._thread_name_prefix}")
    
    # 模拟一些网络请求
    print("\n发送多个测试请求...")
    
    # 创建模拟的 BarData 对象
    class MockBarData:
        def __init__(self, date, open_price, high, low, close, volume):
            self.date = date
            self.open = open_price
            self.high = high
            self.low = low
            self.close = close
            self.volume = volume
    
    # 发送多个请求
    for i in range(10):
        mock_bar = MockBarData(
            date=f"20250622 09:{30+i}:00 US/Eastern",
            open_price=150.0 + i,
            high=152.0 + i,
            low=149.0 + i,
            close=151.0 + i,
            volume=10000 * (i + 1)
        )
        
        try:
            push_realtime_bar_to_frontend(f"TEST{i}", mock_bar)
            print(f"  请求 {i+1} 已提交到线程池")
        except Exception as e:
            print(f"  请求 {i+1} 提交失败: {e}")
        
        time.sleep(0.1)  # 短暂间隔
    
    print("\n等待所有请求完成...")
    time.sleep(3)  # 等待请求完成
    
    # 检查线程池状态
    print(f"\n线程池状态:")
    print(f"  是否关闭: {_network_thread_pool._shutdown}")
    
    # 获取活跃线程数
    active_threads = threading.active_count()
    print(f"  当前活跃线程数: {active_threads}")
    
    # 列出所有线程
    print(f"\n当前线程列表:")
    for thread in threading.enumerate():
        print(f"  - {thread.name} (守护线程: {thread.daemon})")

def test_thread_pool_cleanup():
    """测试线程池清理"""
    print("\n=== 测试线程池清理 ===")
    
    from src.api.external_APIs import _cleanup_thread_pool, _network_thread_pool
    
    print("手动触发线程池清理...")
    _cleanup_thread_pool()
    
    print(f"清理后线程池状态:")
    print(f"  是否关闭: {_network_thread_pool._shutdown}")
    
    # 等待一下，让线程完全关闭
    time.sleep(1)
    
    active_threads = threading.active_count()
    print(f"  清理后活跃线程数: {active_threads}")
    
    print(f"\n清理后线程列表:")
    for thread in threading.enumerate():
        print(f"  - {thread.name} (守护线程: {thread.daemon})")

if __name__ == '__main__':
    print("开始测试线程池管理...")
    
    # 记录初始线程数
    initial_threads = threading.active_count()
    print(f"初始活跃线程数: {initial_threads}")
    
    # 运行测试
    test_thread_pool_management()
    test_thread_pool_cleanup()
    
    print("\n=== 测试完成 ===")
    print("线程池管理测试完成，请检查:")
    print("1. 线程池是否正确创建和管理")
    print("2. 网络请求是否通过线程池处理")
    print("3. 线程池是否能正确清理")
