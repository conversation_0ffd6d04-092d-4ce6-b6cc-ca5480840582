#!/usr/bin/env python3
"""
测试网络请求和线程池的具体问题

这个脚本将模拟实际的网络请求场景，找出为什么send_async函数不执行。
"""

import sys
import os
import time
import threading
import logging
import requests
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def test_network_request_directly():
    """直接测试网络请求"""
    print("=== 直接测试网络请求 ===")
    
    payload = {
        'stock_list': ['TEST1', 'TEST2'],
        'timestamp': '2025-06-22T12:00:00'
    }
    
    def send_request():
        print(f"  开始发送网络请求，线程: {threading.current_thread().name}")
        try:
            response = requests.post(
                'http://localhost:5001/api/stock_list_update',
                json=payload,
                timeout=3.0
            )
            print(f"  收到响应状态码: {response.status_code}")
            return response.status_code
        except requests.exceptions.ConnectionError as e:
            print(f"  连接错误: {e}")
            return "ConnectionError"
        except requests.exceptions.Timeout as e:
            print(f"  超时错误: {e}")
            return "Timeout"
        except Exception as e:
            print(f"  其他错误: {e}")
            return f"Error: {e}"
    
    # 直接调用
    print("直接调用网络请求:")
    result = send_request()
    print(f"直接调用结果: {result}")
    
    # 通过线程池调用
    print("\n通过线程池调用网络请求:")
    with ThreadPoolExecutor(max_workers=2, thread_name_prefix="NetworkTest") as executor:
        future = executor.submit(send_request)
        print("任务已提交到线程池")
        
        try:
            result = future.result(timeout=10)
            print(f"线程池调用结果: {result}")
        except Exception as e:
            print(f"线程池调用失败: {e}")

def test_project_network_functions():
    """测试项目中的网络函数"""
    print("\n=== 测试项目网络函数 ===")
    
    # 导入项目模块
    from src.api.external_APIs import _network_thread_pool, push_stock_list_to_frontend
    
    print(f"项目线程池状态: 关闭={_network_thread_pool._shutdown}")
    
    # 测试股票列表推送
    print("测试股票列表推送函数:")
    test_stock_list = ['TEST1', 'TEST2']
    
    try:
        push_stock_list_to_frontend(test_stock_list)
        print("股票列表推送函数调用完成")
    except Exception as e:
        print(f"股票列表推送函数调用失败: {e}")
        import traceback
        traceback.print_exc()

def test_mock_bar_data():
    """测试模拟的bar数据推送"""
    print("\n=== 测试模拟bar数据推送 ===")
    
    from src.api.external_APIs import push_realtime_bar_to_frontend, update_active_stock_list
    
    # 创建模拟的BarData
    class MockBarData:
        def __init__(self):
            self.date = "20250622 09:30:00 US/Eastern"
            self.open = 150.0
            self.high = 152.0
            self.low = 149.0
            self.close = 151.0
            self.volume = 10000
    
    # 设置活跃股票列表
    update_active_stock_list(['TEST'])
    
    mock_bar = MockBarData()
    
    print("测试实时bar数据推送:")
    try:
        push_realtime_bar_to_frontend('TEST', mock_bar)
        print("实时bar数据推送函数调用完成")
    except Exception as e:
        print(f"实时bar数据推送函数调用失败: {e}")
        import traceback
        traceback.print_exc()

def test_thread_pool_with_detailed_logging():
    """使用详细日志测试线程池"""
    print("\n=== 详细日志测试线程池 ===")
    
    def detailed_task(task_id):
        thread_name = threading.current_thread().name
        print(f"  [开始] 任务 {task_id} 在线程 {thread_name} 中开始执行")
        
        try:
            # 模拟网络请求
            print(f"  [进行] 任务 {task_id} 正在执行网络请求...")
            time.sleep(0.5)
            
            # 模拟可能的异常
            if task_id == 3:
                raise ValueError(f"任务 {task_id} 模拟异常")
            
            print(f"  [完成] 任务 {task_id} 执行成功")
            return f"任务 {task_id} 成功"
            
        except Exception as e:
            print(f"  [异常] 任务 {task_id} 执行异常: {e}")
            raise
        finally:
            print(f"  [结束] 任务 {task_id} 在线程 {thread_name} 中结束")
    
    # 创建线程池
    with ThreadPoolExecutor(max_workers=3, thread_name_prefix="DetailedTest") as executor:
        print("提交多个任务到线程池:")
        
        futures = []
        for i in range(5):
            print(f"提交任务 {i+1}")
            future = executor.submit(detailed_task, i+1)
            futures.append(future)
        
        print("等待所有任务完成...")
        
        # 逐个检查结果
        for i, future in enumerate(futures, 1):
            try:
                result = future.result(timeout=5)
                print(f"任务 {i} 结果: {result}")
            except Exception as e:
                print(f"任务 {i} 异常: {e}")

def test_thread_pool_lifecycle():
    """测试线程池生命周期"""
    print("\n=== 测试线程池生命周期 ===")
    
    from src.api.external_APIs import _network_thread_pool
    
    print(f"当前线程池状态:")
    print(f"  最大工作线程数: {_network_thread_pool._max_workers}")
    print(f"  是否关闭: {_network_thread_pool._shutdown}")
    print(f"  当前线程数: {len(_network_thread_pool._threads)}")
    print(f"  工作队列大小: {_network_thread_pool._work_queue.qsize()}")
    
    def lifecycle_task(task_id):
        print(f"  生命周期任务 {task_id} 执行中，线程: {threading.current_thread().name}")
        time.sleep(0.2)
        return f"生命周期任务 {task_id} 完成"
    
    # 提交任务
    print("提交生命周期测试任务:")
    futures = []
    for i in range(3):
        future = _network_thread_pool.submit(lifecycle_task, i+1)
        futures.append(future)
        print(f"  生命周期任务 {i+1} 已提交")
    
    # 检查线程池状态
    time.sleep(0.1)  # 让任务开始执行
    print(f"任务提交后线程池状态:")
    print(f"  当前线程数: {len(_network_thread_pool._threads)}")
    print(f"  工作队列大小: {_network_thread_pool._work_queue.qsize()}")
    
    # 等待结果
    print("等待生命周期任务完成...")
    for i, future in enumerate(futures, 1):
        try:
            result = future.result(timeout=2)
            print(f"  生命周期任务 {i} 结果: {result}")
        except Exception as e:
            print(f"  生命周期任务 {i} 失败: {e}")

if __name__ == '__main__':
    print("开始网络请求和线程池测试...")
    
    # 记录初始状态
    initial_threads = threading.active_count()
    print(f"初始活跃线程数: {initial_threads}")
    
    # 运行测试
    test_network_request_directly()
    test_project_network_functions()
    test_mock_bar_data()
    test_thread_pool_with_detailed_logging()
    test_thread_pool_lifecycle()
    
    print("\n=== 网络测试完成 ===")
    final_threads = threading.active_count()
    print(f"最终活跃线程数: {final_threads}")
    print("最终线程列表:")
    for thread in threading.enumerate():
        print(f"  - {thread.name} (守护线程: {thread.daemon}, 活跃: {thread.is_alive()})")
