#!/usr/bin/env python3
"""
测试时间解析修复的脚本

这个脚本测试修复后的时间解析功能，确保能正确处理IB API返回的时间格式。
"""

import sys
import os
import requests
import time
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_time_parsing_fix():
    """测试时间解析修复"""
    print("=== 测试时间解析修复 ===")
    
    # 测试用例：模拟IB API返回的各种时间格式
    test_cases = [
        {
            'name': 'IB API标准格式',
            'time': '20250620 08:16:00 US/Eastern',
            'description': '标准IB API时间格式'
        },
        {
            'name': 'IB API纽约时区格式',
            'time': '20250620 09:30:00 America/New_York',
            'description': 'IB API纽约时区格式'
        },
        {
            'name': '无时区格式',
            'time': '20250620 10:45:00',
            'description': '无时区信息的格式'
        },
        {
            'name': '时间戳格式',
            'time': 1719747360,  # 数字时间戳
            'description': 'Unix时间戳格式'
        },
        {
            'name': 'ISO格式',
            'time': '2025-06-20T14:30:00',
            'description': 'ISO 8601格式'
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases):
        print(f"\n--- 测试案例 {i+1}: {test_case['name']} ---")
        print(f"时间格式: {test_case['time']}")
        print(f"描述: {test_case['description']}")
        
        # 构造测试数据
        test_bar_data = {
            'time': test_case['time'],
            'open': 150.0 + i,
            'high': 152.0 + i,
            'low': 149.0 + i,
            'close': 151.0 + i,
            'volume': 10000 * (i + 1)
        }
        
        payload = {
            'stock_code': f'TEST{i+1}',
            'bar_data': test_bar_data,
            'data_type': 'realtime'
        }
        
        try:
            # 发送测试请求
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=payload,
                timeout=5.0
            )
            
            if response.status_code == 200:
                print(f"✅ {test_case['description']} - 推送成功")
                success_count += 1
            else:
                print(f"❌ {test_case['description']} - 推送失败: 状态码 {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"⚠️ {test_case['description']} - 连接失败（前端服务可能未启动）")
        except Exception as e:
            print(f"❌ {test_case['description']} - 错误: {e}")
        
        # 间隔一下，避免请求过快
        time.sleep(0.5)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！时间解析修复成功。")
    elif success_count > 0:
        print("⚠️ 部分测试通过，可能需要进一步检查。")
    else:
        print("❌ 所有测试都失败了，需要检查修复代码。")

def test_log_reduction():
    """测试日志减少功能"""
    print("\n=== 测试日志减少功能 ===")
    
    # 发送多个相同的错误请求，测试日志是否被正确减少
    print("发送多个相同的错误请求，测试日志减少...")
    
    # 构造一个会导致错误的请求（缺少必要字段）
    invalid_payload = {
        'stock_code': 'TEST_LOG',
        # 故意缺少 bar_data 字段
    }
    
    for i in range(5):
        try:
            response = requests.post(
                'http://localhost:5001/api/realtime_bar_update',
                json=invalid_payload,
                timeout=2.0
            )
            print(f"请求 {i+1}: 状态码 {response.status_code}")
        except Exception as e:
            print(f"请求 {i+1}: 异常 {type(e).__name__}")
        
        time.sleep(0.2)
    
    print("✅ 日志减少测试完成，请检查日志文件确认重复日志是否被减少。")

if __name__ == '__main__':
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    print("开始测试时间解析修复...")
    print("注意：需要确保前端服务（localhost:5001）正在运行")
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 运行测试
    test_time_parsing_fix()
    test_log_reduction()
    
    print("\n测试完成！")
