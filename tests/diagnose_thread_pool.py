#!/usr/bin/env python3
"""
深度诊断ThreadPoolExecutor问题的脚本

这个脚本将详细分析ThreadPoolExecutor为什么不执行提交的任务。
"""

import sys
import os
import time
import threading
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def test_basic_thread_pool():
    """测试基本的ThreadPoolExecutor功能"""
    print("=== 测试基本ThreadPoolExecutor功能 ===")
    
    def simple_task(task_id):
        print(f"  任务 {task_id} 开始执行，线程: {threading.current_thread().name}")
        time.sleep(0.5)
        print(f"  任务 {task_id} 执行完成")
        return f"任务 {task_id} 结果"
    
    # 创建新的线程池
    with ThreadPoolExecutor(max_workers=3, thread_name_prefix="TestPool") as executor:
        print(f"线程池创建成功，最大工作线程数: {executor._max_workers}")
        
        # 提交任务
        futures = []
        for i in range(5):
            future = executor.submit(simple_task, i+1)
            futures.append(future)
            print(f"任务 {i+1} 已提交")
        
        print("等待任务完成...")
        
        # 等待所有任务完成
        for i, future in enumerate(as_completed(futures), 1):
            try:
                result = future.result(timeout=5)
                print(f"收到结果 {i}: {result}")
            except Exception as e:
                print(f"任务 {i} 执行失败: {e}")
        
        print(f"当前活跃线程数: {threading.active_count()}")
        print("当前线程列表:")
        for thread in threading.enumerate():
            print(f"  - {thread.name} (守护线程: {thread.daemon}, 活跃: {thread.is_alive()})")

def test_project_thread_pool():
    """测试项目中的线程池"""
    print("\n=== 测试项目线程池 ===")
    
    # 导入项目模块
    from src.api.external_APIs import _network_thread_pool
    
    print(f"项目线程池状态:")
    print(f"  最大工作线程数: {_network_thread_pool._max_workers}")
    print(f"  线程名前缀: {_network_thread_pool._thread_name_prefix}")
    print(f"  是否关闭: {_network_thread_pool._shutdown}")
    
    # 检查线程池内部状态
    print(f"  线程池内部状态:")
    print(f"    _threads: {len(_network_thread_pool._threads) if hasattr(_network_thread_pool, '_threads') else 'N/A'}")
    print(f"    _work_queue: {_network_thread_pool._work_queue.qsize() if hasattr(_network_thread_pool, '_work_queue') else 'N/A'}")
    
    def test_task(task_id):
        print(f"  项目线程池任务 {task_id} 开始执行，线程: {threading.current_thread().name}")
        time.sleep(0.2)
        print(f"  项目线程池任务 {task_id} 执行完成")
        return f"项目任务 {task_id} 结果"
    
    # 提交任务到项目线程池
    futures = []
    for i in range(3):
        try:
            future = _network_thread_pool.submit(test_task, i+1)
            futures.append(future)
            print(f"项目任务 {i+1} 已提交")
        except Exception as e:
            print(f"提交项目任务 {i+1} 失败: {e}")
            traceback.print_exc()
    
    print("等待项目任务完成...")
    time.sleep(2)
    
    # 检查任务结果
    for i, future in enumerate(futures, 1):
        try:
            if future.done():
                result = future.result(timeout=0.1)
                print(f"项目任务 {i} 完成: {result}")
            else:
                print(f"项目任务 {i} 未完成")
        except Exception as e:
            print(f"获取项目任务 {i} 结果失败: {e}")
    
    print(f"测试后活跃线程数: {threading.active_count()}")
    print("测试后线程列表:")
    for thread in threading.enumerate():
        print(f"  - {thread.name} (守护线程: {thread.daemon}, 活跃: {thread.is_alive()})")

def test_thread_pool_recreation():
    """测试线程池重新创建"""
    print("\n=== 测试线程池重新创建 ===")
    
    from src.api.external_APIs import _network_thread_pool
    
    # 关闭现有线程池
    print("关闭现有线程池...")
    _network_thread_pool.shutdown(wait=True)
    print(f"线程池关闭状态: {_network_thread_pool._shutdown}")
    
    # 重新创建线程池
    print("重新创建线程池...")
    import src.api.external_APIs as external_apis
    external_apis._network_thread_pool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="NewNetworkAPI")
    
    new_pool = external_apis._network_thread_pool
    print(f"新线程池状态:")
    print(f"  最大工作线程数: {new_pool._max_workers}")
    print(f"  线程名前缀: {new_pool._thread_name_prefix}")
    print(f"  是否关闭: {new_pool._shutdown}")
    
    def new_test_task(task_id):
        print(f"  新线程池任务 {task_id} 开始执行，线程: {threading.current_thread().name}")
        time.sleep(0.2)
        print(f"  新线程池任务 {task_id} 执行完成")
        return f"新任务 {task_id} 结果"
    
    # 提交任务到新线程池
    futures = []
    for i in range(3):
        try:
            future = new_pool.submit(new_test_task, i+1)
            futures.append(future)
            print(f"新任务 {i+1} 已提交")
        except Exception as e:
            print(f"提交新任务 {i+1} 失败: {e}")
    
    print("等待新任务完成...")
    time.sleep(2)
    
    # 检查任务结果
    for i, future in enumerate(futures, 1):
        try:
            if future.done():
                result = future.result(timeout=0.1)
                print(f"新任务 {i} 完成: {result}")
            else:
                print(f"新任务 {i} 未完成")
        except Exception as e:
            print(f"获取新任务 {i} 结果失败: {e}")

def test_thread_pool_with_exception():
    """测试线程池异常处理"""
    print("\n=== 测试线程池异常处理 ===")
    
    def failing_task(task_id):
        print(f"  失败任务 {task_id} 开始执行，线程: {threading.current_thread().name}")
        if task_id == 2:
            raise ValueError(f"任务 {task_id} 故意失败")
        time.sleep(0.2)
        print(f"  失败任务 {task_id} 执行完成")
        return f"失败任务 {task_id} 结果"
    
    with ThreadPoolExecutor(max_workers=2, thread_name_prefix="ExceptionTest") as executor:
        futures = []
        for i in range(4):
            future = executor.submit(failing_task, i+1)
            futures.append(future)
            print(f"失败测试任务 {i+1} 已提交")
        
        print("等待失败测试任务完成...")
        
        for i, future in enumerate(futures, 1):
            try:
                result = future.result(timeout=2)
                print(f"失败测试任务 {i} 成功: {result}")
            except Exception as e:
                print(f"失败测试任务 {i} 异常: {e}")

if __name__ == '__main__':
    print("开始ThreadPoolExecutor深度诊断...")
    
    # 记录初始状态
    initial_threads = threading.active_count()
    print(f"初始活跃线程数: {initial_threads}")
    print("初始线程列表:")
    for thread in threading.enumerate():
        print(f"  - {thread.name} (守护线程: {thread.daemon})")
    
    # 运行测试
    test_basic_thread_pool()
    test_project_thread_pool()
    test_thread_pool_recreation()
    test_thread_pool_with_exception()
    
    print("\n=== 诊断完成 ===")
    final_threads = threading.active_count()
    print(f"最终活跃线程数: {final_threads}")
    print("最终线程列表:")
    for thread in threading.enumerate():
        print(f"  - {thread.name} (守护线程: {thread.daemon}, 活跃: {thread.is_alive()})")
