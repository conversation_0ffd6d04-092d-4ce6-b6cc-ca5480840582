#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
StockWatchApp测试模块

该模块用于测试股票监控应用程序的GUI界面，使用模拟数据运行完整窗口。
测试内容包括：
1. 创建主窗口
2. 添加模拟股票数据
3. 测试闪烁功能
4. 测试其他GUI组件功能
"""

# 在导入任何其他模块之前，先设置模拟
import sys
import os
from unittest.mock import MagicMock, patch

# 添加src目录到Python路径，这样可以直接导入src下的模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

# 模拟数据库连接和其他外部依赖
sys.modules['mysql.connector'] = MagicMock()
sys.modules['mysql.connector.pooling'] = MagicMock()
sys.modules['mysql.connector.errors'] = MagicMock()

# 模拟 manager_res_dict_test 和 number_of_transactions_dict
mock_manager_res_dict = {}
mock_number_of_transactions_dict = {}

# 应用模拟到 api.get_stock_price_ibapi 模块
sys.modules['api.get_stock_price_ibapi'] = MagicMock()
sys.modules['api.get_stock_price_ibapi'].manager_res_dict_test = mock_manager_res_dict
sys.modules['api.get_stock_price_ibapi'].number_of_transactions_dict = mock_number_of_transactions_dict

# 模拟 AppHelper 和 NSEvent 等 macOS 特定模块
sys.modules['AppKit'] = MagicMock()
sys.modules['AppKit.NSEvent'] = MagicMock()
sys.modules['PyObjCTools.AppHelper'] = MagicMock()

# 现在导入其他模块
import time
import tkinter as tk
import threading
import logging
from multiprocessing import Manager

# 导入常量和实际的key_generator模块
from utils.constants import (
    NOW_PRICE, 
    VOLUME, 
    VOLUME_AVG, 
    NEWS, 
    SIGNAL_STRENGTH,
    SIGNAL_STRONG_BUY,
    TRANSACTION_RANK,
    TRANSACTION_VOLUME,
    IS_SPREAD_NORMAL,
    STOCK_SHARESOUTSTANDING,
    BEHAVIOR_SCORE,
    BEHAVIOR_SCORE_15MIN,
    BREAKOUT_FLG,
    SET_FLG,
    CHECK_VOLUME
)

# 导入实际的key_generator
from utils.key_generator import key_generator
from utils.config import STOCK_THRESHOLDS

# 使用模拟替代实际的模块
with patch('utils.screenshot.get_stock_code') as mock_get_stock_code:
    mock_get_stock_code.return_value = 'MOCK'
    from utils.screenshot import get_stock_code

# 使用模拟替代 StockWatchApp 中的外部依赖
with patch('gui.stock_watch_app.NSEvent') as mock_NSEvent, \
     patch('gui.stock_watch_app.AppHelper') as mock_AppHelper:
    from gui.stock_watch_app import StockWatchApp
    from gui.gui_components import StockFrame

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 重写logger的info、error等方法，改为使用print输出
def print_log(level, message):
    print(f"[{level}] {time.strftime('%Y-%m-%d %H:%M:%S')} - {message}")

logger.info = lambda msg: print_log("INFO", msg)
logger.error = lambda msg, *args, **kwargs: print_log("ERROR", msg)
logger.warning = lambda msg: print_log("WARNING", msg)
logger.debug = lambda msg: print_log("DEBUG", msg)

# 模拟 MarketDataManager 类中使用的外部依赖
class MockKeyGenerator:
    @staticmethod
    def get_keys_for_stock(stock_code):
        return {
            NOW_PRICE: f"{stock_code}_now_price",
            VOLUME: f"{stock_code}_volume",
            VOLUME_AVG: f"{stock_code}_volume_avg",
            NEWS: f"{stock_code}_news",
            SET_FLG: f"{stock_code}_set_flg",
            STOCK_SHARESOUTSTANDING: f"{stock_code}_shares",
            CHECK_VOLUME: f"{stock_code}_check_volume",
            IS_SPREAD_NORMAL: f"{stock_code}_is_spread_normal",
            BREAKOUT_FLG: f"{stock_code}_breakout_flg",
            SIGNAL_STRENGTH: f"{stock_code}_signal_strength",
        }

# 替换实际的 key_generator
key_generator = MockKeyGenerator()

# 导入真实的MarketDataManager
from core.market_data_manager import MarketDataManager

class MockMarketDataManager(MarketDataManager):
    """扩展真实的MarketDataManager类，只模拟需要外部API的方法"""
    
    def __init__(self, shared_dict):
        """初始化模拟市场数据管理器
        
        Args:
            shared_dict: 进程间共享的字典
        """
        super().__init__(shared_dict)
        self.active_stocks = ['TGLD', 'NBCA', 'USEH', 'KNW']
        self.market_data = {
            'TGLD': {
                NOW_PRICE: 15.5,
                VOLUME: 1500000,
                VOLUME_AVG: 500000,
                NEWS: ['公司业绩良好', '新产品发布'],
                SIGNAL_STRENGTH: 'BUY',
                TRANSACTION_RANK: 1,
                TRANSACTION_VOLUME: 1200,
                IS_SPREAD_NORMAL: True,
                STOCK_SHARESOUTSTANDING: 5000000,
                BEHAVIOR_SCORE: 8.5,
                BEHAVIOR_SCORE_15MIN: 7.8,
                BREAKOUT_FLG: True
            },
            'NBCA': {
                NOW_PRICE: 22.8,
                VOLUME: 2500000,
                VOLUME_AVG: 300000,
                NEWS: ['季度财报超预期'],
                SIGNAL_STRENGTH: SIGNAL_STRONG_BUY,  # 测试闪烁功能
                TRANSACTION_RANK: 2,
                TRANSACTION_VOLUME: 980,
                IS_SPREAD_NORMAL: True,
                STOCK_SHARESOUTSTANDING: 8000000,
                BEHAVIOR_SCORE: 9.2,
                BEHAVIOR_SCORE_15MIN: 8.7,
                BREAKOUT_FLG: False
            },
            'USEH': {
                NOW_PRICE: 35.6,
                VOLUME: 800000,
                VOLUME_AVG: 600000,
                NEWS: [],
                SIGNAL_STRENGTH: 'WATCH',
                TRANSACTION_RANK: 3,
                TRANSACTION_VOLUME: 650,
                IS_SPREAD_NORMAL: False,
                STOCK_SHARESOUTSTANDING: 3000000,
                BEHAVIOR_SCORE: 6.5,
                BEHAVIOR_SCORE_15MIN: 5.9,
                BREAKOUT_FLG: False
            },
            'KNW': {
                NOW_PRICE: 42.1,
                VOLUME: 3500000,
                VOLUME_AVG: 2800000,
                NEWS: ['战略合作协议签署', 'FDA批准新药'],
                SIGNAL_STRENGTH: 'AVOID',
                TRANSACTION_RANK: 0,
                TRANSACTION_VOLUME: 320,
                IS_SPREAD_NORMAL: True,
                STOCK_SHARESOUTSTANDING: 12000000,
                BEHAVIOR_SCORE: 4.2,
                BEHAVIOR_SCORE_15MIN: 3.8,
                BREAKOUT_FLG: False
            }
        }
        
    def get_active_stocks(self):
        """获取活跃股票列表"""
        return self.active_stocks
        
    def get_market_data(self, stock_code):
        """获取指定股票的市场数据"""
        return self.market_data.get(stock_code, {})
        
    @staticmethod
    def get_current_price(stock_code):
        """获取股票当前价格 - 模拟外部API调用"""
        return mock_manager_res_dict.get(f"{stock_code}_now_price", 0)


def test_blinking_functionality(app, stock_code, mock_manager):
    """测试闪烁功能
    
    直接测试闪烁功能是否正常工作
    
    Args:
        app: StockWatchApp实例
        stock_code: 要测试的股票代码
        mock_manager: 模拟数据管理器实例
    """
    print(f"[INFO] 测试股票 {stock_code} 的闪烁功能...")
    
    # 获取股票框架
    frame = app.stock_frames.get(stock_code)
    if not frame:
        print(f"[ERROR] 找不到股票 {stock_code} 的框架")
        return
    
    # 测试闪烁启动
    print("[INFO] 测试启动闪烁...")
    frame.start_blinking('signal_strength')
    
    # 验证闪烁状态
    is_blinking = 'signal_strength' in frame._blinking
    print(f"[INFO] 闪烁状态: {'活跃' if is_blinking else '未活跃'}")
    
    # 等待一段时间
    time.sleep(2)
    
    # 测试停止闪烁
    print("[INFO] 测试停止闪烁...")
    frame.stop_blinking('signal_strength')
    
    # 验证闪烁状态
    is_blinking = 'signal_strength' in frame._blinking
    print(f"[INFO] 闪烁状态: {'活跃' if is_blinking else '未活跃'}")
    
    return is_blinking == False  # 确认已停止闪烁


def test_update_stock_indicators(app, stock_code, mock_manager):
    """测试更新股票指标功能
    
    直接调用_update_stock_indicators方法，测试指标更新功能
    
    Args:
        app: StockWatchApp实例
        stock_code: 要测试的股票代码
        mock_manager: 模拟数据管理器实例
    """
    print(f"[INFO] 测试更新股票 {stock_code} 的指标...")
    
    # 获取股票框架
    frame = app.stock_frames.get(stock_code)
    if not frame:
        print(f"[ERROR] 找不到股票 {stock_code} 的框架")
        return
    
    # 记录更新前的状态
    before_signal_text = frame.components['signal_strength'].cget("text")
    
    # 更改信号强度
    current_signal = mock_manager.market_data[stock_code].get(SIGNAL_STRENGTH, '')
    new_signal = SIGNAL_STRONG_BUY if current_signal != SIGNAL_STRONG_BUY else 'BUY'
    mock_manager.market_data[stock_code][SIGNAL_STRENGTH] = new_signal
    
    # 调用更新方法
    app._update_stock_indicators(stock_code, frame)
    
    # 记录更新后的状态
    after_signal_text = frame.components['signal_strength'].cget("text")
    
    # 检查是否正确更新
    print(f"[INFO] 信号更新前: {before_signal_text}")
    print(f"[INFO] 信号更新后: {after_signal_text}")
    print(f"[INFO] 信号强度: {new_signal}")
    
    # 检查闪烁状态
    is_blinking = 'signal_strength' in frame._blinking
    print(f"[INFO] 闪烁状态: {'活跃' if is_blinking else '未活跃'}")
    
    # 验证STRONG_BUY信号是否触发闪烁
    if new_signal == SIGNAL_STRONG_BUY:
        return is_blinking
    else:
        return not is_blinking


def test_stock_watch_app():
    """测试StockWatchApp的主函数"""
    try:
        # 创建Manager用于进程间共享数据
        manager = Manager()
        shared_dict = manager.dict()
        
        # 使用实际的声音文件路径
        # 如果文件不存在，可以使用一个默认的系统声音文件
        sound_warn = "/System/Library/Sounds/Ping.aiff"  # macOS系统声音
        
        # 创建应用程序实例
        app = StockWatchApp(sound_warn, shared_dict)
        
        # 替换数据管理器为模拟版本
        mock_manager = MockMarketDataManager(shared_dict)
        app.data_manager = mock_manager
        
        # 添加一些测试数据
        for stock_code in mock_manager.active_stocks:
            # 设置一些初始买入价格
            mock_manager.update_buy_price(stock_code, mock_manager.get_current_price(stock_code) * 0.9)
        
        # 添加线程终止标志
        stop_threads = threading.Event()
        
        # 创建测试线程
        def run_tests():
            # 等待GUI初始化完成
            time.sleep(3)
            print("[INFO] 开始运行测试...")
            
            # 测试闪烁功能
            test_blinking_functionality(app, 'NBCA', mock_manager)
            
            # 测试更新股票指标
            test_update_stock_indicators(app, 'TGLD', mock_manager)
            test_update_stock_indicators(app, 'NBCA', mock_manager)
            
            # 测试key_generator功能
            print("[INFO] 测试key_generator功能...")
            for stock_code in mock_manager.active_stocks:
                keys = key_generator.get_keys_for_stock(stock_code)
                print(f"[INFO] 股票 {stock_code} 的SET_FLG键: {keys[SET_FLG]}")
            
            # 测试条件检查
            print("[INFO] 测试股票条件检查...")
            for stock_code in mock_manager.active_stocks:
                mock_manager.check_stock_conditions(stock_code, mock_manager.get_market_data(stock_code))
                is_condition_met = mock_manager.all_conditions.get(stock_code, False)
                is_high_condition_met = mock_manager.high_all_conditions.get(stock_code, False)
                print(f"[INFO] 股票 {stock_code} 条件满足: {is_condition_met}, 高优先级条件满足: {is_high_condition_met}")
            
            # 测试完成后，让GUI继续运行一段时间
            time.sleep(10)
            print("[INFO] 测试完成")
            
        # 启动测试线程
        test_thread = threading.Thread(target=run_tests, daemon=True)
        test_thread.start()
        
        # 启动定时器，每15秒更新一次STRONG_BUY信号的股票，测试闪烁功能
        def toggle_signal_strength():
            while not stop_threads.is_set():
                time.sleep(15)
                if stop_threads.is_set():
                    break
                print("[INFO] 切换股票信号强度...")
                # 在不同股票之间切换STRONG_BUY信号
                for stock_code in mock_manager.active_stocks:
                    current_signal = mock_manager.market_data[stock_code].get(SIGNAL_STRENGTH, '')
                    if current_signal == SIGNAL_STRONG_BUY:
                        print(f"[INFO] 将股票 {stock_code} 的信号从 STRONG_BUY 切换为 BUY")
                        mock_manager.market_data[stock_code][SIGNAL_STRENGTH] = 'BUY'
                    else:
                        print(f"[INFO] 将股票 {stock_code} 的信号切换为 STRONG_BUY")
                        mock_manager.market_data[stock_code][SIGNAL_STRENGTH] = SIGNAL_STRONG_BUY
                    # 只切换一个股票，然后退出循环
                    break
                
        # 启动信号切换线程
        signal_thread = threading.Thread(target=toggle_signal_strength, daemon=True)
        signal_thread.start()
        
        # 保存线程引用，便于后续清理
        app.test_threads = [test_thread, signal_thread]
        app.stop_threads_event = stop_threads
        
        # 重写应用程序的关闭方法，确保线程正确关闭
        original_destroy = app.window.destroy
        def destroy_override():
            print("[INFO] 正在关闭应用程序和所有线程...")
            # 设置线程停止标志
            stop_threads.set()
            # 等待线程结束
            for thread in app.test_threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)
            # 关闭Manager
            if hasattr(manager, 'shutdown'):
                manager.shutdown()
            # 调用原始的destroy方法
            original_destroy()
        
        # 替换应用程序的destroy方法
        app.window.destroy = destroy_override
        
        # 运行应用程序
        print("[INFO] 启动StockWatchApp测试...")
        app.run()
        
    except Exception as e:
        print(f"[ERROR] 测试过程中发生错误: {e}")
        import traceback
        print(traceback.format_exc())
        
if __name__ == "__main__":
    test_stock_watch_app() 