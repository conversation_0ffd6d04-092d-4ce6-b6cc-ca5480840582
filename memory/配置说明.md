# Augment MCP 知识图谱配置说明

## 配置步骤

### 方法一：通过Augment Settings Panel（推荐）

1. **打开设置面板**：
   - 点击Augment面板右上角的齿轮图标 ⚙️
   - 找到"MCP servers"部分

2. **添加服务器**：
   - 点击MCP标题旁的 `+` 按钮
   - 填写以下信息：
     - **Name**: `knowledge-graph`
     - **Command**: `npx`
     - **Args**: 
       ```
       -y
       mcp-knowledge-graph
       --memory-path
       /Users/<USER>/git/stock_xxx/memory/augment_memory.jsonl
       ```

### 方法二：编辑settings.json文件

1. **打开设置**：
   - 按 `Cmd + Shift + P` (macOS) 或 `Ctrl + Shift + P` (Windows)
   - 选择 "Edit Settings"
   - 在Advanced下点击 "Edit in settings.json"

2. **添加配置**：
   将 `augment_settings_mcp_config.json` 文件中的内容添加到你的settings.json中

   **正确格式**（包含自动批准）：
   ```json
   {
     "mcpServers": {
       "knowledge-graph": {
         "command": "npx",
         "args": [
           "-y",
           "mcp-knowledge-graph",
           "--memory-path",
           "/Users/<USER>/git/stock_xxx/memory/augment_memory.jsonl"
         ],
         "autoapprove": [
           "create_entities",
           "create_relations",
           "add_observations",
           "delete_entities",
           "delete_observations",
           "delete_relations",
           "read_graph",
           "search_nodes",
           "open_nodes"
         ]
       }
     }
   }
   ```

## 配置完成后

1. **重启编辑器**：配置完成后需要重启VSCode/Cursor
2. **验证Node.js**：确保已安装Node.js (`node --version`)
3. **测试功能**：重启后AI助手就能使用知识图谱记忆功能

## 记忆文件位置

- **完整路径**: `/Users/<USER>/git/stock_xxx/memory/augment_memory.jsonl`
- **相对路径**: `./memory/augment_memory.jsonl`

## 故障排除

如果配置不生效：
1. 检查Node.js是否正确安装
2. 验证文件路径是否正确
3. 确认settings.json语法正确（注意逗号和括号）
4. 重启编辑器
5. 查看Augment日志中的错误信息

## 自动化配置

### 1. 自动批准MCP工具调用
配置中的 `autoapprove` 数组应该能让记忆更新无需手动点击"run"按钮。如果仍需要手动确认：
- 检查Augment Settings中是否有全局自动批准选项
- 重启编辑器确保配置生效
- 如果问题持续，可能需要联系Augment支持

### 2. 每次回答结束时的确认机制
AI助手应该在每次回答结束时调用 `interactive_feedback` 工具进行确认。这需要：
- AI助手养成在回答最后调用该工具的习惯
- 确保不会在循环中重复调用
- 只在真正需要用户反馈时调用

## 功能说明

配置成功后，AI助手将能够：
- 记住你的技术偏好和项目信息
- 跨对话保持上下文连续性
- 学习你的编程习惯和决策模式
- 提供更个性化的建议和解决方案
- 自动更新记忆（如果自动批准配置成功）
- 每次回答结束时主动寻求确认
