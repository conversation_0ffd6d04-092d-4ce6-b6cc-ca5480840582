{"type":"entity","name":"atreeoht","entityType":"developer","observations":["使用Python 3.10.14开发环境","在虚拟环境/Users/<USER>/venv_stock/bin/python中工作","使用MacBook Pro M1 16GB内存 1TSSD","偏好使用中文简体进行交流","强调在修改前必须阅读官方文档","喜欢使用Tailwind CSS进行样式设计","希望MCP记忆更新无需手动点击run按钮","要求AI助手每次回答结束时通过feedback确认","重视自动化和交互体验的优化","在进行复杂功能添加和修改前一定要先确认方案","认为蜡烛柱更新问题可能是临界点问题","当到达下一根蜡烛柱时，上一根的最新价格没有被更新","强调需要通过feedback确认方案","修改被撤回，认为上一根K线最后价格就在web_stock_data里面","提到有很多次聚会（可能是指聚合函数调用）","对之前的复杂解决方案不满意"]}
{"type":"entity","name":"stock_xxx_project","entityType":"project","observations":["实时股票图表系统项目","使用Flask + IB API + LightWeight Charts技术栈","显示5分钟K线图的实时股票数据","支持多股票显示功能","图表时间轴显示纽约时间","项目结构以src目录为主要位置","位于/Users/<USER>/git/stock_xxx路径","蜡烛柱更新问题：收盘价和实际价格不一致","可能是更新频率问题导致价格不是最新的","需要检查实时数据聚合和推送机制","前端使用candleSeries.update()方法更新蜡烛柱","后端使用resample_to_5min()进行5分钟K线聚合","已实施蜡烛柱更新修复方案","后端添加了最新价格跟踪功能：update_latest_price()","新增API端点：/latest_prices和/latest_price/<stock_code>","前端修改了aggregateTo5MinData()函数支持临界点修复","在创建新蜡烛柱时会用最新实时价格更新上一根蜡烛柱的收盘价"]}
{"type":"entity","name":"IB_API","entityType":"data_source","observations":["Interactive Brokers API","提供实时股票数据","时间格式：20241223 09:30:00 US/Eastern","数据为纽约时间（不需要减1分钟）","用于获取历史数据和实时tick数据"]}
{"type":"entity","name":"database_tables","entityType":"data_source","observations":["stock_trand.stock_price_ibkr表","stock_trand.stock_price_ibkr_120表","date_time字段时间为纽约时间+1分钟","需要减1分钟处理（既有逻辑保持不变）","时间格式：2025-05-18 08:38:00"]}
{"type":"entity","name":"TradingView_LightWeight_Charts","entityType":"frontend_library","observations":["用于显示股票图表的前端库","支持蜡烛图和EMA指标","官方CDN：https://unpkg.com/lightweight-charts@5.1.0/dist/lightweight-charts.esm.js","使用ES6模块导入方式","需要参考官方文档进行修改"]}
{"type":"entity","name":"EMA_indicators","entityType":"technical_indicator","observations":["EMA9指标，颜色#ff9800，默认宽度","EMA20指标，颜色#f321e5，中等宽度","EMA120指标，颜色#41f321，大宽度","使用开盘价计算","用户偏好不修改EMA参数","保持原始EMA120设置"]}
{"type":"entity","name":"MCP_knowledge_graph","entityType":"tool","observations":["用于AI助手持久化记忆的工具","通过实体、关系、观察记录构建知识图谱","配置在Augment Settings中","记忆文件位置：/Users/<USER>/git/stock_xxx/memory/augment_memory.jsonl","支持跨对话保持上下文连续性","已配置autoapprove参数尝试自动批准工具调用","需要AI助手在每次回答结束时调用interactive_feedback","配置文件已更新包含完整的自动批准列表"]}
{"type":"relation","from":"atreeoht","to":"stock_xxx_project","relationType":"develops"}
{"type":"relation","from":"stock_xxx_project","to":"IB_API","relationType":"uses"}
{"type":"relation","from":"stock_xxx_project","to":"database_tables","relationType":"stores_data_in"}
{"type":"relation","from":"stock_xxx_project","to":"TradingView_LightWeight_Charts","relationType":"displays_with"}
{"type":"relation","from":"atreeoht","to":"TradingView_LightWeight_Charts","relationType":"prefers_official_docs_for"}
{"type":"relation","from":"stock_xxx_project","to":"EMA_indicators","relationType":"displays"}
{"type":"relation","from":"atreeoht","to":"EMA_indicators","relationType":"prefers_specific_styling_for"}
{"type":"relation","from":"atreeoht","to":"MCP_knowledge_graph","relationType":"uses"}
{"type":"relation","from":"MCP_knowledge_graph","to":"stock_xxx_project","relationType":"remembers_context_for"}