[{"mcpServers": {"knowledge-graph": {"command": "npx", "args": ["-y", "mcp-knowledge-graph", "--memory-path", "/Users/<USER>/git/stock_xxx/memory/augment_memory.jsonl"], "autoapprove": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"]}}}, {"mcpServers": {"browser-tools": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@1.2.0"], "autoapprove": ["browser_navigate", "browser_click", "browser_type", "browser_take_screenshot", "browser_snapshot", "browser_wait_for", "browser_select_option", "browser_hover", "browser_drag", "browser_press_key", "browser_file_upload", "browser_handle_dialog", "browser_console_messages", "browser_network_requests", "browser_tab_new", "browser_tab_select", "browser_tab_close", "browser_tab_list", "browser_close", "browser_resize", "browser_pdf_save", "browser_install", "browser_generate_playwright_test"]}}}]