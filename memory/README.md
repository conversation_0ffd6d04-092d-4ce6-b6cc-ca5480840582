# Augment MCP 知识图谱记忆目录

这个目录用于存储Augment MCP知识图谱工具的记忆文件。

## 文件说明

- `augment_memory.jsonl`: 主要的知识图谱记忆文件，包含：
  - 项目实体（人员、组织、项目、技术栈等）
  - 实体间的关系
  - 观察记录（偏好、决策、学习过程等）

## 数据结构

### 实体 (Entities)
```json
{
  "type": "entity",
  "name": "实体名称",
  "entityType": "实体类型",
  "observations": ["观察记录1", "观察记录2"]
}
```

### 关系 (Relations)
```json
{
  "type": "relation", 
  "from": "源实体",
  "to": "目标实体",
  "relationType": "关系类型"
}
```

## 使用建议

1. 定期备份记忆文件
2. 保持数据结构的一致性
3. 使用描述性的实体名称和关系类型
4. 及时更新过时的观察记录
