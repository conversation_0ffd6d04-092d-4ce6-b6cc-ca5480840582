#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import signal
import sys
import os
import subprocess
from typing import Optional, Tuple
from dataclasses import dataclass
from faster_whisper import WhisperModel

@dataclass
class TranscribeConfig:
    model_size: str = "large-v3"
    device: str = "cpu"
    compute_type: str = "int8"
    cpu_threads: int = 6
    language: str = "ja"
    temperature: float = 0.0
    beam_size: int = 5
    best_of: int = 5
    vad_filter: bool = True
    initial_prompt: str = "これは日本語の会話です。"
    word_timestamps: bool = False
    download_root: str = "./whisper_models"

class TimeoutException(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutException("转录超时：任务被中断")

def format_time(seconds: float) -> str:
    """将秒数转换为 SRT 格式的时间字符串

    Args:
        seconds: 秒数（可以是小数）

    Returns:
        str: 格式化的时间字符串，如 "00:00:01,500"
    """
    millisec = int(round(seconds * 1000))
    h = millisec // 3600000
    m = (millisec % 3600000) // 60000
    s = (millisec % 60000) // 1000
    ms = millisec % 1000
    return f"{h:02}:{m:02}:{s:02},{ms:03}"

def extract_audio(input_path: str, output_audio: str = "temp_audio.wav") -> bool:
    """使用 FFmpeg 提取并修正音频时间轴

    Args:
        input_path: 输入视频文件路径
        output_audio: 输出音频文件路径

    Returns:
        bool: 提取是否成功
    """
    if not os.path.exists(input_path):
        print(f"❌ 输入文件不存在: {input_path}")
        return False

    ffmpeg_cmd = [
        "ffmpeg",
        "-i", input_path,
        "-map", "0:a:0",
        "-c:a", "pcm_s16le",
        "-ar", "16000",
        "-ac", "1",
        "-fflags", "+genpts",
        "-avoid_negative_ts", "make_zero",
        "-y",  # 覆盖已有文件
        output_audio
    ]
    try:
        print(f"🔧 正在提取音频: {output_audio}")
        # 将错误输出重定向到 PIPE
        result = subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
        if result.stderr:
            print(f"⚠️ FFmpeg 警告信息:\n{result.stderr}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 音频提取失败: {e}")
        if e.stderr:
            print(f"错误详情:\n{e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def transcribe_audio_safe(
    audio_path: str,
    output_srt: str = "output.srt",
    timeout_minutes: int = 45,
    config: Optional[TranscribeConfig] = None
) -> Tuple[bool, float]:
    """安全地转录音频文件

    Args:
        audio_path: 输入音频文件路径
        output_srt: 输出字幕文件路径
        timeout_minutes: 超时时间（分钟）
        config: 转录配置参数

    Returns:
        Tuple[bool, float]: (是否成功, 处理时间)
    """
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return False, 0

    if config is None:
        config = TranscribeConfig()

    print(f"🟢 准备转录：{audio_path}")
    print(f"⚙️ 配置信息：模型={config.model_size}, 设备={config.device}, 计算类型={config.compute_type}")
    
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(timeout_minutes * 60)

    try:
        t0 = time.time()
        print("⏳ 加载模型中...")
        model = WhisperModel(
            config.model_size,
            device=config.device,
            compute_type=config.compute_type,
            cpu_threads=config.cpu_threads,
            download_root=config.download_root
        )
        print("✅ 模型加载完成，开始转录...")

        segments, info = model.transcribe(
            audio_path,
            language=config.language,
            temperature=config.temperature,
            vad_filter=config.vad_filter,
            beam_size=config.beam_size,
            best_of=config.best_of,
            initial_prompt=config.initial_prompt,
            word_timestamps=config.word_timestamps
        )

        # 确保输出目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_srt)), exist_ok=True)

        with open(output_srt, "w", encoding="utf-8") as f:
            for i, segment in enumerate(segments, start=1):
                start = format_time(segment.start)
                end = format_time(segment.end)
                print(f"[{i:03}] {start} --> {end} | {segment.text}")
                f.write(f"{i}\n{start} --> {end}\n{segment.text}\n\n")

        t1 = time.time()
        duration = t1 - t0
        print(f"✅ 转录完成！字幕保存为：{output_srt}")
        print(f"🌐 检测语言：{info.language}（置信度：{info.language_probability:.2f}）")
        print(f"⏱️ 总耗时：{duration:.1f} 秒")
        signal.alarm(0)
        return True, duration

    except TimeoutException as te:
        print(f"⛔ 超时中断：{te}")
    except Exception as e:
        print(f"❌ 转录失败：{e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")
    finally:
        signal.alarm(0)
        sys.stdout.flush()

    return False, 0

def main():
    # 配置转录参数
    config = TranscribeConfig(
        model_size="large-v3",
        device="cpu",
        compute_type="int8",
        cpu_threads=os.cpu_count() or 6,  # 自动检测 CPU 核心数
        language="ja",
        temperature=0.0,
        beam_size=5,
        best_of=5,
        vad_filter=True,
        initial_prompt="これは日本語の会話です。"
    )

    input_video = "/Users/<USER>/Makemodel2015.11.14/松宮ひすい/ARMF-021/ARMF-021_001_000.mp4"
    temp_audio = "/Users/<USER>/Makemodel2015.11.14/松宮ひすい/ARMF-021/audio_optimized.wav"
    output_srt = os.path.splitext(temp_audio)[0] + ".srt"

    if not os.path.exists(temp_audio):
        if not extract_audio(input_video, temp_audio):
            return

    success, duration = transcribe_audio_safe(temp_audio, output_srt, config=config)
    if success:
        print(f"🎉 任务完成！总耗时：{duration:.1f} 秒")
    else:
        print("❌ 任务失败")

if __name__ == "__main__":
    main()