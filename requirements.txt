# IB API实时数据推送系统 - Python依赖包

# Flask Web框架
Flask==2.3.3
Flask-SocketIO==5.3.6

# WebSocket支持
python-socketio==5.8.0
eventlet==0.33.3

# 数据处理
pandas==2.0.3
numpy==1.24.3

# HTTP客户端
requests==2.31.0
urllib3==2.0.4

# IB API
ibapi==9.81.1.post1

# 数据库连接
SQLAlchemy==2.0.20
PyMySQL==1.1.0

# 时区处理
pytz==2023.3

# 加密和安全
cryptography==41.0.4

# 生产环境WSGI服务器
gunicorn==21.2.0

# 性能监控
psutil==5.9.5

# 日志处理
colorlog==6.7.0

# 配置管理
python-dotenv==1.0.0

# 测试框架
pytest==7.4.2
pytest-cov==4.1.0

# 开发工具
black==23.7.0
flake8==6.0.0
isort==5.12.0

# 类型检查
mypy==1.5.1

# 文档生成
Sphinx==7.1.2
sphinx-rtd-theme==1.3.0

# API限流
Flask-Limiter==3.5.0

# 缓存
Flask-Caching==2.1.0

# 跨域支持
Flask-CORS==4.0.0

# 任务队列（可选）
celery==5.3.1
redis==4.6.0

# 监控和指标（可选）
prometheus-client==0.17.1

# 邮件通知（可选）
Flask-Mail==0.9.1
