from setuptools import setup, find_packages

setup(
    name="ib_data_fetcher",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "ibapi",
        "pandas",
        "pytz"
    ],
    python_requires=">=3.10",
    author="Your Name",
    author_email="<EMAIL>",
    description="Interactive Brokers历史数据获取工具",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.10",
    ],
) 