WITH premarket_and_open_prices AS (
    SELECT
        stock_code,
        date_,
        MIN(CASE WHEN TIME(date_time) < '09:30:00' THEN Open END) AS premarket_open,
        MIN(CASE WHEN TIME(date_time) >= '09:30:00' THEN Open END) AS regular_open
    FROM stock_trand.stock_price_ibkr
    GROUP BY stock_code, date_
),
premarket_return_calc AS (
    SELECT
        stock_code,
        date_,
        premarket_open,
        regular_open,
        ((regular_open - premarket_open) / premarket_open) * 100 AS premarket_return
    FROM premarket_and_open_prices
    WHERE premarket_open IS NOT NULL AND regular_open IS NOT NULL
        AND premarket_open > 0
        AND ((regular_open - premarket_open) / premarket_open) * 100 > 80
),
daily_volume AS (
    SELECT
        stock_code,
        date_,
        SUM(volume) AS total_volume
    FROM stock_trand.stock_price_ibkr
    GROUP BY stock_code, date_
)
SELECT
    prc.stock_code,
    prc.date_,
    prc.premarket_open,
    prc.regular_open,
    prc.premarket_return
FROM premarket_return_calc prc
JOIN daily_volume dv
    ON prc.stock_code = dv.stock_code
    AND prc.date_ = dv.date_
WHERE dv.total_volume > 3000000
ORDER BY prc.premarket_return DESC;