# IB API实时数据推送系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com)
[![IB API](https://img.shields.io/badge/IB%20API-9.81+-orange.svg)](https://interactivebrokers.github.io/tws-api/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 基于Interactive Brokers API的实时股票数据推送系统，支持毫秒级数据传输和5分钟K线聚合

## ✨ 核心特性

- 🚀 **毫秒级实时性** - 端到端延迟 < 200ms
- 📊 **5分钟K线聚合** - 自动聚合1分钟数据为5分钟K线
- 🔄 **历史数据处理** - 支持历史数据加载和实时数据无缝衔接
- ⚡ **高性能架构** - HTTP连接池、WebSocket批处理
- 🛡️ **完整错误处理** - 自动重连、数据完整性检查
- 📈 **TradingView图表** - 专业级图表展示 (v5.0.7 standalone)
- 🔧 **生产就绪** - 完整的监控和部署方案

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd stock_xxx

# 安装依赖
pip install -r requirements.txt
```

### 2. 启动系统
```bash
# 启动Flask服务
cd stock_gui_demo
python app.py

# 访问前端页面
open http://localhost:5001
```

### 3. 运行测试
```bash
# 运行所有测试
python scripts/run_all_tests.py

# 或运行单个测试
python tests/test_basic/test_stock_list_push.py
```

## 📁 项目结构

```
stock_xxx/
├── 📋 README.md                    # 项目说明
├── 📦 requirements.txt             # 依赖包
├── 🔧 src/                        # 源代码
│   └── api/
│       └── get_stock_price_ibapi.py # IB API集成
├── 🌐 stock_gui_demo/             # Web应用
│   ├── app.py                      # Flask主应用
│   ├── data_service.py             # 数据服务层
│   ├── performance_optimizer.py    # 性能优化组件
│   └── static/realtime-charts.js   # 前端图表
├── 🧪 tests/                      # 测试文件
│   ├── test_basic/                 # 基础功能测试
│   ├── test_integration/           # 集成测试
│   └── test_performance/           # 性能测试
├── 📚 docs/                       # 文档
│   ├── user/                       # 用户文档
│   ├── technical/                  # 技术文档
│   ├── project/                    # 项目文档
│   └── deployment/                 # 部署文档
└── 🛠️ scripts/                    # 工具脚本
    └── run_all_tests.py            # 测试运行脚本
```

## 📚 文档导航

### 📋 项目入门
- [快速上手指南](docs/快速上手指南.md) - 新开发者必读，快速了解项目核心
- [开发进度和规则](docs/开发进度和规则.md) - 开发规则和项目进度
- [启动器使用说明](docs/Launcher启动器使用说明.md) - 三种启动模式详解

### 🏗️ 架构文档
- [项目架构说明](docs/项目架构说明.md) - 完整的项目结构和架构设计
- [Web数据流说明](docs/Web数据流说明.md) - 详细的数据流程和处理机制
- [API接口文档](docs/API接口文档.md) - Web API接口的完整说明

### 📦 版本迁移
- [NPM版本使用指南](docs/migration/快速使用指南.md) - NPM版本使用指南
- [当前系统状态](docs/当前系统状态.md) - 最新系统状态文档

### 📝 技术报告
- [TradingView Charts升级指南](docs/Lightweight_Charts_v5_升级指南.md) - Charts v5升级详解
- [WebSocket修复说明](docs/websocket-fix-explanation.md) - WebSocket问题修复报告

## 📊 性能指标

| 指标 | 数值 | 说明 |
|------|------|------|
| HTTP延迟 | 8.8ms | 平均HTTP请求延迟 |
| 端到端延迟 | < 200ms | 数据推送总延迟 |
| 数据处理 | 3.6ms | 5分钟聚合处理时间 |
| 成功率 | 100% | 数据推送成功率 |
| 性能提升 | 47.8% | HTTP连接池优化效果 |

## 📞 技术支持

- 📧 Email: <EMAIL>
- 💬 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [项目文档](docs/)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**🎉 感谢使用IB API实时数据推送系统！**
