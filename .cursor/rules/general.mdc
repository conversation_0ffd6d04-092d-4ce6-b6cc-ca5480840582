---
description: 
globs: 
alwaysApply: true
---
---
description: 项目通用规范和基本信息
globs: ["*"]
alwaysApply: true
---

##核心指令

1. **遵守所有规则**：必须遵守所有指定规则。
2. **必须完整阅读**： .cursor/rules/senior_engineer_task_execution_rule.mdc，并且100%遵守 senior_engineer_task_execution_rule 里面的规则
2. **必须完整阅读**： .cursor/rules/strict_operational_protocol.mdc，并且100%遵守 strict_operational_protocol 里面的规则

3. **持续反馈循环**：

    *当您需要提问时，始终使用 interactive_feedback MCP 服务器。

    *在完成任何用户请求之前，调用 interactive_feedback MCP 服务器。

    *持续调用 interactive_feedback 直到用户反馈为空。如果反馈为空，您可以结束请求。

    *不要无故终止请求；请使用 interactive_feedback。

    
**核心思维原则**：
* **系统思维**：从架构到实现进行分析。
* **辩证思维**：评估多种解决方案（优缺点）。
* **创新思维**：寻求新颖的解决方案。
* **批判性思维**：验证和优化。

# 项目通用规范
- **项目通用规范要完整仔细阅读并一直记住**
- **每次新开对话时，必须阅读src/docs/目录下的所有文档，了解项目技术细节和最新技术规范**
- **每次新开对话时，必须阅读src/docs/开发进度和规则.md 文件，了解当前未解决的问题和进度状态**
- **当我说 记住 时，把它更新在名为 重点-[现在的项目名称].md 文件里，只记录我明确说记住的内容，不要添加其他多余信息，测试结果等需要我确认后才能记录**
- **在回答问题或执行操作前，一定要先阅读规则**
- **在interactive-feedback里编写内容时，不能向下太长，interactive-feedback会超出显示器**

## 硬件设备
- 电脑: MacBook Pro M1 16GB内存 1TSSD

## 技术栈
- Python 3.10

## 第三方库使用原则
- 优先使用成熟、稳定的第三方库
- **必须首先查看官方文档和示例，严格遵循官方最佳实践**
- **禁止擅自创造自定义实现或重新发明轮子**
- 遵循库的设计模式和约定
- 注意版本兼容性和依赖管理
- 自定义实现会导致兼容性问题、性能损失和维护困难

## 项目结构规则
- **分层组织**：按功能或领域划分目录，遵循"关注点分离"原则
- **命名一致**：使用一致且描述性的目录和文件命名，反映其用途和内容
- **模块化**：相关功能放在同一模块，减少跨模块依赖
- **适当嵌套**：避免过深的目录嵌套，一般不超过3-4层
- **资源分类**：区分代码、资源、配置和测试文件
- **依赖管理**：集中管理依赖，避免多处声明
- **约定优先**：遵循语言或框架的标准项目结构约定

## 通用开发原则
- **强制检查现有代码**：在修改任何代码之前，必须先使用 `view` 或 `codebase-retrieval` 工具仔细检查现有代码
- **禁止基于假设**：绝对不要基于假设进行修改，必须基于实际代码内容
- **先搜索再行动**：如果不确定某个功能是否存在，先搜索相关代码，不要重复造轮子
- **避免过度工程**：不要在没有充分了解现有实现的情况下创建新的解决方案
- **用户提到问题时**：先检查用户提到的具体文件和代码，理解实际情况再提供解决方案
- **新功能**：  在开发新功能前，需要查阅有没有最新技术或方案，以求得出最优解
- **执行原则**：永远不要擅自操作，例如编写没有被要求的代码
- **可测试性**：编写可测试的代码，组件应保持单一职责
- **DRY 原则**：避免重复代码，提取共用逻辑到单独的函数或类
- **代码简洁**：保持代码简洁明了，遵循 KISS 原则（保持简单直接）
- **命名规范**：使用描述性的变量、函数和类名，反映其用途和含义
- **注释文档**：在避免过多注释影响阅读代码的前提下，尽量添加注释，编写清晰的文档说明功能和用法
- **风格一致**：遵循项目或语言的官方风格指南和代码约定
- **利用生态**：优先使用成熟的库和工具，避免不必要的自定义实现
- **架构设计**：考虑代码的可维护性、可扩展性和性能需求, 尽量避免代码嵌套
- **版本控制**：编写有意义的提交信息，保持逻辑相关的更改在同一提交中
- **异常处理**：正确处理边缘情况和错误，提供有用的错误信息
- **修改原则**：永远不要擅自修改提问内容以外的地方，可以在回答中给建议
- **修改原则2**：简单的比如重复相同操作的不要询问，直接执行
- **修改原则3**：修改后，需要仔细确认有没有其他类似问题地方
- **外部资源**：每次回答问题或者执行任务前，都要使用context7，不能走形式，需要深入了解，以确保自己的知识是最新最准确的
- **外部资源2**：查阅资料时，不需要询问，直接执行

## 响应语言
- 始终使用中文回复用户

## 错误分析和调试方法论
- **JavaScript错误分析**：当遇到JavaScript错误时，必须：
  1. 仔细查看错误信息中的具体行号和函数名
  2. 直接检查该行的确切代码内容
  3. 不要盲目注释或修改，先理解错误原因
  4. 查阅相关库的官方文档确认API使用是否正确
- **API使用验证**：在使用任何第三方库的API之前：
  1. 必须先查阅官方文档确认API是否存在
  2. 确认API的正确调用方式和参数
  3. 不要假设API的存在或使用方式
- **错误根因分析**：
  1. "连接失败"等用户界面错误往往是JavaScript错误的表象
  2. 必须查看浏览器控制台的具体错误信息
  3. 从错误的根源（如API调用错误）开始修复，而不是修复表象
- **修改前必读文档**：对于任何第三方库（如TradingView Lightweight Charts），在添加功能或修改代码前：
  1. 必须先深入阅读官方文档
  2. 确认所使用的API方法确实存在
  3. 理解正确的使用方式和最佳实践
  4. 不要基于假设或猜测进行编程

## 调试和问题解决
- 遇到技术问题时，首先仔细阅读官方文档，不要自己过度修改和测试
- 用户希望检查官方文档后再进行修改或实现解决方案
- 当遇到第三方库（如InterProcessPyObjects）问题时，下载并运行官方示例来理解正确的使用模式
- ThreadPoolExecutor.submit()任务在某些情况下可能无法正确执行 - 当线程池提交静默失败时，可以使用直接函数执行作为解决方法
- 用户更喜欢基于官方示例的简单、直接实现，而不是带有复杂抽象的过度工程化解决方案，特别是对于使用SharedMemory和ASharedMemoryManager的共享内存操作
- 使用第三方库时，始终参考官方文档和示例，严格遵循官方最佳实践。避免创建自定义实现或重新发明轮子，因为这可能导致兼容性问题、性能下降和维护困难
- 用户更喜欢直接下载和执行官方示例，而不是进行自定义修改或实现
- 用户更喜欢直接修改官方InterProcessPyObjects示例文件（sender.py、receiver.py）来添加项目功能，而不是从头创建新的实现

## 调试和验证规则

### 代码修改验证流程
1. 修改代码后，必须添加验证代码来确认修改是否生效
2. **严禁在验证修改效果之前就移除调试/验证代码**
3. 验证代码应该包含详细的输出信息，帮助分析问题
4. 只有在确认修改完全生效后，才能移除验证代码
5. 验证流程：修改代码 → 添加验证代码 → 测试验证 → 根据结果调整 → 确认生效后才移除验证代码

### CSS修改验证
1. CSS修改后必须通过JavaScript验证实际的DOM样式是否生效
2. 检查计算样式(getComputedStyle)和实际宽度(offsetWidth)
3. 验证CSS选择器是否正确匹配目标元素
4. 检查是否有内联样式覆盖CSS规则
5. **不要假设CSS修改生效，必须通过实际测试验证**

### TradingView图表修改特殊注意事项
1. TradingView Lightweight Charts使用强制内联样式，CSS优先级需要特别处理
2. 图表DOM结构复杂，需要通过调试代码确认正确的选择器
3. 价格列宽度修改需要针对多个单元格位置(第3、7、10个单元格)
4. 必须使用!important和精确选择器来覆盖内联样式

## 本项目规则文件说明
本项目使用以下规则文件：
- general.mdc：通用规范（本文件）
- document.mdc：文档规范
- python.mdc：python 语言开发规范
