{"name": "stock_xxx", "version": "1.0.0", "description": "Multi-stock real-time monitoring with TradingView Lightweight Charts", "main": "index.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "python start_with_web.py", "test": "node --experimental-vm-modules ./node_modules/.bin/jest"}, "dependencies": {"lightweight-charts": "^5.0.8", "socket.io-client": "^4.7.2"}, "devDependencies": {"jest": "^30.0.3", "vite": "^5.0.0"}, "keywords": ["trading", "charts", "realtime", "stock", "lightweight-charts"], "author": "", "license": "MIT"}