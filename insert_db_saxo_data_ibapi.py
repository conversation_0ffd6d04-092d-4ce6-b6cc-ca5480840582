import pandas as pd
import glob
import os
import time
import threading
from datetime import datetime
from sqlalchemy import text
from sqlalchemy import create_engine
import warnings
from ibapi.client import EClient
from ibapi import wrapper
from ibapi.contract import Contract
from ibapi.utils import *
from ibapi.common import *

# 禁用警告
warnings.simplefilter(action='ignore', category=FutureWarning)


class MyWrapper(wrapper.EWrapper):
    def __init__(self):
        wrapper.EWrapper.__init__(self)
        self.data = {}
        self.contractDetails_dict = {}

    def contractDetails(self, reqId, contractDetails):
        print('合同详情', reqId, contractDetails.contract.primaryExchange)
        # logging.info(f'合同详情 {reqId} {contractDetails.contract.primaryExchange}')
        if contractDetails.contract.primaryExchange in ['NASDAQ', 'AMEX', 'NYSE']:
            self.contractDetails_dict[reqId] = contractDetails.contract

    def historicalData(self, reqId, bar):
        # print("历史数据:", bar)
        if bar.volume > -1:
            if reqId not in self.data:
                self.data[reqId] = {}
            self.data[reqId][bar.date] = {"Date":bar.date,"Open":bar.open,"High":bar.high,"Low":bar.low,"Close":bar.close,"Volume":bar.volume}

class MyClient(EClient):
    def __init__(self, wrapper):
        EClient.__init__(self, wrapper)

class IBApp(MyWrapper, MyClient):
    def __init__(self):
        MyWrapper.__init__(self)
        MyClient.__init__(self, wrapper=self)
        self.symbol = None
        self.reqId_dict = {}
        self.start_date = None
        self.contract_dict = {}

    def create_contract(self):
        # 创建合约实例
        contract = Contract()
        contract.symbol = self.symbol  # 股票代码
        contract.secType = "STK"  # 证券类型（股票）
        contract.exchange = "SMART"  # 交易所
        contract.currency = "USD"  # 货币
        self.contract_dict[self.reqId_dict[self.symbol]] = contract

    # 获取合同详情
    def get_ContractDetails(self):
        self.reqContractDetails(self.reqId_dict[self.symbol], self.contract_dict[self.reqId_dict[self.symbol]])

    def request_historical_data(self):
        # 设置结束日期为今天
        count = 0
        req_flg = True
        reqid_ = self.reqId_dict[self.symbol]
        while(count < 3 and req_flg):
            count += 1
            if reqid_ in self.contractDetails_dict:
                self.reqHistoricalData(reqId=reqid_ , contract=self.contractDetails_dict[reqid_], 
                                    endDateTime=f"{self.start_date.replace('-', '')} 15:59:00 America/New_York", 
                                    durationStr='5 D', barSizeSetting='1 min', whatToShow='TRADES',
                                        useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])
                req_flg = False
            if req_flg:
                time.sleep(1)

app = IBApp()
# ! [connect]
app.connect("127.0.0.1", 7497, clientId=1000)
# ! [connect]
# print("serverVersion:%s connectionTime:%s" % (app.serverVersion(),
#                                               app.twsConnectionTime()))
logging.info(f'serverVersion:{app.serverVersion()} connectionTime:{app.twsConnectionTime()}')
con_thread = threading.Thread(target=app.run, daemon=True)
con_thread.start()
time.sleep(1)
def get_price_ibapi(file_name, start_date, count_):
    app.symbol = file_name
    app.reqId_dict[file_name] = count_
    app.start_date = start_date
    # 创建合约实例
    app.create_contract()
    # 获取合同详情
    app.get_ContractDetails()
    app.request_historical_data()

def get_price_live(start_date):
    # 指定文件夹路径
    folder_path = "/Users/<USER>/seach_stcok/" + start_date + "/"
    # 创建一个空的集合，用于存放文件名
    file_names_set = set()
    # 检查文件夹是否存在
    df_csv_dict = {}
    if os.path.exists(folder_path):
        # 获取文件夹下所有子文件夹的路径
        subfolders = sorted([f.path for f in os.scandir(folder_path) if f.is_dir()])
        # 遍历每个子文件夹，获取文件名
        count_ = 0
        for subfolder in subfolders:
            # print("Files in folder:", subfolder)
            # files = [os.path.splitext(f)[0] for f in os.listdir(subfolder) if os.path.isfile(os.path.join(subfolder, f))]
            files = glob.glob(os.path.join(subfolder, "*"))
            # files = ['MLGO']
            for file in files:
                # 判断文件是否为空
                if os.path.getsize(file) == 0:
                    print(f"警告：文件为空，跳过 {file}")
                    continue
                file_name = os.path.splitext(file)[0].split('/')[6]
                timp_df = pd.read_csv(file)
                timp_df['stock_code'] = file_name
                timp_df['date_'] = start_date
                timp_df.rename(columns={'Time': 'date_time'}, inplace=True)
                if file_name in df_csv_dict:
                    # 添加数据到最后一行
                    timp_df['live_data'] = 1
                    df_csv_dict[file_name].loc[len(df_csv_dict[file_name])] = timp_df.iloc[-1]
                else:
                    get_price_ibapi(file_name, start_date, count_)
                    count_ += 1
                    timp_df['live_data'] = 0  # 先创建新列，并将所有值设置为 0
                    timp_df.iloc[-1, -1] = 1   # 将最后一行的新列值设置为 1
                    df_csv_dict[file_name] = timp_df
    return df_csv_dict

def remove_timezone(date_str):
    # 分离日期时间和时区部分
    dt_str, tz_str = date_str.rsplit(' ', 1)
    # 解析日期时间部分
    dt = pd.to_datetime(dt_str, format='%Y%m%d %H:%M:%S')
    # 返回不含时区的字符串
    return dt.strftime('%Y-%m-%d %H:%M:%S')

def test():
    start_date = '2025-07-03'
    df_csv_dict = get_price_live(start_date)
    reqId_dict_size = len(app.reqId_dict)
    data_size = len(app.data)
    while data_size < 0.8 * reqId_dict_size:
        print(f"等待数据。。。。{reqId_dict_size} {data_size}")
        time.sleep(2)
    df_ibapi_dict = {}
    for stock_code, reqId in app.reqId_dict.items():
        if reqId in app.data:
            df_ibapi_dict[stock_code] = pd.DataFrame(app.data[reqId].values())
            df_ibapi_dict[stock_code].rename(columns={'Date': 'date_time'}, inplace=True)
            # 将时间列设置为索引，并转换为时间戳格式
            # df_ibapi_dict[stock_code]['date_time'] = pd.to_datetime(df_ibapi_dict[stock_code]['date_time'], format='%Y%m%d %H:%M:%S %Z', utc=True).dt.strftime('%Y-%m-%d %H:%M:%S')
            df_ibapi_dict[stock_code]['date_time'] = df_ibapi_dict[stock_code]['date_time'].apply(remove_timezone)

            # df_ibapi_dict[stock_code]['date_time'] = df_ibapi_dict[stock_code]['date_time'].apply(lambda x: datetime.strptime(x).strftime('%Y-%m-%d %H:%M:%S'))
            df_ibapi_dict[stock_code]['date_time'] = pd.to_datetime(df_ibapi_dict[stock_code]['date_time']) + pd.Timedelta(minutes=1)
            # df_ibapi_dict[stock_code].set_index("date_time",inplace=True)
            df_ibapi_dict[stock_code]['Volume'] = df_ibapi_dict[stock_code]['Volume'].astype(float) 
            df_ibapi_dict[stock_code]['stock_code'] = stock_code
            df_ibapi_dict[stock_code]['date_'] = start_date
            df_ibapi_dict[stock_code]['live_data'] = 0
    app.disconnect()
    # 创建一个连接到 MySQL 数据库的引擎
    # 使用pymysql作为MySQL驱动程序
    engine = create_engine('mysql+pymysql://root:admin1234@localhost/stock_trand')
    # 假设 df 是你要插入的 DataFrame，columns_to_insert 是你想要插入的字段列表
    columns_to_insert = ['stock_code', 'date_', 'date_time', 'Open', 'High', 'Low', 'Close', 'Volume', 'live_data']
    # 要插入或更新的列
    columns_1800 = ['stock_code', 'date_time', 'Open', 'High', 'Low', 'Close', 'Volume']
    for key, df_csv in df_csv_dict.items():
        # if key == 'BPTH':
        #     continue
        print('插入:', key, '\n')
        # if key != 'SPPL':
        #     continue

        # 将日期时间列转换为日期时间格式，处理无效值
        # df_csv['date_time'] = pd.to_datetime(df_csv['date_time'], errors='coerce')

        # 检查是否有非日期时间值
        # non_datetime_values = df_csv[df_csv['date_time'].isna()]
        # if not non_datetime_values.empty:
        #     print("有非日期时间值:", non_datetime_values)

        # 对日期时间列进行进一步处理，例如修改时区或格式化日期时间
        # 或者将日期时间格式化为特定的字符串格式
        # df_csv.loc[:, 'date_time'] = pd.to_datetime(df_csv['date_time'], format='%Y-%m-%d %H:%M:%S')
        # df_csv.loc[:, 'date_time'] = df_csv['date_time'].dt.strftime('%Y-%m-%d %H:%M:%S')

        # 解析日期时间
        df_csv['date_time'] = pd.to_datetime(df_csv['date_time'], utc=True)

        # 将日期时间转换为字符串
        df_csv['date_time'] = df_csv['date_time'].dt.strftime('%Y-%m-%d %H:%M:%S')


      
        # 删除重复
        df_csv = df_csv.drop_duplicates(subset=['date_time'])

        filtered_df = df_csv[df_csv['live_data'] == 0]
        # 指定要删除的日期列表
        specified_dates = filtered_df['date_time'].tolist()
        # 删除指定日期的行
        df_csv = df_csv[~df_csv['date_time'].isin(specified_dates)]


        if key not in df_ibapi_dict:
            print(f"df_ibapi_dict中没有数据 {key}")
            continue
        df_ibapi = df_ibapi_dict[key]
        # 将时间列转换为字符串格式
        df_ibapi.loc[:, 'date_time'] = pd.to_datetime(df_ibapi['date_time'])
        df_ibapi.loc[:, 'date_time'] = df_ibapi['date_time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        # 将 'date_time' 列设置为索引
        df_ibapi.set_index('date_time', inplace=True)

        # 筛选时间段内的数据
        df_ibapi_before = df_ibapi.loc[f'{start_date} 04:01:00':f'{start_date} 09:31:00']
        # 计算记录数量
        record_count = df_ibapi_before.shape[0]
        df_ibapi_120 = pd.DataFrame()
        # if record_count < 120:
        # df_ibapi_120 = df_ibapi.loc[:f'{start_date} 04:01:00'].tail(1800-record_count)
        df_ibapi_120 = df_ibapi.loc[df_ibapi.index < pd.Timestamp(f'{start_date} 04:01:00')].tail(1800-record_count)
        # 重置索引，将 'date_time' 列变为普通列
        df_ibapi_120.reset_index(inplace=True)


        # 构建SQL插入语句，使用SQLAlchemy的text进行参数绑定
        insert_sql = text(f"""
            INSERT INTO stock_price_ibkr_120 (stock_code, date_time, Open, High, Low, Close, Volume)
            VALUES (:stock_code, :date_time, :Open, :High, :Low, :Close, :Volume)
            ON DUPLICATE KEY UPDATE
            Open=VALUES(Open), High=VALUES(High), Low=VALUES(Low), Close=VALUES(Close), Volume=VALUES(Volume)
        """)

        # 将DataFrame转换为字典列表，用于批量插入
        data_to_insert = df_ibapi_120[columns_1800].to_dict(orient='records')

        # 使用SQLAlchemy执行插入或更新操作
        with engine.connect() as conn:
            for row in data_to_insert:
                conn.execute(insert_sql, row)  # 传递参数为单一字典，而不是解包
            conn.commit()


        # 截取 04:01:00 以后的数据
        df_ibapi = df_ibapi.loc[df_ibapi.index >= pd.Timestamp(f'{start_date} 04:01:00')].copy()
        # 重置索引，将 'date_time' 列变为普通列
        df_ibapi.reset_index(inplace=True)
        
        # 将数据插入到名为 "table_name" 的表中，并只插入指定的字段 
        df_csv[columns_to_insert].to_sql('stock_price_ibkr', con=engine, if_exists='append', index=False)
        df_ibapi[columns_to_insert].to_sql('stock_price_ibkr', con=engine, if_exists='append', index=False)
       
        # # 将时间列转换为字符串格式
        # df_csv.loc[:, 'date_time'] = pd.to_datetime(df_csv['date_time'])
        # df_csv.loc[:, 'date_time'] = pd.to_datetime(df_csv.date_time, format='%Y-%m-%d %H:%M:%S')
        # df_csv.loc[:, 'date_time'] = df_csv['date_time'].dt.strftime('%Y-%m-%d %H:%M:%S')

        # if_exists='replace' 不能使用replace 会删除所有数据和表结构
        
        # if not df_ibapi_120.empty:
        #     df_ibapi_120[columns_to_insert].to_sql('stock_price_ibkr_120', con=engine, if_exists='append', index=False)

        # 构建带有参数的 SQL 查询字符串
        sql_query = """
            delete from stock_price_ibkr 
            where date_ = :start_date and date_ != SUBSTRING_INDEX(date_time, ' ', 1)
        """

        # 使用 text() 函数将 SQL 查询字符串转换为可执行对象
        stmt = text(sql_query)

        # 使用 execute() 方法执行 SQL 查询，并传递参数
        with engine.connect() as conn:
            conn.execute(stmt, {'start_date': start_date})
            conn.commit()

test()