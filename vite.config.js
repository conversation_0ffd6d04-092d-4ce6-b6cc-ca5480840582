import { defineConfig } from 'vite'

export default defineConfig({
  root: 'src/web',
  publicDir: 'static',
  build: {
    outDir: '../../dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: 'templates/index-npm.html'
      }
    }
  },
  server: {
    port: 3000,
    strictPort: false, // 允许自动选择其他端口
    host: true, // 允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true
      },
      '/socket.io': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        ws: true
      }
    }
  }
})
