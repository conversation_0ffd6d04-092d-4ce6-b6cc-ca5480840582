#!/usr/bin/env node

/**
 * 前端工具函数快速测试脚本
 * 简化版测试，专注于核心功能验证
 */

const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(colors[color] + message + colors.reset);
}

// 手动定义前端函数（简化版）
function parseDateTime(dateTimeStr) {
    if (!dateTimeStr || typeof dateTimeStr !== 'string') {
        return Math.floor(Date.now() / 1000);
    }
    
    let processedStr = dateTimeStr
        .replace(' US/Eastern', '')
        .replace(' America/New_York', '');
    
    // IB API格式: "20250623 14:31:00"
    const ibMatch = processedStr.match(/^(\d{4})(\d{2})(\d{2}) (\d{2}):(\d{2}):(\d{2})$/);
    if (ibMatch) {
        const date = new Date(
            parseInt(ibMatch[1]), 
            parseInt(ibMatch[2]) - 1, 
            parseInt(ibMatch[3]), 
            parseInt(ibMatch[4]), 
            parseInt(ibMatch[5]), 
            parseInt(ibMatch[6])
        );
        return Math.floor(date.getTime() / 1000);
    }
    
    // 标准格式: "2025-06-23 14:31:00"
    const standardMatch = processedStr.match(/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/);
    if (standardMatch) {
        const date = new Date(
            parseInt(standardMatch[1]), 
            parseInt(standardMatch[2]) - 1, 
            parseInt(standardMatch[3]), 
            parseInt(standardMatch[4]), 
            parseInt(standardMatch[5]), 
            parseInt(standardMatch[6])
        );
        return Math.floor(date.getTime() / 1000);
    }
    
    return Math.floor(Date.now() / 1000);
}

function fillDataGaps(candleData) {
    if (!Array.isArray(candleData) || candleData.length === 0) {
        return candleData;
    }

    const processedData = candleData.map(candle => ({
        ...candle,
        timeStamp: typeof candle.time === 'string' ? parseDateTime(candle.time) : candle.time
    }));
    
    const sortedData = processedData.sort((a, b) => a.timeStamp - b.timeStamp);
    const filledData = [];
    
    for (let i = 0; i < sortedData.length; i++) {
        const currentCandle = sortedData[i];
        
        filledData.push({
            time: currentCandle.timeStamp,
            open: currentCandle.open,
            high: currentCandle.high,
            low: currentCandle.low,
            close: currentCandle.close,
            volume: currentCandle.volume
        });
        
        if (i < sortedData.length - 1) {
            const nextCandle = sortedData[i + 1];
            const timeDiff = nextCandle.timeStamp - currentCandle.timeStamp;
            
            if (timeDiff > 60) {
                let fillTime = currentCandle.timeStamp + 60;
                const prevClose = currentCandle.close;
                
                while (fillTime < nextCandle.timeStamp) {
                    filledData.push({
                        time: fillTime,
                        open: prevClose,
                        high: prevClose,
                        low: prevClose,
                        close: prevClose,
                        volume: 0
                    });
                    fillTime += 60;
                }
            }
        }
    }
    
    return filledData;
}

function ensureFiveMinuteGroups(candleData, targetGroups = 120) {
    if (!Array.isArray(candleData) || candleData.length === 0) {
        return candleData;
    }

    // 先填充缺口
    const filledData = fillDataGaps(candleData);
    
    // 确保有足够的数据
    const targetDataPoints = targetGroups * 5;
    if (filledData.length < targetDataPoints) {
        const lastCandle = filledData[filledData.length - 1];
        const lastTime = lastCandle.time;
        const lastClose = lastCandle.close;
        
        // 向前补充数据
        for (let i = filledData.length; i < targetDataPoints; i++) {
            const newTime = lastTime - (targetDataPoints - i) * 60;
            filledData.unshift({
                time: newTime,
                open: lastClose,
                high: lastClose,
                low: lastClose,
                close: lastClose,
                volume: 0
            });
        }
    }
    
    // 返回最新的targetDataPoints个数据点
    return filledData.slice(-targetDataPoints);
}

// 测试用例
function runQuickTests() {
    log('\n🚀 前端工具函数快速测试', 'cyan');
    
    // 1. parseDateTime 测试
    log('\n=== parseDateTime 测试 ===', 'blue');
    const timeTests = [
        '20250623 14:30:00',
        '20250623 14:31:00 US/Eastern',
        '2025-06-23 14:32:00'
    ];
    
    timeTests.forEach((timeStr, i) => {
        const result = parseDateTime(timeStr);
        log(`✓ 测试 ${i+1}: ${timeStr} → ${result}`, 'green');
    });
    
    // 2. fillDataGaps 测试
    log('\n=== fillDataGaps 测试 ===', 'blue');
    const testData = [
        { time: '20250623 14:30:00', open: 100, high: 101, low: 99, close: 100.5, volume: 1000 },
        { time: '20250623 14:32:00', open: 100.5, high: 102, low: 100, close: 101, volume: 1200 }, // 缺1分钟
        { time: '20250623 14:33:00', open: 101, high: 101.5, low: 100.5, close: 101.2, volume: 800 }
    ];
    
    const filled = fillDataGaps(testData);
    log(`原始数据: ${testData.length} 条`, 'yellow');
    log(`补齐后: ${filled.length} 条`, 'green');
    
    // 3. ensureFiveMinuteGroups 测试
    log('\n=== ensureFiveMinuteGroups 测试 ===', 'blue');
    const grouped = ensureFiveMinuteGroups(testData, 120);
    log(`分组结果: ${grouped.length} 条 (期望: 600)`, grouped.length === 600 ? 'green' : 'red');
    
    // 验证5分钟分组
    let validGroups = 0;
    for (let i = 0; i < grouped.length; i += 5) {
        const group = grouped.slice(i, i + 5);
        if (group.length === 5) {
            let isConsecutive = true;
            for (let j = 1; j < group.length; j++) {
                if (group[j].time - group[j-1].time !== 60) {
                    isConsecutive = false;
                    break;
                }
            }
            if (isConsecutive) validGroups++;
        }
    }
    log(`有效5分钟组: ${validGroups}/120`, validGroups === 120 ? 'green' : 'yellow');
    
    // 4. 性能测试
    log('\n=== 性能测试 ===', 'blue');
    
    const start1 = Date.now();
    for (let i = 0; i < 1000; i++) {
        parseDateTime('20250623 14:30:00');
    }
    const time1 = Date.now() - start1;
    log(`parseDateTime: ${time1}ms/1000次 = ${(time1/1000).toFixed(4)}ms/次`, 'cyan');
    
    const start2 = Date.now();
    for (let i = 0; i < 100; i++) {
        fillDataGaps(testData);
    }
    const time2 = Date.now() - start2;
    log(`fillDataGaps: ${time2}ms/100次 = ${(time2/100).toFixed(4)}ms/次`, 'cyan');
    
    const start3 = Date.now();
    for (let i = 0; i < 10; i++) {
        ensureFiveMinuteGroups(testData, 120);
    }
    const time3 = Date.now() - start3;
    log(`ensureFiveMinuteGroups: ${time3}ms/10次 = ${(time3/10).toFixed(4)}ms/次`, 'cyan');
    
    log('\n✅ 快速测试完成!', 'green');
}

// 运行测试
runQuickTests();
