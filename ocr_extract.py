import pytesseract
from PIL import Image
import os

import shutil
import sys

# 图片路径列表
image_paths = [
    '/Users/<USER>/Downloads/楽天カード利用状況照会.JPG',
    '/Users/<USER>/Downloads/入出金明細 _ 照会結果印刷 - 三菱ＵＦＪ銀行3.jpg',
    '/Users/<USER>/Downloads/入出金明細 _ 照会結果印刷 - 三菱ＵＦＪ銀行2.jpg',
    '/Users/<USER>/Downloads/入出金明細 _ 照会結果印刷 - 三菱ＵＦＪ銀行.jpg',
    '/Users/<USER>/Downloads/auカードローン利用状況照会2.jpg',
    '/Users/<USER>/Downloads/auカードローン利用状況照会.jpg',
    '/Users/<USER>/Downloads/auじぶん銀行取引明細2.jpg',
    '/Users/<USER>/Downloads/auじぶん銀行取引明細3.jpg',
    '/Users/<USER>/Downloads/auじぶん銀行取引明細.jpg',
]

output_dir = 'ocr_results'
os.makedirs(output_dir, exist_ok=True)

# 检查 tesseract 是否安装及日文语言包
def check_tesseract():
    tesseract_cmd = shutil.which('tesseract')
    if not tesseract_cmd:
        print("[错误] 未检测到 tesseract，请先安装: brew install tesseract")
        sys.exit(1)
    # 检查日文语言包
    try:
        from subprocess import check_output
        langs = check_output([tesseract_cmd, '--list-langs'], encoding='utf-8')
        if 'jpn' not in langs:
            print("[错误] tesseract 未安装日文语言包。请执行: brew install tesseract-lang")
            sys.exit(1)
    except Exception as e:
        print(f"[错误] 检查 tesseract 语言包失败: {e}")
        sys.exit(1)

check_tesseract()

for img_path in image_paths:
    try:
        img = Image.open(img_path)
        text = pytesseract.image_to_string(img, lang='jpn+eng')
        base = os.path.basename(img_path)
        txt_path = os.path.join(output_dir, base + '.txt')
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(text)
        print(f"已提取: {img_path} -> {txt_path}")
    except Exception as e:
        print(f"处理失败: {img_path}, 错误: {e}")
