import tkinter as tk
from AppKit import NSEvent, NSEventModifierFlagCommand, NSEventMaskKeyDown
from PyObjCTools import AppHelper
import threading
import logging
import pyperclip
import os

from src.utils.constants import *
from src.config.config import GUI_CONFIG, HOTKEYS, COLORS
from src.gui.gui_components import StockFrame
from src.utils.screenshot import get_stock_code
from src.data.market_data_manager import MarketDataManager
from src.utils.stock_utils import format_volume


"""
股票监控应用程序的GUI模块

本模块实现了股票监控应用的图形用户界面，主要功能包括：
1. 创建和管理主窗口及其组件
2. 实时更新股票信息显示
3. 处理用户交互事件
4. 管理热键功能
5. 自动刷新数据

主要组件：
- 主窗口：使用tkinter实现的应用程序窗口
- 股票框架：每支股票的信息显示区域
- 数据管理器：处理股票数据的获取和更新
- 价格管理器：管理股票价格相关的计算和更新

依赖：
- tkinter：GUI框架
- AppKit：macOS系统功能支持
- PyObjCTools：Objective-C桥接
- threading：多线程支持
- logging：日志记录
"""

class StockWatchApp:
    """股票监控应用程序的主类
    
    这个类负责创建和管理整个应用程序的GUI界面，包括：
    1. 初始化主窗口和所有必要的组件
    2. 管理股票数据的显示和更新
    3. 处理用户交互和热键事件
    4. 维护应用程序的整体状态
    
    属性：
        window (tk.Tk): 主窗口实例
        data_manager (MarketDataManager): 数据管理器实例
        sound_warn (str): 警告音效文件路径
        stock_frames (dict): 股票代码到StockFrame实例的映射
        volume_ratio_high (set): 缓存成交量倍数大于5的股票代码
        sound_process (subprocess.Popen or None): 播放声音的子进程句柄
    """
    
    def __init__(self, sound_warn: str) -> None:
        """初始化股票监控应用程序

        Args:
            sound_warn (str): 警告音效文件路径
        """
        self.window = tk.Tk()  # 主窗口
        self.data_manager = MarketDataManager()  # 数据管理器
        self.sound_warn = sound_warn  # 警告声音文件路径
        self.stock_frames = {}  # 股票框架字典，key为股票代码，value为StockFrame实例
        self.volume_ratio_cache = {}  # 缓存成交量倍数
        self.volume_ratio_high = set()  # 缓存成交量倍数大于5的股票代码
        self.shares_outstanding_cache = set()  # 缓存流通股股票代码
        self.sound_process = None  # 播放声音的子进程句柄
        
        self._setup_window()  # 设置窗口
        self._setup_canvas()  # 设置画布
        self._setup_callbacks()  # 设置回调函数
        self._start_update_threads()  # 启动更新线程
        
    def _setup_window(self) -> None:
        """设置主窗口
        
        配置主窗口的基本属性，包括：
        - 窗口标题
        - 窗口大小和位置
        - 窗口置顶状态
        """
        self.window.title(GUI_CONFIG['WINDOW_TITLE'])  # 设置窗口标题
        self.window.geometry(GUI_CONFIG['WINDOW_GEOMETRY'])  # 设置窗口大小和位置
        self.window.attributes('-topmost', GUI_CONFIG['WINDOW_TOPMOST'])  # 设置窗口置顶
        
    def _setup_canvas(self) -> None:
        """设置画布和滚动条
        
        创建和配置主画布及其滚动条，包括：
        1. 创建主画布和垂直滚动条
        2. 设置滚动条和画布的关联
        3. 创建主框架并放入画布
        4. 绑定配置事件
        """
        self.canvas = tk.Canvas(self.window)  # 创建画布
        self.scrollbar = tk.Scrollbar(self.window, orient="vertical", command=self.canvas.yview)  # 创建垂直滚动条
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)  # 放置滚动条
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)  # 放置画布
        self.canvas.configure(yscrollcommand=self.scrollbar.set)  # 设置滚动条命令
        
        # 创建主框架
        self.main_frame = tk.Frame(self.canvas)  # 创建主框架
        self.canvas.create_window((0, 0), window=self.main_frame, anchor="nw")  # 将主框架放入画布
        self.main_frame.bind("<Configure>", self._on_frame_configure)  # 绑定配置事件
        
    def _setup_callbacks(self) -> None:
        """设置回调函数
        
        初始化所有需要的回调函数，包括：
        - 打开富途界面
        - 更新买入价格
        """
        self.callbacks = {
            'open_futu': self._open_futu_interface,  # 打开富途界面的回调
            'update_buy_price': self._update_buy_price  # 更新买入价格的回调
        }
        
    def _start_update_threads(self) -> None:
        """启动更新线程
        
        启动所有需要的更新线程，包括：
        1. 股票框架更新
        2. 股票数据更新
        3. 退出条件检查
        4. 快捷键监听
        """
        self.window.after(1000, self._update_stock_frames)
        self.window.after(GUI_CONFIG['REFRESH_INTERVAL'], self._update_stock_data)
        self.window.after(10000, self._check_exit_condition)
        
        # 启动快捷键监听线程
        threading.Thread(target=self._start_hotkey_listener, daemon=True).start()
        
    def _on_frame_configure(self, event: tk.Event) -> None:
        """更新画布滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def _update_stock_frames(self) -> None:
        """更新股票框架
        
        定期更新股票显示框架，包括：
        1. 隐藏不活跃的股票
        2. 显示或创建活跃的股票框架
        3. 设置下一次更新
        """
        active_stocks = self.data_manager.get_active_stocks()
        
        # 隐藏不活跃的股票框架
        for stock_code in list(self.stock_frames.keys()):
            if stock_code not in active_stocks:
                self.stock_frames[stock_code].pack_forget()
                
        # 显示或创建活跃的股票框架，并确保顺序
        last_widget = None  # 用于跟踪最后一个已 pack 的部件
        for stock_code in active_stocks:
            if stock_code not in self.stock_frames:
                self.stock_frames[stock_code] = StockFrame(
                    self.main_frame,
                    stock_code,
                    self.callbacks
                )
            
            frame = self.stock_frames[stock_code]
            if not frame.is_mapped():
                # 如果框架未显示，则显示它
                frame.pack(side=tk.TOP, ipadx=1, ipady=1, expand=1)
            elif last_widget:
                # 如果已经显示但需要调整顺序，使用 pack_configure
                frame.frame.pack_configure(
                    side=tk.TOP,
                    ipadx=1,
                    ipady=1,
                    expand=1,
                    after=last_widget
                )
            
            # 更新 last_widget
            last_widget = frame.frame
                
        self.window.after(1000, self._update_stock_frames)
        
    def _update_stock_data(self) -> None:
        """更新股票数据
        
        定期更新所有可见股票的数据，包括：
        1. 价格信息
        2. 成交量
        3. 技术指标
        4. 新闻信息
        
        注意：
        - 只更新当前显示的股票
        - 使用价格管理器获取最新数据
        - 处理可能的异常情况
        """
        for stock_code, frame in self.stock_frames.items():
            if not frame.is_mapped():
                continue
                
            # 更新各项指标
            self._update_stock_indicators(stock_code, frame)
            
        self.window.after(GUI_CONFIG['REFRESH_INTERVAL'], self._update_stock_data)
        
    def _update_stock_indicators(self, stock_code: str, frame: StockFrame) -> None:
        """更新股票指标
        
        更新单个股票的所有指标显示，包括：
        1. 价格变化和百分比
        2. 成交量和平均成交量
        3. 流通股本
        4. 新闻信息
        5. 价差状态
        6. 突破标志
        7. 交易次数
        
        Args:
            stock_code (str): 股票代码
            frame (StockFrame): 股票显示框架实例
        """
        try:
            # 获取所有市场数据
            market_data = self.data_manager.get_market_data(stock_code)
                
            # 更新价格变化（仅当有买入价格时）
            current_price = market_data.get(NOW_PRICE, 0)
            buy_price = market_data.get('buy_price', 0)
            if buy_price > 0 and current_price > 0:
                price_change = current_price - buy_price
                price_change_percent = (price_change / buy_price) * 100
                frame.update_component('up_price', text=f'{price_change:.2f}')
                frame.update_component('up_percentage', text=f'{price_change_percent:.2f}%')
                
            # 更新成交量和平均成交量
            volume = market_data.get(VOLUME, 0)
            volume_avg = market_data.get(VOLUME_AVG, 0)
            if volume > 0:
                # 计算成交量倍数
                volume_ratio = int(volume / volume_avg if volume_avg > 0 else 0)
                
                # 检查是否需要更新缓存和设置颜色
                if volume_ratio >= 5 and stock_code not in self.volume_ratio_high:
                    self.volume_ratio_high.add(stock_code)
                    frame.update_component('stock_volume', text=format_volume(volume), fg=COLORS['HIGH_PRIORITY_TEXT'])
                    frame.update_component('volume_ratio', text=f'{volume_ratio}x')
                else:
                    # 只更新文本
                    frame.update_component('stock_volume', text=format_volume(volume))
                    if volume_ratio > self.volume_ratio_cache.get(stock_code, 0):
                        self.volume_ratio_cache[stock_code] = volume_ratio
                        frame.update_component('volume_ratio', text=f'{volume_ratio}x')
                    
            if volume_avg > 0:
                frame.update_component('volume_avg', text=f'μ:{format_volume(volume_avg)}')
                
            # 更新流通股本
            if stock_code not in self.shares_outstanding_cache:
                if STOCK_SHARESOUTSTANDING in market_data:
                    self.shares_outstanding_cache.add(stock_code)
                shares_outstanding = market_data.get(STOCK_SHARESOUTSTANDING, 0)
                # 检查流通股是否少于1千万
                if shares_outstanding > 0 and shares_outstanding < 10_000_000:
                    frame.update_component('shares_outstanding', text=f'{format_volume(shares_outstanding)}', fg=COLORS['HIGH_PRIORITY_TEXT'])
                else:
                    frame.update_component('shares_outstanding', text=f'{format_volume(shares_outstanding)}')
                
            # 更新新闻
            news_list = market_data.get(NEWS, [])
            if news_list:
                logging.info(f'{stock_code} news_list: {news_list}')
                news_text = '\n'.join(news_list)
                frame.update_component('stock_news', text=news_text)
                if 'FDA' in news_list:
                    self.data_manager.high_all_conditions[stock_code] = True
                
            # 更新价差状态背景色
            is_spread_normal = market_data.get(IS_SPREAD_NORMAL, False)
            frame.frame.configure(bg=COLORS['ACTIVE_BG'] if is_spread_normal else COLORS['DEFAULT_BG'])
            
            # 更新突破标志
            breakout_normal = market_data.get(BREAKOUT_FLG, False)
            # breakout_red = market_data.get(BREAKOUT_RED_FLG, False)
            frame.update_component('breakout_flag', text='⬆️' if breakout_normal else '')
            # frame.update_component('breakout_red_flag', text='⬆' if breakout_red else '')

            # 更新信号强度、置信度和买压
            signal_strength = market_data.get(SIGNAL_STRENGTH, '')
            if signal_strength == REMOVED_FROM_TOP5 or signal_strength == '':
                signal_text = ''
                upward_pressure_text = ''
            else:
                # 更新上涨压力检测标志
                upward_pressure = market_data.get(UPWARD_PRESSURE_DETECTED, False)
                upward_pressure_text = 'T' if upward_pressure else 'F'
                # 更新信号强度
                signal_strength_text = SIGNAL_STRENGTH_TEXT.get(signal_strength, '')
                # 更新行为评分
                behavior_score = market_data.get(BEHAVIOR_SCORE, 0.0)
                behavior_score_15min = market_data.get(BEHAVIOR_SCORE_15MIN, 0.0)
                signal_text = f'{signal_strength_text}  {behavior_score_15min:.1f}  {behavior_score:.1f}'
                
                # 如果是强烈买入信号，启动闪烁效果
                # 启动标签闪烁效果，持续20秒后自动停止
                if signal_strength == SIGNAL_STRONG_BUY:
                    # 检查是否已经在闪烁，如果是则不重复启动
                    if BLINK_COMPONENT_SIGNAL_STRENGTH not in frame._blinking:
                        frame.start_blinking(component_name=BLINK_COMPONENT_SIGNAL_STRENGTH, duration=BLINK_DURATION, interval=BLINK_INTERVAL)
            # 更新信号强度
            frame.update_component(component_name=BLINK_COMPONENT_SIGNAL_STRENGTH, text=signal_text)
            # 更新上涨压力检测标志
            frame.update_component(UPWARD_PRESSURE_DETECTED, text=upward_pressure_text)
            
            
            # 更新交易次数和背景色
            rank = market_data.get(TRANSACTION_RANK, 0)
            volume = market_data.get(TRANSACTION_VOLUME, 0)
            frame.update_component('number_flag', text=format_volume(volume))
            bg_color = RANK_COLORS.get(rank, DEFAULT_COLOR)
            frame.update_component('number_flag', bg=bg_color)
                
            # 检查股票条件
            if not market_data.get(SET_FLG, False):
                self.data_manager.check_stock_conditions(stock_code, market_data)

            # 更新股票代码颜色
            if self.data_manager.all_conditions.get(stock_code, False):
                color = COLORS['HIGH_PRIORITY_TEXT'] if self.data_manager.high_all_conditions.get(stock_code, False) else COLORS['NORMAL_PRIORITY_TEXT']
                frame.update_component('code', fg=color)
                # 通过data_manager设置标志，但主要依赖market_data获取
                self.data_manager.set(stock_code, SET_FLG, True)  # 使用新的方法
                self.data_manager.all_conditions[stock_code] = False
                logging.info(f'符合条件: {stock_code}')
                
        except Exception as e:
            logging.error(f'更新股票 {stock_code} 指标时发生错误: {e}')
        
    def _check_exit_condition(self) -> None:
        """检查是否需要退出"""
        # 获取程序状态，如果为0则退出
        program_status = self.data_manager._data_manager.get_shared_data(0, 1)
        if program_status == 0:
            self.window.destroy()
        self.window.after(10000, self._check_exit_condition)
        
    def _start_hotkey_listener(self) -> None:
        """启动快捷键监听"""
        def key_handler(event):
            key_code = event.keyCode()
            modifiers = event.modifierFlags()
            
            if modifiers & NSEventModifierFlagCommand:
                if key_code in HOTKEYS['BUY_KEYS']:
                    print(f"🔥 Command + {key_code}被触发！")
                    self._handle_buy_hotkey()
                elif key_code == HOTKEYS['STOP_SOUND_KEY']:
                    print(f"🔥 Command + {key_code}被触发！")
                    self._handle_stop_sound_hotkey()
            return event
            
        NSEvent.addGlobalMonitorForEventsMatchingMask_handler_(
            NSEventMaskKeyDown,
            key_handler
        )
        AppHelper.runConsoleEventLoop()
        
    def _handle_buy_hotkey(self) -> None:
        """处理买入快捷键"""
        try:
            stock_code = get_stock_code()
            if stock_code in self.stock_frames:
                current_price = self.data_manager.get_current_price(stock_code)
                if current_price > 0:
                    self.data_manager.set_buy_flag(stock_code, True)
                    self.data_manager.update_buy_price(stock_code, current_price)
                    frame = self.stock_frames.get(stock_code)
                    frame.update_component('buy_price', text=current_price)
                    logging.info(f'股票 {stock_code} 买入价格设置为: {current_price}')
                else:
                    logging.warning(f'无法设置股票 {stock_code} 的买入价格，因为无法获取当前价格')
        except Exception as e:
            if 'list index out of range' not in str(e):
                logging.error(f'处理买入快捷键时发生错误: {e}')
            
    def _handle_stop_sound_hotkey(self) -> None:
        """处理停止声音快捷键
        
        当用户按下停止声音快捷键时：
        1. 获取当前选中的股票代码
        2. 如果该股票已设置买入标志，则取消标志
        3. 记录日志
        """
        try:
            stock_code = get_stock_code()
            if self.data_manager.is_buy_flag_set(stock_code):
                # 取消买入标志
                self.data_manager.set_buy_flag(stock_code, False)
                logging.info(f'已停止股票 {stock_code} 的声音提醒')
        except Exception as e:
            if 'list index out of range' not in str(e):
                logging.error(f'处理停止声音快捷键时发生错误: {e}')
            
    def _open_futu_interface(self, event: tk.Event, stock_code: str) -> None:
        """打开富途界面"""
        pyperclip.copy(stock_code)
        os.system("""osascript -e 'tell app "moomooCommand" to open'""")
        
    def _update_buy_price(self, event: tk.Event, stock_code: str) -> None:
        """更新买入价格"""
        frame = self.stock_frames.get(stock_code)
        if frame:
            price_text = frame.components['buy_price'].get("1.0", "end-1c").strip()
            if price_text:
                try:
                    price = float(price_text)
                    self.data_manager.update_buy_price(stock_code, price)
                except ValueError:
                    logging.error(f'价格格式无效 {stock_code}: {price_text}')

    def run(self) -> None:
        """运行应用"""
        self.window.mainloop()