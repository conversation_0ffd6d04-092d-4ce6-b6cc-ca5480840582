import tkinter as tk

class BlinkingDemo:
    def __init__(self, root):
        self.root = root
        root.title("文字闪烁效果演示")
        root.geometry("400x300")
        
        # 初始化闪烁状态变量
        self.is_blinking = True
        
        # 创建标题
        tk.Label(root, text="Tkinter 文字闪烁效果演示", font=("Arial", 16, "bold")).pack(pady=10)
        
        # 方法1：前景色闪烁
        frame1 = tk.Frame(root, bd=2, relief=tk.GROOVE)
        frame1.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(frame1, text="方法1: 前景色闪烁").pack(anchor=tk.W)
        self.label1 = tk.Label(frame1, text="价格突破!", font=("Arial", 12, "bold"))
        self.label1.pack(pady=5)
        self.toggle_color(self.label1, ["red", "black"])
        
        # 方法2：背景色闪烁
        frame2 = tk.Frame(root, bd=2, relief=tk.GROOVE)
        frame2.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(frame2, text="方法2: 背景色闪烁").pack(anchor=tk.W)
        self.label2 = tk.Label(frame2, text="重要提醒!", font=("Arial", 12, "bold"))
        self.label2.pack(pady=5)
        self.blink_background(self.label2, ["red", "yellow"], ["white", "black"])
        
        # 方法3：文本显示/隐藏闪烁
        frame3 = tk.Frame(root, bd=2, relief=tk.GROOVE)
        frame3.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(frame3, text="方法3: 文本显示/隐藏闪烁").pack(anchor=tk.W)
        self.label3 = tk.Label(frame3, text="", font=("Arial", 12, "bold"))
        self.label3.pack(pady=5)
        self.blink_text(self.label3, "注意!")
        
        # 控制按钮
        control_frame = tk.Frame(root)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.toggle_button = tk.Button(control_frame, text="暂停闪烁", command=self.toggle_blinking)
        self.toggle_button.pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="退出", command=root.quit).pack(side=tk.RIGHT, padx=5)
    
    def toggle_color(self, label, colors, index=0):
        """切换标签颜色以实现闪烁效果"""
        if not self.is_blinking:
            return
            
        label.config(fg=colors[index])
        next_index = (index + 1) % len(colors)
        label._blink_id = label.after(500, self.toggle_color, label, colors, next_index)
    
    def blink_background(self, label, bg_colors, fg_colors, index=0):
        """切换标签背景色和前景色以实现闪烁效果"""
        if not self.is_blinking:
            return
            
        label.config(bg=bg_colors[index], fg=fg_colors[index])
        next_index = (index + 1) % len(bg_colors)
        label._blink_id = label.after(500, self.blink_background, label, bg_colors, fg_colors, next_index)
    
    def blink_text(self, label, text, empty=False):
        """通过显示和隐藏文本实现闪烁效果"""
        if not self.is_blinking:
            return
            
        if empty:
            label.config(text="")
        else:
            label.config(text=text)
        label._blink_id = label.after(500, self.blink_text, label, text, not empty)
    
    def toggle_blinking(self):
        """切换闪烁状态"""
        self.is_blinking = not self.is_blinking
        
        if self.is_blinking:
            self.toggle_button.config(text="暂停闪烁")
            # 重新启动所有闪烁效果
            self.toggle_color(self.label1, ["red", "black"])
            self.blink_background(self.label2, ["red", "yellow"], ["white", "black"])
            self.blink_text(self.label3, "注意!")
        else:
            self.toggle_button.config(text="恢复闪烁")
            # 停止所有闪烁效果
            for label in [self.label1, self.label2, self.label3]:
                if hasattr(label, '_blink_id'):
                    label.after_cancel(label._blink_id)
            
            # 重置标签状态
            self.label1.config(fg="black")
            self.label2.config(bg="SystemButtonFace", fg="black")
            self.label3.config(text="注意!")

if __name__ == "__main__":
    root = tk.Tk()
    app = BlinkingDemo(root)
    root.mainloop()