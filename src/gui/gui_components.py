import tkinter as tk
from typing import Any, Callable
from src.config.config import COLORS, GUI_CONFIG
from src.utils.constants import UPWARD_PRESSURE_DETECTED
import logging

class StockFrame:
    """单个股票信息展示框架
    
    这个类负责创建和管理单个股票的GUI展示组件，包括：
    - 股票代码和价格信息
    - 新闻展示区域
    - 成交量信息
    - 各类指标显示
    """
    def __init__(self, parent: tk.Frame, stock_code: str, callbacks: dict[str, Callable[[tk.Event, str], None]]) -> None:
        """初始化股票展示框架
        
        Args:
            parent: 父级窗口组件
            stock_code: 股票代码
            callbacks: 回调函数字典，包含处理点击等事件的函数
        """
        # 创建主框架，设置默认背景色
        self.frame = tk.Frame(parent, borderwidth=0, bg=COLORS['DEFAULT_BG'])
        self.stock_code = stock_code
        # 存储所有子组件的引用
        self.components = {}
        # 缓存文本组件的值
        self._component_cached_text = {}
        # 缓存其他组件的值
        self._component_cached_values = {}
        # 闪烁状态和定时器ID
        self._blinking = {}  # 存储各组件的闪烁状态信息，包括颜色列表和当前颜色索引
        self._blink_timers = {}  # 存储各组件的闪烁定时器ID，用于取消定时器
        # 创建所有子组件
        self._create_components(callbacks)
        
    def _create_components(self, callbacks: dict[str, Callable[[tk.Event, str], None]]) -> None:
        """创建所有子组件
        
        创建并布局所有需要的GUI组件，包括：
        - 股票代码标签
        - 价格信息区域
        - 新闻显示区域
        - 成交量信息
        - 其他技术指标

        Args:
            callbacks: 回调函数字典，包含处理点击等事件的函数
        """
        # 创建主框架，固定大小以确保布局一致性
        code_frame = tk.Frame(self.frame, width=210, height=105, borderwidth=0)
        code_frame.pack(anchor=tk.W, side=tk.TOP, ipadx=1, ipady=1, expand=1)
        code_frame.pack_propagate(0)  # 防止框架自动调整大小
        
        # 创建各行组件
        self._create_first_row(code_frame, callbacks)  # 第一行：股票代码和价格信息
        self._create_news_row(code_frame)  # 第二行：新闻框架
        self._create_volume_row(code_frame)  # 第三行：成交量信息
        self._create_indicators_row(code_frame)  # 第四行：其他指标
        
    def _create_first_row(self, parent: tk.Frame, callbacks: dict[str, Callable[[tk.Event, str], None]]) -> None:
        """创建第一行组件：股票代码和价格信息
        
        包含：
        - 可点击的股票代码标签
        - 买入价格输入框
        - 涨跌价格显示
        - 涨跌百分比显示

        Args:
            parent: 父级窗口组件
            callbacks: 回调函数字典，包含处理点击等事件的函数
        """
        # 创建第一行框架
        row = tk.Frame(parent)
        row.pack(anchor=tk.W, side=tk.TOP, fill=tk.X)
        
        # 股票代码标签，点击可打开富途交易界面
        code_label = tk.Label(row, text=self.stock_code)
        code_label.pack(side=tk.LEFT, padx=0, pady=0)
        code_label.bind("<Button-1>", lambda e: callbacks['open_futu'](e, self.stock_code))
        self.components['code'] = code_label
        
        # 买入价格输入框，失去焦点时更新价格
        buy_price = tk.Text(row, width=5, height=1.3)
        buy_price.pack(side=tk.LEFT, padx=0, pady=0)
        buy_price.bind("<FocusOut>", lambda e: callbacks['update_buy_price'](e, self.stock_code))
        self.components['buy_price'] = buy_price
        
        # 涨跌价格标签
        self.components['up_price'] = tk.Label(row, width=5)
        self.components['up_price'].pack(side=tk.LEFT, padx=0, pady=0)
        
        # 涨跌百分比标签
        self.components['up_percentage'] = tk.Label(row, width=7)
        self.components['up_percentage'].pack(side=tk.LEFT, padx=0, pady=0)
        
    def _create_news_row(self, parent: tk.Frame) -> None:
        """创建新闻显示区域
        
        创建一个带滚动条的文本框，用于显示股票相关新闻

        Args:
            parent: 父级窗口组件
        """
        # 创建第二行框架作为容器
        row2_frame = tk.Frame(parent)
        row2_frame.pack(anchor=tk.W, side=tk.TOP, fill=tk.X)
        
        # 创建滚动框架，用于容纳文本框和滚动条
        scroll_frame = tk.Frame(row2_frame)
        scroll_frame.pack(anchor="w", side=tk.LEFT, fill=tk.X, expand=True)
        
        # 创建垂直滚动条
        news_scrollbar = tk.Scrollbar(scroll_frame, orient="vertical")
        news_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建新闻文本框，设置为只读模式
        news_text = tk.Text(
            scroll_frame,
            wrap="word",  # 自动换行
            width=30,
            height=3,
            yscrollcommand=news_scrollbar.set,
            state="disabled"  # 设置为只读
        )
        news_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        news_scrollbar.config(command=news_text.yview)  # 绑定滚动条到文本框
        self.components['stock_news'] = news_text
        
    def _create_volume_row(self, parent: tk.Frame) -> None:
        """创建成交量信息行
        
        显示：
        - 当前成交量
        - 成交量倍数
        - 平均成交量
        - 流通股数

        Args:
            parent: 父级窗口组件
        """
        # 创建第三行框架
        row = tk.Frame(parent)
        row.pack(anchor=tk.W, side=tk.TOP, fill=tk.X)
        
        # 当前成交量标签
        self.components['stock_volume'] = tk.Label(row, width=5)
        self.components['stock_volume'].pack(side=tk.LEFT, padx=0, pady=0)
        
        # 成交量倍数标签
        self.components['volume_ratio'] = tk.Label(row, width=3)
        self.components['volume_ratio'].pack(side=tk.LEFT, padx=0, pady=0)
        
        # 平均成交量标签
        self.components['volume_avg'] = tk.Label(row, width=7)
        self.components['volume_avg'].pack(side=tk.LEFT, padx=0, pady=0)
        
        # 流通股数标签
        self.components['shares_outstanding'] = tk.Label(row, width=5)
        self.components['shares_outstanding'].pack(side=tk.LEFT, padx=0, pady=0)
        
    def _create_indicators_row(self, parent: tk.Frame) -> None:
        """创建技术指标行
        
        显示：
        - 交易次数标志
        - 突破标志
        - 红色K线突破标志

        Args:
            parent: 父级窗口组件
        """
        # 创建第四行框架
        row = tk.Frame(parent)
        row.pack(anchor=tk.W, side=tk.TOP, fill=tk.X)
        
        # 交易次数标志
        self.components['number_flag'] = tk.Label(row, width=8)
        self.components['number_flag'].pack(side=tk.LEFT, padx=0, pady=0)
        
        # 突破标志
        self.components['breakout_flag'] = tk.Label(row, width=2)
        self.components['breakout_flag'].pack(side=tk.LEFT, padx=0, pady=0)
        
        # # 红色K线突破标志
        # self.components['breakout_red_flag'] = tk.Label(row, width=3)
        # self.components['breakout_red_flag'].pack(side=tk.LEFT, padx=0, pady=0)

        # 信号强度
        self.components['signal_strength'] = tk.Label(row, width=9)
        self.components['signal_strength'].pack(side=tk.LEFT, padx=0, pady=0)
        # 检测到上涨压力
        self.components[UPWARD_PRESSURE_DETECTED] = tk.Label(row, width=2)
        self.components[UPWARD_PRESSURE_DETECTED].pack(side=tk.LEFT, padx=0, pady=0)
        
    def update_component(self, component_name: str, **kwargs: Any) -> None:
        """更新组件属性
        
        Args:
            component_name: 要更新的组件名称
            **kwargs: 要更新的属性和值，例如：
                     - text: 文本内容
                     - fg: 前景色
                     - bg: 背景色
                     等tkinter支持的属性
        """
        if component_name in self.components:
            component = self.components[component_name]
            if isinstance(component, tk.Text):
                if 'text' in kwargs:
                    new_text = kwargs['text']
                    cached_text = self._component_cached_text.get(component_name, "")
                    logging.info(f'{component_name} cached_text: {cached_text}')
                    if new_text == cached_text: # 只有文本不同时才更新
                        return
                    self._component_cached_text[component_name] = new_text
                    component.config(state="normal")  # 临时启用编辑
                    component.delete("1.0", "end")  # 清空现有内容
                    component.insert("1.0", new_text)  # 插入新内容
                    component.config(state="disabled")  # 恢复只读状态
            else:
                # 只有有需要更新的属性时才调用 configure
                if kwargs != self._component_cached_values.get(component_name, {}):
                    self._component_cached_values[component_name] = kwargs
                    component.configure(**kwargs)
                
    def start_blinking(self, component_name: str, blink_colors: list[str] | None = None, interval: int = 500, duration: int = 20000) -> None:
        """开始组件闪烁效果
        
        通过定时切换组件的前景色，实现闪烁效果。闪烁会在指定的持续时间后自动停止。
        
        Args:
            component_name: 要闪烁的组件名称
            blink_colors: 闪烁颜色列表，默认为红色和默认颜色
            interval: 闪烁间隔，单位为毫秒
            duration: 闪烁持续时间，单位为毫秒，默认20秒
        """
        if component_name not in self.components:
            return
            
        # 如果已经在闪烁，先停止当前闪烁效果
        self.stop_blinking(component_name)
        
        # 设置默认闪烁颜色，如果没有指定则使用红色和默认文本颜色
        if blink_colors is None:
            blink_colors = [COLORS.get('HIGH_PRIORITY_TEXT', 'red'), COLORS.get('DEFAULT_TEXT', 'black')]
        
        # 获取要闪烁的组件
        component = self.components[component_name]
        # 初始化闪烁状态数据
        self._blinking[component_name] = {'colors': blink_colors, 'index': 0}
        
        # 定义闪烁函数，实现颜色切换的核心逻辑
        def blink():
            # 检查组件是否仍在闪烁列表中
            if component_name in self._blinking:
                blink_data = self._blinking[component_name]
                color_index = blink_data['index']
                # 获取当前应该显示的颜色
                current_color = blink_data['colors'][color_index]
                
                # 更新组件颜色
                component.configure(fg=current_color)
                
                # 切换到下一个颜色索引，循环使用颜色列表
                blink_data['index'] = (color_index + 1) % len(blink_data['colors'])
                
                # 设置下一次闪烁的定时器
                self._blink_timers[component_name] = component.after(interval, blink)
        
        # 开始第一次闪烁
        blink()
        
        # 设置自动停止闪烁的定时器，在指定持续时间后停止闪烁
        component.after(duration, lambda: self.stop_blinking(component_name))
    
    def stop_blinking(self, component_name: str) -> None:
        """停止组件闪烁
        
        取消闪烁定时器，恢复组件的默认颜色。
        
        Args:
            component_name: 要停止闪烁的组件名称
        """
        # 如果存在闪烁定时器，取消它
        if component_name in self._blink_timers:
            self.components[component_name].after_cancel(self._blink_timers[component_name])
            del self._blink_timers[component_name]
            
        # 清除闪烁状态数据
        if component_name in self._blinking:
            del self._blinking[component_name]
            
            # 恢复组件的默认颜色
            self.components[component_name].configure(fg=COLORS.get('DEFAULT_TEXT', 'black'))
        
    def pack(self, **kwargs: Any) -> None:
        """显示框架
        
        Args:
            **kwargs: pack方法的参数，例如：
                     - side: 停靠方向
                     - fill: 填充方式
                     - expand: 是否扩展
        """
        self.frame.pack(**kwargs)
        
    def pack_forget(self) -> None:
        """隐藏框架
        
        从父容器中移除此框架，但保留框架实例以便后续重新显示
        """
        # 停止所有闪烁效果，防止隐藏的组件继续消耗资源
        for component_name in list(self._blink_timers.keys()):
            self.stop_blinking(component_name)
            
        self.frame.pack_forget()
        
    def is_mapped(self) -> bool:
        """检查框架是否显示
        
        Returns:
            bool: 框架是否正在显示
        """
        return self.frame.winfo_ismapped() 