"""MACD指标检查模块"""

import pandas as pd
from src.utils.constants import (
    MACD_FLG,
    MACD_5_FLG
)
from src.data.market_data_manager import MarketDataManager

data_manager = MarketDataManager()
"""
市场数据管理器
"""


# 计算macd
def calculate_macd(close: pd.Series) -> pd.Series:
    """
    计算MACD 指标
    返回: macd_hist
    """
    dif = close.ewm(span=12, adjust=False).mean() - close.ewm(span=26, adjust=False).mean()
    dea = dif.ewm(span=9, adjust=False).mean()
    macd_hist = dif - dea
    return macd_hist

def macd_signal(stock_code: str, df: pd.DataFrame) -> None:
    """计算MACD信号
    
    Args:
        stock_code: 股票代码
        df: 股票数据DataFrame
    """
    macd_hist = calculate_macd(df['Close'])
    # 当整体最大值小于当前值的 5 倍时返回 True
    max_macd = macd_hist.iloc[-121:-1].max() if len(macd_hist) >= 121 else 0
    last_macd = macd_hist.iloc[-1]

    data_manager.set(stock_code, MACD_FLG, (
        max_macd <= 7 * last_macd and last_macd > 0
    ))

    # 对 df 进行 5 分钟重采样
    close_5min = df['Close'].resample('5min', label='right', closed='right').last()
    macd_hist_5min = calculate_macd(close_5min)
    max_macd_5 = macd_hist_5min.iloc[-59:-1].max() if len(macd_hist_5min) >= 59 else 0
    last_macd_5 = macd_hist_5min.iloc[-1]
    
    # 当整体最大值小于当前值的 5 倍时返回 True
    data_manager.set(stock_code, MACD_5_FLG, (
        max_macd_5 <= 7 * last_macd_5 and last_macd_5 > 0
    ))

    # logging.info(f'{stock_code} macd {max_macd} {last_macd} {manager_res_dict_test[f'{stock_code}_{MACD_FLG}']}')
    # logging.info(f'{stock_code} macd5 {max_macd_5} {last_macd_5} {manager_res_dict_test[f'{stock_code}_{MACD_5_FLG}']}')