"""
历史回测验证模块
用于验证细价股上涨压力分析系统的准确性和有效性
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
import json
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from src.analysis.stock_analyzer import StockAnalyzer, TechnicalSignal, SignalStrength, OrderBookBehavior

@dataclass
class BacktestResult:
    """回测结果数据类"""
    stock_code: str
    test_date: str
    analysis_time: datetime
    
    # 预测结果
    predicted_pressure: bool
    predicted_signal_strength: SignalStrength
    predicted_confidence: float
    
    # 实际结果（未来N分钟的表现）
    actual_price_change_5min: float
    actual_price_change_15min: float
    actual_price_change_30min: float
    actual_max_drawdown_5min: float
    actual_max_drawdown_15min: float
    actual_max_drawdown_30min: float
    
    # 准确性指标
    pressure_prediction_correct: bool
    signal_strength_correct: bool
    confidence_calibrated: bool
    
    # 详细特征
    pressure_features: Dict
    ml_features: Optional[Dict] = None

@dataclass
class BacktestSummary:
    """回测汇总统计"""
    total_tests: int
    pressure_accuracy: float
    signal_accuracy: float
    confidence_calibration: float
    
    # 分时段准确率
    accuracy_5min: float
    accuracy_15min: float
    accuracy_30min: float
    
    # 风险指标
    false_positive_rate: float
    false_negative_rate: float
    max_loss_when_predicted_safe: float
    
    # 性能指标
    avg_analysis_time: float
    total_analysis_time: float

class BacktestValidator:
    """历史回测验证器"""
    
    def __init__(self, analyzer: Optional[StockAnalyzer] = None):
        self.analyzer = analyzer or StockAnalyzer(enable_ml=True)
        self.results: List[BacktestResult] = []
        
    def run_single_backtest(self, 
                           ticker_data: pd.DataFrame,
                           orderbook_data: pd.DataFrame,
                           analysis_time: datetime,
                           stock_code: str = "TEST") -> BacktestResult:
        """
        运行单次回测
        
        Args:
            ticker_data: 逐笔成交数据
            orderbook_data: 买卖盘数据
            analysis_time: 分析时间点
            stock_code: 股票代码
            
        Returns:
            BacktestResult: 回测结果
        """
        start_time = time.time()
        
        try:
            # 1. 准备分析窗口数据（分析时间前15分钟）
            window_start = analysis_time - timedelta(minutes=15)
            window_end = analysis_time
            
            ticker_window = ticker_data[
                (ticker_data['date_time'] >= window_start) & 
                (ticker_data['date_time'] <= window_end)
            ].copy()
            
            orderbook_window = orderbook_data[
                (orderbook_data['date_time'] >= window_start) & 
                (orderbook_data['date_time'] <= window_end)
            ].copy()
            
            # 历史数据（分析时间前30分钟）
            history_start = analysis_time - timedelta(minutes=30)
            history_data = ticker_data[
                (ticker_data['date_time'] >= history_start) & 
                (ticker_data['date_time'] <= window_end)
            ].copy()
            
            if ticker_window.empty or orderbook_window.empty:
                logging.warning(f"数据不足，跳过时间点: {analysis_time}")
                return self._create_default_result(stock_code, analysis_time)
            
            # 2. 执行分析
            signal = self.analyzer.analyze_window(ticker_window, orderbook_window, history_data)
            
            # 3. 计算实际结果（未来表现）
            future_results = self._calculate_future_performance(
                ticker_data, analysis_time
            )
            
            # 4. 评估预测准确性
            accuracy_metrics = self._evaluate_prediction_accuracy(
                signal, future_results
            )
            
            # 5. 提取特征信息
            pressure_features = self._extract_pressure_features(signal.book_behavior)
            ml_features = signal.tech_indicators if signal.tech_indicators else None
            
            analysis_duration = time.time() - start_time
            
            return BacktestResult(
                stock_code=stock_code,
                test_date=analysis_time.strftime('%Y-%m-%d'),
                analysis_time=analysis_time,
                predicted_pressure=signal.book_behavior.upward_pressure_detected if signal.book_behavior else False,
                predicted_signal_strength=signal.signal_strength,
                predicted_confidence=signal.confidence,
                actual_price_change_5min=future_results['price_change_5min'],
                actual_price_change_15min=future_results['price_change_15min'],
                actual_price_change_30min=future_results['price_change_30min'],
                actual_max_drawdown_5min=future_results['max_drawdown_5min'],
                actual_max_drawdown_15min=future_results['max_drawdown_15min'],
                actual_max_drawdown_30min=future_results['max_drawdown_30min'],
                pressure_prediction_correct=accuracy_metrics['pressure_correct'],
                signal_strength_correct=accuracy_metrics['signal_correct'],
                confidence_calibrated=accuracy_metrics['confidence_calibrated'],
                pressure_features=pressure_features,
                ml_features=ml_features
            )
            
        except Exception as e:
            logging.error(f"回测时出错 {analysis_time}: {str(e)}")
            return self._create_default_result(stock_code, analysis_time)
    
    def _calculate_future_performance(self, 
                                    ticker_data: pd.DataFrame, 
                                    analysis_time: datetime) -> Dict:
        """计算未来表现指标"""
        future_5min = analysis_time + timedelta(minutes=5)
        future_15min = analysis_time + timedelta(minutes=15)
        future_30min = analysis_time + timedelta(minutes=30)
        
        # 获取分析时间点的价格
        current_price_data = ticker_data[ticker_data['date_time'] <= analysis_time]
        if current_price_data.empty:
            return self._default_future_performance()
        
        current_price = current_price_data['price'].iloc[-1]
        
        # 计算未来各时间点的价格变化
        results = {}
        
        for period, future_time in [('5min', future_5min), ('15min', future_15min), ('30min', future_30min)]:
            future_data = ticker_data[
                (ticker_data['date_time'] > analysis_time) & 
                (ticker_data['date_time'] <= future_time)
            ]
            
            if future_data.empty:
                results[f'price_change_{period}'] = 0.0
                results[f'max_drawdown_{period}'] = 0.0
            else:
                # 最终价格变化
                final_price = future_data['price'].iloc[-1]
                price_change = (final_price - current_price) / current_price
                results[f'price_change_{period}'] = price_change
                
                # 最大回撤
                min_price = future_data['price'].min()
                max_drawdown = (min_price - current_price) / current_price
                results[f'max_drawdown_{period}'] = max_drawdown
        
        return results
    
    def _evaluate_prediction_accuracy(self, 
                                    signal: TechnicalSignal, 
                                    future_results: Dict) -> Dict:
        """评估预测准确性"""
        # 压力预测准确性：如果预测有压力，未来应该下跌或横盘
        predicted_pressure = signal.book_behavior.upward_pressure_detected if signal.book_behavior else False
        
        # 定义"准确"的标准
        # 如果预测有压力，未来15分钟内价格变化应该 <= 1%
        actual_change_15min = future_results['price_change_15min']
        
        if predicted_pressure:
            pressure_correct = actual_change_15min <= 0.01  # 预测有压力，实际涨幅不超过1%
        else:
            pressure_correct = actual_change_15min > -0.02  # 预测无压力，实际跌幅不超过2%
        
        # 信号强度准确性
        signal_strength = signal.signal_strength
        if signal_strength in [SignalStrength.STRONG_BUY, SignalStrength.BUY]:
            signal_correct = actual_change_15min > 0  # 买入信号，应该上涨
        elif signal_strength == SignalStrength.AVOID:
            signal_correct = actual_change_15min <= 0.01  # 避免信号，应该不涨或微涨
        else:
            signal_correct = True  # 观望和弱势信号不做严格要求
        
        # 置信度校准：高置信度的预测应该更准确
        confidence_calibrated = True
        if signal.confidence > 0.8:
            # 高置信度预测，要求更严格
            if predicted_pressure:
                confidence_calibrated = actual_change_15min <= 0.005  # 高置信度压力预测，涨幅不超过0.5%
            else:
                confidence_calibrated = actual_change_15min > -0.01  # 高置信度安全预测，跌幅不超过1%
        
        return {
            'pressure_correct': pressure_correct,
            'signal_correct': signal_correct,
            'confidence_calibrated': confidence_calibrated
        }
    
    def _extract_pressure_features(self, behavior: Optional[OrderBookBehavior]) -> Dict:
        """提取压力特征信息"""
        if not behavior:
            return {}
        
        return {
            'large_buy_orders_decrease': behavior.large_buy_orders_decrease,
            'large_buy_frequency_decline': behavior.large_buy_frequency_decline,
            'trading_structure_shift': behavior.trading_structure_shift,
            'high_density_small_orders': behavior.high_density_small_orders,
            'price_jump_decline': behavior.price_jump_decline,
            'sell_orders_piling': behavior.sell_orders_piling,
            'buy_side_thinning': behavior.buy_side_thinning,
            'retail_takeover_top': behavior.retail_takeover_top,
            'pressure_intensifying_top': behavior.pressure_intensifying_top,
            'bull_trap_top': behavior.bull_trap_top,
            'weak_limit_up_top': behavior.weak_limit_up_top,
            'critical_15_30min_pressure': behavior.critical_15_30min_pressure,
            'volume_price_divergence': behavior.volume_price_divergence,
            'order_flow_momentum_loss': behavior.order_flow_momentum_loss
        }
    
    def _create_default_result(self, stock_code: str, analysis_time: datetime) -> BacktestResult:
        """创建默认回测结果"""
        return BacktestResult(
            stock_code=stock_code,
            test_date=analysis_time.strftime('%Y-%m-%d'),
            analysis_time=analysis_time,
            predicted_pressure=False,
            predicted_signal_strength=SignalStrength.AVOID,
            predicted_confidence=0.0,
            actual_price_change_5min=0.0,
            actual_price_change_15min=0.0,
            actual_price_change_30min=0.0,
            actual_max_drawdown_5min=0.0,
            actual_max_drawdown_15min=0.0,
            actual_max_drawdown_30min=0.0,
            pressure_prediction_correct=False,
            signal_strength_correct=False,
            confidence_calibrated=False,
            pressure_features={},
            ml_features=None
        )
    
    def _default_future_performance(self) -> Dict:
        """默认未来表现"""
        return {
            'price_change_5min': 0.0,
            'price_change_15min': 0.0,
            'price_change_30min': 0.0,
            'max_drawdown_5min': 0.0,
            'max_drawdown_15min': 0.0,
            'max_drawdown_30min': 0.0
        }

    def run_batch_backtest(self,
                          ticker_data: pd.DataFrame,
                          orderbook_data: pd.DataFrame,
                          start_time: datetime,
                          end_time: datetime,
                          interval_minutes: int = 30,
                          stock_code: str = "TEST",
                          max_workers: int = 4) -> List[BacktestResult]:
        """
        运行批量回测

        Args:
            ticker_data: 逐笔成交数据
            orderbook_data: 买卖盘数据
            start_time: 回测开始时间
            end_time: 回测结束时间
            interval_minutes: 测试间隔（分钟）
            stock_code: 股票代码
            max_workers: 最大并发数

        Returns:
            List[BacktestResult]: 回测结果列表
        """
        # 生成测试时间点
        test_times = []
        current_time = start_time
        while current_time <= end_time:
            test_times.append(current_time)
            current_time += timedelta(minutes=interval_minutes)

        logging.info(f"开始批量回测: {len(test_times)} 个时间点，间隔 {interval_minutes} 分钟")

        results = []

        # 并发执行回测
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_time = {
                executor.submit(
                    self.run_single_backtest,
                    ticker_data,
                    orderbook_data,
                    test_time,
                    stock_code
                ): test_time
                for test_time in test_times
            }

            # 收集结果
            completed = 0
            for future in as_completed(future_to_time):
                test_time = future_to_time[future]
                try:
                    result = future.result()
                    results.append(result)
                    completed += 1

                    if completed % 10 == 0:
                        logging.info(f"已完成 {completed}/{len(test_times)} 个回测")

                except Exception as e:
                    logging.error(f"回测失败 {test_time}: {str(e)}")

        # 按时间排序
        results.sort(key=lambda x: x.analysis_time)
        self.results.extend(results)

        logging.info(f"批量回测完成: {len(results)} 个有效结果")
        return results

    def calculate_summary_statistics(self, results: Optional[List[BacktestResult]] = None) -> BacktestSummary:
        """
        计算汇总统计

        Args:
            results: 回测结果列表，如果为None则使用self.results

        Returns:
            BacktestSummary: 汇总统计
        """
        if results is None:
            results = self.results

        if not results:
            return BacktestSummary(
                total_tests=0,
                pressure_accuracy=0.0,
                signal_accuracy=0.0,
                confidence_calibration=0.0,
                accuracy_5min=0.0,
                accuracy_15min=0.0,
                accuracy_30min=0.0,
                false_positive_rate=0.0,
                false_negative_rate=0.0,
                max_loss_when_predicted_safe=0.0,
                avg_analysis_time=0.0,
                total_analysis_time=0.0
            )

        total_tests = len(results)

        # 基础准确率
        pressure_correct = sum(1 for r in results if r.pressure_prediction_correct)
        signal_correct = sum(1 for r in results if r.signal_strength_correct)
        confidence_calibrated = sum(1 for r in results if r.confidence_calibrated)

        pressure_accuracy = pressure_correct / total_tests
        signal_accuracy = signal_correct / total_tests
        confidence_calibration = confidence_calibrated / total_tests

        # 分时段准确率（基于实际价格变化）
        accuracy_5min = self._calculate_period_accuracy(results, '5min')
        accuracy_15min = self._calculate_period_accuracy(results, '15min')
        accuracy_30min = self._calculate_period_accuracy(results, '30min')

        # 风险指标
        false_positive_rate, false_negative_rate = self._calculate_error_rates(results)
        max_loss_when_predicted_safe = self._calculate_max_loss_when_safe(results)

        # 性能指标（模拟）
        avg_analysis_time = 0.1  # 假设平均分析时间
        total_analysis_time = avg_analysis_time * total_tests

        return BacktestSummary(
            total_tests=total_tests,
            pressure_accuracy=pressure_accuracy,
            signal_accuracy=signal_accuracy,
            confidence_calibration=confidence_calibration,
            accuracy_5min=accuracy_5min,
            accuracy_15min=accuracy_15min,
            accuracy_30min=accuracy_30min,
            false_positive_rate=false_positive_rate,
            false_negative_rate=false_negative_rate,
            max_loss_when_predicted_safe=max_loss_when_predicted_safe,
            avg_analysis_time=avg_analysis_time,
            total_analysis_time=total_analysis_time
        )

    def _calculate_period_accuracy(self, results: List[BacktestResult], period: str) -> float:
        """计算特定时期的准确率"""
        correct_predictions = 0
        total_predictions = 0

        for result in results:
            if period == '5min':
                actual_change = result.actual_price_change_5min
            elif period == '15min':
                actual_change = result.actual_price_change_15min
            elif period == '30min':
                actual_change = result.actual_price_change_30min
            else:
                continue

            # 预测逻辑：如果预测有压力，期望价格不上涨或微涨
            predicted_pressure = result.predicted_pressure

            if predicted_pressure:
                # 预测有压力，实际涨幅应该很小
                correct = actual_change <= 0.01
            else:
                # 预测无压力，实际不应该大跌
                correct = actual_change > -0.02

            if correct:
                correct_predictions += 1
            total_predictions += 1

        return correct_predictions / total_predictions if total_predictions > 0 else 0.0

    def _calculate_error_rates(self, results: List[BacktestResult]) -> Tuple[float, float]:
        """计算假阳性和假阴性率"""
        true_positives = 0  # 正确预测有压力
        false_positives = 0  # 错误预测有压力
        true_negatives = 0  # 正确预测无压力
        false_negatives = 0  # 错误预测无压力

        for result in results:
            predicted_pressure = result.predicted_pressure
            # 实际是否有压力：15分钟内涨幅 <= 1% 认为确实有压力
            actual_pressure = result.actual_price_change_15min <= 0.01

            if predicted_pressure and actual_pressure:
                true_positives += 1
            elif predicted_pressure and not actual_pressure:
                false_positives += 1
            elif not predicted_pressure and not actual_pressure:
                true_negatives += 1
            elif not predicted_pressure and actual_pressure:
                false_negatives += 1

        total_negatives = true_negatives + false_positives
        total_positives = true_positives + false_negatives

        false_positive_rate = false_positives / total_negatives if total_negatives > 0 else 0.0
        false_negative_rate = false_negatives / total_positives if total_positives > 0 else 0.0

        return false_positive_rate, false_negative_rate

    def _calculate_max_loss_when_safe(self, results: List[BacktestResult]) -> float:
        """计算预测安全时的最大损失"""
        max_loss = 0.0

        for result in results:
            if not result.predicted_pressure:  # 预测安全
                # 检查各时期的最大回撤
                max_loss = min(max_loss, result.actual_max_drawdown_5min)
                max_loss = min(max_loss, result.actual_max_drawdown_15min)
                max_loss = min(max_loss, result.actual_max_drawdown_30min)

        return abs(max_loss)  # 返回正值表示损失

    def export_results(self, filepath: str, results: Optional[List[BacktestResult]] = None):
        """导出回测结果到文件"""
        if results is None:
            results = self.results

        # 转换为DataFrame
        data = []
        for result in results:
            row = {
                'stock_code': result.stock_code,
                'test_date': result.test_date,
                'analysis_time': result.analysis_time.isoformat(),
                'predicted_pressure': result.predicted_pressure,
                'predicted_signal_strength': result.predicted_signal_strength.name,
                'predicted_confidence': result.predicted_confidence,
                'actual_price_change_5min': result.actual_price_change_5min,
                'actual_price_change_15min': result.actual_price_change_15min,
                'actual_price_change_30min': result.actual_price_change_30min,
                'actual_max_drawdown_5min': result.actual_max_drawdown_5min,
                'actual_max_drawdown_15min': result.actual_max_drawdown_15min,
                'actual_max_drawdown_30min': result.actual_max_drawdown_30min,
                'pressure_prediction_correct': result.pressure_prediction_correct,
                'signal_strength_correct': result.signal_strength_correct,
                'confidence_calibrated': result.confidence_calibrated
            }

            # 添加压力特征
            for key, value in result.pressure_features.items():
                row[f'feature_{key}'] = value

            # 添加ML特征
            if result.ml_features:
                for key, value in result.ml_features.items():
                    row[f'ml_{key}'] = value

            data.append(row)

        df = pd.DataFrame(data)

        if filepath.endswith('.csv'):
            df.to_csv(filepath, index=False)
        elif filepath.endswith('.json'):
            df.to_json(filepath, orient='records', indent=2)
        else:
            # 默认CSV
            df.to_csv(filepath + '.csv', index=False)

        logging.info(f"回测结果已导出到: {filepath}")

    def generate_report(self, results: Optional[List[BacktestResult]] = None) -> str:
        """生成回测报告"""
        if results is None:
            results = self.results

        summary = self.calculate_summary_statistics(results)

        report = f"""
# 细价股上涨压力分析系统回测报告

## 测试概况
- 总测试次数: {summary.total_tests}
- 测试时间范围: {results[0].analysis_time.strftime('%Y-%m-%d %H:%M')} 至 {results[-1].analysis_time.strftime('%Y-%m-%d %H:%M')}

## 准确率指标
- 压力预测准确率: {summary.pressure_accuracy:.1%}
- 信号强度准确率: {summary.signal_accuracy:.1%}
- 置信度校准度: {summary.confidence_calibration:.1%}

## 分时段准确率
- 5分钟准确率: {summary.accuracy_5min:.1%}
- 15分钟准确率: {summary.accuracy_15min:.1%}
- 30分钟准确率: {summary.accuracy_30min:.1%}

## 风险控制指标
- 假阳性率: {summary.false_positive_rate:.1%}
- 假阴性率: {summary.false_negative_rate:.1%}
- 预测安全时最大损失: {summary.max_loss_when_predicted_safe:.2%}

## 性能指标
- 平均分析时间: {summary.avg_analysis_time:.3f}秒
- 总分析时间: {summary.total_analysis_time:.2f}秒

## 特征分析
"""

        # 添加特征统计
        if results:
            feature_stats = self._analyze_feature_importance(results)
            report += "\n### 最重要的压力特征:\n"
            for feature, importance in feature_stats[:5]:
                report += f"- {feature}: {importance:.1%}\n"

        return report

    def _analyze_feature_importance(self, results: List[BacktestResult]) -> List[Tuple[str, float]]:
        """分析特征重要性"""
        feature_counts = {}
        correct_predictions = 0

        for result in results:
            if result.pressure_prediction_correct:
                correct_predictions += 1
                for feature, value in result.pressure_features.items():
                    if value:  # 特征为True
                        feature_counts[feature] = feature_counts.get(feature, 0) + 1

        # 计算特征重要性（在正确预测中的出现频率）
        feature_importance = []
        for feature, count in feature_counts.items():
            importance = count / correct_predictions if correct_predictions > 0 else 0
            feature_importance.append((feature, importance))

        # 按重要性排序
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        return feature_importance
