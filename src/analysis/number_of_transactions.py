def check_number_of_transactions(items: list[tuple[str, float]], output_dict: dict[str, int]) -> None:
    """将排名前3的结果存入output_dict
    
    Args:
        items (list[tuple[str, float]]): 包含股票代码和交易次数的列表
        output_dict (dict[str, int]): 存储排名结果的字典
    
    Returns:
        None
    """
    # 1. 按值排序（降序排列）
    items.sort(key=lambda x: x[1], reverse=True)

    # 2. 分配排名（最大值=1，次大值=2，...）
    output_dict.clear()  # 清空原有内容
    for rank, (stock_code, _) in enumerate(items[:3], start=1):
        output_dict[stock_code] = rank