import logging
import browser_cookie3
from tradingview_screener import Query, col

def tradingview_screener_stock(trading_session: str) -> list[str]:
    """
    根据不同的交易时段筛选股票。

    Args:
        trading_session: 交易时段（'premarket_change'或'postmarket_change'）
    
    Returns:
        list[str]: 股票列表
    """
    # 从 Chrome 浏览器获取 Cookies
    try:
        # 获取 TradingView 的 Cookies
        cj = browser_cookie3.chrome(domain_name=".tradingview.com")
        # print(cj)
    except Exception as e:
        logging.error(f"获取 Cookies 失败: {e}")
        return []
    if trading_session == 'premarket_change':
        volume = 'premarket_volume' # 盘前成交量
    else:
        volume = 'postmarket_volume' # 盘后成交量

    try:
        # 执行 TradingView Screener 查询
        result = (Query()
            .select('name', 'close', 'change', trading_session)  # 选择字段
            .where(
                col('exchange').isin(['AMEX', 'NASDAQ', 'NYSE']),
                col("subtype").isin(["common", "foreign-issuer"]),
                col('close').between(0.2, 30),  # 价格范围
                col(volume).between(10000, 900000000)
            )
            .order_by(trading_session, ascending=False)  # 按涨幅降序排序
            .limit(10)  # 取前10个结果
            .get_scanner_data(cookies=cj))  # 传递 Cookies
    except Exception as e:
        logging.error(f"查询 TradingView 失败: {e}")
        return []

    # 解析返回的数据
    stock_names = []
    if isinstance(result, tuple) and len(result) == 2:
        df = result[1]  # 提取 DataFrame
        if "name" in df.columns:
            stock_names = df["name"].tolist()  # 获取 name 列
        else:
            logging.error("警告：返回的数据中缺少 'name' 列")
    else:
        logging.error("警告：TradingView 返回的数据格式异常")
    # print(result)
    return stock_names

# tradingview_screener_stock('postmarket_change')
