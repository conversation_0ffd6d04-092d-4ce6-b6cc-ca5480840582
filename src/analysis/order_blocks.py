import pandas as pd
import numpy as np

# =============================================================================
# 常量和设置（类似于 Pine Script 的输入）
# =============================================================================
BULLISH_LEG = 1
BEARISH_LEG = 0

BULLISH = +1
BEARISH = -1

GREEN = "#089981"
RED   = "#F23645"
BLUE  = "#2157f3"
GRAY  = "#878b94"
MONO_BULLISH = "#b2b5be"
MONO_BEARISH = "#5d606b"

HISTORICAL = 'Historical'
PRESENT    = 'Present'

COLORED    = 'Colored'
MONOCHROME = 'Monochrome'

ALL  = 'All'
BOS  = 'BOS'
CHOCH = 'CHoCH'

# 为简便起见，这里将尺寸定义为字符串（以后可以映射到具体的字体大小）
TINY = "tiny"
SMALL = "small"
NORMAL = "normal"

# 订单区块过滤方法
ATR = 'Atr'
RANGE = 'Cumulative Mean Range'

# 订单区块缓解数据源
CLOSE = 'Close'
HIGHLOW = 'High/Low'

# 线条样式
SOLID = 'SOLID'
DASHED = 'DASHED'
DOTTED = 'DOTTED'

# （用户设置——这些参数通常来自图形界面或配置文件）
modeInput = HISTORICAL
styleInput = COLORED
showTrendInput = True

showInternalsInput = True
showInternalBullInput = ALL
internalBullColorInput = GREEN
showInternalBearInput = ALL
internalBearColorInput = RED
internalFilterConfluenceInput = False
internalStructureSize = TINY

showStructureInput = True
showSwingBullInput = ALL
# 定义 swingBullColorInput 和 swingBearColorInput
swingBullColorInput = GREEN  
showSwingBearInput = ALL
swingBearColorInput = RED  
swingStructureSize = SMALL
showSwingsInput = False
swingsLengthInput = 50
showHighLowSwingsInput = True

showInternalOrderBlocksInput = True
internalOrderBlocksSizeInput = 5
# 设置为 True 以便绘制订单区块
showSwingOrderBlocksInput = True  
swingOrderBlocksSizeInput = 5
orderBlockFilterInput = ATR
orderBlockMitigationInput = HIGHLOW
internalBullishOrderBlockColor = "#3179f580"  # 8位 RGBA 十六进制
internalBearishOrderBlockColor = "#f77c8080"
swingBullishOrderBlockColor = "#1848cc80"
swingBearishOrderBlockColor = "#b2283380"

showEqualHighsLowsInput = True
equalHighsLowsLengthInput = 3
equalHighsLowsThresholdInput = 0.1
equalHighsLowsSizeInput = TINY

showFairValueGapsInput = False
fairValueGapsThresholdInput = True
fairValueGapsTimeframeInput = "60"  # 例如60分钟
fairValueGapsExtendInput = 1
fairValueGapsBullColorInput = "#00ff6846"
fairValueGapsBearColorInput = "#ff000846"

showDailyLevelsInput = False
dailyLevelsStyleInput = SOLID
dailyLevelsColorInput = BLUE
showWeeklyLevelsInput = False
weeklyLevelsStyleInput = SOLID
weeklyLevelsColorInput = BLUE
showMonthlyLevelsInput = False
monthlyLevelsStyleInput = SOLID
monthlyLevelsColorInput = BLUE

showPremiumDiscountZonesInput = False
premiumZoneColorInput = RED
equilibriumZoneColorInput = GRAY
discountZoneColorInput = GREEN

# =============================================================================
# 辅助函数：转换颜色为可视颜色（去除透明度）
# =============================================================================
def get_visible_color(color):
    """
    如果颜色是8位十六进制（例如 "#b2283380"），返回前7个字符（例如 "#b22833"），
    否则直接返回原颜色。
    """
    if isinstance(color, str) and len(color) == 9 and color.startswith("#"):
        return color[:7]
    return color

# 定义颜色名称映射，将可视颜色的十六进制值转换为中文描述
color_name_mapping = {
    "#089981": "绿色",
    "#F23645": "红色",
    "#2157f3": "蓝色",
    "#878b94": "灰色",
    "#b2b5be": "浅灰色",
    "#5d606b": "深灰色",
    "#3179f5": "蓝色",
    "#f77c80": "粉红色",
    "#1848cc": "蓝色",
    "#b22833": "红色"
}

# =============================================================================
# 数据结构（用 Python 类模拟 Pine Script 的类型）
# =============================================================================
class Alerts:
    def __init__(self):
        self.internalBullishBOS = False
        self.internalBearishBOS = False
        self.internalBullishCHoCH = False
        self.internalBearishCHoCH = False
        self.swingBullishBOS = False
        self.swingBearishBOS = False
        self.swingBullishCHoCH = False
        self.swingBearishCHoCH = False
        self.internalBullishOrderBlock = False
        self.internalBearishOrderBlock = False
        self.swingBullishOrderBlock = False
        self.swingBearishOrderBlock = False
        self.equalHighs = False
        self.equalLows = False
        self.bullishFairValueGap = False
        self.bearishFairValueGap = False

class TrailingExtremes:
    def __init__(self):
        self.top = np.nan
        self.bottom = np.nan
        self.barTime = None
        self.barIndex = None
        self.lastTopTime = None
        self.lastBottomTime = None

class FairValueGap:
    def __init__(self, top, bottom, bias, topBox, bottomBox):
        self.top = top
        self.bottom = bottom
        self.bias = bias
        self.topBox = topBox
        self.bottomBox = bottomBox

class Trend:
    def __init__(self, bias=0):
        self.bias = bias  # BULLISH 或 BEARISH

class EqualDisplay:
    def __init__(self):
        self.line = None
        self.label = None

class Pivot:
    def __init__(self, currentLevel=np.nan, lastLevel=np.nan, crossed=False, barTime=None, barIndex=None):
        self.currentLevel = currentLevel
        self.lastLevel = lastLevel
        self.crossed = crossed
        self.barTime = barTime
        self.barIndex = barIndex

# 修改：在 OrderBlock 中增加累计成交量属性以及激活状态属性
class OrderBlock:
    def __init__(self, barHigh, barLow, barTime, bias):
        self.barHigh = barHigh
        self.barLow = barLow
        self.barTime = barTime
        self.bias = bias
        self.cumulativeVolume = 0  # 累计成交量
        self.active = True         # 当前区间是否激活

# =============================================================================
# 全局变量 / 状态
# =============================================================================
swingHigh = Pivot()
swingLow  = Pivot()
internalHigh = Pivot()
internalLow  = Pivot()
equalHigh = Pivot()
equalLow  = Pivot()
swingTrend = Trend()
internalTrend = Trend()
equalHighDisplay = EqualDisplay()
equalLowDisplay = EqualDisplay()

fairValueGaps = []          # FairValueGap 列表
parsedHighs = []            # 解析后的最高价列表
parsedLows = []             # 解析后的最低价列表
rawHighs    = []            # 原始最高价列表
rawLows     = []            # 原始最低价列表
times       = []            # bar 的时间列表
trailing = TrailingExtremes()
swingOrderBlocks = []       # Swing OrderBlock 列表
internalOrderBlocks = []    # Internal OrderBlock 列表

# 模拟“盒子”，通过列表存储绘图指令
swingOrderBlocksBoxes = [None] * swingOrderBlocksSizeInput if showSwingOrderBlocksInput else []
internalOrderBlocksBoxes = [None] * internalOrderBlocksSizeInput if showInternalOrderBlocksInput else []

# 根据样式设置颜色（转换为可视颜色时在打印中使用）
swingBullishColor = MONO_BULLISH if styleInput == MONOCHROME else swingBullColorInput
swingBearishColor = MONO_BEARISH if styleInput == MONOCHROME else swingBearColorInput
fairValueGapBullishColor = MONO_BULLISH if styleInput == MONOCHROME else fairValueGapsBullColorInput
fairValueGapBearishColor = MONO_BEARISH if styleInput == MONOCHROME else fairValueGapsBearColorInput

# 针对订单区块缓解，选择数据源：这里模拟为列名
bearishOrderBlockMitigationSource = CLOSE if orderBlockMitigationInput == CLOSE else "High"
bullishOrderBlockMitigationSource = CLOSE if orderBlockMitigationInput == CLOSE else "Low"

# =============================================================================
# 辅助函数
# =============================================================================
def atr(df, period=200):
    """对包含 High、Low、Close 列的 DataFrame 进行简单的 ATR 计算"""
    tr = np.maximum(df['High'] - df['Low'],
                    np.abs(df['High'] - df['Close'].shift(1)),
                    np.abs(df['Low'] - df['Close'].shift(1)))
    return tr.rolling(window=period, min_periods=1).mean()

def leg(df, index, lookback):
    """判断指定 index 处的当前波段类型。
       如果出现新高则返回 BEARISH_LEG，如果出现新低则返回 BULLISH_LEG，
       否则返回 0（默认值）。"""
    if index < lookback:
        return 0  # 历史数据不足
    window = df.iloc[index - lookback:index]
    current_bar = df.iloc[index]
    newLegHigh = current_bar['High'] > window['High'].max()
    newLegLow  = current_bar['Low']  < window['Low'].min()
    if newLegHigh:
        return BEARISH_LEG
    elif newLegLow:
        return BULLISH_LEG
    else:
        return 0

def start_of_new_leg(current_leg, previous_leg):
    """判断是否为新波段的起始"""
    return current_leg != previous_leg

def start_of_bullish_leg(current_leg, previous_leg):
    """判断是否为新看涨波段"""
    return current_leg - previous_leg > 0

def start_of_bearish_leg(current_leg, previous_leg):
    """判断是否为新看跌波段"""
    return current_leg - previous_leg < 0

# 模拟绘图函数 —— 在实际中可使用 matplotlib 绘图
# 如果需要绘图，请实现或取消下面注释
# def draw_label(label_time, label_price, tag, label_color, label_style):
#     print(f"绘制标签 '{tag}'，时间 {label_time}，价格 {label_price}，颜色 {label_color}，样式 {label_style}")

# def draw_line(point1, point2, color, style):
#     print(f"绘制线段，从 {point1} 到 {point2}，颜色 {color}，样式 {style}")

def draw_box(top_left, bottom_right, color):
    # 转换颜色为可视颜色（去除透明度），并转换为文字描述
    visible_color = get_visible_color(color)
    color_name = color_name_mapping.get(visible_color, visible_color)
    print(f"绘制盒子，从 {top_left} 到 {bottom_right}，颜色 {color_name}")
    return {"top_left": top_left, "bottom_right": bottom_right, "color": color_name}

# =============================================================================
# 核心函数（结构、订单区块、合理价值缺口等）
# =============================================================================
def get_current_structure(df, index, size, equalHighLow=False, internal=False):
    """
    根据检测到的新波段更新当前枢轴。
    该函数模拟 Pine Script 中 getCurrentStructure 的行为。
    """
    global swingLow, swingHigh, internalLow, internalHigh, equalLow, equalHigh, trailing
    current_leg = leg(df, index, size)
    previous_leg = leg(df, index - 1, size) if index > 0 else current_leg
    new_pivot = start_of_new_leg(current_leg, previous_leg)
    
    if new_pivot:
        current_bar = df.iloc[index]
        if start_of_bullish_leg(current_leg, previous_leg):
            # 新看涨波段 ⇒ 更新 swing low
            p = equalLow if equalHighLow else (internalLow if internal else swingLow)
            p.lastLevel = p.currentLevel
            p.currentLevel = current_bar['Low']
            p.crossed = False
            p.barTime = current_bar['date_time']
            p.barIndex = index
            trailing.bottom = p.currentLevel
            trailing.barTime = current_bar['date_time']
            trailing.barIndex = index
        else:
            # 新看跌波段 ⇒ 更新 swing high
            p = equalHigh if equalHighLow else (internalHigh if internal else swingHigh)
            p.lastLevel = p.currentLevel
            p.currentLevel = current_bar['High']
            p.crossed = False
            p.barTime = current_bar['date_time']
            p.barIndex = index
            trailing.top = p.currentLevel
            trailing.barTime = current_bar['date_time']
            trailing.barIndex = index

def display_structure(df, index, internal=False):
    """
    检测收盘价与存储的枢轴价是否交叉，以信号波段结构（BOS 或 CHoCH），并更新趋势，
    同时存储订单区块。
    """
    global swingHigh, swingLow, internalHigh, internalLow, swingTrend, internalTrend
    current_bar = df.iloc[index]
    
    # 检测看涨结构：当 Close 大于 swing low 的枢轴价时
    p_low = internalLow if internal else swingLow
    if current_bar['Close'] > p_low.currentLevel and not p_low.crossed:
        tag = BOS if (internalTrend.bias != BULLISH) else CHOCH
        p_low.crossed = True
        (internalTrend if internal else swingTrend).bias = BULLISH
        # 存储看涨订单区块
        store_order_block(p_low, internal=internal, bias=BULLISH)
    
    # 检测看跌结构：当 Close 小于 swing high 的枢轴价时
    p_high = internalHigh if internal else swingHigh
    if current_bar['Close'] < p_high.currentLevel and not p_high.crossed:
        tag = BOS if (internalTrend.bias != BEARISH) else CHOCH
        p_high.crossed = True
        (internalTrend if internal else swingTrend).bias = BEARISH
        # 存储看跌订单区块
        store_order_block(p_high, internal=internal, bias=BEARISH)

def store_order_block(pivot, internal=False, bias=None):
    """
    根据枢轴存储订单区块。
    这里简化地以枢轴价本身作为订单区块，同时初始化累计成交量为 0。
    当创建新订单区块时，将同类型中之前已存在的订单区块标记为非激活，
    这样累计成交量只在新区间内计算。
    """
    current_time = pivot.barTime
    new_ob = OrderBlock(barHigh=pivot.currentLevel, barLow=pivot.currentLevel, barTime=current_time, bias=bias)
    new_ob.active = True  # 新区间激活
    if internal:
        # 将已有的内部订单区块标记为非激活
        for ob in internalOrderBlocks:
            ob.active = False
        internalOrderBlocks.insert(0, new_ob)
    else:
        # 将已有的 Swing 订单区块标记为非激活
        for ob in swingOrderBlocks:
            ob.active = False
        swingOrderBlocks.insert(0, new_ob)

def delete_order_blocks(df, index, internal=False):
    """
    检查价格是否突破了订单区块。
    """
    current_bar = df.iloc[index]
    order_blocks = internalOrderBlocks if internal else swingOrderBlocks
    for ob in order_blocks.copy():
        if ob.bias == BEARISH and current_bar['Close'] > ob.barHigh:
            print("看跌订单区块突破")
            order_blocks.remove(ob)
        elif ob.bias == BULLISH and current_bar['Close'] < ob.barLow:
            print("看涨订单区块突破")
            order_blocks.remove(ob)

def draw_order_blocks():
    """
    遍历存储的订单区块并“绘制”它们，同时显示累计成交量。
    """
    for ob in swingOrderBlocks:
        draw_box((ob.barTime, ob.barHigh), ("now", ob.barLow),
                 swingBullishOrderBlockColor if ob.bias == BULLISH else swingBearishOrderBlockColor)
        print(f"累计成交量: {ob.cumulativeVolume}")

def update_trailing_extremes(df, index):
    """
    更新当前数据中的最高和最低波段极值。
    """
    global trailing
    current_bar = df.iloc[index]
    if np.isnan(trailing.top) or current_bar['High'] > trailing.top:
        trailing.top = current_bar['High']
        trailing.lastTopTime = current_bar['date_time']
    if np.isnan(trailing.bottom) or current_bar['Low'] < trailing.bottom:
        trailing.bottom = current_bar['Low']
        trailing.lastBottomTime = current_bar['date_time']

def draw_premium_discount_zones():
    """
    绘制优质区、均衡区和折价区。
    """
    premium_top = trailing.top
    premium_bottom = 0.95 * trailing.top + 0.05 * trailing.bottom
    draw_box((trailing.barTime, premium_top), ("now", premium_bottom), premiumZoneColorInput)
    equilibrium = (trailing.top + trailing.bottom) / 2
    # 如有需要可取消注释绘制均衡区标签
    # draw_label("now", equilibrium, "Equilibrium", equilibriumZoneColorInput, "label_left")
    discount_top = 0.95 * trailing.bottom + 0.05 * trailing.top
    discount_bottom = trailing.bottom
    draw_box((trailing.barTime, discount_top), ("now", discount_bottom), discountZoneColorInput)

# =============================================================================
# 主处理循环
# =============================================================================
def process_data(df):
    """
    处理包含 OHLC 数据的 DataFrame。
    DataFrame 至少应包含以下列：date_time, Open, High, Low, Close。
    如果数据中包含 'Volume' 列，则同时累计成交量（仅对激活区间）。
    """
    global parsedHighs, parsedLows, rawHighs, rawLows, times
    alerts = Alerts()
    df = df.reset_index(drop=True)
    atr_values = atr(df, period=200)
    
    for index in range(len(df)):
        current_bar = df.iloc[index]
        # 计算波动性指标以及解析后的高低价：
        vol_measure = atr_values.iloc[index] if orderBlockFilterInput == ATR else np.mean(df['High'] - df['Low'][:index+1])
        high_vol = (current_bar['High'] - current_bar['Low']) >= (2 * vol_measure)
        parsedHigh = current_bar['Low'] if high_vol else current_bar['High']
        parsedLow  = current_bar['High'] if high_vol else current_bar['Low']
        parsedHighs.append(parsedHigh)
        parsedLows.append(parsedLow)
        rawHighs.append(current_bar['High'])
        rawLows.append(current_bar['Low'])
        times.append(current_bar['date_time'])
        
        # （在 Pine Script 中，此处会绘制带颜色的 K 线，此处仅模拟输出）
        candle_color = swingBullishColor if internalTrend.bias == BULLISH else swingBearishColor
        # print(f"Bar {index} time {current_bar['date_time']}: Open={current_bar['Open']} High={current_bar['High']} Low={current_bar['Low']} Close={current_bar['Close']} Color={candle_color}")
        
        # 更新波段极值（如果启用）
        if showHighLowSwingsInput or showPremiumDiscountZonesInput:
            update_trailing_extremes(df, index)
            if showPremiumDiscountZonesInput:
                draw_premium_discount_zones()
        
        # 调用 get_current_structure 更新波段枢轴数据
        get_current_structure(df, index, swingsLengthInput, equalHighLow=False, internal=False)
        if showInternalsInput:
            get_current_structure(df, index, 5, equalHighLow=False, internal=True)
        if showEqualHighsLowsInput:
            get_current_structure(df, index, equalHighsLowsLengthInput, equalHighLow=True, internal=False)
        
        # 检测结构，同时存储订单区块
        if showInternalsInput:
            display_structure(df, index, internal=True)
        if showStructureInput:
            display_structure(df, index, internal=False)
        
        # 订单区块：删除和绘制
        if showInternalOrderBlocksInput:
            delete_order_blocks(df, index, internal=True)
        if showSwingOrderBlocksInput:
            delete_order_blocks(df, index, internal=False)
        if showSwingOrderBlocksInput:
            draw_order_blocks()
        
        # 累计成交量更新：如果数据中包含 'Volume' 列，则对所有激活状态的订单区块累计当前 bar 的成交量
        if 'Volume' in df.columns:
            current_volume = current_bar['Volume']
            for ob in swingOrderBlocks:
                if ob.active:
                    ob.cumulativeVolume += current_volume
            for ob in internalOrderBlocks:
                if ob.active:
                    ob.cumulativeVolume += current_volume
        
        # （合理价值缺口等处理类似）
        # （多时间框架水平绘制 —— 此处未详细实现）
        # 根据 alerts 对象的标志，可打印提示信息。
    
    print("Processing complete.")

def find_valid_order_blocks(df):
    """
    从最新 K 线开始倒退，寻找仍然有效（未被突破）的订单块
    """
    valid_swing_blocks = []
    valid_internal_blocks = []
    
    # 数据有效性检查：至少需要1根K线
    if len(df) < 1:
        return valid_swing_blocks, valid_internal_blocks
    
    # 遍历 Swing Order Blocks
    for ob in reversed(swingOrderBlocks):  # 逆序遍历
        latest_close = df.iloc[-1]['Close']  # 最新的收盘价
        if ob.bias == BULLISH and latest_close >= ob.barLow:
            valid_swing_blocks.append(ob)
        elif ob.bias == BEARISH and latest_close <= ob.barHigh:
            valid_swing_blocks.append(ob)
    
    # 遍历 Internal Order Blocks
    for ob in reversed(internalOrderBlocks):  # 逆序遍历
        latest_close = df.iloc[-1]['Close']  # 最新的收盘价
        if ob.bias == BULLISH and latest_close >= ob.barLow:
            valid_internal_blocks.append(ob)
        elif ob.bias == BEARISH and latest_close <= ob.barHigh:
            valid_internal_blocks.append(ob)

    return valid_swing_blocks, valid_internal_blocks

def calculate_order_blocks(df):
    process_data(df)
    # 查找仍然有效的订单块
    swing_blocks, internal_blocks = find_valid_order_blocks(df)
    # 打印未突破的订单块（增加了累计成交量显示）
    print("未被突破的 Swing Order Blocks:")
    for ob in swing_blocks:
        print(f"时间: {ob.barTime}, 价格区间: ({ob.barHigh}, {ob.barLow}), 累计成交量: {ob.cumulativeVolume}, 类型: {'看涨' if ob.bias == BULLISH else '看跌'}")

    print("\n未被突破的 Internal Order Blocks:")
    for ob in internal_blocks:
        print(f"时间: {ob.barTime}, 价格区间: ({ob.barHigh}, {ob.barLow}), 累计成交量: {ob.cumulativeVolume}, 类型: {'看涨' if ob.bias == BULLISH else '看跌'}")

# =============================================================================
# 示例用法
# =============================================================================
# if __name__ == "__main__":
#     # 从 CSV 文件中读取数据，确保 CSV 包含列：date_time, Open, High, Low, Close
#     # 如有成交量数据，CSV 中应包含 'Volume' 列
#     df = pd.read_csv('/Users/<USER>/PycharmProjects/openapi-samples-python-master/stock_trade/stock_data.csv')
#     process_data(df)
#     # 查找仍然有效的订单块
#     swing_blocks, internal_blocks = find_valid_order_blocks(df)

#     # 打印未突破的订单块（增加了累计成交量显示）
#     print("未被突破的 Swing Order Blocks:")
#     for ob in swing_blocks:
#         print(f"时间: {ob.barTime}, 价格区间: ({ob.barHigh}, {ob.barLow}), 累计成交量: {ob.cumulativeVolume}, 类型: {'看涨' if ob.bias == BULLISH else '看跌'}")

#     print("\n未被突破的 Internal Order Blocks:")
#     for ob in internal_blocks:
#         print(f"时间: {ob.barTime}, 价格区间: ({ob.barHigh}, {ob.barLow}), 累计成交量: {ob.cumulativeVolume}, 类型: {'看涨' if ob.bias == BULLISH else '看跌'}")