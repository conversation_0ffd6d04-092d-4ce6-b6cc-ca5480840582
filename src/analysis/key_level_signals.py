import pandas as pd

def check_break_and_retest(df, i, df_5min, first_min5_i, target, start_date, interception):
    """
    检查是否满足突破、回测和反弹条件"
    """
    if df.index[i] < pd.Timestamp(f'{start_date} 09:38:00')\
            or df.index[i] > pd.Timestamp(f'{start_date} 09:50:00'):
        return None, None, None

    breakout_level = df_5min['High'].iloc[first_min5_i]
    key_close = df_5min['Close'].iloc[first_min5_i]
    key_open = df_5min['Open'].iloc[first_min5_i]
    # 1. 检查关键K线是否为阳线（上涨）
    if key_close < key_open:
        return None, None, None

    # 2. 在关键K线结束后的1分钟数据中查找是否出现突破（1分钟K线的最高价 > 关键K线最高价）
    # 注意：关键K线时间段为9:30~9:35，所以从9:35开始的1分钟数据用于判断
    # start_time = df_5min.index[first_min5_i] + pd.Timedelta(minutes=3)
    df_after_key = df[df.index > target].iloc[:interception]

    # # 2. 在关键K线结束后的1分钟数据中查找是否出现突破（1分钟K线的最高价 > 关键K线最高价）
    # df_after_key = df.iloc[first_min5_i+1:i]
    
    # 判断是否有1分钟K线的Close突破关键K线的最高价
    # 找到 df_after_key 中 High 最高的值
    max_close = df_after_key['High'].max()
    if max_close < breakout_level:
        return None, None, None
    # 找出 df_after_key 中Close价对应的时间点(返回第一根的时间)
    max_high_time = df_after_key['High'].idxmax()

    # # 找出对应的行（如果有多行相同，取第一行）
    # max_row = df_after_key.loc[df_after_key['Close'] == max_close].iloc[0]

    # # 阳线判断（Close > Open）
    # if max_row['Close'] <= max_row['Open']:
    #     return False

    # 3. 判断是否有回测至关键K线的收盘价附近的情况
    # 定义“附近”为价格在关键K线收盘价附近（例如：+-1%的容差）
    tolerance = 0.01 * breakout_level
    # 回测条件：该K线最低价跌至或低于key_close，同时最高价高于或等于key_close
    # retest_df = df_after_key[(df_after_key['Low'] <= key_close + tolerance) & (df_after_key['High'] >= key_close - tolerance)]
    # 只考虑在最高价之后的K线，同时满足回测条件
    retest_df = df_after_key[
        (df_after_key.index > max_high_time) &
        (df_after_key['Low'] <= breakout_level + tolerance) &
        (df_after_key['High'] >= breakout_level - tolerance)
    ]
    if retest_df.empty:
        return None, None, None

    # retest_time = retest_df.index[0]
    # # print(f"回测在 {retest_time} 时刻出现，检查回测后的反弹情况。")
    
    # # 4. 判断回测后最后1分钟K线是否从该点反弹
    # # 此处我们选择回测后的最后一个1分钟K线数据来判断反弹（可根据策略调整）
    # df_after_retest = df_after_key[df_after_key.index > retest_time]
    # if df_after_retest.empty:
        # return False

    last_bar = df.iloc[-1]
    # 反弹条件：最后1分钟K线的收盘价高于关键K线的收盘价
    if last_bar['Close'] > key_close:
        return max_close, str(max_high_time), str(retest_df.index[0])
    
    # 确保函数始终返回三元组
    return None, None, None