from src.utils.futu_utils import *
import numpy as np
import logging
import time
from src.analysis.detect_pattern_test import detect_pattern
from src.utils.constants import *
import pandas as pd

# 计算macd
def calculate_mack(df):

    df['ema_fast'] = df['Close'].ewm(span=12, adjust=False).mean()
    df['ema_slow'] = df['Close'].ewm(span=26, adjust=False).mean()
    df['DIF'] = df['ema_fast'] - df['ema_slow']
    df['DEA'] = df['DIF'].ewm(span=9, adjust=False).mean()
    df['MACD'] = (df['DIF'] - df['DEA'])

def check_stock_volume(pre_historicalData, quote_ctx, start_date, signal_time):
    # 删除已存在的提醒股票
    delete_stock_reminder(quote_ctx)
    for stock_code, df in pre_historicalData.items():
        logging.info(f'股票:{stock_code} 盘前盘后信号检查---------------------------------------')
        # MACD 1分钟
        calculate_mack(df)
        # 截取 09:31:00 以后的数据
        df_data = df.loc[df.index >= pd.Timestamp(f'{start_date} 04:01:00')].copy()
        
        # 检查索引中是否包含 '08:01'
        target_datetime_0801 = f'{start_date} 08:01'  # 目标日期和时间
        target_datetime_0800 = f'{start_date} 08:00'  # 目标日期和时间
        # 检查是否包含具体的日期和时间
        if target_datetime_0800 in df.index.strftime('%Y-%m-%d %H:%M')\
            and target_datetime_0801 in df.index.strftime('%Y-%m-%d %H:%M'):
            # 获取 '08:00' 的成交量
            previous_volume = df.loc[df.index.strftime('%Y-%m-%d %H:%M') == target_datetime_0800, 'Volume'].values[0]
            # 获取 '08:01' 的成交量
            previous_volume_01 = df.loc[df.index.strftime('%Y-%m-%d %H:%M') == target_datetime_0801, 'Volume'].values[0]
            # 替换 '08:01' 的成交量为 '08:00' 的成交量
            df.loc[df.index.strftime('%Y-%m-%d %H:%M') == target_datetime_0801, 'Volume'] = previous_volume

            logging.info(f"已将 {target_datetime_0801} 的成交量：{previous_volume_01} 修改为 {target_datetime_0800} 的成交量：{previous_volume}")

        # 计算 VWAP
        vwap_numerator = (df_data['Close'] + df_data['High'] + df_data['Low']) / 3 * df_data['Volume']
        vwap_denominator = np.where(df_data['Volume'] != 0, df_data['Volume'], 1)
        df_data.loc[:, 'vwap'] = vwap_numerator.cumsum() / vwap_denominator.cumsum()

        # 检查并处理df_data中可能存在的重复索引
        if df_data.index.duplicated().any():
            duplicate_indices = df_data.index[df_data.index.duplicated(keep=False)]
            logging.warning(f"股票:{stock_code} 检测到df_data中存在重复索引: {duplicate_indices.tolist()}")
            # 记录重复索引的数据，帮助调试
            for idx in duplicate_indices:
                duplicate_rows = df_data.loc[idx]
                if isinstance(duplicate_rows, pd.DataFrame):  # 多行
                    logging.warning(f"重复索引 {idx} 的数据:\n{duplicate_rows}")
            # 保留第一个出现的索引，删除重复的
            df_data = df_data[~df_data.index.duplicated(keep='first')]
        
        # 检查df_data中是否包含vwap列
        if 'vwap' not in df_data.columns:
            logging.error(f"股票:{stock_code} df_data中不包含vwap列！列名: {df_data.columns.tolist()}")
            # 如果不存在vwap列，创建一个默认值为0的列
            df_data['vwap'] = 0.0
            
        # 使用索引对齐方式安全地赋值
        try:
            df['vwap'] = df_data['vwap'].reindex(df.index)
        except Exception as e:
            logging.error(f"股票:{stock_code} vwap列赋值失败: {e}")
            # 如果赋值失败，使用更安全的方式
            for idx in df.index:
                if idx in df_data.index:
                    df.loc[idx, 'vwap'] = df_data.loc[idx, 'vwap']
                else:
                    df.loc[idx, 'vwap'] = 0.0
        # 穿过vwap
        df['near_vwap_2'] = (df['High'] >= df['vwap']) & (df['Low'] <= df['vwap'])

        df['Turnover'] = df['Volume'] * (df['High'] + df['Close'] + df['Open'] + df['Low']) / 4
        # 计算EMA20
        df['EMA20'] = df['Close'].ewm(span=20, min_periods=0, adjust=False).mean()
        # 计算EMA120
        df['EMA120'] = df['Close'].ewm(span=120, min_periods=0, adjust=False).mean()

        df['STAR'] = ""

        # 使用 resample 方法将 1 分钟数据转换为 5 分钟数据，并对其他列进行聚合操作
        # 对 df 进行 5 分钟重采样，并将起始时间调整为以 :35 结尾
        df_5min = df.resample('5min', label='right', closed='right').agg({
            'Close': 'last',  # 最后一条数据
            'Open': 'first',  # 第一条数据
            'High': 'max',    # 最大值
            'Low': 'min',     # 最小值
            'Volume': 'sum'   # 总和
        }).dropna()
        # 将索引重命名为 'Time_index'
        df_5min.index.name = 'Time_5min'
        # EMA
        # 计算EMA20
        df_5min['EMA20'] = df_5min['Close'].ewm(span=20, min_periods=0, adjust=False).mean()
        # MACD 5分钟
        calculate_mack(df_5min)

        # 前10根均量
        # 为避免除零错误，先过滤掉 Open <= 0 的数据（或可以选择在计算中处理）
        valid_df = df_5min[df_5min['Open'] > 0]
        # 计算涨幅
        increase_ratio = (valid_df['Close'] - valid_df['Open']) / valid_df['Open']
        # 找出涨幅 > 20% 的记录
        match = increase_ratio[increase_ratio > 0.2]

        if match.empty:
            first_index = None
            prev_10_mean_vol = None
        else:
            # 获取第一根满足条件的 K 线的 index（按原 df 的索引）
            first_index = match.index[0]
            # 找到该 index 在原 DataFrame 中的位置
            pos = df_5min.index.get_loc(first_index)
            data_size_5 = len(df_5min)
            # 添加条件：要求该 K 线必须位于倒数第5根到倒数第3根之间，
            # 即其位置必须为 data_size - 5, data_size - 4 或 data_size - 3
            if pos not in [data_size_5 - 5, data_size_5 - 4, data_size_5 - 3]:
                # 如果不满足条件，则视为没有找到符合要求的 K 线
                first_index = None
                prev_10_mean_vol = None
            else:
                # 4. 计算该 K 线前10根 K 线的平均成交量
                start_pos = max(0, pos - 10)
                prev_10_mean_vol = df['Volume'].iloc[start_pos:pos].mean()

        # 截取 04:01:00 以后的数据
        df_5min = df_5min.loc[df_5min.index >= pd.Timestamp(f'{start_date} 04:05:00')].copy()


        # 对 df 进行 15 分钟重采样
        df_15min = df.resample('15min', label='right', closed='right').agg({
            'Close': 'last',  # 最后一条数据
            'Open': 'first',  # 第一条数据
            'High': 'max',    # 最大值
            'Low': 'min',     # 最小值
            'Volume': 'sum'   # 总和
        }).dropna()
        # 将索引重命名为 'Time_index'
        df_15min.index.name = 'Time_15min'
        # EMA
        # 计算EMA20
        df_15min['EMA20'] = df_15min['Close'].ewm(span=20, min_periods=0, adjust=False).mean()
        df_15min['EMA120'] = df_15min['Close'].ewm(span=120, min_periods=0, adjust=False).mean()
        # MACD 15分钟
        calculate_mack(df_15min)
        # 截取 04:01:00 以后的数据
        df_15min = df_15min.loc[df_15min.index >= pd.Timestamp(f'{start_date} 04:15:00')].copy()

        # 截取 04:01:00 以后的数据
        df = df.loc[df.index >= pd.Timestamp(f'{start_date} 04:01:00')].copy()

        data_size = len(df)
        if data_size < 3:
            break
        if not first_index:
            break

        # 获取盘前开始1分钟成交额的平均值
        # recent_volume_avg_all = df['Volume'].mean()

        for i in range(data_size-1-1, data_size):
            def get_index(df_, i__):
                # 找到第一个大于或等于 当前1分钟的更高周期的索引
                corresponding_15min_index = df_.index[df_.index >= df.index[i__]][0]
                # 获取更高周期索引的整数位置
                return df_.index.get_loc(corresponding_15min_index)
            min5_i = get_index(df_5min, i)
            # min15_i = get_index(df_15min, i)

            # 截取数
            # number_interceptions = 2
            # temp_df = df.tail(number_interceptions)
            # recent_volume_avg_10 = temp_df['Volume'].mean()

            # # 获取最近10个1分钟成交额的平均值
            # recent_turnover_avg = temp_df['Turnover'].mean()
            # if df['Turnover'].iloc[i] < 150000:
            #     logging.info(f'当前成交额:{df['Turnover'].iloc[i]}')
            #     continue

            # # if recent_volume_avg_10 < 50000:
            # #     logging.info(f'最近2个1分钟成交量的平均值:{recent_volume_avg_10}')
            # #     continue

            # if df['EMA20'].iloc[i] < df['EMA120'].iloc[i]:
            #     logging.info(f'1EMA20<1EMA120:{df['EMA20'].iloc[i]} {df['EMA120'].iloc[i]}')
            #     continue
            # if df_15min['EMA20'].iloc[min15_i] < df_15min['EMA120'].iloc[min15_i]:
            #     logging.info(f'15EMA20<15EMA120:{df_15min['EMA20'].iloc[min15_i]} {df_15min['EMA120'].iloc[min15_i]}')
            #     continue

            # current_volume = df['Volume'].iloc[i]  # 获取当前（最新）的成交量
            # last_20_volumes = df['Volume'].iloc[i-20:i]  # 获取当前索引前20个成交量
            # # last_20_datetimes = df.index[i-20:i]  # 获取对应的时间
            

            # # 检查是否有任何一个成交量大于当前成交量的1.4倍
            # if any(last_20_volumes > (1.4 * current_volume)):
            #     logging.info(f'检查是否有任何一个成交量大于当前成交量的1.4倍 当前成交量: {current_volume}')
            #     continue

            # df.loc[df.index[i], 'recent_volume_avg_all_flg'] = df['Volume'].iloc[i] > recent_volume_avg_all * 1.3
            # df.loc[df.index[i], 'Volume_2'] = (df['Volume'].iloc[i] > df['Volume'].iloc[i-1] * 2 or df['Volume'].iloc[i] > df['Volume'].iloc[i-2] * 2)
            # df.loc[df.index[i], 'Volume_3'] = df['Volume'].iloc[i] > ((df['Volume'].iloc[i-1]+df['Volume'].iloc[i-2]+df['Volume'].iloc[i-3])/3) * 2
            # df.loc[df.index[i], 'red'] = df['Close'].iloc[i] > df['Open'].iloc[i]
            # df.loc[df.index[i], 'recent_volume_avg_10_14'] = df['Volume'].iloc[i] > recent_volume_avg_10 * 1.4
            # df.loc[df.index[i], 'close_up'] = df['Close'].iloc[i] > df['Open'].iloc[i-1]

            # logging.info(f'recent_volume_avg_all_flg:{df['recent_volume_avg_all_flg'].iloc[i]}')
            # logging.info(f'Volume_2:{df['Volume_2'].iloc[i]}')
            # logging.info(f'Volume_3:{df['Volume_3'].iloc[i]}')
            # logging.info(f'red:{df['red'].iloc[i]}')
            # logging.info(f'recent_volume_avg_10_14:{df['recent_volume_avg_10_14'].iloc[i]}')
            # logging.info(f'close_up:{df['close_up'].iloc[i]}')

            # logging.info(f'5分钟dif:{df_5min['DIF'].iloc[min5_i]} {df_5min['DIF'].iloc[min5_i-1]}')
            # logging.info(f'5分钟dif dev:{df_5min['DIF'].iloc[min5_i]} {df_5min['DEA'].iloc[min5_i]}')
            # logging.info(f'15分钟dif dev:{df_15min['DIF'].iloc[min15_i]} {df_15min['DEA'].iloc[min15_i]}')

            # if df['Volume'].iloc[i] > recent_volume_avg_all * 2\
            #     and (df['Volume'].iloc[i] > df['Volume'].iloc[i-1] * 2 or df['Volume'].iloc[i] > df['Volume'].iloc[i-2] * 2)\
            #         and df['Close'].iloc[i] > df['Open'].iloc[i]\
            #             and df['Volume'].iloc[i] > recent_volume_avg_10 * 1.4\
            #                 and (df['near_vwap_2'].iloc[i] or df['near_vwap_2'].iloc[i-1])\
            #                     and df['Close'].iloc[i] > df['Open'].iloc[i-1]\
            #                         and df_5min['DIF'].iloc[min5_i] > df_5min['DIF'].iloc[min5_i-1]\
            #                             and df_5min['DIF'].iloc[min5_i] > df_5min['DEA'].iloc[min5_i]\
            #                                 and df_15min['DIF'].iloc[min15_i] > df_15min['DEA'].iloc[min15_i]:
            # and df['close_up'].iloc[i]\
            # and df['recent_volume_avg_10_14'].iloc[i]\
            # if df['recent_volume_avg_all_flg'].iloc[i]\
            #     and (df['Volume_2'].iloc[i] or df['Volume_3'].iloc[i])\
            #         and df['red'].iloc[i]\
            #             and (df['near_vwap_2'].iloc[i] or df['near_vwap_2'].iloc[i-1])\
            #                 and df_5min['DIF'].iloc[min5_i] > df_5min['DIF'].iloc[min5_i-1]\
            #                     and df_5min['DIF'].iloc[min5_i] > df_5min['DEA'].iloc[min5_i]\
            #                         and df_15min['DIF'].iloc[min15_i] > df_15min['DEA'].iloc[min15_i]:
            #     df.loc[df.index[i], 'STAR'] = '⭐️⭐️⭐️'

            # if df['STAR'].iloc[i-1] == '⭐️⭐️⭐️' and df['Close'].iloc[i] < df['Open'].iloc[i-1]:
            #     df.loc[df.index[i-1], 'STAR'] = None
            # 获取当前时间戳
            t1 = time.time()
            last_time = signal_time.get(stock_code, 0)
            if t1 - last_time > 10 * 59:
                start_dateTime, end_dateTime, mid_count = detect_pattern(df_5min, first_index, prev_10_mean_vol)
                if start_dateTime:
                    signal_time[stock_code] = t1
                    df.loc[df.index[i], 'STAR'] = '⭐️⭐️⭐️'

                # if df['STAR'].loc[df.index[i]] == '⭐️⭐️⭐️' or df['STAR'].loc[df.index[i-1]] == '⭐️⭐️⭐️':
                    sys.stdout.flush()  # 刷新输出缓冲区
                    logging.info(f'股票:{stock_code} ：⭐️⭐️⭐️  形态起始: {start_dateTime}, 结束: {end_dateTime}, 中间K线数量: {mid_count}')
                    # 显示通知
                    if i == data_size-1 or i == data_size-2:
                        # 添加提醒
                        set_stock_reminder(stock_code, quote_ctx, '5分钟突破')
                        # 复制到黏贴版
                        # pyperclip.copy(stock_code)

        # -----------------------------------------------------------------------------------
        # 获取最后三根K线
        # last_3 = df.iloc[-3:].copy()
        # last_3['Turnover'] = last_3['Volume'] * ((last_3['Close'] + last_3['Open']) / 2)

        # # logging.info(f'股票: {stock_code} Turnover: {last_3['Turnover']} Volume: {last_3['Volume']}')
        # logging.info(f'股票: {stock_code} {last_3}')
        # # 检查最后两根K线中的成交额是否大于20万且是倒数第三根的两倍以上
        # # 如果满足条件则返回True，否则返回False
        # result = (
        #     ((last_3.iloc[-1]['Turnover'] > 200000) and (last_3.iloc[-1]['Turnover'] > 2 * last_3.iloc[-3]['Turnover'])) or
        #     ((last_3.iloc[-1]['Turnover'] > 200000) and (last_3.iloc[-1]['Turnover'] > 2 * last_3.iloc[-2]['Turnover'])) or
        #     ((last_3.iloc[-2]['Turnover'] > 200000) and (last_3.iloc[-2]['Turnover'] > 2 * last_3.iloc[-3]['Turnover']))
        # )
        # if result:
        #     # 添加提醒
        #     set_stock_reminder(stock_code, quote_ctx)
