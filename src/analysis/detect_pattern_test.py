def detect_pattern(df, first_index, prev_10_mean_vol):
    """
    检测满足特定形态的 K 线组合，要求如下：
    
    1) 候选 K 线（位于 first_index 处）需满足：
       - 涨幅 > 20%
       - 成交量大于前10根 K 线平均成交量的10倍（通过 prev_10_mean_vol 参数给出）
    
    2) 中间连续的 1 到 3 根 K 线需要满足：
       - 每根 K 线的振幅明显缩小（示例中目标为振幅 < 5%，但代码中用的阈值为 0.2，即20%，请根据实际需求调整）
    
    3) 最后一根 K 线需要满足：
       - 为阳线（收盘价 > 开盘价）
       - （可选）成交量大于其前一根 K 线
       - 收盘价高于候选 K 线的收盘价

    如果找到满足条件的形态，返回一个三元组：(候选 K 线的索引, 最后一根 K 线的索引, 中间 K 线数量)（索引转换为字符串返回），
    否则返回 (None, None, None)。
    """
    # 获取 DataFrame 的总行数
    data_size = len(df)
    
    # 根据传入的 first_index（通常为时间索引）获取其在 DataFrame 中的整数位置
    first_idx = df.index.get_loc(first_index)
    
    # --- 1) 检查候选 K 线（第一根 K 线）的条件 ---
    # 提取候选 K 线的数据
    # open_i = df['Open'].iloc[first_idx]
    close_i = df['Close'].iloc[first_idx]
    volume_i = df['Volume'].iloc[first_idx]
    
    # 判断候选 K 线的成交量是否大于前10根均量的10倍
    cond_vol_10x = (volume_i > 10 * prev_10_mean_vol)
    if not cond_vol_10x:
        # 如果不满足成交量条件，则直接返回未匹配结果
        return None, None, None
    
    # --- 2) 检查中间连续 1～3 根 K 线是否满足振幅缩小条件 ---
    # 尝试不同数量的中间 K 线：1、2 或 3 根
    for middle_bars_count in [1, 2, 3]:
        # 计算最后一根 K 线的索引位置：
        # 候选 K 线之后依次跟随 middle_bars_count 根中间 K 线，再加1根作为最后 K 线
        final_idx = first_idx + 1 + middle_bars_count
        if final_idx >= data_size:
            break  # 超出数据范围则退出循环
        
        # 检查这几根中间 K 线是否都满足“振幅明显缩小”的条件
        middle_indices = range(first_idx + 1, first_idx + 1 + middle_bars_count)
        all_middle_small = True  # 标志位，记录所有中间 K 线是否都符合条件
        for j in middle_indices:
            open_j = df['Open'].iloc[j]
            close_j = df['Close'].iloc[j]
            # 如果开盘价无效（<= 0），则视为不满足条件
            if open_j <= 0:
                all_middle_small = False
                break
            # 计算该 K 线的振幅（绝对涨跌幅占比）
            amplitude_j = abs(close_j - open_j) / open_j
            # 判断该 K 线的振幅是否超过阈值（此处用 0.2，即20%，如目标为5%则调整为0.05）
            if amplitude_j > 0.2:
                all_middle_small = False
                break
        
        # 如果中间任一 K 线不满足缩小条件，则尝试下一个中间 K 线数量
        if not all_middle_small:
            continue
        
        # --- 3) 检查最后一根 K 线的条件 ---
        if final_idx >= data_size:
            break  # 防止超出数据范围
        
        # 提取最后一根 K 线的数据
        open_final = df['Open'].iloc[final_idx]
        close_final = df['Close'].iloc[final_idx]
        volume_final = df['Volume'].iloc[final_idx]
        
        # 3.1 判断最后一根 K 线是否为阳线（收盘价 > 开盘价）
        cond_final_up = (close_final > open_final)
        
        # 3.2 判断最后一根 K 线的成交量是否大于其前一根（此条件计算后未在最终判断中使用）
        # volume_prev = df['Volume'].iloc[final_idx - 1]
        # cond_final_vol = (volume_final > volume_prev)
        
        # 3.3 判断最后一根 K 线的收盘价是否高于候选 K 线的收盘价
        cond_final_close = (close_final > close_i)
        
        # 如果最后一根 K 线满足为阳线和收盘价更高的条件，则认为找到了符合条件的形态
        if cond_final_up and cond_final_close:
            # 返回候选 K 线和最后一根 K 线的索引（转换为字符串）以及中间 K 线数量
            return str(df.index[first_idx]), str(df.index[final_idx]), middle_bars_count
    
    # 如果所有尝试均未找到满足条件的形态，则返回未匹配结果
    return None, None, None