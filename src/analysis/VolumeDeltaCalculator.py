import pandas as pd

class VolumeDeltaCalculator:
    def __init__(self, original_data: pd.DataFrame):
        """
        :param original_data: 原始K线数据 DataFrame，需包含datetime和volume列
        """
        self.data = original_data
        self.cum_vol = 0.0
        
    def calculate_delta(self, 
                      use_custom_timeframe: bool = False,
                      custom_timeframe: str = '1') -> pd.DataFrame:
        """主计算函数"""
        # 1. 确定时间框架
        timeframe = self._determine_timeframe(use_custom_timeframe, custom_timeframe)
        
        # 2. 获取对应时间框架数据
        resampled_data = self._resample_data(timeframe)
        
        # 3. 计算Delta指标
        return self._calculate_volume_delta(resampled_data)
    
    def _determine_timeframe(self, 
                            use_custom: bool, 
                            custom_tf: str) -> str:
        """自动确定时间框架"""
        if use_custom:
            return custom_tf
        
        # 自动选择逻辑（需根据实际数据频率调整）
        freq = pd.infer_freq(self.data['datetime'])
        if 'S' in freq:    # 如果原始数据是秒级
            return '1S'    # 降采样到1秒
        elif 'T' in freq or 'H' in freq:  # 如果原始数据是分钟或小时级
            return '1T'    # 降采样到1分钟
        elif 'D' in freq:  # 如果原始数据是日线
            return '5T'    # 降采样到5分钟
        else:
            return '60T'   # 其他情况降采样到60分钟
    
    def _resample_data(self, timeframe: str) -> pd.DataFrame:
        """重采样到指定时间框架"""
        return (self.data
                .set_index('datetime')
                .resample(timeframe)
                .agg({
                    'volume': ['first', 'max', 'min', 'last']
                }))
    
    def _calculate_volume_delta(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算成交量Delta"""
        """输入数据结构示例：
            datetime	        ('volume', 'first')	('volume', 'last')	
            2024-01-01 09:30:00	  100 (开盘成交量)	    150 (收盘成交量)"""
        # 计算结果
        data['volume_delta'] = data[('volume', 'last')] - data[('volume', 'first')]
        
        # 累积成交量
        self.cum_vol += data[('volume', 'sum')].sum()
        
        # 验证数据
        if self.cum_vol == 0:
            raise ValueError("数据供应商未提供成交量数据")
            
        return data[['volume_delta']]

# 使用示例
if __name__ == "__main__":
    # 假设从CSV加载原始数据
    raw_data = pd.read_csv('data.csv', parse_dates=['datetime'])
    
    # 初始化计算器
    calculator = VolumeDeltaCalculator(raw_data)
    
    # 计算结果（使用自动时间框架）
    result = calculator.calculate_delta()
    
    # 查看结果
    print(result.tail())