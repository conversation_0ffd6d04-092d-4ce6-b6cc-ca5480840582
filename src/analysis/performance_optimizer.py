"""
性能优化模块
提供股票分析器的性能监控和优化功能
"""

import time
import psutil
import threading
import queue
from typing import Dict, List, Optional, Callable, Any
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from functools import wraps
import cProfile
import pstats
import io
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    function_name: str
    execution_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    data_size: int
    timestamp: datetime
    
@dataclass
class OptimizationResult:
    """优化结果数据类"""
    original_time: float
    optimized_time: float
    improvement_ratio: float
    memory_saved_mb: float
    optimization_method: str

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
        self.profiler_enabled = False
        self.profiler = None
        
    def monitor_function(self, func: Callable) -> Callable:
        """函数性能监控装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024
            start_cpu = psutil.cpu_percent()
            
            try:
                result = func(*args, **kwargs)
                
                end_time = time.time()
                end_memory = psutil.Process().memory_info().rss / 1024 / 1024
                end_cpu = psutil.cpu_percent()
                
                # 计算数据大小
                data_size = self._estimate_data_size(args, kwargs)
                
                # 记录性能指标
                metrics = PerformanceMetrics(
                    function_name=func.__name__,
                    execution_time=end_time - start_time,
                    memory_usage_mb=end_memory - start_memory,
                    cpu_usage_percent=(end_cpu + start_cpu) / 2,
                    data_size=data_size,
                    timestamp=datetime.now()
                )
                
                self.metrics.append(metrics)
                
                # 如果执行时间过长，记录警告
                if metrics.execution_time > 1.0:
                    logging.warning(f"函数 {func.__name__} 执行时间过长: {metrics.execution_time:.3f}秒")
                
                return result
                
            except Exception as e:
                logging.error(f"函数 {func.__name__} 执行出错: {str(e)}")
                raise
                
        return wrapper
    
    def _estimate_data_size(self, args: tuple, kwargs: dict) -> int:
        """估算数据大小"""
        total_size = 0
        
        for arg in args:
            if isinstance(arg, pd.DataFrame):
                total_size += arg.memory_usage(deep=True).sum()
            elif isinstance(arg, (list, tuple)):
                total_size += len(arg) * 8  # 估算
            elif isinstance(arg, dict):
                total_size += len(arg) * 16  # 估算
        
        for value in kwargs.values():
            if isinstance(value, pd.DataFrame):
                total_size += value.memory_usage(deep=True).sum()
            elif isinstance(value, (list, tuple)):
                total_size += len(value) * 8
            elif isinstance(value, dict):
                total_size += len(value) * 16
        
        return total_size
    
    def get_performance_summary(self) -> Dict:
        """获取性能汇总"""
        if not self.metrics:
            return {}
        
        df = pd.DataFrame([
            {
                'function': m.function_name,
                'time': m.execution_time,
                'memory': m.memory_usage_mb,
                'cpu': m.cpu_usage_percent,
                'data_size': m.data_size
            }
            for m in self.metrics
        ])
        
        summary = {}
        for func in df['function'].unique():
            func_data = df[df['function'] == func]
            summary[func] = {
                'avg_time': func_data['time'].mean(),
                'max_time': func_data['time'].max(),
                'min_time': func_data['time'].min(),
                'avg_memory': func_data['memory'].mean(),
                'max_memory': func_data['memory'].max(),
                'call_count': len(func_data),
                'total_time': func_data['time'].sum()
            }
        
        return summary
    
    def start_profiling(self):
        """开始性能分析"""
        self.profiler = cProfile.Profile()
        self.profiler.enable()
        self.profiler_enabled = True
        
    def stop_profiling(self) -> str:
        """停止性能分析并返回报告"""
        if not self.profiler_enabled:
            return "性能分析未启动"
        
        self.profiler.disable()
        self.profiler_enabled = False
        
        # 生成报告
        s = io.StringIO()
        ps = pstats.Stats(self.profiler, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats(20)  # 显示前20个最耗时的函数
        
        return s.getvalue()

class DataOptimizer:
    """数据优化器"""
    
    @staticmethod
    def optimize_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame内存使用"""
        optimized_df = df.copy()
        
        for col in optimized_df.columns:
            col_type = optimized_df[col].dtype
            
            if col_type != 'object':
                c_min = optimized_df[col].min()
                c_max = optimized_df[col].max()
                
                if str(col_type)[:3] == 'int':
                    if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                        optimized_df[col] = optimized_df[col].astype(np.int8)
                    elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                        optimized_df[col] = optimized_df[col].astype(np.int16)
                    elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                        optimized_df[col] = optimized_df[col].astype(np.int32)
                
                elif str(col_type)[:5] == 'float':
                    if c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                        optimized_df[col] = optimized_df[col].astype(np.float32)
        
        return optimized_df
    
    @staticmethod
    def create_data_cache(max_size: int = 100) -> Dict:
        """创建数据缓存"""
        return {
            'cache': {},
            'access_times': {},
            'max_size': max_size
        }
    
    @staticmethod
    def get_from_cache(cache: Dict, key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if key in cache['cache']:
            cache['access_times'][key] = datetime.now()
            return cache['cache'][key]
        return None
    
    @staticmethod
    def put_to_cache(cache: Dict, key: str, value: Any):
        """将数据放入缓存"""
        # 如果缓存已满，删除最久未访问的项
        if len(cache['cache']) >= cache['max_size']:
            oldest_key = min(cache['access_times'], key=cache['access_times'].get)
            del cache['cache'][oldest_key]
            del cache['access_times'][oldest_key]
        
        cache['cache'][key] = value
        cache['access_times'][key] = datetime.now()

class ParallelProcessor:
    """并行处理器"""
    
    def __init__(self, max_workers: Optional[int] = None):
        self.max_workers = max_workers or min(4, mp.cpu_count())
        
    def process_data_parallel(self, 
                            data_chunks: List[Any], 
                            process_func: Callable,
                            use_processes: bool = False) -> List[Any]:
        """并行处理数据"""
        if use_processes:
            executor_class = ProcessPoolExecutor
        else:
            executor_class = ThreadPoolExecutor
        
        results = []
        with executor_class(max_workers=self.max_workers) as executor:
            futures = [executor.submit(process_func, chunk) for chunk in data_chunks]
            
            for future in futures:
                try:
                    result = future.result(timeout=30)  # 30秒超时
                    results.append(result)
                except Exception as e:
                    logging.error(f"并行处理出错: {str(e)}")
                    results.append(None)
        
        return results
    
    def split_dataframe(self, df: pd.DataFrame, n_chunks: Optional[int] = None) -> List[pd.DataFrame]:
        """将DataFrame分割为多个块"""
        if n_chunks is None:
            n_chunks = self.max_workers
        
        chunk_size = len(df) // n_chunks
        chunks = []
        
        for i in range(n_chunks):
            start_idx = i * chunk_size
            if i == n_chunks - 1:  # 最后一块包含剩余所有数据
                end_idx = len(df)
            else:
                end_idx = (i + 1) * chunk_size
            
            chunks.append(df.iloc[start_idx:end_idx].copy())
        
        return chunks

class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self):
        self.monitor = PerformanceMonitor()
        self.data_optimizer = DataOptimizer()
        self.parallel_processor = ParallelProcessor()
        self.optimization_history: List[OptimizationResult] = []
        
    def optimize_analyzer_performance(self, analyzer) -> OptimizationResult:
        """优化分析器性能"""
        # 记录原始性能
        original_time = self._benchmark_analyzer(analyzer)
        
        # 应用优化
        optimizations_applied = []
        
        # 1. 启用数据缓存
        if not hasattr(analyzer, '_data_cache'):
            analyzer._data_cache = self.data_optimizer.create_data_cache()
            optimizations_applied.append("数据缓存")
        
        # 2. 优化配置参数
        if hasattr(analyzer, 'config'):
            # 减少不必要的计算
            analyzer.config.enable_detailed_logging = False
            optimizations_applied.append("配置优化")
        
        # 3. 启用性能监控
        self._apply_monitoring_to_analyzer(analyzer)
        optimizations_applied.append("性能监控")
        
        # 记录优化后性能
        optimized_time = self._benchmark_analyzer(analyzer)
        
        # 计算改进
        improvement_ratio = (original_time - optimized_time) / original_time if original_time > 0 else 0
        
        result = OptimizationResult(
            original_time=original_time,
            optimized_time=optimized_time,
            improvement_ratio=improvement_ratio,
            memory_saved_mb=0.0,  # 简化，实际应该测量
            optimization_method=", ".join(optimizations_applied)
        )
        
        self.optimization_history.append(result)
        
        logging.info(f"性能优化完成: 提升 {improvement_ratio:.1%}")
        return result
    
    def _benchmark_analyzer(self, analyzer) -> float:
        """基准测试分析器性能"""
        # 创建测试数据
        test_data = self._create_test_data()
        
        start_time = time.time()
        
        try:
            # 运行分析
            analyzer.analyze_window(
                test_data['ticker'],
                test_data['orderbook'],
                test_data['history']
            )
        except Exception as e:
            logging.warning(f"基准测试时出错: {str(e)}")
        
        return time.time() - start_time
    
    def _create_test_data(self) -> Dict[str, pd.DataFrame]:
        """创建测试数据"""
        # 简化的测试数据
        n_points = 100
        base_time = datetime.now()
        
        ticker_data = pd.DataFrame({
            'date_time': [base_time + timedelta(seconds=i) for i in range(n_points)],
            'price': np.random.uniform(2.0, 3.0, n_points),
            'size': np.random.randint(100, 1000, n_points),
            'ticker_direction': np.random.choice(['BUY', 'SELL'], n_points)
        })
        
        orderbook_data = pd.DataFrame({
            'date_time': [base_time + timedelta(seconds=i) for i in range(n_points)],
            'bid_price': np.random.uniform(1.9, 2.9, n_points),
            'ask_price': np.random.uniform(2.1, 3.1, n_points),
            'bid_size': np.random.randint(1000, 5000, n_points),
            'ask_size': np.random.randint(1000, 5000, n_points),
            'bid_past_low': np.random.choice([True, False], n_points),
            'ask_past_high': np.random.choice([True, False], n_points)
        })
        
        return {
            'ticker': ticker_data,
            'orderbook': orderbook_data,
            'history': ticker_data.copy()
        }
    
    def _apply_monitoring_to_analyzer(self, analyzer):
        """为分析器应用性能监控"""
        # 为关键方法添加监控装饰器
        if hasattr(analyzer, 'analyze_window'):
            analyzer.analyze_window = self.monitor.monitor_function(analyzer.analyze_window)
        
        if hasattr(analyzer, '_analyze_upward_pressure'):
            analyzer._analyze_upward_pressure = self.monitor.monitor_function(analyzer._analyze_upward_pressure)
    
    def generate_optimization_report(self) -> str:
        """生成优化报告"""
        if not self.optimization_history:
            return "暂无优化历史"
        
        latest = self.optimization_history[-1]
        
        report = f"""
# 性能优化报告

## 最新优化结果
- 原始执行时间: {latest.original_time:.3f}秒
- 优化后执行时间: {latest.optimized_time:.3f}秒
- 性能提升: {latest.improvement_ratio:.1%}
- 优化方法: {latest.optimization_method}

## 性能监控汇总
"""
        
        summary = self.monitor.get_performance_summary()
        for func_name, metrics in summary.items():
            report += f"""
### {func_name}
- 平均执行时间: {metrics['avg_time']:.3f}秒
- 最大执行时间: {metrics['max_time']:.3f}秒
- 调用次数: {metrics['call_count']}
- 总执行时间: {metrics['total_time']:.3f}秒
"""
        
        return report
