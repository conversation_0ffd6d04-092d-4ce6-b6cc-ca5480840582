"""
股票交易信号分析模块

该模块提供了对股票交易数据的实时分析功能，包括：
1. 成交量分析
2. 买卖盘分析
3. 技术指标计算
4. 交易信号生成
5. 置信度评估

主要组件：
- SignalStrength: 信号强度枚举类
- TickerDirection: 成交方向枚举类
- VolumeAnalysis: 成交量分析数据类
- OrderBookAnalysis: 买卖盘分析数据类
- OrderBookBehavior: 买卖盘行为特征类
- TechnicalSignal: 技术信号数据类
- StockAnalyzer: 股票分析器类
- MultiStockAnalyzer: 多股票并发分析器类

系统改进方向：
1. 主力特征权重提高：主动吸筹和假卖真拉同时出现的情况非常强烈地预示了上涨，可以考虑增加这种组合的权重
2. 买压指标阈值调整：数据显示，买压指标在1.3-1.5之间时，如果配合主力行为特征，预测效果很好。不需要过高的买压指标
3. 买盘积极性指标：几乎所有数据点都显示买盘积极性为True，可以考虑调整其阈值，使其更具区分度
4. 连续性分析：可以添加对连续几个时间窗口信号的分析，而不仅仅是单个窗口

评分系统说明：
系统使用多维度评分来评估交易信号质量，主要包括以下几个维度：

买压指标(buying_pressure)与成交量比率(volume_ratio)：
表示买入成交量与卖出成交量的比率，是衡量市场买方力量相对于卖方力量的重要指标。
- 买压指标 > 1：表示买方力量大于卖方力量，市场呈现净买入状态
- 买压指标 = 1：表示买卖力量平衡
- 买压指标 < 1：表示卖方力量大于买方力量，市场呈现净卖出状态
- 买压指标上限为10，即使实际比率更高也会被限制在10
- 成交量比率是买压指标的原始计算值，没有上限限制

1. 价格评分 (price_score)：
   评估价格变化的幅度和质量。分数范围通常是-5到+6分：
   - 3分左右：表示1-2%的良好上涨
   - 4-5分：表示2-5%的较强上涨
   - 6分：表示有主力推动的大幅上涨
   - 负分：表示价格下跌

2. 成交量评分 (volume_score)：
   评估成交量的强度和质量。成交量评分最高为8分：
   - 2-3分：表示适中的买卖比
   - 4-5分：表示较高的买卖比
   - 6-8分：表示非常高的买卖比，且有主力特征支撑
   
   系统中有两个相关指标：
   - volume_score_raw: 计算出的原始成交量评分，可能超过上限
   - volume_score: 应用上限(8分)后的最终成交量评分

3. 行为评分 (behavior_score)：
   评估盘口行为和主力特征。主要基于：
   - 主力吸筹特征（如主动吸筹、隐藏买盘）
   - 盘口支撑特征（如买盘支撑、卖单撤单）
   - 负面特征（如被动拉升、真实卖压、买单撤销）

4. 总评分 (total_score)：
   以上三项评分的总和，用于最终确定信号强度：
   - 总分 >= 14分：STRONG_BUY（强烈买入）
   - 总分 >= 11分：BUY（买入）
   - 总分 >= 8分：WATCH（观望）
   - 总分 >= 4分：WEAK（弱势）
   - 总分 < 4分：AVOID（避免）

主力行为特征说明：
1. 买盘支撑 (bid_support)：买盘力量强大，买一价挂单量远大于卖一价挂单量
2. 卖盘压力 (ask_pressure)：卖盘力量强大，卖一价挂单量远大于买一价挂单量
3. 买盘积极性 (bid_aggression)：买方积极性高，买单价格接近或高于卖一价
4. 主动吸筹 (active_absorption)：主力资金积极买入，大买单主动吃掉卖盘
5. 隐藏买盘 (hidden_buying)：隐蔽的买盘行为，成交方向不明确但价格上涨
6. 假卖真拉 (fake_pressure)：制造卖盘压力假象，同时暗中拉升价格
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum, auto
import concurrent.futures
from functools import partial
import threading
import queue
import logging
import psutil  # 用于监控系统资源
import pytz
import time
from src.utils.analysis_formatter import create_analyzer_result_dict
from src.utils.stock_utils import format_volume
from src.utils.analysis_score_utils import calculate_fifteen_min_behavior_score
from src.utils.constants import *

# 导入机器学习模式识别模块
try:
    from src.analysis.pattern_recognition import PatternRecognizer, PatternFeatures
    PATTERN_RECOGNITION_AVAILABLE = True
except ImportError:
    logging.warning("模式识别模块不可用，将使用基础分析功能")
    PATTERN_RECOGNITION_AVAILABLE = False

# 初始化CPU使用率
process = psutil.Process(os.getpid())
process.cpu_percent(interval=None) # 初始化采样点

class PRICE_TREND(Enum):
    """价格趋势枚举"""
    UP = auto()      # 上涨
    DOWN = auto()    # 下跌
    NEUTRAL = auto() # 横盘

class VOLUME_TREND(Enum):
    """成交量趋势枚举"""
    UP = auto()      # 放量
    DOWN = auto()    # 缩量
    NEUTRAL = auto() # 正常

class TickerDirection(Enum):
    """交易方向枚举类"""
    STRONG_BUY = 'STRONG_BUY'     # 强买入（高于卖一价）
    WEAK_BUY = 'WEAK_BUY'         # 弱买入（接近卖一价）
    NEUTRAL = 'NEUTRAL'           # 中性（价格在买卖盘中间）
    WEAK_SELL = 'WEAK_SELL'       # 弱卖出（接近买一价）
    STRONG_SELL = 'STRONG_SELL'   # 强卖出（低于买一价）
    UNKNOWN = 'UNKNOWN'           # 未知方向
    
    @classmethod
    def is_buy(cls, direction: str) -> bool:
        """判断是否为买入方向"""
        buy_values = {cls.STRONG_BUY.value, cls.WEAK_BUY.value, 'BUY', 'B'}
        return direction in buy_values
    
    @classmethod
    def is_sell(cls, direction: str) -> bool:
        """判断是否为卖出方向"""
        sell_values = {cls.STRONG_SELL.value, cls.WEAK_SELL.value, 'SELL', 'S'}
        return direction in sell_values

@dataclass
class AnalyzerConfig:
    """分析器配置类"""
    # 成交量相关阈值
    volume_surge_threshold: float = 2.0
    volume_ratio_threshold: float = 1.5
    
    # 价格相关阈值
    price_change_threshold: float = 0.001
    spread_threshold: float = 0.2
    
    # 买卖盘相关阈值
    withdrawal_threshold: float = 1000
    
    # 技术指标参数

class SignalStrength(Enum):
    """信号强度枚举"""
    STRONG_BUY = auto()  # 强烈买入
    BUY = auto()         # 买入
    WATCH = auto()       # 观望
    WEAK = auto()        # 弱势
    AVOID = auto()       # 避免

@dataclass
class VolumeAnalysis:
    """成交量分析结果"""
    buy_volume: float
    sell_volume: float
    volume_ratio: float
    is_volume_surge: bool
    avg_price: float
    neutral_volume: float
    neutral_bias: float
    
@dataclass
class OrderBookAnalysis:
    """买卖盘分析结果"""
    ask_price: float
    ask_size: float
    ask_size_change: float
    is_ask_withdrawn: bool
    is_ask_suppressed: bool
    bid_price: float
    bid_size: float
    bid_size_change: float
    is_bid_supported: bool
    is_bid_withdrawn: bool

@dataclass
class OrderBookBehavior:
    """买卖盘行为特征"""
    bid_support: bool = False          # 买盘支撑（买盘量远大于卖盘量）
    ask_pressure: bool = False         # 卖盘压力（卖盘量远大于买盘量）
    bid_aggression: bool = False       # 买盘积极性（买单价格接近卖一价）
    extreme_bid_aggression: bool = False # 极度买盘积极性（买单价格非常接近卖一价）
    sudden_bid_cancel: bool = False    # 买单突然撤单
    sudden_ask_cancel: bool = False    # 卖单突然撤单
    price_pushing: bool = False        # 推价行为
    hidden_buying: bool = False        # 隐藏买盘
    active_absorption: bool = False    # 主动吸筹
    passive_pushing: bool = False      # 被动拉升
    fake_pressure: bool = False        # 假卖真拉

    # === 上涨压力分析特征 ===
    large_buy_orders_decrease: bool = False  # 大单主动买入减少
    high_density_small_orders: bool = False  # 高位密集小单成交
    price_diff_narrowing: bool = False       # 逐笔价差变小
    sell_orders_piling: bool = False         # 卖盘堆积
    fake_sell_orders: bool = False           # 假压单行为
    real_sell_pressure: bool = False         # 真实卖压
    buy_side_thinning: bool = False          # 买盘变薄
    upward_pressure_detected: bool = False   # 检测到上涨压力
    weak_limit_up_orders: bool = False       # 封板挂单弱

    # === 增强的上涨压力分析特征 ===
    large_buy_frequency_decline: bool = False    # 大单买入频率下降
    trading_structure_shift: bool = False        # 交易结构转变（大单→小单）
    price_jump_decline: bool = False             # 价格跳跃性下降
    retail_chasing_pattern: bool = False         # 散户追高模式
    sell_order_layering: bool = False            # 卖盘梯队堆积
    institutional_sell_pressure: bool = False    # 机构卖压
    withdrawal_hanging_frequency: bool = False   # 撤单-挂单频率异常
    bid_ask_imbalance_surge: bool = False        # 买卖盘不平衡度激增
    market_acceptance_weakness: bool = False     # 市场承接力减弱

    # === 典型压力信号组合 ===
    retail_takeover_top: bool = False           # 散户接盘型顶部
    pressure_intensifying_top: bool = False     # 压力增强型顶部
    bull_trap_top: bool = False                 # 诱多陷阱型顶部
    weak_limit_up_top: bool = False             # 无法有效封板型顶部

    # === 时间窗口分析特征 ===
    critical_15_30min_pressure: bool = False    # 15-30分钟关键压力期
    scalping_10sec_signal: bool = False         # 10秒剥头皮信号
    volume_price_divergence: bool = False       # 量价背离
    order_flow_momentum_loss: bool = False      # 订单流动能丧失

@dataclass
class TechnicalSignal:
    """技术分析信号"""
    signal_strength: SignalStrength
    confidence: float
    price_trend: PRICE_TREND
    volume_trend: VOLUME_TREND
    buying_pressure: float
    volume_analysis: Optional[VolumeAnalysis] = None
    tech_indicators: Optional[Dict] = None
    book_behavior: Optional[OrderBookBehavior] = None

class StockAnalyzer:
    # 技术指标常量
    
    def __init__(self, config: Optional[AnalyzerConfig] = None, enable_ml: bool = True):
        """
        初始化分析器

        Args:
            config: 分析器配置对象，如果为None则使用默认配置
            enable_ml: 是否启用机器学习功能
        """
        self.config = config or AnalyzerConfig()
        self.last_orderbook_snapshots = []  # 存储最近的订单簿快照
        self.signal_history = []  # 存储最近的信号历史，用于连续性分析
        self.max_signal_history = 5  # 保留的最大信号历史数量
        self.historical_metrics = {}  # 存储历史指标分布，用于动态阈值计算

        # 机器学习模式识别器
        self.pattern_recognizer = None
        if enable_ml and PATTERN_RECOGNITION_AVAILABLE:
            try:
                self.pattern_recognizer = PatternRecognizer()
                logging.info("机器学习模式识别器已启用")
            except Exception as e:
                logging.warning(f"无法初始化模式识别器: {str(e)}")
        else:
            logging.info("机器学习功能已禁用或不可用")
        
    def _calculate_dynamic_threshold(self, 
                                   data_series: pd.Series, 
                                   metric_name: str, 
                                   default_quantile: float = 0.7, 
                                   min_samples: int = 20,
                                   use_historical: bool = True) -> float:
        """
        计算动态阈值，基于数据分布的分位数
        
        Args:
            data_series: 数据序列
            metric_name: 指标名称，用于从历史数据中检索
            default_quantile: 默认分位数
            min_samples: 最小样本数，低于此数量时使用简单统计
            use_historical: 是否使用历史数据
            
        Returns:
            float: 动态阈值
        """
        if data_series.empty:
            # 如果有历史数据，使用历史阈值
            if use_historical and metric_name in self.historical_metrics:
                return self.historical_metrics[metric_name]
            return 0.0
            
        # 如果样本量足够，使用分位数
        if len(data_series) >= min_samples:
            threshold = data_series.quantile(default_quantile)
        else:
            # 样本量不足，使用均值+标准差
            threshold = data_series.mean() + data_series.std()
            
        # 更新历史指标
        if use_historical:
            # 如果已有历史数据，使用指数移动平均更新
            if metric_name in self.historical_metrics:
                old_value = self.historical_metrics[metric_name]
                # 使用0.8的权重保留历史值，0.2的权重采用新值
                self.historical_metrics[metric_name] = old_value * 0.8 + threshold * 0.2
            else:
                self.historical_metrics[metric_name] = threshold
                
        return threshold if threshold > 0 else 0.0
        
    def _create_default_volume_analysis(self) -> VolumeAnalysis:
        """创建默认的成交量分析对象"""
        return VolumeAnalysis(
            buy_volume=0,
            sell_volume=0,
            volume_ratio=0,
            is_volume_surge=False,
            avg_price=0,
            neutral_volume=0,
            neutral_bias=0
        )
    
    def _create_default_orderbook_analysis(self) -> OrderBookAnalysis:
        """创建默认的买卖盘分析对象"""
        return OrderBookAnalysis(
            ask_price=0,
            ask_size=0,
            ask_size_change=0,
            is_ask_withdrawn=False,
            is_ask_suppressed=False,
            bid_price=0,
            bid_size=0,
            bid_size_change=0,
            is_bid_supported=False,
            is_bid_withdrawn=False
        )
    
    def _create_default_technical_indicators(self) -> Dict:
        """创建默认的技术指标字典"""
        return {}
    
    def _calculate_volume_metrics(self, 
                                window_data: pd.DataFrame,
                                history_data: pd.DataFrame) -> VolumeAnalysis:
        """计算成交量相关指标 - 使用统计分位法优化"""
        try:
            if window_data.empty or history_data.empty:
                logging.warning("成交量数据不足")
                return self._create_default_volume_analysis()

            # 优化1: 直接使用numpy数组进行所有计算，完全避免pandas操作
            window_size_array = window_data['size'].values  # 直接获取numpy数组
            window_price_array = window_data['price'].values  # 直接获取numpy数组
            
            # 优化2: 一次性计算总量
            total_volume = np.sum(window_size_array)
            
            # 优化3: 简化成交均价计算
            if total_volume > 0:
                avg_price = np.sum(window_price_array * window_size_array) / total_volume
            else:
                avg_price = np.mean(window_price_array) if len(window_price_array) > 0 else 0

            # 优化4: 更高效的成交量放大检测 - 使用动态阈值
            # 计算历史成交量的分布
            history_sizes = history_data['size'].values
            
            # 使用_calculate_dynamic_threshold计算动态成交量放大阈值
            volume_surge_base = self._calculate_dynamic_threshold(
                pd.Series(history_sizes), 
                'volume_surge_base', 
                default_quantile=0.9,  # 使用90%分位点作为放大阈值的基准
                min_samples=20
            )
            
            # 当前窗口总量与历史高位比较
            is_volume_surge = total_volume > volume_surge_base
            
            # 如果无法计算动态阈值，使用配置的固定阈值
            if volume_surge_base == 0.0:
                window_size = min(20, len(history_data))
                if window_size > 0:
                    avg_volume = np.mean(history_sizes[-window_size:]) if len(history_sizes) >= window_size else np.mean(history_sizes)
                    is_volume_surge = total_volume > avg_volume * self.config.volume_surge_threshold
                else:
                    is_volume_surge = False

            # 优化5: 高效计算买卖压力
            if 'ticker_direction' in window_data.columns:
                # 获取方向数组
                ticker_directions = window_data['ticker_direction'].values
                
                # 创建预计算的买入卖出掩码 - 使用vectorized操作
                buy_mask = np.array([TickerDirection.is_buy(d) for d in ticker_directions])
                sell_mask = np.array([TickerDirection.is_sell(d) for d in ticker_directions])
                neutral_mask = ~(buy_mask | sell_mask)
                
                # 使用numpy掩码索引直接计算
                buy_volume = np.sum(window_size_array[buy_mask]) if np.any(buy_mask) else 0.0
                sell_volume = np.sum(window_size_array[sell_mask]) if np.any(sell_mask) else 0.0
                neutral_volume = np.sum(window_size_array[neutral_mask]) if np.any(neutral_mask) else 0.0
            else:
                # 无方向信息时使用简单计算
                buy_volume = total_volume * 0.5
                sell_volume = total_volume * 0.5
                neutral_volume = 0.0
            
            # 优化6: 简化比率计算逻辑
            # 预设默认值
            volume_ratio = 1.0
            
            # 一次性计算最终值
            if sell_volume > 0:
                volume_ratio = buy_volume / sell_volume
            elif buy_volume > 0:
                volume_ratio = 10.0  # 只有买入没有卖出的情况
            
            # 优化7: 简化中性偏向计算
            neutral_bias = 0.0
            if neutral_volume > 0 and total_volume > 0:
                neutral_bias = (buy_volume - sell_volume) / total_volume

            # 直接返回结果对象
            return VolumeAnalysis(
                buy_volume=buy_volume,
                sell_volume=sell_volume,
                volume_ratio=volume_ratio,
                is_volume_surge=is_volume_surge,
                avg_price=avg_price,
                neutral_volume=neutral_volume,
                neutral_bias=neutral_bias
            )
        except Exception as e:
            logging.error(f"计算成交量指标时出错: {str(e)}", exc_info=True)
            return self._create_default_volume_analysis()
    
    def _analyze_orderbook(self, 
                          orderbook_window: pd.DataFrame) -> OrderBookAnalysis:
        """分析买卖盘数据 - 使用统计分位法优化"""
        try:
            if orderbook_window.empty or len(orderbook_window) < 2:
                logging.warning("买卖盘数据不足")
                return self._create_default_orderbook_analysis()

            # 获取开始和结束时的买卖盘数据
            start_bid_size = orderbook_window['bid_size'].iloc[0]
            start_ask_size = orderbook_window['ask_size'].iloc[0]
            end_bid_size = orderbook_window['bid_size'].iloc[-1]
            end_ask_size = orderbook_window['ask_size'].iloc[-1]
            end_bid_price = orderbook_window['bid_price'].iloc[-1]
            end_ask_price = orderbook_window['ask_price'].iloc[-1]

            # 计算买卖盘变化
            bid_size_change = end_bid_size - start_bid_size
            ask_size_change = end_ask_size - start_ask_size

            # 收集历史买卖盘变化率数据
            historical_changes = []
            if len(self.last_orderbook_snapshots) >= 10:
                for i in range(1, min(10, len(self.last_orderbook_snapshots))):
                    prev_ob = self.last_orderbook_snapshots[i-1].iloc[-1]
                    curr_ob = self.last_orderbook_snapshots[i].iloc[-1]
                    
                    prev_bid = prev_ob.get('bid_size', 0)
                    curr_bid = curr_ob.get('bid_size', 0)
                    if prev_bid > 0:
                        historical_changes.append((curr_bid - prev_bid) / prev_bid)
            
            # 使用_calculate_dynamic_threshold计算动态撤单阈值
            withdrawal_threshold = self._calculate_dynamic_threshold(
                pd.Series(historical_changes) if historical_changes else pd.Series(),
                'withdrawal_threshold',
                default_quantile=0.2,  # 使用20%分位点作为显著减少的阈值
                min_samples=5
            )
            
            # 如果无法计算动态阈值，使用默认值
            if withdrawal_threshold == 0.0:
                withdrawal_threshold = -0.5  # 默认减少50%
            else:
                # 确保阈值为负值且至少为-0.3
                withdrawal_threshold = min(-0.3, withdrawal_threshold)
            
            # 判断买卖盘撤单 - 使用动态阈值
            is_bid_withdrawn = bid_size_change / start_bid_size < withdrawal_threshold if start_bid_size > 0 else False
            is_ask_withdrawn = ask_size_change / start_ask_size < withdrawal_threshold if start_ask_size > 0 else False

            # 收集历史买卖盘比率数据
            bid_ask_ratios = []
            for snapshot in self.last_orderbook_snapshots:
                if not snapshot.empty:
                    last_row = snapshot.iloc[-1]
                    bid = last_row.get('bid_size', 0)
                    ask = last_row.get('ask_size', 0)
                    if ask > 0:
                        bid_ask_ratios.append(bid / ask)
                    if bid > 0:
                        bid_ask_ratios.append(ask / bid)
            
            # 使用_calculate_dynamic_threshold计算动态支撑/压力阈值
            support_threshold = self._calculate_dynamic_threshold(
                pd.Series(bid_ask_ratios) if bid_ask_ratios else pd.Series(),
                'support_threshold',
                default_quantile=0.9,  # 使用90%分位点作为显著支撑/压力的阈值
                min_samples=10
            )
            
            # 如果无法计算动态阈值，使用默认值
            if support_threshold == 0.0:
                support_threshold = 3.0  # 默认3倍差异
            else:
                # 限制在2-5倍范围内
                support_threshold = max(2.0, min(5.0, support_threshold))
            
            # 判断买盘支撑和卖盘压制 - 使用动态阈值
            is_bid_supported = end_bid_size > end_ask_size * support_threshold
            is_ask_suppressed = end_ask_size > end_bid_size * support_threshold

            return OrderBookAnalysis(
                ask_price=end_ask_price,
                ask_size=end_ask_size,
                ask_size_change=ask_size_change,
                is_ask_withdrawn=is_ask_withdrawn,
                is_ask_suppressed=is_ask_suppressed,
                bid_price=end_bid_price,
                bid_size=end_bid_size,
                bid_size_change=bid_size_change,
                is_bid_supported=is_bid_supported,
                is_bid_withdrawn=is_bid_withdrawn
            )
        except Exception as e:
            logging.error(f"分析买卖盘时出错: {str(e)}", exc_info=True)
            return self._create_default_orderbook_analysis()
    
    def _calculate_technical_indicators(self,
                                     window_data: pd.DataFrame,
                                     history_data: pd.DataFrame) -> Dict:
        """计算技术指标"""
        # 返回空字典，因为已经移除了MACD相关指标
        return {}
    
    def _analyze_orderbook_behavior(self, current_orderbook: pd.DataFrame) -> OrderBookBehavior:
        """
        分析盘口行为，识别主力行为特征 - 使用统计分位法优化
        
        Args:
            current_orderbook: 当前订单簿数据
            
        Returns:
            OrderBookBehavior: 包含各种盘口行为特征
        """
        signals = OrderBookBehavior()
        
        try:
            # 快速路径: 空数据检查
            if current_orderbook.empty or len(current_orderbook) < 1:
                return signals
            
            # 优化1: 一次性获取所有需要的数据，避免多次访问
            last_row = current_orderbook.iloc[-1]
            bid_size = last_row.get('bid_size')
            ask_size = last_row.get('ask_size')
            bid_price = last_row.get('bid_price')
            ask_price = last_row.get('ask_price')
            
            # 优化2: 合并条件检查，减少分支判断
            valid_size = not pd.isna(bid_size) and not pd.isna(ask_size)
            valid_price = not pd.isna(bid_price) and not pd.isna(ask_price) and ask_price > 0
            
            # 收集历史买卖盘比率数据
            bid_ask_size_ratios = []
            if len(self.last_orderbook_snapshots) >= 10:
                for snapshot in self.last_orderbook_snapshots:
                    if not snapshot.empty:
                        snap_row = snapshot.iloc[-1]
                        snap_bid = snap_row.get('bid_size', 0)
                        snap_ask = snap_row.get('ask_size', 0)
                        if snap_ask > 0:
                            bid_ask_size_ratios.append(snap_bid / snap_ask)
                        if snap_bid > 0:
                            bid_ask_size_ratios.append(snap_ask / snap_bid)
            
            # 使用_calculate_dynamic_threshold计算动态支撑/压力阈值
            support_threshold = self._calculate_dynamic_threshold(
                pd.Series(bid_ask_size_ratios) if bid_ask_size_ratios else pd.Series(),
                'behavior_support_threshold',
                default_quantile=0.9,  # 使用90%分位点作为支撑/压力阈值
                min_samples=5
            )
            
            # 如果无法计算动态阈值，使用默认值
            if support_threshold == 0.0:
                support_threshold = 3.0  # 默认阈值
            else:
                # 限制在合理范围内
                support_threshold = max(2.0, min(5.0, support_threshold))
            
            # 优化3: 使用动态阈值分析买卖盘支撑/压力
            if valid_size:
                signals.bid_support = bid_size > ask_size * support_threshold
                signals.ask_pressure = ask_size > bid_size * support_threshold
            
            # 收集历史买卖价比率数据
            bid_ask_price_ratios = []
            if len(self.last_orderbook_snapshots) >= 10:
                for snapshot in self.last_orderbook_snapshots:
                    if not snapshot.empty:
                        snap_row = snapshot.iloc[-1]
                        snap_bid_price = snap_row.get('bid_price', 0)
                        snap_ask_price = snap_row.get('ask_price', 0)
                        if snap_ask_price > 0:
                            bid_ask_price_ratios.append(snap_bid_price / snap_ask_price)
            
            # 使用_calculate_dynamic_threshold计算动态买盘积极性阈值
            aggression_threshold = self._calculate_dynamic_threshold(
                pd.Series(bid_ask_price_ratios) if bid_ask_price_ratios else pd.Series(),
                'aggression_threshold',
                default_quantile=0.75,  # 使用75%分位点作为一般积极性阈值
                min_samples=5
            )
            
            # 使用_calculate_dynamic_threshold计算动态极度买盘积极性阈值
            extreme_aggression_threshold = self._calculate_dynamic_threshold(
                pd.Series(bid_ask_price_ratios) if bid_ask_price_ratios else pd.Series(),
                'extreme_aggression_threshold',
                default_quantile=0.9,  # 使用90%分位点作为极度积极性阈值
                min_samples=5
            )
            
            # 如果无法计算动态阈值，使用默认值
            if aggression_threshold == 0.0:
                aggression_threshold = 0.95  # 默认阈值
            else:
                # 限制在合理范围内
                aggression_threshold = max(0.9, min(0.97, aggression_threshold))
                
            if extreme_aggression_threshold == 0.0:
                extreme_aggression_threshold = 0.98  # 默认阈值
            else:
                # 限制在合理范围内
                extreme_aggression_threshold = max(0.95, min(0.99, extreme_aggression_threshold))
            
            # 优化4: 使用动态阈值检查买盘积极性
            if valid_price:
                # 只计算一次比率
                bid_ask_ratio = bid_price / ask_price
                # 使用动态阈值设置两个相关信号
                signals.bid_aggression = bid_ask_ratio > aggression_threshold
                signals.extreme_bid_aggression = bid_ask_ratio > extreme_aggression_threshold
            
            # 收集历史买卖盘变化率数据
            size_change_ratios = []
            if len(self.last_orderbook_snapshots) >= 3:
                for i in range(1, len(self.last_orderbook_snapshots)):
                    prev_row = self.last_orderbook_snapshots[i-1].iloc[-1]
                    curr_row = self.last_orderbook_snapshots[i].iloc[-1]
                    
                    prev_bid = prev_row.get('bid_size', 0)
                    curr_bid = curr_row.get('bid_size', 0)
                    prev_ask = prev_row.get('ask_size', 0)
                    curr_ask = curr_row.get('ask_size', 0)
                    
                    if prev_bid > 0:
                        size_change_ratios.append(abs((curr_bid - prev_bid) / prev_bid))
                    if prev_ask > 0:
                        size_change_ratios.append(abs((curr_ask - prev_ask) / prev_ask))
            
            # 使用_calculate_dynamic_threshold计算动态撤单阈值
            withdrawal_threshold = self._calculate_dynamic_threshold(
                pd.Series(size_change_ratios) if size_change_ratios else pd.Series(),
                'behavior_withdrawal_threshold',
                default_quantile=0.75,  # 使用75%分位点作为显著变化的阈值
                min_samples=3
            )
            
            # 如果无法计算动态阈值，使用默认值
            if withdrawal_threshold == 0.0:
                withdrawal_threshold = 0.5  # 默认阈值
            else:
                # 限制在合理范围内
                withdrawal_threshold = max(0.3, min(0.7, withdrawal_threshold))
            
            # 优化5: 使用动态阈值进行盘口变化分析
            if len(self.last_orderbook_snapshots) >= 2:
                # 获取前一个快照的最后一行
                before_last_row = self.last_orderbook_snapshots[-2].iloc[-1]
                
                # 一次性获取所有需要的历史值
                before_ask_size = before_last_row.get('ask_size', 0)
                before_bid_size = before_last_row.get('bid_size', 0)
                before_ask_price = before_last_row.get('ask_price', 0)
                before_bid_price = before_last_row.get('bid_price', 0)
                
                # 检测卖单撤单与假卖真拉 - 使用动态阈值
                if before_ask_size > 0 and ask_size < before_ask_size * (1 - withdrawal_threshold):
                    signals.sudden_ask_cancel = True
                    signals.fake_pressure = ask_price > before_ask_price
                
                # 检测买单撤单 - 使用动态阈值
                if before_bid_size > 0 and bid_size < before_bid_size * (1 - withdrawal_threshold):
                    signals.sudden_bid_cancel = True
                
                # 检测推价行为 - 合并多个条件检查
                if bid_price > before_bid_price:
                    # 检查隐藏买盘和价格推动
                    has_neutral_direction = ('ticker_direction' in current_orderbook.columns and 
                                             current_orderbook['ticker_direction'].iloc[-1] == TickerDirection.NEUTRAL.value)
                    
                    if has_neutral_direction:
                        signals.hidden_buying = True
                        signals.price_pushing = True
                    
                    # 高效检测主动吸筹和被动推高
                    if signals.bid_support and (signals.sudden_ask_cancel or signals.fake_pressure):
                        signals.active_absorption = True
                    elif signals.ask_pressure and bid_price > before_ask_price:
                        signals.passive_pushing = True
            
            # 优化6: 高效管理订单簿快照历史
            # 使用浅复制减少内存开销
            self.last_orderbook_snapshots.append(current_orderbook)
            # 使用条件检查避免频繁弹出操作
            if len(self.last_orderbook_snapshots) > 5:
                self.last_orderbook_snapshots = self.last_orderbook_snapshots[-5:]
                
        except Exception as e:
            logging.error(f"分析盘口行为时出错: {str(e)}", exc_info=True)
            
        return signals
    
    def _analyze_upward_pressure(self, ticker_window: pd.DataFrame, orderbook_window: pd.DataFrame) -> OrderBookBehavior:
        """
        增强的上涨压力分析，支持多时间窗口分析
        包括15-30分钟关键观察期和10秒剥头皮分析
        """
        signals = OrderBookBehavior()

        if ticker_window.empty or orderbook_window.empty:
            return signals

        ticker_df = ticker_window.copy()
        orderbook_df = orderbook_window.copy()

        # 防止相同时间戳导致排序错误
        ticker_df['date_time'] += pd.to_timedelta(ticker_df.groupby('date_time').cumcount(), unit='us')
        orderbook_df['date_time'] += pd.to_timedelta(orderbook_df.groupby('date_time').cumcount(), unit='us')

        ticker_df.sort_values("date_time", inplace=True)
        orderbook_df.sort_values("date_time", inplace=True)

        # 多时间窗口分析
        end_time = ticker_df['date_time'].max()

        # 1. 10秒窗口（剥头皮分析）
        scalping_start = end_time - pd.Timedelta(seconds=10)
        scalping_ticker = ticker_df[(ticker_df['date_time'] >= scalping_start) & (ticker_df['date_time'] <= end_time)].copy()
        scalping_orderbook = orderbook_df[(orderbook_df['date_time'] >= scalping_start) & (orderbook_df['date_time'] <= end_time)].copy()

        # 2. 1分钟窗口（原有分析）
        minute_start = end_time - pd.Timedelta(minutes=1)
        recent_ticker = ticker_df[(ticker_df['date_time'] >= minute_start) & (ticker_df['date_time'] <= end_time)].copy()
        recent_orderbook = orderbook_df[(orderbook_df['date_time'] >= minute_start) & (orderbook_df['date_time'] <= end_time)].copy()

        # 3. 15-30分钟窗口（关键观察期）
        critical_start_15 = end_time - pd.Timedelta(minutes=15)
        critical_start_30 = end_time - pd.Timedelta(minutes=30)
        critical_ticker_15 = ticker_df[(ticker_df['date_time'] >= critical_start_15) & (ticker_df['date_time'] <= end_time)].copy()
        critical_ticker_30 = ticker_df[(ticker_df['date_time'] >= critical_start_30) & (ticker_df['date_time'] <= end_time)].copy()
        critical_orderbook_15 = orderbook_df[(orderbook_df['date_time'] >= critical_start_15) & (orderbook_df['date_time'] <= end_time)].copy()
        critical_orderbook_30 = orderbook_df[(orderbook_df['date_time'] >= critical_start_30) & (orderbook_df['date_time'] <= end_time)].copy()

        if recent_ticker.empty or recent_orderbook.empty:
            return signals

        # === 增强的大单主动买入减少检测 ===
        signals = self._analyze_large_buy_decrease(recent_ticker, critical_ticker_15, critical_ticker_30, signals)

        # === 10秒剥头皮信号分析 ===
        signals = self._analyze_scalping_signals(scalping_ticker, scalping_orderbook, signals)

        # === 15-30分钟关键观察期分析 ===
        signals = self._analyze_critical_period_pressure(critical_ticker_15, critical_ticker_30,
                                                        critical_orderbook_15, critical_orderbook_30, signals)

        # === 高位小单密集（稳健std + 波动保护）===
        q75, q25 = recent_ticker['price'].quantile(0.75), recent_ticker['price'].quantile(0.25)
        iqr_price = q75 - q25
        if iqr_price < recent_ticker['price'].median() * 0.002:  # 低波动保护
            small_orders = recent_ticker[recent_ticker['size'] <= recent_ticker['size'].quantile(0.25)]
            if len(small_orders) >= 10 and small_orders['price'].median() > recent_ticker['price'].median():
                signals.high_density_small_orders = True

        # === 逐笔价差缩小 ===
        if len(recent_ticker) > 2:
            price_diff = recent_ticker['price'].diff().abs()
            if price_diff.dropna().mean() < recent_ticker['price'].mean() * 0.001:
                signals.price_diff_narrowing = True

        # === 卖盘堆积 ===
        ask_stack = recent_orderbook.groupby('ask_price')['ask_size'].sum()
        if not ask_stack.empty:
            max_ask_size = ask_stack.max()
            median_ask_size = ask_stack.median()
            ask_volatility = recent_orderbook['ask_price'].std()
            if max_ask_size > 2 * median_ask_size and ask_volatility < recent_orderbook['ask_price'].mean() * 0.001:
                signals.sell_orders_piling = True

        # === 假卖压单 ===
        fake_sell = recent_orderbook[
            (recent_orderbook['ask_past_high']) &
            (recent_orderbook['ask_size'] >= recent_orderbook['ask_size'].quantile(0.9))
        ]
        # 重新计算large_buy和total_amt用于假卖压单判断
        large_buy_for_fake = recent_ticker[
            (recent_ticker['ticker_direction'].apply(TickerDirection.is_buy)) &
            (recent_ticker['size'] >= recent_ticker['size'].quantile(0.75))
        ]
        if not fake_sell.empty and not large_buy_for_fake.empty:
            signals.fake_sell_orders = True

        # === 真实卖压 ===
        large_sell = recent_ticker[(recent_ticker['ticker_direction'].apply(TickerDirection.is_sell)) & (recent_ticker['size'] >= recent_ticker['size'].quantile(0.75))]
        sell_amt = (large_sell['price'] * large_sell['size']).sum()
        total_amt_for_sell = (recent_ticker['price'] * recent_ticker['size']).sum()
        if total_amt_for_sell > 0 and sell_amt > total_amt_for_sell * 0.5:
            # 判断价格是否有明显走低趋势
            if recent_ticker['price'].iloc[-1] < recent_ticker['price'].iloc[0] * 0.995:
                signals.real_sell_pressure = True

        # === 买盘变薄 ===
        if len(recent_orderbook) >= 2:
            last_snapshot = recent_orderbook.iloc[-1]
            prev_snapshot = recent_orderbook.iloc[-2]
            if last_snapshot['bid_size'] < prev_snapshot['bid_size'] * 0.7:
                signals.buy_side_thinning = True

        # === 封板疲软 ===
        if not recent_ticker.empty:
            last_price = recent_ticker['price'].iloc[-1]
            upper_limit_price = round(last_price * 1.099, 2)
            near_limit = recent_orderbook[recent_orderbook['ask_price'] >= upper_limit_price - 0.01]
            if not near_limit.empty and near_limit['ask_size'].mean() < recent_orderbook['ask_size'].quantile(0.5):
                signals.weak_limit_up_orders = True

        # === 典型压力信号组合识别 ===
        signals = self._identify_pressure_signal_combinations(signals)

        # === 最终信号组合判定 ===
        pressure_signals_count = sum([
            signals.large_buy_orders_decrease,
            signals.high_density_small_orders,
            signals.price_diff_narrowing,
            signals.sell_orders_piling,
            signals.fake_sell_orders,
            signals.buy_side_thinning,
            signals.weak_limit_up_orders,
            signals.real_sell_pressure,
            signals.large_buy_frequency_decline,
            signals.trading_structure_shift,
            signals.price_jump_decline,
            signals.critical_15_30min_pressure,
            signals.volume_price_divergence,
            signals.order_flow_momentum_loss
        ])

        # 如果检测到3个或以上压力信号，或者检测到典型压力组合
        if pressure_signals_count >= 3 or any([
            signals.retail_takeover_top,
            signals.pressure_intensifying_top,
            signals.bull_trap_top,
            signals.weak_limit_up_top
        ]):
            signals.upward_pressure_detected = True

        return signals

    def _identify_pressure_signal_combinations(self, signals: OrderBookBehavior) -> OrderBookBehavior:
        """
        识别典型的压力信号组合
        """
        # 1. 散户接盘型顶部：大单买入消失 + 小单成交密集 + 价格不再上涨
        if (signals.large_buy_orders_decrease and
            signals.high_density_small_orders and
            (signals.price_diff_narrowing or signals.price_jump_decline)):
            signals.retail_takeover_top = True

        # 2. 压力增强型顶部：卖盘逐渐加厚 + 成交量萎缩 + 价格横盘或小幅回落
        if (signals.sell_orders_piling and
            signals.volume_price_divergence and
            (signals.buy_side_thinning or signals.order_flow_momentum_loss)):
            signals.pressure_intensifying_top = True

        # 3. 诱多陷阱型顶部：快速拉升至涨停 → 涨停板打开 → 价格迅速回落
        if (signals.weak_limit_up_orders and
            signals.fake_sell_orders and
            signals.trading_structure_shift):
            signals.bull_trap_top = True

        # 4. 无法有效封板型顶部：涨停板反复开合 + 封单量小且不稳定
        if (signals.weak_limit_up_orders and
            signals.buy_side_thinning and
            (signals.large_buy_frequency_decline or signals.order_flow_momentum_loss)):
            signals.weak_limit_up_top = True

        return signals

    def _analyze_large_buy_decrease(self, recent_ticker: pd.DataFrame,
                                   critical_ticker_15: pd.DataFrame,
                                   critical_ticker_30: pd.DataFrame,
                                   signals: OrderBookBehavior) -> OrderBookBehavior:
        """
        增强的大单主动买入减少分析
        通过时间序列分析检测大单买入频率和规模的变化
        """
        try:
            if recent_ticker.empty:
                return signals

            # 1. 基础大单买入分析（1分钟窗口）
            large_buy_recent = recent_ticker[
                (recent_ticker['ticker_direction'].apply(TickerDirection.is_buy)) &
                (recent_ticker['size'] >= recent_ticker['size'].quantile(0.75))
            ]
            large_buy_amt_recent = (large_buy_recent['price'] * large_buy_recent['size']).sum()
            total_amt_recent = (recent_ticker['price'] * recent_ticker['size']).sum()

            # 2. 大单买入频率分析（15分钟对比）
            if not critical_ticker_15.empty and len(critical_ticker_15) > len(recent_ticker):
                # 计算15分钟内大单买入频率
                large_buy_15min = critical_ticker_15[
                    (critical_ticker_15['ticker_direction'].apply(TickerDirection.is_buy)) &
                    (critical_ticker_15['size'] >= critical_ticker_15['size'].quantile(0.75))
                ]

                # 计算频率变化（每分钟大单买入次数）
                recent_frequency = len(large_buy_recent) / 1.0  # 1分钟内的频率
                historical_frequency = len(large_buy_15min) / 15.0  # 15分钟内的平均频率

                # 如果最近1分钟的大单买入频率显著低于历史平均
                if historical_frequency > 0 and recent_frequency < historical_frequency * 0.3:
                    signals.large_buy_frequency_decline = True

            # 3. 交易结构转变分析（大单→小单）
            if total_amt_recent > 0:
                large_buy_ratio = large_buy_amt_recent / total_amt_recent
                small_orders = recent_ticker[recent_ticker['size'] <= recent_ticker['size'].quantile(0.25)]
                small_order_ratio = len(small_orders) / len(recent_ticker) if len(recent_ticker) > 0 else 0

                # 大单成交额占比低且小单成交频率高
                if large_buy_ratio < 0.1 and small_order_ratio > 0.6:
                    signals.trading_structure_shift = True
                    signals.large_buy_orders_decrease = True

            # 4. 30分钟长期趋势分析
            if not critical_ticker_30.empty and len(critical_ticker_30) > len(critical_ticker_15):
                large_buy_30min = critical_ticker_30[
                    (critical_ticker_30['ticker_direction'].apply(TickerDirection.is_buy)) &
                    (critical_ticker_30['size'] >= critical_ticker_30['size'].quantile(0.75))
                ]

                # 计算30分钟内大单买入的衰减趋势
                if len(large_buy_30min) > 10:  # 确保有足够样本
                    # 将30分钟分为3个10分钟段，分析趋势
                    time_segments = pd.cut(large_buy_30min.index, bins=3, labels=['early', 'middle', 'recent'])
                    segment_counts = time_segments.value_counts()

                    # 如果最近10分钟的大单买入明显少于前20分钟
                    if 'recent' in segment_counts and 'early' in segment_counts:
                        if segment_counts['recent'] < segment_counts['early'] * 0.4:
                            signals.large_buy_frequency_decline = True

        except Exception as e:
            logging.error(f"分析大单买入减少时出错: {str(e)}", exc_info=True)

        return signals

    def _analyze_scalping_signals(self, scalping_ticker: pd.DataFrame,
                                 scalping_orderbook: pd.DataFrame,
                                 signals: OrderBookBehavior) -> OrderBookBehavior:
        """
        10秒剥头皮信号分析
        专门用于短期交易信号识别
        """
        try:
            if scalping_ticker.empty or scalping_orderbook.empty:
                return signals

            # 1. 快速价格跳跃分析
            if len(scalping_ticker) >= 3:
                price_changes = scalping_ticker['price'].diff().abs()
                avg_price_change = price_changes.mean()
                recent_price_change = price_changes.iloc[-1] if not price_changes.empty else 0

                # 如果最近的价格变动明显小于平均变动
                if avg_price_change > 0 and recent_price_change < avg_price_change * 0.3:
                    signals.price_jump_decline = True

            # 2. 快速买卖盘变化分析
            if len(scalping_orderbook) >= 2:
                bid_size_changes = scalping_orderbook['bid_size'].diff()
                ask_size_changes = scalping_orderbook['ask_size'].diff()

                # 检测买盘快速减少
                if bid_size_changes.iloc[-1] < 0 and abs(bid_size_changes.iloc[-1]) > scalping_orderbook['bid_size'].std():
                    signals.buy_side_thinning = True

                # 检测卖盘快速增加
                if ask_size_changes.iloc[-1] > 0 and ask_size_changes.iloc[-1] > scalping_orderbook['ask_size'].std():
                    signals.sell_orders_piling = True

            # 3. 剥头皮交易模式识别
            buy_orders = scalping_ticker[scalping_ticker['ticker_direction'].apply(TickerDirection.is_buy)]
            sell_orders = scalping_ticker[scalping_ticker['ticker_direction'].apply(TickerDirection.is_sell)]

            # 如果买卖单都很小且频繁
            if len(buy_orders) > 0 and len(sell_orders) > 0:
                avg_buy_size = buy_orders['size'].mean()
                avg_sell_size = sell_orders['size'].mean()
                total_avg_size = scalping_ticker['size'].mean()

                if avg_buy_size < total_avg_size * 0.5 and avg_sell_size < total_avg_size * 0.5:
                    signals.scalping_10sec_signal = True

        except Exception as e:
            logging.error(f"分析剥头皮信号时出错: {str(e)}", exc_info=True)

        return signals

    def _analyze_critical_period_pressure(self, critical_ticker_15: pd.DataFrame,
                                         critical_ticker_30: pd.DataFrame,
                                         critical_orderbook_15: pd.DataFrame,
                                         critical_orderbook_30: pd.DataFrame,
                                         signals: OrderBookBehavior) -> OrderBookBehavior:
        """
        15-30分钟关键观察期压力分析
        这是急速拉升后的关键时间窗口
        """
        try:
            # 1. 15分钟关键期分析
            if not critical_ticker_15.empty and not critical_orderbook_15.empty:
                # 分析价格趋势变化
                if len(critical_ticker_15) >= 10:
                    # 将15分钟分为3个5分钟段
                    segment_size = len(critical_ticker_15) // 3
                    early_prices = critical_ticker_15['price'].iloc[:segment_size]
                    middle_prices = critical_ticker_15['price'].iloc[segment_size:2*segment_size]
                    recent_prices = critical_ticker_15['price'].iloc[2*segment_size:]

                    early_avg = early_prices.mean()
                    middle_avg = middle_prices.mean()
                    recent_avg = recent_prices.mean()

                    # 如果价格呈现递减趋势
                    if early_avg > middle_avg > recent_avg:
                        signals.critical_15_30min_pressure = True

                # 分析成交量变化
                volume_trend = critical_ticker_15['size'].rolling(window=5).mean()
                if len(volume_trend) >= 10:
                    early_volume = volume_trend.iloc[:5].mean()
                    recent_volume = volume_trend.iloc[-5:].mean()

                    # 如果成交量明显萎缩
                    if early_volume > 0 and recent_volume < early_volume * 0.6:
                        signals.volume_price_divergence = True

            # 2. 30分钟长期压力分析
            if not critical_ticker_30.empty:
                # 分析订单流动能
                if len(critical_ticker_30) >= 20:
                    # 计算订单流强度（买入订单数量 vs 卖出订单数量）
                    buy_orders = critical_ticker_30[critical_ticker_30['ticker_direction'].apply(TickerDirection.is_buy)]
                    sell_orders = critical_ticker_30[critical_ticker_30['ticker_direction'].apply(TickerDirection.is_sell)]

                    # 将30分钟分为6个5分钟段，分析订单流变化
                    segment_size = len(critical_ticker_30) // 6
                    order_flow_strength = []

                    for i in range(6):
                        start_idx = i * segment_size
                        end_idx = (i + 1) * segment_size if i < 5 else len(critical_ticker_30)
                        segment_data = critical_ticker_30.iloc[start_idx:end_idx]

                        segment_buy = segment_data[segment_data['ticker_direction'].apply(TickerDirection.is_buy)]
                        segment_sell = segment_data[segment_data['ticker_direction'].apply(TickerDirection.is_sell)]

                        buy_strength = len(segment_buy) * segment_buy['size'].mean() if len(segment_buy) > 0 else 0
                        sell_strength = len(segment_sell) * segment_sell['size'].mean() if len(segment_sell) > 0 else 0

                        net_strength = buy_strength - sell_strength
                        order_flow_strength.append(net_strength)

                    # 如果订单流强度呈现明显下降趋势
                    if len(order_flow_strength) >= 4:
                        early_strength = np.mean(order_flow_strength[:2])
                        recent_strength = np.mean(order_flow_strength[-2:])

                        if early_strength > 0 and recent_strength < early_strength * 0.3:
                            signals.order_flow_momentum_loss = True
                            signals.critical_15_30min_pressure = True

        except Exception as e:
            logging.error(f"分析关键观察期压力时出错: {str(e)}", exc_info=True)

        return signals

    def _evaluate_combined_signal(self,
                                tech_indicators: Dict,
                                book_signals: OrderBookBehavior,
                                volume_analysis: VolumeAnalysis,
                                price_change_ratio: float) -> SignalStrength:
        """
        综合评估技术指标和盘口行为，判断最终信号强度 - 使用统计分位法优化
        """
        # 优化1: 预先缓存所有频繁使用的值到局部变量，避免重复属性访问
        volume_ratio = volume_analysis.volume_ratio
        """
        成交量评分
        """
        is_volume_surge = volume_analysis.is_volume_surge
        """
        主力行为评分
        """
        active_absorption = book_signals.active_absorption
        """
        主力吸筹
        """
        hidden_buying = book_signals.hidden_buying
        """
        隐藏买盘
        """
        fake_pressure = book_signals.fake_pressure
        """假卖真拉"""
        price_pushing = book_signals.price_pushing
        """
        推价行为
        """
        bid_support = book_signals.bid_support
        """
        买盘支撑
        """
        ask_pressure = book_signals.ask_pressure
        """
        卖盘压力
        """
        sudden_ask_cancel = book_signals.sudden_ask_cancel
        """
        卖单突然撤单
        """
        sudden_bid_cancel = book_signals.sudden_bid_cancel
        """
        买单突然撤单
        """
        passive_pushing = book_signals.passive_pushing
        """
        被动拉升
        """
        extreme_bid_aggression = book_signals.extreme_bid_aggression
        """
        极度买盘积极性
        """
        bid_aggression = book_signals.bid_aggression
        """
        买盘积极性
        """
        
        # 计算动态信号阈值
        # 基于历史信号分布调整阈值
        strong_buy_threshold = 14.0  # 默认强买入阈值
        buy_threshold = 11.0  # 默认买入阈值
        watch_threshold = 8.0  # 默认观望阈值
        weak_threshold = 4.0  # 默认弱势阈值
        
        # 如果有足够的历史数据，使用统计分位法调整阈值
        if len(self.signal_history) >= 5:
            # 收集历史评分数据
            historical_scores = []
            for signal in self.signal_history:
                # 计算每个历史信号的评分
                if hasattr(signal, 'volume_analysis') and signal.volume_analysis:
                    hist_volume_ratio = signal.volume_analysis.volume_ratio
                    hist_price_change_ratio = 0.0
                    
                    # 计算价格评分
                    hist_price_score = 0.0
                    if hasattr(signal, 'price_trend'):
                        if signal.price_trend == PRICE_TREND.UP:
                            hist_price_score = 3.0
                        elif signal.price_trend == PRICE_TREND.DOWN:
                            hist_price_score = -2.0
                    
                    # 计算成交量评分
                    hist_volume_score = 0.0
                    if hist_volume_ratio > 1.0:
                        hist_volume_score = min(8.0, (hist_volume_ratio - 1.0) * 4.0)
                    
                    # 计算行为评分
                    hist_behavior_score = 0.0
                    if hasattr(signal, 'book_behavior') and signal.book_behavior:
                        if signal.book_behavior.active_absorption:
                            hist_behavior_score += 2.0
                        if signal.book_behavior.hidden_buying:
                            hist_behavior_score += 1.5
                        if signal.book_behavior.fake_pressure:
                            hist_behavior_score += 2.0
                        if signal.book_behavior.bid_support:
                            hist_behavior_score += 1.0
                        if signal.book_behavior.extreme_bid_aggression:
                            hist_behavior_score += 1.5
                        if signal.book_behavior.ask_pressure:
                            hist_behavior_score -= 1.0
                        if signal.book_behavior.sudden_bid_cancel:
                            hist_behavior_score -= 1.5
                        if signal.book_behavior.passive_pushing:
                            hist_behavior_score -= 1.0
                    
                    # 计算总分
                    hist_total_score = hist_price_score + hist_volume_score + hist_behavior_score
                    historical_scores.append(hist_total_score)
            
            # 如果有足够的历史评分数据
            if len(historical_scores) >= 3:
                # 计算历史评分分布
                hist_scores = np.array(historical_scores)
                # 使用分位数调整阈值
                strong_buy_threshold = np.percentile(hist_scores, 90)  # 90%分位点作为强买入阈值
                buy_threshold = np.percentile(hist_scores, 75)  # 75%分位点作为买入阈值
                watch_threshold = np.percentile(hist_scores, 50)  # 50%分位点作为观望阈值
                weak_threshold = np.percentile(hist_scores, 25)  # 25%分位点作为弱势阈值
                
                # 限制阈值在合理范围内
                strong_buy_threshold = max(13.0, min(16.0, strong_buy_threshold))
                buy_threshold = max(10.0, min(13.0, buy_threshold))
                watch_threshold = max(7.0, min(10.0, watch_threshold))
                weak_threshold = max(3.0, min(6.0, weak_threshold))
        
        # 优化2: 快速路径返回 - 避免不必要的计算
        if price_change_ratio <= -0.02 and volume_ratio < 0.8:
            return SignalStrength.AVOID
            
        if volume_ratio > 10 and not (active_absorption or hidden_buying):
            return SignalStrength.AVOID
        
        # 优化3: 使用局部变量存储所有评分，避免多次读写
        price_score = 0.0
        """
        价格评分
        """
        volume_score = 0.0
        """
        成交量评分
        """
        behavior_score = 0.0
        """
        行为评分
        """
        
        # 优化4: 简化价格评分逻辑 - 减少条件判断分支
        if price_change_ratio > 0:
            # 使用范围检查和预先组合的逻辑简化条件分支
            has_main_force = active_absorption or hidden_buying
            
            if price_change_ratio <= 0.01:
                price_score = 2
            elif price_change_ratio <= 0.02:
                price_score = 3
            elif price_change_ratio <= 0.05:
                price_score = 4 + (1 if has_main_force else 0)
            else:  # > 0.05
                if has_main_force:
                    price_score = 6
                elif passive_pushing:
                    price_score = 3
                else:
                    price_score = 4
        else:
            # 直接计算负分
            price_score = max(-5.0, price_change_ratio * 250)  # 价格每下跌1%，减2.5分，最低-5分
        
        # 优化5: 成交量评分 - 使用简化的计算和缓存
        if volume_ratio >= 0.95:
            # 直接使用连续区间计算，减少条件分支
            if volume_ratio <= 1.5:
                volume_score = (volume_ratio - 0.95) * 5
            elif volume_ratio <= 2.5:
                volume_score = 2.75 + (volume_ratio - 1.5) * 2
            elif volume_ratio <= 5:
                volume_score = 4.75 + np.log2(volume_ratio / 2.5)
            else:
                # 超大成交量需要主力特征
                volume_score = 6 + np.log2(volume_ratio / 5) if (active_absorption or hidden_buying) else 3
        
        # 优化6: 成交量放大时的评分调整 - 直接使用缓存的变量和乘数
        if is_volume_surge:
            if active_absorption:
                volume_score *= 1.3
            elif hidden_buying:
                volume_score *= 1.2
            elif passive_pushing:
                volume_score *= 0.7
        
        # 限制最大值
        volume_score = min(8.0, volume_score)
        
        # 优化7: 行为评分 - 合并判断，减少重复计算
        # 主力吸筹特征评分
        if active_absorption:
            behavior_score += 4.0
            # 内联计算额外加分
            behavior_score += (1 if volume_ratio > 1.3 else 0) + (3 if fake_pressure else 0)

        # 隐藏买盘特征评分
        if hidden_buying:
            behavior_score += 3.0 + (1 if price_pushing else 0)

        # 推价行为特征评分
        if price_pushing:
            behavior_score += 2.0

        # 盘口支撑特征评分
        if bid_support:
            behavior_score += 2.0 + (1 if not ask_pressure else 0)

        # 卖单突然撤单特征评分
        if sudden_ask_cancel:
            if fake_pressure:
                behavior_score += 3.0 + (1 if price_change_ratio > 0.02 else 0)
            else:
                behavior_score += 1.5

        # 买盘积极性特征评分
        if extreme_bid_aggression:
            behavior_score += 1.5 + (1 if bid_support or active_absorption else 0)
        elif bid_aggression:
            behavior_score += 0.5

        # === 新增：上涨压力相关评分调整 ===
        # 检测到典型压力信号组合时大幅降低评分
        if book_signals.retail_takeover_top:
            behavior_score -= 6.0  # 散户接盘型顶部
        elif book_signals.pressure_intensifying_top:
            behavior_score -= 5.0  # 压力增强型顶部
        elif book_signals.bull_trap_top:
            behavior_score -= 7.0  # 诱多陷阱型顶部（最危险）
        elif book_signals.weak_limit_up_top:
            behavior_score -= 4.0  # 无法有效封板型顶部

        # 检测到关键时间窗口压力时降低评分
        if book_signals.critical_15_30min_pressure:
            behavior_score -= 2.0

        # 检测到订单流动能丧失时降低评分
        if book_signals.order_flow_momentum_loss:
            behavior_score -= 2.5

        # 检测到量价背离时降低评分
        if book_signals.volume_price_divergence:
            behavior_score -= 1.5

        # 检测到交易结构转变时降低评分
        if book_signals.trading_structure_shift:
            behavior_score -= 2.0

        # 负面特征评分 - 合并计算
        # 被动拉升特征评分
        if passive_pushing:
            behavior_score -= 3.0 + (1 if volume_ratio > 2 else 0)

        # 卖盘压力特征评分
        if ask_pressure and not fake_pressure:
            behavior_score -= 2.0 + (1 if volume_ratio < 1 else 0)

        # 买单突然撤单特征评分
        if sudden_bid_cancel:
            behavior_score -= 2.0 + (1 if price_change_ratio > 0 else 0)
        
        # 总分
        score = price_score + volume_score + behavior_score
        
        # 优化9: 使用动态阈值确定信号强度
        if score >= strong_buy_threshold:
            return SignalStrength.BUY if (volume_ratio > 5 and not (active_absorption or hidden_buying)) else SignalStrength.STRONG_BUY
            
        if score >= buy_threshold:
            return SignalStrength.WATCH if (passive_pushing and volume_ratio > 2) else SignalStrength.BUY
            
        if score >= watch_threshold:
            return SignalStrength.WATCH
            
        if score >= weak_threshold:
            return SignalStrength.WEAK
            
        return SignalStrength.AVOID

    def _calculate_confidence(self,
                            volume_analysis: VolumeAnalysis,
                            orderbook_analysis: OrderBookBehavior,
                            tech_indicators: Dict,
                            price_change_ratio: float) -> float:
        """计算信号的置信度 - 使用统计分位法优化"""
        try:
            # 计算动态置信度阈值
            # 默认阈值
            volume_ratio_high = 5.0
            volume_ratio_medium = 2.5
            volume_ratio_low = 1.5
            price_change_medium = 0.02
            price_change_high = 0.05
            
            # 如果有足够的历史数据，使用统计分位法调整阈值
            if len(self.signal_history) >= 5:
                # 收集历史成交量比率数据
                historical_volume_ratios = []
                historical_price_changes = []
                
                for signal in self.signal_history:
                    if hasattr(signal, 'volume_analysis') and signal.volume_analysis:
                        historical_volume_ratios.append(signal.volume_analysis.volume_ratio)
                    
                    # 尝试从历史信号中提取价格变化率
                    if hasattr(signal, 'price_trend'):
                        if signal.price_trend == PRICE_TREND.UP:
                            # 假设上涨约为1%
                            historical_price_changes.append(0.01)
                        elif signal.price_trend == PRICE_TREND.DOWN:
                            # 假设下跌约为-1%
                            historical_price_changes.append(-0.01)
                
                # 如果有足够的历史数据
                if len(historical_volume_ratios) >= 3:
                    # 使用分位数计算动态阈值
                    volume_ratios = np.array(historical_volume_ratios)
                    volume_ratio_high = np.percentile(volume_ratios, 90)  # 90%分位点作为高阈值
                    volume_ratio_medium = np.percentile(volume_ratios, 75)  # 75%分位点作为中阈值
                    volume_ratio_low = np.percentile(volume_ratios, 50)  # 50%分位点作为低阈值
                    
                    # 限制在合理范围内
                    volume_ratio_high = max(3.0, min(7.0, volume_ratio_high))
                    volume_ratio_medium = max(1.8, min(3.5, volume_ratio_medium))
                    volume_ratio_low = max(1.2, min(2.0, volume_ratio_low))
                
                # 如果有足够的价格变化数据
                if len(historical_price_changes) >= 3:
                    # 使用分位数计算动态阈值
                    price_changes = np.array([abs(pc) for pc in historical_price_changes])
                    price_change_medium = np.percentile(price_changes, 75)  # 75%分位点作为中阈值
                    price_change_high = np.percentile(price_changes, 90)  # 90%分位点作为高阈值
                    
                    # 限制在合理范围内
                    price_change_medium = max(0.01, min(0.03, price_change_medium))
                    price_change_high = max(0.03, min(0.07, price_change_high))

            # 1. 主力行为置信度 (权重: 0.4)
            behavior_confidence = 0.0
            if orderbook_analysis.active_absorption:
                behavior_confidence = 1.0
                if volume_analysis.volume_ratio > volume_ratio_low:
                    behavior_confidence = min(1.0, behavior_confidence * 1.1)
            elif orderbook_analysis.hidden_buying:
                behavior_confidence = 0.8
                if orderbook_analysis.price_pushing:
                    behavior_confidence = min(1.0, behavior_confidence * 1.1)
            elif orderbook_analysis.price_pushing:
                behavior_confidence = 0.7
            elif orderbook_analysis.bid_support:
                behavior_confidence = 0.6
                if not orderbook_analysis.ask_pressure:
                    behavior_confidence = min(1.0, behavior_confidence * 1.1)
            
            # 2. 成交量置信度 (权重: 0.2)
            volume_confidence = 0.0
            if volume_analysis.volume_ratio >= 0.95:
                if volume_analysis.volume_ratio > volume_ratio_high:
                    if orderbook_analysis.active_absorption or orderbook_analysis.hidden_buying:
                        volume_confidence = 1.0
                    else:
                        volume_confidence = 0.5  # 超大成交量但无主力特征降低置信度
                elif volume_analysis.volume_ratio > volume_ratio_medium:
                    volume_confidence = 0.9
                elif volume_analysis.volume_ratio > volume_ratio_low:
                    volume_confidence = 0.8
                else:
                    volume_confidence = 0.6
            
            # 3. MACD置信度部分已移除
            
            # 4. 价格趋势置信度 (权重: 0.2)
            price_confidence = 0.0
            if price_change_ratio > 0:
                if orderbook_analysis.active_absorption or orderbook_analysis.hidden_buying:
                    price_confidence = 1.0  # 有主力特征时最高置信
                elif price_change_ratio <= price_change_medium:
                    price_confidence = 0.8
                elif price_change_ratio <= price_change_high:
                    if orderbook_analysis.passive_pushing:
                        price_confidence = 0.6  # 被动拉升降低置信度
                    else:
                        price_confidence = 0.7
                else:
                    if orderbook_analysis.passive_pushing:
                        price_confidence = 0.4  # 大幅被动拉升最低置信
                    else:
                        price_confidence = 0.6
            
            # 计算加权平均 - 调整权重（MACD部分已移除）
            total_confidence = (
                behavior_confidence * 0.5 +
                volume_confidence * 0.25 +
                price_confidence * 0.25
            )
            
            # 5. 信号质量修正 - 使用动态阈值
            if orderbook_analysis.passive_pushing and volume_analysis.volume_ratio > volume_ratio_medium:
                total_confidence *= 0.8  # 放量被动拉升降低整体置信度
            if volume_analysis.volume_ratio > volume_ratio_high and not (orderbook_analysis.active_absorption or orderbook_analysis.hidden_buying):
                total_confidence *= 0.7  # 超大成交量无主力特征显著降低置信度
            
            return min(max(0.0, total_confidence), 1.0)
            
        except Exception as e:
            logging.error(f"计算置信度时出错: {str(e)}", exc_info=True)
            return 0.0

    def _analyze_signal_continuity(self, current_signal: TechnicalSignal) -> float:
        """
        分析信号的连续性，根据历史信号调整当前信号的置信度
        
        Args:
            current_signal: 当前的技术信号
            
        Returns:
            float: 连续性分析后的置信度调整系数
        """
        if not self.signal_history:
            return 1.0  # 没有历史信号，返回默认系数
            
        # 计算连续买入信号的数量
        buy_signals = [s for s in self.signal_history if s.signal_strength in 
                      {SignalStrength.STRONG_BUY, SignalStrength.BUY}]
        
        # 计算连续上涨趋势的数量
        up_trends = [s for s in self.signal_history if s.price_trend == PRICE_TREND.UP]
        
        # 计算连续主力行为的数量
        active_behaviors = [s for s in self.signal_history if 
                           (s.book_behavior and 
                            (s.book_behavior.active_absorption or 
                             s.book_behavior.hidden_buying or 
                             s.book_behavior.fake_pressure))]
        
        # 根据连续性调整置信度
        continuity_factor = 1.0
        
        # 1. 连续买入信号增强置信度
        if len(buy_signals) >= 2:
            continuity_factor *= 1.1
        
        # 2. 连续上涨趋势增强置信度
        if len(up_trends) >= 3:
            continuity_factor *= 1.15
        
        # 3. 连续主力行为大幅增强置信度
        if len(active_behaviors) >= 2:
            continuity_factor *= 1.2
            
        # 4. 如果当前信号是买入，但之前有多次AVOID信号，降低置信度
        avoid_signals = [s for s in self.signal_history if s.signal_strength == SignalStrength.AVOID]
        if current_signal.signal_strength in {SignalStrength.STRONG_BUY, SignalStrength.BUY} and len(avoid_signals) >= 3:
            continuity_factor *= 0.8
            
        return continuity_factor

    def analyze_window(self,
                      ticker_window: pd.DataFrame,
                      orderbook_window: pd.DataFrame,
                      history_data: pd.DataFrame) -> TechnicalSignal:
        """分析一个时间窗口的交易信号"""
        try:
            # 数据验证
            if ticker_window.empty or len(ticker_window) < 2:
                logging.warning("Ticker窗口数据不足")
                return TechnicalSignal(
                    signal_strength=SignalStrength.AVOID,
                    confidence=0.0,
                    price_trend=PRICE_TREND.NEUTRAL,
                    volume_trend=VOLUME_TREND.NEUTRAL,
                    buying_pressure=0.0,
                    volume_analysis=self._create_default_volume_analysis(),
                    tech_indicators=self._create_default_technical_indicators(),
                    book_behavior=OrderBookBehavior()
                )
            if orderbook_window.empty or len(orderbook_window) < 2:
                logging.warning("Orderbook窗口数据不足")
                return TechnicalSignal(
                    signal_strength=SignalStrength.AVOID,
                    confidence=0.0,
                    price_trend=PRICE_TREND.NEUTRAL,
                    volume_trend=VOLUME_TREND.NEUTRAL,
                    buying_pressure=0.0,
                    volume_analysis=self._create_default_volume_analysis(),
                    tech_indicators=self._create_default_technical_indicators(),
                    book_behavior=OrderBookBehavior()
                )
            if history_data.empty:
                logging.warning("历史数据不足")
                return TechnicalSignal(
                    signal_strength=SignalStrength.AVOID,
                    confidence=0.0,
                    price_trend=PRICE_TREND.NEUTRAL,
                    volume_trend=VOLUME_TREND.NEUTRAL,
                    buying_pressure=0.0,
                    volume_analysis=self._create_default_volume_analysis(),
                    tech_indicators=self._create_default_technical_indicators(),
                    book_behavior=OrderBookBehavior()
                )

            # 1. 分析成交量
            volume_analysis = self._calculate_volume_metrics(ticker_window, history_data)
            
            # 2. 分析买卖盘
            orderbook_analysis = self._analyze_orderbook(orderbook_window)
            
            # 3. 分析盘口行为
            book_behavior = self._analyze_orderbook_behavior(orderbook_window)
            
            # 4. 分析上涨压力
            upward_pressure = self._analyze_upward_pressure(ticker_window, orderbook_window)
            
            # 合并上涨压力分析结果到盘口行为中
            book_behavior.large_buy_orders_decrease = upward_pressure.large_buy_orders_decrease
            book_behavior.high_density_small_orders = upward_pressure.high_density_small_orders
            book_behavior.price_diff_narrowing = upward_pressure.price_diff_narrowing
            book_behavior.sell_orders_piling = upward_pressure.sell_orders_piling
            book_behavior.fake_sell_orders = upward_pressure.fake_sell_orders
            book_behavior.real_sell_pressure = upward_pressure.real_sell_pressure
            book_behavior.buy_side_thinning = upward_pressure.buy_side_thinning
            book_behavior.upward_pressure_detected = upward_pressure.upward_pressure_detected
            
            # 5. 计算技术指标
            tech_indicators = self._calculate_technical_indicators(ticker_window, history_data)

            # 6. 机器学习模式识别（如果可用）
            ml_pressure_probability = 0.5  # 默认概率
            ml_anomaly_detected = False

            if self.pattern_recognizer is not None:
                try:
                    # 提取特征
                    pattern_features = self.pattern_recognizer.extract_features(ticker_window, orderbook_window)

                    # 预测上涨压力概率
                    ml_pressure_probability = self.pattern_recognizer.predict_pressure_probability(pattern_features)

                    # 检测异常模式
                    ml_anomaly_detected = self.pattern_recognizer.detect_anomaly(pattern_features)

                    # 将机器学习结果集成到技术指标中
                    tech_indicators['ml_pressure_probability'] = ml_pressure_probability
                    tech_indicators['ml_anomaly_detected'] = ml_anomaly_detected

                    logging.debug(f"ML分析: 压力概率={ml_pressure_probability:.3f}, 异常检测={ml_anomaly_detected}")

                except Exception as e:
                    logging.error(f"机器学习分析时出错: {str(e)}")

            # 7. 计算价格变化率
            price_change_ratio = (ticker_window['price'].iloc[-1] - ticker_window['price'].iloc[0]) / ticker_window['price'].iloc[0]

            # 8. 评估综合信号（集成ML结果）
            signal_strength = self._evaluate_combined_signal(tech_indicators, book_behavior, volume_analysis, price_change_ratio)

            # 9. 计算置信度（集成ML结果）
            confidence = self._calculate_confidence(volume_analysis, book_behavior, tech_indicators, price_change_ratio)

            # 10. 机器学习置信度调整
            if self.pattern_recognizer is not None:
                # 如果ML检测到高压力概率，降低置信度
                if ml_pressure_probability > 0.7:
                    confidence *= 0.8
                    logging.debug(f"ML检测到高压力概率，置信度调整: {confidence:.3f}")

                # 如果检测到异常模式，进一步降低置信度
                if ml_anomaly_detected:
                    confidence *= 0.7
                    logging.debug(f"ML检测到异常模式，置信度调整: {confidence:.3f}")
            
            # 9. 确定价格趋势
            price_trend = PRICE_TREND.UP if price_change_ratio > 0 else (PRICE_TREND.DOWN if price_change_ratio < 0 else PRICE_TREND.NEUTRAL)
            
            # 10. 确定成交量趋势
            volume_trend = VOLUME_TREND.UP if volume_analysis.is_volume_surge else (VOLUME_TREND.DOWN if volume_analysis.volume_ratio < 0.8 else VOLUME_TREND.NEUTRAL)
            
            # 11. 计算买压指标（买入量/卖出量的比率，上限为10）
            buying_pressure = volume_analysis.volume_ratio if volume_analysis.volume_ratio <= 10 else 10
            
            # 创建信号对象
            signal = TechnicalSignal(
                signal_strength=signal_strength,
                confidence=confidence,
                price_trend=price_trend,
                volume_trend=volume_trend,
                buying_pressure=buying_pressure,
                volume_analysis=volume_analysis,
                tech_indicators=tech_indicators,
                book_behavior=book_behavior
            )
            
            # 12. 应用连续性分析调整置信度
            continuity_factor = self._analyze_signal_continuity(signal)
            signal.confidence = min(1.0, signal.confidence * continuity_factor)
            
            # 13. 更新信号历史
            self.signal_history.append(signal)
            if len(self.signal_history) > self.max_signal_history:
                self.signal_history.pop(0)
            
            return signal
            
        except Exception as e:
            logging.error(f"分析窗口时出错: {str(e)}", exc_info=True)
            return TechnicalSignal(
                signal_strength=SignalStrength.AVOID,
                confidence=0.0,
                price_trend=PRICE_TREND.NEUTRAL,
                volume_trend=VOLUME_TREND.NEUTRAL,
                buying_pressure=0.0,
                volume_analysis=self._create_default_volume_analysis(),
                tech_indicators=self._create_default_technical_indicators(),
                book_behavior=OrderBookBehavior()
            )
    
    def infer_ticker_direction(self, price: float, bid: float, ask: float) -> str:
        """
        精细推断成交方向，适用于高频/细价股。
        
        Args:
            price: 成交价格
            bid: 买一价
            ask: 卖一价
            
        Returns:
            str: 交易方向，可能的值为：
                - STRONG_BUY: 价格高于卖一价
                - WEAK_BUY: 价格接近但未超过卖一价
                - NEUTRAL: 价格在买卖盘中间
                - WEAK_SELL: 价格接近但未低于买一价
                - STRONG_SELL: 价格低于买一价
                - UNKNOWN: 无法判断方向
        """
        try:
            if pd.isna(bid) or pd.isna(ask):
                return TickerDirection.UNKNOWN.value
            spread = ask - bid
            if spread == 0:
                return TickerDirection.NEUTRAL.value
                
            # 动态阈值：基于价差与价格的比例来调整，而不是固定值
            # 计算价差占平均价格的比例
            avg_price = (ask + bid) / 2
            spread_ratio = spread / avg_price if avg_price > 0 else 0
            
            # 根据价差比例动态调整阈值
            if spread_ratio > 0.01:  # 价差大于平均价格的1%
                dynamic_threshold = 0.6
            elif spread_ratio > 0.005:  # 价差大于平均价格的0.5%
                dynamic_threshold = 0.4
            else:  # 价差较小
                dynamic_threshold = 0.2

            # 使用相对价差来判断买卖方向
            if price >= ask:
                # 根据超出卖一价的程度判断强度
                strength_ratio = (price - ask) / spread
                return TickerDirection.STRONG_BUY.value if strength_ratio > 0.5 else TickerDirection.WEAK_BUY.value
            elif price <= bid:
                # 根据低于买一价的程度判断强度
                strength_ratio = (bid - price) / spread
                return TickerDirection.STRONG_SELL.value if strength_ratio > 0.5 else TickerDirection.WEAK_SELL.value
            else:
                mid_price = (ask + bid) / 2
                relative_position = (price - mid_price) / spread
                if relative_position > dynamic_threshold:
                    return TickerDirection.WEAK_BUY.value
                elif relative_position < -dynamic_threshold:
                    return TickerDirection.WEAK_SELL.value
                else:
                    return TickerDirection.NEUTRAL.value
        except Exception:
            return TickerDirection.UNKNOWN.value

class MultiStockAnalyzer:
    """多股票并发分析器类"""
    
    def __init__(self, config: Optional[AnalyzerConfig] = None):
        """
        初始化多股票分析器
        
        Args:
            config: 分析器配置对象
        """
        self.config = config or AnalyzerConfig()
        self.analyzers = {}  # 每只股票一个专用分析器
        self.data_cache = {}  # 数据缓存
        self._lock = threading.Lock()
        self._result_queue = queue.Queue()
        
    def _load_and_cache_data(self, stock_code: str, ticker_file: str, orderbook_file: str) -> None:
        """加载并缓存股票数据"""
        with self._lock:
            if stock_code not in self.data_cache:
                # 读取CSV文件
                ticker_df = pd.read_csv(ticker_file)
                orderbook_df = pd.read_csv(orderbook_file)
                
                # 检查并转换时间戳
                if pd.api.types.is_integer_dtype(ticker_df['date_time']):
                    # 如果是整数时间戳，先解析为UTC再转为纽约时区，最后去除时区信息
                    ticker_df['date_time'] = pd.to_datetime(ticker_df['date_time'], unit='s', utc=True)
                    ticker_df['date_time'] = ticker_df['date_time'].dt.tz_convert('America/New_York').dt.tz_localize(None)
                else:
                    # 如果不是整数，假设是字符串格式，尝试解析
                    ticker_df['date_time'] = pd.to_datetime(ticker_df['date_time'])
                    
                if pd.api.types.is_integer_dtype(orderbook_df['date_time']):
                    # 如果是整数时间戳，先解析为UTC再转为纽约时区，最后去除时区信息
                    orderbook_df['date_time'] = pd.to_datetime(orderbook_df['date_time'], unit='s', utc=True)
                    orderbook_df['date_time'] = orderbook_df['date_time'].dt.tz_convert('America/New_York').dt.tz_localize(None)
                else:
                    # 如果不是整数，假设是字符串格式，尝试解析
                    orderbook_df['date_time'] = pd.to_datetime(orderbook_df['date_time'])
                
                self.data_cache[stock_code] = {
                    'ticker': ticker_df,
                    'orderbook': orderbook_df
                }
    
    def _analyze_single_stock(self, 
                            stock_code: str,
                            window_size: str = '10s') -> List[Dict]:
        """分析单只股票"""
        if stock_code not in self.analyzers:
            self.analyzers[stock_code] = StockAnalyzer(self.config)
            
        cached_data = self.data_cache.get(stock_code)
        if not cached_data:
            return [_create_default_result()]
            
        ticker_df = cached_data['ticker']
        orderbook_df = cached_data['orderbook']
        
        try:
            results = analyze_stock_data(
                ticker_df,
                orderbook_df,
                window_size
            )
            return results
        except Exception as e:
            logging.error(f"分析股票 {stock_code} 时出错: {str(e)}", exc_info=True)
            return [_create_default_result()]
    
    def analyze_stocks(self,
                      stock_data: List[Dict],
                      window_size: str = '10s') -> Dict[str, List[Dict]]:
        """
        并发分析多只股票
        
        Args:
            stock_data: 股票数据列表，每个字典包含：
                       - stock_code: 股票代码
                       - ticker_file: 逐笔成交数据文件路径
                       - orderbook_file: 买卖盘数据文件路径
            window_size: 分析窗口大小
            
        Returns:
            Dict[str, List[Dict]]: 每只股票的分析结果
        """
        # 预加载数据
        for stock in stock_data:
            self._load_and_cache_data(
                stock['stock_code'],
                stock['ticker_file'],
                stock['orderbook_file']
            )
        
        # 并发分析
        results = {}
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(stock_data), 10)) as executor:
            future_to_stock = {
                executor.submit(
                    self._analyze_single_stock,
                    stock['stock_code'],
                    window_size
                ): stock['stock_code']
                for stock in stock_data
            }
            
            for future in concurrent.futures.as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                try:
                    results[stock_code] = future.result()
                except Exception as e:
                    logging.error(f"获取股票 {stock_code} 的分析结果时出错: {str(e)}", exc_info=True)
                    results[stock_code] = [_create_default_result()]
                    
        return results

def _create_default_result() -> Dict:
    """创建默认的分析结果
    
    当分析失败或数据不足时，返回包含默认值的结果字典
    
    Returns:
        Dict: 包含默认值的分析结果字典
    """
    # 创建默认的信号对象
    default_signal = TechnicalSignal(
        signal_strength=SignalStrength.AVOID,
        confidence=0.0,
        price_trend=PRICE_TREND.NEUTRAL,
        volume_trend=VOLUME_TREND.NEUTRAL,
        buying_pressure=0.0,
        volume_analysis=None,
        tech_indicators=None,
        book_behavior=None
    )
    
    # 使用封装函数创建结果字典，包含所有参数
    result = create_analyzer_result_dict(
        signal=default_signal,
        current_price=0.0,
        price_high=0.0,
        price_low=0.0,
        price_change_ratio=0.0,
        total_score=0.0,
        price_score=0.0,
        volume_score_raw=0.0,
        volume_score_final=0.0,
        behavior_score=0.0,
        analysis_time=None,
        analysis_start_time=None,
        analysis_end_time=None,
        behavior_score_15min=0.0,
        strong_fake_pressure_15min=False,
        strong_active_absorption_15min=False,
        strong_bid_support_15min=False,
        strong_ask_pressure_15min=False,
        strong_bid_aggression_15min=False,
        strong_extreme_bid_aggression_15min=False,
        strong_hidden_buying_15min=False,
        fake_pressure_count_15min=0,
        fake_pressure_ratio_15min=0.0,
        active_absorption_count_15min=0,
        active_absorption_ratio_15min=0.0,
        bid_support_count_15min=0,
        bid_support_ratio_15min=0.0,
        ask_pressure_count_15min=0,
        ask_pressure_ratio_15min=0.0,
        bid_aggression_count_15min=0,
        bid_aggression_ratio_15min=0.0,
        extreme_bid_aggression_count_15min=0,
        extreme_bid_aggression_ratio_15min=0.0,
        hidden_buying_count_15min=0,
        hidden_buying_ratio_15min=0.0,
        total_orderbook_15min=0
    )
    
    # 添加上涨压力分析相关的默认值
    result.update({
        UPWARD_PRESSURE_DETECTED: False,
        LARGE_BUY_ORDERS_DECREASE: False,
        HIGH_DENSITY_SMALL_ORDERS: False,
        PRICE_DIFF_NARROWING: False,
        SELL_ORDERS_PILING: False,
        FAKE_SELL_ORDERS: False,
        REAL_SELL_PRESSURE: False,
        BUY_SIDE_THINNING: False
    })
    
    return result

def export_results(results: List[Dict], output_file: str) -> None:
    """
    导出分析结果到CSV文件
    
    Args:
        results: 分析结果列表
        output_file: 输出文件路径
    """
    df = pd.DataFrame(results)
    df.to_csv(output_file, index=False)
    logging.info(f"分析结果已导出到: {output_file}")

def analyze_stock_data(ticker_data: Union[str, pd.DataFrame],
                      orderbook_data: Union[str, pd.DataFrame],
                      window_size: str = '10s',
                      output_file: Optional[str] = None,
                      analysis_time: Optional[datetime] = None,
                      last_n_windows: int = 0,
                      lookback_minutes: int = 15) -> List[Dict]:
    """
    分析股票数据，生成交易信号
    
    Args:
        ticker_data: 逐笔成交数据，可以是CSV文件路径或DataFrame
        orderbook_data: 买卖盘数据，可以是CSV文件路径或DataFrame
        window_size: 时间窗口大小，默认'10s'，支持的格式：'Ns'（秒）或'Nmin'（分钟）
        output_file: 可选的输出文件路径，用于保存分析结果
        analysis_time: 可选的分析触发时间，用于精确确定分析窗口
        last_n_windows: 分析最后n个时间窗口，0表示分析所有窗口，默认为0
        lookback_minutes: 向前查找的分钟数，默认为15分钟
        
    Returns:
        List[Dict]: 分析结果列表，每个字典包含一个时间窗口的分析结果
    """
    try:
        # 获取初始内存使用情况
        # mem_before = process.memory_info().rss / 1024 / 1024  # 转换为MB
        # cpu_percent = process.cpu_percent(interval=1)
        
        # 1. 读取数据
        # logging.info(f'\n')
        # logging.info(f"正在读取数据...当前内存使用: {mem_before:.2f} MB, 当前CPU使用率: {cpu_percent:.2f}%")
        if isinstance(ticker_data, str):
            ticker_df = pd.read_csv(ticker_data)
        else:
            ticker_df = ticker_data.copy()
        
        if isinstance(orderbook_data, str):
            orderbook_df = pd.read_csv(orderbook_data)
        else:
            orderbook_df = orderbook_data.copy()
        
        # 2. 处理时间戳字段
        # 检查date_time是否为整数时间戳格式
        if ticker_df.empty or orderbook_df.empty:
            logging.info("警告：输入数据为空！")
            return [_create_default_result()]
            
        # 转换时间戳为datetime对象
        if 'date_time' in ticker_df.columns:
            if pd.api.types.is_integer_dtype(ticker_df['date_time']):
                # 如果是整数时间戳，先解析为UTC再转为纽约时区，最后去除时区信息
                ticker_df['date_time'] = pd.to_datetime(ticker_df['date_time'], unit='s', utc=True)
                ticker_df['date_time'] = ticker_df['date_time'].dt.tz_convert('America/New_York').dt.tz_localize(None)
            else:
                # 如果不是整数，假设是字符串格式，尝试解析
                ticker_df['date_time'] = pd.to_datetime(ticker_df['date_time'])
        
        if 'date_time' in orderbook_df.columns:
            if pd.api.types.is_integer_dtype(orderbook_df['date_time']):
                # 如果是整数时间戳，先解析为UTC再转为纽约时区，最后去除时区信息
                orderbook_df['date_time'] = pd.to_datetime(orderbook_df['date_time'], unit='s', utc=True)
                orderbook_df['date_time'] = orderbook_df['date_time'].dt.tz_convert('America/New_York').dt.tz_localize(None)
            else:
                # 如果不是整数，假设是字符串格式，尝试解析
                orderbook_df['date_time'] = pd.to_datetime(orderbook_df['date_time'])
        
        logging.info(f"原始数据行数 - Ticker: {len(ticker_df)}, Orderbook: {len(orderbook_df)}")
        
        if ticker_df.empty or orderbook_df.empty:
            logging.info("警告：输入数据为空！")
            return [_create_default_result()]
        
        # 3. 推断交易方向
        analyzer = StockAnalyzer()
        
        # 合并最近时间的bid/ask到ticker_df
        merged = pd.merge_asof(
            ticker_df.sort_values('date_time'),
            orderbook_df[['date_time', 'bid_price', 'ask_price']].sort_values('date_time'),
            on='date_time',
            direction='backward'
        )
        
        # 使用向量化操作替代apply
        def infer_directions(df):
            return df.apply(
                lambda row: analyzer.infer_ticker_direction(
                    row['price'],
                    row['bid_price'],
                    row['ask_price']
                ),
                axis=1
            )
        
        # 添加交易方向
        ticker_df['ticker_direction'] = infer_directions(merged)
        
        # 4. 确定分析时间范围
        # 获取数据中的最后一个时间点作为分析结束时间
        latest_time = ticker_df['date_time'].max()

        # 确定基准窗口（最后一个窗口）
        if analysis_time:
            logging.info(f"使用指定的分析时间: {analysis_time}")
            # 使用分析时间确定精确窗口
            current_window_start, current_window_end = get_analysis_window(analysis_time)
        else:
            logging.info(f"使用数据中的最后时间点: {latest_time}")
            # 使用最后时间点确定精确窗口
            current_window_start, current_window_end = get_analysis_window(latest_time)
            
        # 解析窗口大小
        # if window_size.endswith('s'):
        #     window_seconds = int(window_size[:-1])
        # else:
        #     window_minutes = int(window_size[:-3])
        #     window_seconds = window_minutes * 60
        
        # 基于当前窗口，计算多个独立的窗口
        window_starts = []
        window_ends = []
        
        # 如果是分析所有窗口的情况(last_n_windows=0)
        if last_n_windows == 0:
            # 确定数据时间范围内的所有窗口
            window_length = current_window_end - current_window_start
            
            # 确定数据的总时间范围
            data_min_time = ticker_df['date_time'].min()
            data_max_time = ticker_df['date_time'].max()
            
            # 从数据最早时间开始，按窗口大小创建所有窗口
            # 先对齐到窗口边界
            aligned_start = data_min_time.replace(second=(data_min_time.second // 10) * 10, microsecond=0)
            current_start = aligned_start
            
            while current_start + window_length <= data_max_time:
                window_starts.append(current_start)
                window_ends.append(current_start + window_length)
                current_start = current_start + window_length + timedelta(microseconds=1)
            
            logging.info(f"分析所有窗口: 共{len(window_starts)}个窗口，从 {window_starts[0]} 到 {window_ends[-1]}")
        else:
            # 先添加当前窗口
            window_starts.append(current_window_start)
            window_ends.append(current_window_end)
            
            # 根据last_n_windows参数，向前添加更多窗口
            if last_n_windows > 1:
                for i in range(1, last_n_windows):
                    # 向前移动一个完整窗口
                    prev_window_end = window_starts[-1] - timedelta(microseconds=1)
                    prev_window_start = prev_window_end - (current_window_end - current_window_start)
                    window_starts.append(prev_window_start)
                    window_ends.append(prev_window_end)
                
                # 为日志记录翻转列表，以便从早到晚显示
                window_times_log = [f"窗口{i+1}: {start.strftime('%Y-%m-%d %H:%M:%S')} - {end.strftime('%Y-%m-%d %H:%M:%S')}" 
                                 for i, (start, end) in enumerate(zip(reversed(window_starts), reversed(window_ends)))]
                logging.info(f"分析{last_n_windows}个窗口: \n" + "\n".join(window_times_log))
            else:
                logging.info(f"分析单个窗口: {current_window_start.strftime('%Y-%m-%d %H:%M:%S')} - {current_window_end.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 确定数据开始时间（向前额外的lookback_minutes，用于计算指标）
        all_window_start = min(window_starts) if window_starts else current_window_start
        data_start_time = all_window_start - pd.Timedelta(minutes=lookback_minutes)
        
        logging.info(f"数据起始时间: {data_start_time} (向前查找 {lookback_minutes} 分钟)")
        
        # 5. 过滤数据时间范围并同时排序，减少后续排序开销
        ticker_df = ticker_df[(ticker_df['date_time'] >= data_start_time)].sort_values('date_time')
        orderbook_df = orderbook_df[(orderbook_df['date_time'] >= data_start_time)].sort_values('date_time')
        
        logging.info(f"过滤后数据行数 - Ticker: {len(ticker_df)}, Orderbook: {len(orderbook_df)}")
        
        if len(ticker_df) < 2 or len(orderbook_df) < 2:
            logging.info("警告：数据点数量不足！")
            return [_create_default_result()]
        
        # 6. 分析每个窗口
        results = []
        
        # 反转列表以便从早到晚处理窗口
        for i, (window_start, window_end) in enumerate(zip(reversed(window_starts), reversed(window_ends))):
            window_idx = i + 1
            logging.info(f"分析窗口 {window_idx}/{len(window_starts)}: {window_start} - {window_end}")
            
            # 过滤当前窗口的数据
            ticker_mask = (ticker_df['date_time'] >= window_start) & (ticker_df['date_time'] <= window_end)
            orderbook_mask = (orderbook_df['date_time'] >= window_start) & (orderbook_df['date_time'] <= window_end)
            ticker_window = ticker_df.loc[ticker_mask]
            orderbook_window = orderbook_df.loc[orderbook_mask]
            
            # 检查窗口是否包含足够的数据
            if ticker_window.empty or len(ticker_window) < 2 or orderbook_window.empty or len(orderbook_window) < 2:
                logging.info(f"窗口 {window_idx} 数据不足，跳过分析。需要至少2个数据点才能计算价格变化等指标。")
                continue
                
            # 获取历史数据（用于计算历史指标）
            history_mask = ticker_df['date_time'] <= window_end
            history_data = ticker_df.loc[history_mask]
            if history_data.empty:
                continue
                
            # 获取15分钟窗口的数据
            fifteen_min_start_time = window_start - pd.Timedelta(minutes=15)
            fifteen_min_ticker_mask = (ticker_df['date_time'] >= fifteen_min_start_time) & (ticker_df['date_time'] <= window_end)
            fifteen_min_orderbook_mask = (orderbook_df['date_time'] >= fifteen_min_start_time) & (orderbook_df['date_time'] <= window_end)
            
            # 继续后面的15分钟窗口处理代码...
            fifteen_min_ticker_window = ticker_df.loc[fifteen_min_ticker_mask]
            fifteen_min_orderbook_window = orderbook_df.loc[fifteen_min_orderbook_mask]
            
            # 检查15分钟窗口是否包含足够的数据
            if fifteen_min_ticker_window.empty or len(fifteen_min_ticker_window) < 10 or fifteen_min_orderbook_window.empty or len(fifteen_min_orderbook_window) < 10:
                logging.info(f"15分钟窗口数据不足，跳过15分钟特征计算。")
                fifteen_min_behavior_score = 0.0
                fifteen_min_features = OrderBookBehavior()
                # 新增：统计特征全部为0/False
                fifteen_min_stats = {
                    'bid_support_count': 0,
                    'bid_support_ratio': 0.0,
                    'ask_pressure_count': 0,
                    'ask_pressure_ratio': 0.0,
                    'bid_aggression_count': 0,
                    'bid_aggression_ratio': 0.0,
                    'extreme_bid_aggression_count': 0,
                    'extreme_bid_aggression_ratio': 0.0,
                    'active_absorption_count': 0,
                    'active_absorption_ratio': 0.0,
                    'hidden_buying_count': 0,
                    'hidden_buying_ratio': 0.0,
                    'fake_pressure_count': 0,
                    'fake_pressure_ratio': 0.0,
                    'has_fake_pressure': False,
                    'total': 0
                }
            else:
                df = fifteen_min_orderbook_window.reset_index(drop=True)
                total = len(df)
                # 买盘支撑
                bid_support = df['bid_size'] > 3 * df['ask_size']
                bid_support_count = bid_support.sum()
                bid_support_ratio = bid_support.mean()
                # 卖盘压力
                ask_pressure = df['ask_size'] > 3 * df['bid_size']
                ask_pressure_count = ask_pressure.sum()
                ask_pressure_ratio = ask_pressure.mean()
                # 买盘积极性
                bid_aggression = (df['bid_price'] / df['ask_price']) > 0.95
                bid_aggression_count = bid_aggression.sum()
                bid_aggression_ratio = bid_aggression.mean()
                # 极度买盘积极性
                extreme_bid_aggression = (df['bid_price'] / df['ask_price']) > 0.98
                extreme_bid_aggression_count = extreme_bid_aggression.sum()
                extreme_bid_aggression_ratio = extreme_bid_aggression.mean()
                # 主动吸筹（近似：买盘支撑且卖单撤单）
                # 这里用买盘支撑和卖单撤单的向量化近似
                ask_size_change = df['ask_size'].diff().fillna(0)
                sudden_ask_cancel = ask_size_change < -df['ask_size'].shift(1).fillna(0) * 0.5
                active_absorption = bid_support & (sudden_ask_cancel | (df['ask_price'] > df['ask_price'].shift(1).fillna(0)))
                active_absorption_count = active_absorption.sum()
                active_absorption_ratio = active_absorption.mean()
                # 假卖真拉（近似：卖单撤单且价格上涨）
                fake_pressure = sudden_ask_cancel & (df['ask_price'] > df['ask_price'].shift(1).fillna(0))
                fake_pressure_count = fake_pressure.sum()
                fake_pressure_ratio = fake_pressure.mean()
                # 隐藏买盘（近似：价格上涨且买卖价接近）
                hidden_buying = (df['bid_price'] > df['bid_price'].shift(1).fillna(0)) & (df['ask_price'] > df['ask_price'].shift(1).fillna(0)) & (df['bid_price'] / df['ask_price'] > 0.95)
                hidden_buying_count = hidden_buying.sum()
                hidden_buying_ratio = hidden_buying.mean()
                has_fake_pressure = fake_pressure.any()


                # 15分钟行为评分可用统计特征加权
                # 新评分方式：次数达到阈值才满分，否则按比例递减

                # 15分钟主力特征评分已封装，原有各项评分与注释全部移除，仅保留如下调用：
                fifteen_min_behavior_score = calculate_fifteen_min_behavior_score(
                    bid_support_ratio, bid_support_count,
                    ask_pressure_ratio, ask_pressure_count,
                    bid_aggression_ratio, bid_aggression_count,
                    extreme_bid_aggression_ratio, extreme_bid_aggression_count,
                    active_absorption_ratio, active_absorption_count,
                    hidden_buying_ratio, hidden_buying_count,
                    fake_pressure_ratio, fake_pressure_count
                )

                fifteen_min_stats = {
                    'bid_support_count': int(bid_support_count),
                    'bid_support_ratio': float(bid_support_ratio),
                    'ask_pressure_count': int(ask_pressure_count),
                    'ask_pressure_ratio': float(ask_pressure_ratio),
                    'bid_aggression_count': int(bid_aggression_count),
                    'bid_aggression_ratio': float(bid_aggression_ratio),
                    'extreme_bid_aggression_count': int(extreme_bid_aggression_count),
                    'extreme_bid_aggression_ratio': float(extreme_bid_aggression_ratio),
                    'active_absorption_count': int(active_absorption_count),
                    'active_absorption_ratio': float(active_absorption_ratio),
                    'hidden_buying_count': int(hidden_buying_count),
                    'hidden_buying_ratio': float(hidden_buying_ratio),
                    'fake_pressure_count': int(fake_pressure_count),
                    'fake_pressure_ratio': float(fake_pressure_ratio),
                    'has_fake_pressure': bool(has_fake_pressure),
                    'total': total
                }
            
            # 使用StockAnalyzer分析窗口数据
            signal = analyzer.analyze_window(ticker_window, orderbook_window, history_data)
            
            # 获取价格变化和其他指标
            if ticker_window.empty:
                current_price = 0.0
                price_high = 0.0
                price_low = 0.0
                price_change_ratio = 0.0
            else:
                current_price = ticker_window['price'].iloc[-1]
                price_high = ticker_window['price'].max()
                price_low = ticker_window['price'].min()
                if len(ticker_window) > 1:
                    start_price = ticker_window['price'].iloc[0]
                    end_price = ticker_window['price'].iloc[-1]
                    price_change_ratio = (end_price - start_price) / start_price if start_price > 0 else 0
                else:
                    price_change_ratio = 0.0
            
            # 计算各项评分
            price_score = 0.0
            volume_score_raw = 0.0
            volume_score_final = 0.0
            behavior_score = 0.0
            total_score = 0.0
            
            # 价格评分：基于价格变化和买入方向的占比
            if price_change_ratio > 0:
                # 计算买入方向的成交数量占比
                if not ticker_window.empty:
                    buy_direction_mask = ticker_window['ticker_direction'].apply(TickerDirection.is_buy)
                    buy_direction_ratio = buy_direction_mask.mean() if len(ticker_window) > 0 else 0
                    
                    # 价格上涨且主要由买入驱动
                    if buy_direction_ratio > 0.6:
                        price_score = min(6.0, price_change_ratio * 300)  # 价格每上涨1%，加3分，最高6分
                    # 价格上涨但买入占比不高
                    else:
                        price_score = min(3.0, price_change_ratio * 200)  # 价格每上涨1%，加2分，最高3分
                else:
                    price_score = 0.0
            else:
                # 价格下跌
                price_score = max(-5.0, price_change_ratio * 250)  # 价格每下跌1%，减2.5分，最低-5分
            
            # 成交量评分：基于买卖量比率和主力特征
            if hasattr(signal, 'volume_analysis') and signal.volume_analysis is not None:
                volume_ratio = signal.volume_analysis.volume_ratio
                if volume_ratio > 1.0:
                    # 买入量大于卖出量
                    volume_score_raw = min(8.0, (volume_ratio - 1.0) * 4.0)  # 买卖比每高出1.0，加4分
                    
                    # 如果同时有主力特征，额外加分
                    if hasattr(signal, 'book_behavior') and signal.book_behavior is not None:
                        if signal.book_behavior.active_absorption:
                            volume_score_raw += 2.0
                        if signal.book_behavior.hidden_buying:
                            volume_score_raw += 1.5
                else:
                    # 卖出量大于买入量
                    volume_score_raw = max(-4.0, (volume_ratio - 1.0) * 2.0)  # 买卖比每低于1.0，减2分
                
                # 应用上限
                volume_score_final = min(8.0, volume_score_raw)
            
            # 行为评分：基于主力行为特征
            if hasattr(signal, 'book_behavior') and signal.book_behavior is not None:
                behavior_features = signal.book_behavior
                
                # 正面特征加分
                if behavior_features.active_absorption:
                    behavior_score += 2.0
                if behavior_features.hidden_buying:
                    behavior_score += 1.5
                if behavior_features.fake_pressure:
                    behavior_score += 2.0
                if behavior_features.bid_support:
                    behavior_score += 1.0
                if behavior_features.extreme_bid_aggression:
                    behavior_score += 1.5
                
                # 负面特征减分
                if behavior_features.ask_pressure:
                    behavior_score -= 1.0
                if behavior_features.sudden_bid_cancel:
                    behavior_score -= 1.5
                if behavior_features.passive_pushing:
                    behavior_score -= 1.0
            
            # 计算总分
            total_score = price_score + volume_score_final + behavior_score
            
            # 批量强信号联合判定（比例+次数）
            # 15分钟假卖真拉（比例+次数）
            strong_fake_pressure_15min = (
                fifteen_min_stats['fake_pressure_ratio'] > FAKE_PRESSURE_RATIO_TH_15MIN and
                fifteen_min_stats['fake_pressure_count'] > FAKE_PRESSURE_COUNT_TH_15MIN
            )
            # 15分钟主动吸筹（比例+次数）
            strong_active_absorption_15min = (
                fifteen_min_stats['active_absorption_ratio'] > ACTIVE_ABSORPTION_RATIO_TH_15MIN and
                fifteen_min_stats['active_absorption_count'] > ACTIVE_ABSORPTION_COUNT_TH_15MIN
            )
            # 15分钟买盘支撑（比例+次数）
            strong_bid_support_15min = (
                fifteen_min_stats['bid_support_ratio'] > BID_SUPPORT_RATIO_TH_15MIN and
                fifteen_min_stats['bid_support_count'] > BID_SUPPORT_COUNT_TH_15MIN
            )
            # 15分钟卖盘压力（比例+次数）
            strong_ask_pressure_15min = (
                fifteen_min_stats['ask_pressure_ratio'] > ASK_PRESSURE_RATIO_TH_15MIN and
                fifteen_min_stats['ask_pressure_count'] > ASK_PRESSURE_COUNT_TH_15MIN
            )
            # 15分钟极度买盘积极性（比例+次数）
            strong_extreme_bid_aggression_15min = (
                fifteen_min_stats['extreme_bid_aggression_ratio'] > EXTREME_BID_AGGRESSION_RATIO_TH_15MIN and
                fifteen_min_stats['extreme_bid_aggression_count'] > EXTREME_BID_AGGRESSION_COUNT_TH_15MIN
            )
            # 15分钟买盘积极性（比例+次数）
            strong_bid_aggression_15min = (
                fifteen_min_stats['bid_aggression_ratio'] > BID_AGGRESSION_RATIO_TH_15MIN and
                fifteen_min_stats['bid_aggression_count'] > BID_AGGRESSION_COUNT_TH_15MIN
            )
            # 15分钟隐藏买盘（比例+次数）
            strong_hidden_buying_15min = (
                fifteen_min_stats['hidden_buying_ratio'] > HIDDEN_BUYING_RATIO_TH_15MIN and
                fifteen_min_stats['hidden_buying_count'] > HIDDEN_BUYING_COUNT_TH_15MIN
            )

            # 创建结果字典
            result = create_analyzer_result_dict(
                signal=signal,
                current_price=current_price,
                price_high=price_high,
                price_low=price_low,
                price_change_ratio=price_change_ratio,
                total_score=total_score,
                price_score=price_score,
                volume_score_raw=volume_score_raw,
                volume_score_final=volume_score_final,
                behavior_score=behavior_score,
                analysis_time=window_end,
                analysis_start_time=window_start,
                analysis_end_time=window_end,
                behavior_score_15min=fifteen_min_behavior_score,
                strong_fake_pressure_15min=strong_fake_pressure_15min,
                strong_active_absorption_15min=strong_active_absorption_15min,
                strong_bid_support_15min=strong_bid_support_15min,
                strong_ask_pressure_15min=strong_ask_pressure_15min,
                strong_bid_aggression_15min=strong_bid_aggression_15min,
                strong_extreme_bid_aggression_15min=strong_extreme_bid_aggression_15min,
                strong_hidden_buying_15min=strong_hidden_buying_15min,
                fake_pressure_count_15min=fifteen_min_stats.get('fake_pressure_count', 0),
                fake_pressure_ratio_15min=fifteen_min_stats.get('fake_pressure_ratio', 0.0),
                active_absorption_count_15min=fifteen_min_stats.get('active_absorption_count', 0),
                active_absorption_ratio_15min=fifteen_min_stats.get('active_absorption_ratio', 0.0),
                bid_support_count_15min=fifteen_min_stats.get('bid_support_count', 0),
                bid_support_ratio_15min=fifteen_min_stats.get('bid_support_ratio', 0.0),
                ask_pressure_count_15min=fifteen_min_stats.get('ask_pressure_count', 0),
                ask_pressure_ratio_15min=fifteen_min_stats.get('ask_pressure_ratio', 0.0),
                bid_aggression_count_15min=fifteen_min_stats.get('bid_aggression_count', 0),
                bid_aggression_ratio_15min=fifteen_min_stats.get('bid_aggression_ratio', 0.0),
                extreme_bid_aggression_count_15min=fifteen_min_stats.get('extreme_bid_aggression_count', 0),
                extreme_bid_aggression_ratio_15min=fifteen_min_stats.get('extreme_bid_aggression_ratio', 0.0),
                hidden_buying_count_15min=fifteen_min_stats.get('hidden_buying_count', 0),
                hidden_buying_ratio_15min=fifteen_min_stats.get('hidden_buying_ratio', 0.0),
                total_orderbook_15min=fifteen_min_stats.get('total', 0)
            )
            
            # 添加结果
            results.append(result)
            
        # 如果没有找到任何信号，返回一个默认的AVOID信号
        if not results:
            default_result = _create_default_result()
            default_result['behavior_score_15min'] = 0.0
            return [default_result]
            
        # 7. 如果指定了输出文件，保存结果
        if output_file:
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False)
            logging.info(f"分析结果已导出到: {output_file}")
        
        # 记录内存使用变化
        # mem_after = process.memory_info().rss / 1024 / 1024  # 转换为MB
        # mem_diff = mem_after - mem_before
        # cpu_percent = process.cpu_percent(interval=0)
        # logging.info(f"分析完成，内存使用: {mem_after:.2f} MB (变化: {mem_diff:+.2f} MB), 当前CPU使用率: {cpu_percent:.2f}%")
            
        return results
        
    except Exception as e:
        logging.error(f"分析过程中出现错误: {str(e)}", exc_info=True)
        default_result = _create_default_result()
        default_result['behavior_score_15min'] = 0.0
        return [default_result]

def get_analysis_window(analysis_time: datetime) -> Tuple[datetime, datetime]:
    """
    根据分析触发时间，获取正确的分析窗口时间范围
    
    根据用户需求，当在:
    - 02秒分析时，应使用前一分钟的50-59秒窗口
    - 12秒分析时，应使用当前分钟的00-09秒窗口
    - 22秒分析时，应使用当前分钟的10-19秒窗口
    以此类推
    
    Args:
        analysis_time: 分析触发的时间
        
    Returns:
        Tuple[datetime, datetime]: 窗口开始时间和结束时间
    """
    current_second = analysis_time.second
    
    # 根据当前秒数确定分析窗口
    if 0 <= current_second < 10:  # 02秒触发
        # 窗口为前一分钟的50-59秒
        window_start = analysis_time.replace(second=0, microsecond=0) - timedelta(seconds=10)
        window_end = analysis_time.replace(second=0, microsecond=0) - timedelta(microseconds=1)
    elif 10 <= current_second < 20:  # 12秒触发
        # 窗口为当前分钟的00-09秒
        window_start = analysis_time.replace(second=0, microsecond=0)
        window_end = analysis_time.replace(second=9, microsecond=999999)
    elif 20 <= current_second < 30:  # 22秒触发
        # 窗口为当前分钟的10-19秒
        window_start = analysis_time.replace(second=10, microsecond=0)
        window_end = analysis_time.replace(second=19, microsecond=999999)
    elif 30 <= current_second < 40:  # 32秒触发
        # 窗口为当前分钟的20-29秒
        window_start = analysis_time.replace(second=20, microsecond=0)
        window_end = analysis_time.replace(second=29, microsecond=999999)
    elif 40 <= current_second < 50:  # 42秒触发
        # 窗口为当前分钟的30-39秒
        window_start = analysis_time.replace(second=30, microsecond=0)
        window_end = analysis_time.replace(second=39, microsecond=999999)
    else:  # 52秒触发
        # 窗口为当前分钟的40-49秒
        window_start = analysis_time.replace(second=40, microsecond=0)
        window_end = analysis_time.replace(second=49, microsecond=999999)
    
    return window_start, window_end

# === 新增：特征检测工具函数 ===
def detect_continuous_small_sell(trades: pd.DataFrame, size_threshold: int = None, min_count: int = 3) -> bool:
    """
    检测是否存在连续小单卖出（WEAK_SELL/STRONG_SELL）
    
    Args:
        trades: 交易数据
        size_threshold: 小单阈值，若为None则使用数据的30%分位点
        min_count: 连续出现的最小次数
        
    Returns:
        bool: 是否检测到连续小单卖出
    """
    # 使用统计分位法确定小单阈值
    if size_threshold is None:
        size_threshold = trades['size'].quantile(0.30)
    
    count = 0
    for d, s in zip(trades['ticker_direction'], trades['size']):
        if d in ('WEAK_SELL', 'STRONG_SELL') and s <= size_threshold:
            count += 1
            if count >= min_count:
                return True
        else:
            count = 0
    return False

def detect_sparse_strong_buy(trades: pd.DataFrame, price_threshold: float = None, size_threshold: int = None) -> bool:
    """
    检测低价大单强买入
    
    Args:
        trades: 交易数据
        price_threshold: 价格阈值，若为None则不限制价格
        size_threshold: 大单阈值，若为None则使用数据的70%分位点
        
    Returns:
        bool: 是否检测到低价大单强买入
    """
    # 使用统计分位法确定大单阈值
    if size_threshold is None:
        size_threshold = trades['size'].quantile(0.70)
    
    strong_buys = trades[(trades['ticker_direction'] == 'STRONG_BUY') & (trades['size'] >= size_threshold)]
    if strong_buys.empty:
        return False
    if price_threshold is not None:
        return (strong_buys['price'] <= price_threshold).any()
    return True

def check_avg_price_above_start(trades: pd.DataFrame, start_close: float) -> bool:
    """
    检查均价下降但未破启动K线收盘价
    
    Args:
        trades: 交易数据
        start_close: 启动K线收盘价
        
    Returns:
        bool: 是否满足均价下降但未破启动K线收盘价的条件
    """
    # 计算均价
    avg_price = trades['price'].mean()
    # 计算第一个价格与均价的关系
    first_price = trades['price'].iloc[0]
    # 使用相对判断：均价低于第一个价格的2%但高于启动K线收盘价的2%
    price_range_lower = first_price * 0.98
    start_close_upper = start_close * 1.02
    
    return avg_price < price_range_lower and avg_price > start_close_upper

def detect_symmetric_structure(trades: pd.DataFrame, small_threshold: int = None, big_threshold: int = None) -> bool:
    """
    检测等量对称结构：小单下跌后大单买入
    
    Args:
        trades: 交易数据
        small_threshold: 小单阈值，若为None则使用数据的30%分位点
        big_threshold: 大单阈值，若为None则使用数据的70%分位点
        
    Returns:
        bool: 是否检测到等量对称结构
    """
    # 使用统计分位法确定小单和大单阈值
    if small_threshold is None:
        small_threshold = trades['size'].quantile(0.30)
    if big_threshold is None:
        big_threshold = trades['size'].quantile(0.70)
    
    for i in range(1, len(trades)):
        if trades['ticker_direction'].iloc[i-1] in ('WEAK_SELL', 'STRONG_SELL') and trades['size'].iloc[i-1] <= small_threshold:
            if trades['ticker_direction'].iloc[i] == 'STRONG_BUY' and trades['size'].iloc[i] >= big_threshold:
                return True
    return False

def main():
    # 示例：分析多只股票
    stock_data = [
        {
            'stock_code': 'TMDE',
            'ticker_file': '/Users/<USER>/git/test_resources/2025-06-18/RELI_2025-06-18_07_54_32/RELI_ticker_data.csv',
            'orderbook_file': '/Users/<USER>/git/test_resources/2025-06-18/RELI_2025-06-18_07_54_32/RELI_orderbook_data.csv'
        },
        # 添加更多股票...
    ]
    
    try:
        # 创建多股票分析器
        multi_analyzer = MultiStockAnalyzer()
        
        # 分析数据
        print("=== 开始多股票智能交易信号分析 ===")
        results = multi_analyzer.analyze_stocks(
            stock_data,
            # window_size='10s'
        )
        
        # 输出结果
        for stock_code, stock_results in results.items():
            # for r in stock_results:
            buy_signals = [r for r in stock_results 
                         if r['signal_strength'] in ('STRONG_BUY', 'BUY')]
            
            if not buy_signals:
                print(f"\n=== {stock_code} 没有买入信号 ===")
                continue
            print(f"\n=== {stock_code} 买入信号分析结果 ===")
            for r in reversed(stock_results):
            # for r in reversed(buy_signals):
                print(f"\n时间: {r['time']}")
                print(f"当前价格: {r['current_price']:.4f}")
                print(f"区间最高: {r['price_high']:.4f}")
                print(f"区间最低: {r['price_low']:.4f}")
                print(f"信号强度: {r['signal_strength']}")
                print(f"置信度: {r['confidence']:.2f}")
                print(f"价格趋势: {r['price_trend']}")
                print(f"成交量趋势: {r['volume_trend']}")
                print(f"买压指标: {r['buying_pressure']:.2f}  # 买入量/卖出量的比率，>1表示买方力量更强")
                print(f"\n--- 详细指标评分 ---")
                print(f"总评分: {r['total_score']:.2f}  # 所有评分的总和，决定信号强度")
                print(f"价格评分: {r['price_score']:.2f}  # 评估价格变化的幅度和质量，范围-5至+6")
                print(f"成交量评分: {r['volume_score']:.2f} (原始: {r['volume_score_raw']:.2f})  # 评估成交量比率和质量，原始分数可能超过8分，最终评分上限为8分")
                print(f"行为评分: {r['behavior_score']:.2f}  # 评估盘口行为和主力特征 ≥ 5：极高分 ≥ 3：高分，主力行为强烈 < 0：主力行为弱或有负面特征")
                print(f"行为评分15分钟: {r['behavior_score_15min']:.2f}  # 评估15分钟窗口内的盘口行为和主力特征")
                print(f"\n--- 详细指标数据 ---")
                print(f"成交量比率: {r['volume_ratio']:.2f}  # 买入量/卖出量，与买压指标相同但无上限")
                print(f"价格变化率: {r['price_change_ratio']:.4f}  # 窗口内的价格变化百分比")
                print(f"买盘支撑: {r['bid_support']}  # 买盘量是否远大于卖盘量（通常是3倍以上）")
                print(f'买盘支撑 15min: {r["strong_bid_support_15min"]} {r["bid_support_ratio_15min"]*100:.2f}%/{format_volume(r["bid_support_count_15min"])} # 15分钟窗口内买盘支撑强信号（比例>{BID_SUPPORT_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(BID_SUPPORT_COUNT_TH_15MIN)}\n')
                print(f"卖盘压力: {r['ask_pressure']}  # 卖盘量是否远大于买盘量（通常是3倍以上）")
                print(f'卖盘压力 15min: {r["strong_ask_pressure_15min"]} {r["ask_pressure_ratio_15min"]*100:.2f}%/{format_volume(r["ask_pressure_count_15min"])} # 15分钟窗口内卖盘压力强信号（比例>{ASK_PRESSURE_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(ASK_PRESSURE_COUNT_TH_15MIN)}\n')
                print(f"买盘积极性: {r['bid_aggression']}  # 买价是否接近卖价（买价/卖价>0.95）")
                print(f'买盘积极性 15min: {r["strong_bid_aggression_15min"]} {r["bid_aggression_ratio_15min"]*100:.2f}%/{format_volume(r["bid_aggression_count_15min"])} # 15分钟窗口内买盘积极性强信号（比例>{BID_AGGRESSION_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(BID_AGGRESSION_COUNT_TH_15MIN)}\n')
                print(f"极度买盘积极性: {r['extreme_bid_aggression']}  # 买价是否非常接近卖价（买价/卖价>0.98）")
                print(f'极度买盘积极性 15min: {r["strong_extreme_bid_aggression_15min"]} {r["extreme_bid_aggression_ratio_15min"]*100:.2f}%/{format_volume(r["extreme_bid_aggression_count_15min"])} # 15分钟窗口内极度买盘积极性强信号（比例>{EXTREME_BID_AGGRESSION_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(EXTREME_BID_AGGRESSION_COUNT_TH_15MIN)}\n')
                print(f"主动吸筹: {r['active_absorption']}  # 主力资金是否在积极买入 1.有持续的买盘支撑 2.卖盘在减少（撤单）或抬高卖价")
                print(f'主动吸筹 15min: {r["strong_active_absorption_15min"]} {r["active_absorption_ratio_15min"]*100:.2f}%/{format_volume(r["active_absorption_count_15min"])} # 15分钟窗口内主动吸筹强信号（比例>{ACTIVE_ABSORPTION_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(ACTIVE_ABSORPTION_COUNT_TH_15MIN)}\n')
                print(f"隐藏买盘: {r['hidden_buying']}  # 是否存在隐蔽的买盘行为 1.价格在上涨，说明有买盘推动 2.买卖价差很小，说明买卖双方都很积极")
                print(f'隐藏买盘 15min: {r["strong_hidden_buying_15min"]} {r["hidden_buying_ratio_15min"]*100:.2f}%/{format_volume(r["hidden_buying_count_15min"])} # 15分钟窗口内隐藏买盘强信号（比例>{HIDDEN_BUYING_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(HIDDEN_BUYING_COUNT_TH_15MIN)}\n')
                print(f"假卖真拉: {r['fake_pressure']}  # 是否存在制造卖盘压力假象的行为")
                print(f'假卖真拉 15min: {r["strong_fake_pressure_15min"]} {r["fake_pressure_ratio_15min"]*100:.2f}%/{format_volume(r["fake_pressure_count_15min"])} # 15分钟窗口内假卖真拉强信号（比例>{FAKE_PRESSURE_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(FAKE_PRESSURE_COUNT_TH_15MIN)}\n')
                
                # 输出上涨压力分析结果
                print(f"\n--- 上涨压力分析 ---")
                print(f"检测到上涨压力: {r.get(UPWARD_PRESSURE_DETECTED, False)}  # 综合判断是否存在上涨压力（权重达到{UPWARD_PRESSURE_SCORE_THRESHOLD}即可判定为存在上涨压力）")
                print(f"卖盘堆积: {r.get(SELL_ORDERS_PILING, False)}  # 卖盘量明显增加，形成上方阻力 权重{PRESSURE_WEIGHTS[SELL_ORDERS_PILING]}（最重要指标）")
                print(f"真实卖压: {r.get(REAL_SELL_PRESSURE, False)}  # 卖单持续存在且成交量明显萎缩 权重{PRESSURE_WEIGHTS[REAL_SELL_PRESSURE]}（非常重要指标）")
                print(f"大单买入减少: {r.get(LARGE_BUY_ORDERS_DECREASE, False)}  # 拉升后期大单买入频率和规模明显下降 权重{PRESSURE_WEIGHTS[LARGE_BUY_ORDERS_DECREASE]}（重要指标）")
                print(f"买盘变薄: {r.get(BUY_SIDE_THINNING, False)}  # 买盘厚度明显减弱，支撑力减弱 权重{PRESSURE_WEIGHTS[BUY_SIDE_THINNING]}（较重要指标）")
                print(f"高位小单密集: {r.get(HIGH_DENSITY_SMALL_ORDERS, False)}  # 高位出现密集小单成交（散户追高特征） 权重{PRESSURE_WEIGHTS[HIGH_DENSITY_SMALL_ORDERS]}（较重要指标）")
                print(f"逐笔价差变小: {r.get(PRICE_DIFF_NARROWING, False)}  # 逐笔成交价格变动微小，价格推动力减弱 权重{PRESSURE_WEIGHTS[PRICE_DIFF_NARROWING]}（一般指标）")
                print(f"假压单行为: {r.get(FAKE_SELL_ORDERS, False)}  # 大量卖单短时间内出现又迅速撤销 权重{PRESSURE_WEIGHTS[FAKE_SELL_ORDERS]}（辅助指标）")
                print(f"封板挂单弱: {r.get(WEAK_LIMIT_UP_ORDERS, False)}  # 封板挂单弱 权重{PRESSURE_WEIGHTS[WEAK_LIMIT_UP_ORDERS]}（辅助指标）")
    except Exception as e:
        print(f"\n分析过程中出现错误: {str(e)}")
        raise
    
if __name__ == "__main__":
    main() 

