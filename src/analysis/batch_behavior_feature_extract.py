import os
import re
import pandas as pd
import numpy as np
from typing import List, Dict, Tu<PERSON>
from datetime import datetime, timedelta
from src.analysis.stock_analyzer import analyze_stock_data
from src.utils.constants import *

def scan_data_files(root_dir: str) -> List[Dict[str, str]]:
    """
    扫描指定目录下的所有交易数据文件，并将它们配对。
    每对文件代表一只股票在某一天的数据，文件末尾时间点为急速拉升开始时间。
    
    Args:
        root_dir: 要扫描的根目录路径
        
    Returns:
        包含股票代码、日期和对应文件路径的字典列表
    """
    file_pairs = []
    
    # 遍历根目录
    for item in os.listdir(root_dir):
        full_path = os.path.join(root_dir, item)
        
        # 处理子文件夹（格式如：STOCK_DATE_TIME）
        if os.path.isdir(full_path):
            parts = item.split('_')
            if len(parts) >= 3:
                stock_code = parts[0]
                date_str = parts[1]
                time_str = parts[2]
                ticker_file = os.path.join(full_path, f"{stock_code}_ticker_data.csv")
                orderbook_file = os.path.join(full_path, f"{stock_code}_orderbook_data.csv")
                
                if os.path.exists(ticker_file) and os.path.exists(orderbook_file):
                    file_pairs.append({
                        'stock_code': stock_code,
                        'date': date_str,
                        'time': time_str,
                        'ticker_file': ticker_file,
                        'orderbook_file': orderbook_file
                    })
        
        # 处理根目录下的文件（格式如：DATE_TIME_STOCK_TYPE.csv）
        elif item.endswith('.csv'):
            match = re.match(r'(\d+)_(\d+)_([a-zA-Z]+)_(trades|bidask)\.csv', item)
            if match:
                date, time, stock_code, file_type = match.groups()
                base_name = f"{date}_{time}_{stock_code}"
                
                # 查找对应的文件对
                trades_file = os.path.join(root_dir, f"{base_name}_trades.csv")
                bidask_file = os.path.join(root_dir, f"{base_name}_bidask.csv")
                
                # 如果两个文件都存在且还未添加到列表中
                if (os.path.exists(trades_file) and os.path.exists(bidask_file) and 
                    not any(p['stock_code'] == stock_code and 
                           p['date'] == date and
                           p['time'] == time for p in file_pairs)):
                    file_pairs.append({
                        'stock_code': stock_code,
                        'date': date,
                        'time': time,
                        'ticker_file': trades_file,
                        'orderbook_file': bidask_file
                    })
    
    return file_pairs

def calculate_window_metrics(window_data: pd.DataFrame, ticker_window: pd.DataFrame = None) -> Dict[str, Tuple[np.ndarray, np.ndarray]]:
    """
    计算单个窗口的所有指标。
    
    Args:
        window_data: 盘口数据DataFrame
        ticker_window: 成交数据DataFrame，可选
        
    Returns:
        Dict: 包含各个指标的布尔数组和统计值
    """
    metrics = {}
    
    # 原有的盘口指标计算
    bid_support = window_data['bid_size'] > 3 * window_data['ask_size']
    metrics['bid_support'] = (bid_support, (bid_support.sum(), bid_support.mean()))
    
    ask_pressure = window_data['ask_size'] > 3 * window_data['bid_size']
    metrics['ask_pressure'] = (ask_pressure, (ask_pressure.sum(), ask_pressure.mean()))
    
    bid_aggression = (window_data['bid_price'] / window_data['ask_price']) > 0.95
    metrics['bid_aggression'] = (bid_aggression, (bid_aggression.sum(), bid_aggression.mean()))
    
    extreme_bid_aggression = (window_data['bid_price'] / window_data['ask_price']) > 0.98
    metrics['extreme_bid_aggression'] = (extreme_bid_aggression, (extreme_bid_aggression.sum(), extreme_bid_aggression.mean()))
    
    ask_size_change = window_data['ask_size'].diff().fillna(0)
    sudden_ask_cancel = ask_size_change < -window_data['ask_size'].shift(1).fillna(0) * 0.5
    active_absorption = bid_support & (sudden_ask_cancel | (window_data['ask_price'] > window_data['ask_price'].shift(1).fillna(0)))
    metrics['active_absorption'] = (active_absorption, (active_absorption.sum(), active_absorption.mean()))
    
    fake_pressure = sudden_ask_cancel & (window_data['ask_price'] > window_data['ask_price'].shift(1).fillna(0))
    metrics['fake_pressure'] = (fake_pressure, (fake_pressure.sum(), fake_pressure.mean()))
    
    hidden_buying = (window_data['bid_price'] > window_data['bid_price'].shift(1).fillna(0)) & \
                   (window_data['ask_price'] > window_data['ask_price'].shift(1).fillna(0)) & \
                   (window_data['bid_price'] / window_data['ask_price'] > 0.95)
    metrics['hidden_buying'] = (hidden_buying, (hidden_buying.sum(), hidden_buying.mean()))
    
    # 新增成交数据特征
    if ticker_window is not None and not ticker_window.empty:
        # 买方主动成交比例（通过价格判断，高于上一笔成交价认为是主动买入）
        price_changes = ticker_window['price'].diff()
        buy_trades = price_changes > 0
        metrics['buy_trades'] = (buy_trades, (buy_trades.sum(), buy_trades.mean()))
        
        # 大单买入比例（假设单笔成交量超过1000为大单）
        large_trades = ticker_window['size'] > 1000
        large_buy_trades = large_trades & buy_trades
        metrics['large_trades'] = (large_buy_trades, (large_buy_trades.sum(), large_buy_trades.mean()))
        
        # 买方主动成交量占比
        buy_volume = ticker_window[buy_trades]['size'].sum()
        total_volume = ticker_window['size'].sum()
        buy_volume_ratio = buy_volume / total_volume if total_volume > 0 else 0
        metrics['buy_volume'] = (np.array([buy_volume_ratio]), (buy_volume_ratio, buy_volume_ratio))
        
        # 成交量特征
        mean_volume = ticker_window['size'].mean()
        abnormal_volume = ticker_window['size'] > (mean_volume * 3)  # 超过均值3倍
        metrics['abnormal_volume'] = (abnormal_volume, (abnormal_volume.sum(), abnormal_volume.mean()))
        
        # 价格特征
        up_ticks = price_changes > 0
        down_ticks = price_changes < 0
        up_down_ratio = up_ticks.sum() / down_ticks.sum() if down_ticks.sum() > 0 else float('inf')
        metrics['up_down'] = (np.array([up_down_ratio]), (up_down_ratio, up_down_ratio))
        
        # 价格动量（最后价格相对于窗口开始价格的变化率）
        start_price = ticker_window['price'].iloc[0]
        end_price = ticker_window['price'].iloc[-1]
        price_momentum = (end_price - start_price) / start_price if start_price > 0 else 0
        metrics['price_momentum'] = (np.array([price_momentum]), (price_momentum, price_momentum))
        
        # 成交强度（每秒成交次数）
        time_span = (ticker_window['date_time'].max() - ticker_window['date_time'].min()).total_seconds()
        tick_intensity = len(ticker_window) / time_span if time_span > 0 else 0
        metrics['tick_intensity'] = (np.array([tick_intensity]), (tick_intensity, tick_intensity))
        
        # 成交量加速度
        volume_series = ticker_window.groupby(ticker_window['date_time'].dt.second)['size'].sum()
        volume_changes = volume_series.diff()
        volume_acceleration = volume_changes.mean()
        metrics['volume_acceleration'] = (np.array([volume_acceleration]), (volume_acceleration, volume_acceleration))
        
        # 成交量集中度
        volume_concentration = ticker_window['size'].max() / mean_volume if mean_volume > 0 else 0
        metrics['volume_concentration'] = (np.array([volume_concentration]), (volume_concentration, volume_concentration))
        
        # 价格波动率
        price_std = ticker_window['price'].std()
        price_mean = ticker_window['price'].mean()
        price_volatility = price_std / price_mean if price_mean > 0 else 0
        metrics['price_volatility'] = (np.array([price_volatility]), (price_volatility, price_volatility))
        
        # 买方压力指数（结合买方主动性和成交量）
        buy_pressure_index = buy_volume_ratio * volume_concentration
        metrics['buy_pressure'] = (np.array([buy_pressure_index]), (buy_pressure_index, buy_pressure_index))
        
        # 成交量和价格的相关性
        volume_price_correlation = ticker_window['size'].corr(ticker_window['price'])
        metrics['volume_price_corr'] = (np.array([volume_price_correlation]), (volume_price_correlation, volume_price_correlation))
        
        # 特殊条件和交易所特征
        special_trades = ticker_window['special_conditions'].notna()
        metrics['special_trades'] = (special_trades, (special_trades.sum(), special_trades.mean()))
        
        # 主要交易所的成交比例
        major_exchanges = ['FINRA', 'ISLAND', 'ARCA', 'BATS']
        for exchange in major_exchanges:
            exchange_trades = ticker_window['exchange'] == exchange
            metrics[f'{exchange.lower()}_trades'] = (exchange_trades, (exchange_trades.sum(), exchange_trades.mean()))
    
    return metrics

def analyze_pre_surge_data(file_pair: Dict[str, str]) -> Dict:
    """
    分析急速拉升前的数据特征。
    
    Args:
        file_pair: 包含股票代码和文件路径的字典
        
    Returns:
        Dict: 包含各个指标的统计信息
    """
    print(f"\n分析 {file_pair['stock_code']} 的数据...")
    
    # 读取数据
    ticker_df = pd.read_csv(file_pair['ticker_file'], parse_dates=['date_time'])
    orderbook_df = pd.read_csv(file_pair['orderbook_file'], parse_dates=['date_time'])
    
    print(f"原始数据大小 - Ticker: {len(ticker_df)}, Orderbook: {len(orderbook_df)}")
    
    # 检查原始数据的时间跨度
    ticker_time_span = ticker_df['date_time'].max() - ticker_df['date_time'].min()
    orderbook_time_span = orderbook_df['date_time'].max() - orderbook_df['date_time'].min()
    print(f"原始数据时间跨度:")
    print(f"  - Ticker: {ticker_time_span} (从 {ticker_df['date_time'].min()} 到 {ticker_df['date_time'].max()})")
    print(f"  - Orderbook: {orderbook_time_span} (从 {orderbook_df['date_time'].min()} 到 {orderbook_df['date_time'].max()})")
    
    # 确保数据按时间排序
    ticker_df = ticker_df.sort_values('date_time')
    orderbook_df = orderbook_df.sort_values('date_time')
    
    # 获取急速拉升时间点（数据的最后时间点）
    surge_time = orderbook_df['date_time'].max()
    print(f"拉升时间点: {surge_time}")
    
    # 存储两个时间窗口的指标值
    windows = {
        '10s': {
            'total_records': [],
            # 盘口特征
            'bid_support': [], 'bid_support_count': [],
            'ask_pressure': [], 'ask_pressure_count': [],
            'bid_aggression': [], 'bid_aggression_count': [],
            'extreme_bid_aggression': [], 'extreme_bid_aggression_count': [],
            'active_absorption': [], 'active_absorption_count': [],
            'hidden_buying': [], 'hidden_buying_count': [],
            'fake_pressure': [], 'fake_pressure_count': [],
            # 成交方向特征
            'buy_trades': [], 'buy_trades_count': [],
            'large_trades': [], 'large_trades_count': [],
            'buy_volume': [], 'buy_volume_count': [],
            # 成交量特征
            'abnormal_volume': [], 'abnormal_volume_count': [],
            'volume_acceleration': [], 'volume_acceleration_count': [],
            'volume_concentration': [], 'volume_concentration_count': [],
            # 价格特征
            'up_down': [], 'up_down_count': [],
            'price_momentum': [], 'price_momentum_count': [],
            'price_volatility': [], 'price_volatility_count': [],
            # 复合特征
            'tick_intensity': [], 'tick_intensity_count': [],
            'buy_pressure': [], 'buy_pressure_count': [],
            'volume_price_corr': [], 'volume_price_corr_count': [],
            # 特殊条件和交易所特征
            'special_trades': [], 'special_trades_count': [],
            'finra_trades': [], 'finra_trades_count': [],
            'island_trades': [], 'island_trades_count': [],
            'arca_trades': [], 'arca_trades_count': [],
            'bats_trades': [], 'bats_trades_count': []
        },
        '15min': {
            'total_records': [],
            # 盘口特征
            'bid_support': [], 'bid_support_count': [],
            'ask_pressure': [], 'ask_pressure_count': [],
            'bid_aggression': [], 'bid_aggression_count': [],
            'extreme_bid_aggression': [], 'extreme_bid_aggression_count': [],
            'active_absorption': [], 'active_absorption_count': [],
            'hidden_buying': [], 'hidden_buying_count': [],
            'fake_pressure': [], 'fake_pressure_count': [],
            # 成交方向特征
            'buy_trades': [], 'buy_trades_count': [],
            'large_trades': [], 'large_trades_count': [],
            'buy_volume': [], 'buy_volume_count': [],
            # 成交量特征
            'abnormal_volume': [], 'abnormal_volume_count': [],
            'volume_acceleration': [], 'volume_acceleration_count': [],
            'volume_concentration': [], 'volume_concentration_count': [],
            # 价格特征
            'up_down': [], 'up_down_count': [],
            'price_momentum': [], 'price_momentum_count': [],
            'price_volatility': [], 'price_volatility_count': [],
            # 复合特征
            'tick_intensity': [], 'tick_intensity_count': [],
            'buy_pressure': [], 'buy_pressure_count': [],
            'volume_price_corr': [], 'volume_price_corr_count': [],
            # 特殊条件和交易所特征
            'special_trades': [], 'special_trades_count': [],
            'finra_trades': [], 'finra_trades_count': [],
            'island_trades': [], 'island_trades_count': [],
            'arca_trades': [], 'arca_trades_count': [],
            'bats_trades': [], 'bats_trades_count': []
        }
    }
    
    # 获取分析时间段的数据
    analysis_mask = (orderbook_df['date_time'] >= surge_time - pd.Timedelta(minutes=15)) & (orderbook_df['date_time'] < surge_time)
    analysis_data = orderbook_df[analysis_mask].reset_index(drop=True)
    
    ticker_analysis_mask = (ticker_df['date_time'] >= surge_time - pd.Timedelta(minutes=15)) & (ticker_df['date_time'] < surge_time)
    ticker_analysis_data = ticker_df[ticker_analysis_mask].reset_index(drop=True)
    
    print(f"分析时间段内的数据点数 - Orderbook: {len(analysis_data)}, Ticker: {len(ticker_analysis_data)}")
    
    if len(analysis_data) < 10 or len(ticker_analysis_data) < 10:
        print(f"警告: {file_pair['stock_code']} 在急速拉升前15分钟内数据点不足")
        return None
    
    # 分析10秒窗口（只分析最后1-2分钟的每个10秒窗口）
    print("\n分析10秒窗口（最后1-2分钟）...")
    last_2min_start = surge_time - pd.Timedelta(minutes=2)
    current_time = surge_time
    window_count = 0
    
    while current_time >= last_2min_start:
        window_end = current_time
        window_start = window_end - pd.Timedelta(seconds=10)
        
        window_mask = (analysis_data['date_time'] >= window_start) & (analysis_data['date_time'] <= window_end)
        window_data = analysis_data[window_mask].reset_index(drop=True)
        
        ticker_window_mask = (ticker_analysis_data['date_time'] >= window_start) & (ticker_analysis_data['date_time'] <= window_end)
        ticker_window = ticker_analysis_data[ticker_window_mask].reset_index(drop=True)
        
        if len(window_data) >= 2 and len(ticker_window) >= 2:
            window_count += 1
            total = len(window_data)
            windows['10s']['total_records'].append(total)
            
            metrics = calculate_window_metrics(window_data, ticker_window)
            for name, (_, (count, ratio)) in metrics.items():
                windows['10s'][name].append(ratio)
                windows['10s'][f'{name}_count'].append(count)
        
        current_time = window_start - pd.Timedelta(microseconds=1)
    
    print(f"找到 {window_count} 个有效的10秒窗口")
    
    # 分析15分钟窗口（分析整个15分钟的数据）
    print("\n分析15分钟窗口（整个15分钟）...")
    window_end = surge_time
    window_start = window_end - pd.Timedelta(minutes=15)
    
    window_mask = (orderbook_df['date_time'] >= window_start) & (orderbook_df['date_time'] <= window_end)
    window_data = orderbook_df[window_mask].reset_index(drop=True)
    
    ticker_window_mask = (ticker_df['date_time'] >= window_start) & (ticker_df['date_time'] <= window_end)
    ticker_window = ticker_df[ticker_window_mask].reset_index(drop=True)
    
    if len(window_data) >= 10 and len(ticker_window) >= 10:
        total = len(window_data)
        windows['15min']['total_records'].append(total)
        
        metrics = calculate_window_metrics(window_data, ticker_window)
        for name, (_, (count, ratio)) in metrics.items():
            windows['15min'][name].append(ratio)
            windows['15min'][f'{name}_count'].append(count)
        
        print(f"15分钟窗口数据点数: {total}")
    else:
        print("警告: 15分钟窗口数据点不足")
    
    # 计算统计信息
    stats = {'10s': {}, '15min': {}}
    percentiles = [25, 50, 75, 90, 95, 99]
    
    # 所有特征列表
    all_metrics = [
        # 盘口特征
        'bid_support', 'ask_pressure', 'bid_aggression', 'extreme_bid_aggression',
        'active_absorption', 'hidden_buying', 'fake_pressure',
        # 成交方向特征
        'buy_trades', 'large_trades', 'buy_volume',
        # 成交量特征
        'abnormal_volume', 'volume_acceleration', 'volume_concentration',
        # 价格特征
        'up_down', 'price_momentum', 'price_volatility',
        # 复合特征
        'tick_intensity', 'buy_pressure', 'volume_price_corr',
        # 特殊条件和交易所特征
        'special_trades',
        'finra_trades', 'island_trades', 'arca_trades', 'bats_trades'
    ]
    
    for window_size in ['10s', '15min']:
        print(f"\n处理 {window_size} 窗口的统计信息...")
        for metric in all_metrics:
            values = windows[window_size][metric]
            count_values = windows[window_size][f'{metric}_count']
            
            if values:  # 确保有数据
                values_array = np.array(values)
                count_array = np.array(count_values)
                stats[window_size][metric] = {
                    'mean': np.mean(values_array),
                    'std': np.std(values_array),
                    'min': np.min(values_array),
                    'max': np.max(values_array),
                    'percentiles': {
                        f'p{p}': np.percentile(values_array, p)
                        for p in percentiles
                    }
                }
                stats[window_size][f'{metric}_count'] = {
                    'mean': np.mean(count_array),
                    'std': np.std(count_array),
                    'min': np.min(count_array),
                    'max': np.max(count_array),
                    'percentiles': {
                        f'p{p}': np.percentile(count_array, p)
                        for p in percentiles
                    }
                }
                print(f"  - {metric}: 找到 {len(values)} 个数据点")
            else:
                print(f"  - {metric}: 没有找到数据")
    
    # 添加股票信息
    stats['stock_code'] = file_pair['stock_code']
    stats['date'] = file_pair['date']
    stats['time'] = file_pair['time']
    stats['surge_time'] = surge_time.strftime('%Y-%m-%d %H:%M:%S')
    stats['window_count_10s'] = len(windows['10s']['total_records'])
    stats['window_count_15min'] = len(windows['15min']['total_records'])
    
    return stats

# 使用函数扫描指定目录
file_pairs = scan_data_files('/Users/<USER>/git/test_resources/test')

# 分析所有股票数据的阈值
all_stats = []
for file_pair in file_pairs:
    print(f"\n分析 {file_pair['stock_code']} 在 {file_pair['date']} {file_pair['time']} 的拉升前数据特征...")
    stats = analyze_pre_surge_data(file_pair)  # 删除 minutes_before_surge 参数
    if stats:
        all_stats.append(stats)

# 打印汇总信息
for window_size in ['10s', '15min']:
    print(f"\n=== 拉升前{window_size}窗口的特征分布 ===")
    
    # 所有特征列表
    all_metrics = [
        # 盘口特征
        'bid_support', 'ask_pressure', 'bid_aggression', 'extreme_bid_aggression',
        'active_absorption', 'hidden_buying', 'fake_pressure',
        # 成交方向特征
        'buy_trades', 'large_trades', 'buy_volume',
        # 成交量特征
        'abnormal_volume', 'volume_acceleration', 'volume_concentration',
        # 价格特征
        'up_down', 'price_momentum', 'price_volatility',
        # 复合特征
        'tick_intensity', 'buy_pressure', 'volume_price_corr',
        # 特殊条件和交易所特征
        'special_trades',
        'finra_trades', 'island_trades', 'arca_trades', 'bats_trades'
    ]
    
    # 按特征类型分组
    feature_groups = {
        '盘口特征': [
            'bid_support', 'ask_pressure', 'bid_aggression', 'extreme_bid_aggression',
            'active_absorption', 'hidden_buying', 'fake_pressure'
        ],
        '成交方向特征': [
            'buy_trades', 'large_trades', 'buy_volume'
        ],
        '成交量特征': [
            'abnormal_volume', 'volume_acceleration', 'volume_concentration'
        ],
        '价格特征': [
            'up_down', 'price_momentum', 'price_volatility'
        ],
        '复合特征': [
            'tick_intensity', 'buy_pressure', 'volume_price_corr'
        ],
        '特殊条件和交易所特征': [
            'special_trades',
            'finra_trades', 'island_trades', 'arca_trades', 'bats_trades'
        ]
    }
    
    for group_name, metrics in feature_groups.items():
        print(f"\n{group_name}:")
        for metric in metrics:
            try:
                # 收集所有股票的该指标值
                ratios = []
                counts = []
                for s in all_stats:
                    if window_size in s and metric in s[window_size]:
                        ratio_stats = s[window_size][metric]
                        count_stats = s[window_size][f'{metric}_count']
                        if isinstance(ratio_stats, dict) and 'percentiles' in ratio_stats:
                            ratios.append(ratio_stats['percentiles']['p95'])
                            counts.append(count_stats['percentiles']['p95'])
                
                if ratios and counts:
                    print(f"\n{metric}:")
                    print(f"  比率分布: {np.mean(ratios):.3f} ± {np.std(ratios):.3f}")
                    print(f"  次数分布: {int(np.mean(counts))} ± {int(np.std(counts))}")
                    print(f"  建议阈值: 比率 > {np.mean(ratios):.3f}, 次数 > {int(np.mean(counts))}")
                else:
                    print(f"\n{metric}: 没有足够的数据计算分布")
            except Exception as e:
                print(f"\n{metric}: 处理数据时出错 - {str(e)}")

# 保存详细的分析数据
try:
    detailed_results = []
    for s in all_stats:
        # 将字符串时间转换回datetime对象
        surge_time = pd.to_datetime(s['surge_time'])
        stock_result = {
            'stock_code': s['stock_code'],
            'date': s['date'],
            'time': s['time'],
            'surge_time': s['surge_time'],
            'analysis_start_time': (surge_time - pd.Timedelta(minutes=15)).strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 添加所有窗口的指标数据
        for window_size in ['10s', '15min']:
            if window_size in s:
                for metric in all_metrics:
                    if metric in s[window_size] and f'{metric}_count' in s[window_size]:
                        ratio_stats = s[window_size][metric]
                        count_stats = s[window_size][f'{metric}_count']
                        if isinstance(ratio_stats, dict) and 'percentiles' in ratio_stats:
                            stock_result[f'{window_size}_{metric}_p95'] = ratio_stats['percentiles']['p95']
                            stock_result[f'{window_size}_{metric}_mean'] = ratio_stats['mean']
                            stock_result[f'{window_size}_{metric}_count_p95'] = count_stats['percentiles']['p95']
                            stock_result[f'{window_size}_{metric}_count_mean'] = count_stats['mean']
        
        detailed_results.append(stock_result)
    
    # 保存到CSV
    output_path = '/Users/<USER>/git/test_resources/test/pre_surge_analysis.csv'
    pd.DataFrame(detailed_results).to_csv(output_path, index=False)
    print(f"\n详细分析结果已保存到: {output_path}")
except Exception as e:
    print(f"\n保存详细结果时出错: {str(e)}") 