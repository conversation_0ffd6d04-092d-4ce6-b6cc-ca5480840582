def detect_pattern(df):
    """
    检测满足下述条件的形态：
    1) 第 i 根 K 线：涨幅 > 20%，且 volume > 10倍前10根均量
    2) 中间的 1~3 根 K 线：涨跌幅明显缩小（示例中用<5%）
    3) 最后一根 K 线：阳线、成交量大于前一根、且收盘价 > 第一根收盘价

    返回：[(start_idx, end_idx, middle_count), ...]
         其中 start_idx = i, end_idx = i + 1 + middle_count + 1
    """

    # 为了计算均量，需要至少 10 根数据在前面
    # 同时，为了能取到最后一根，需要再留 4 根空间(最少1根中间 + 1根最后)
    # 这里写成 len(df) - 5 是为了保险，具体可根据形态最大跨度调整
    pattern_indices = []
    for i in range(10, len(df) - 5):
        # --- 1) 检查第一根 K 线条件 ---
        open_i = df['Open'].iloc[i]
        close_i = df['Close'].iloc[i]
        volume_i = df['Volume'].iloc[i]

        # (1.1) 涨幅 > 20%
        if open_i <= 0:
            continue  # 避免除零，或者无效数据
        increase_ratio = (close_i - open_i) / open_i
        cond_increase_20 = (increase_ratio > 0.2)

        # (1.2) 成交量 > 10倍前10根均量
        prev_10_mean_vol = df['volume'].iloc[i-10:i].mean()
        cond_vol_10x = (volume_i > 10 * prev_10_mean_vol)

        if not (cond_increase_20 and cond_vol_10x):
            continue

        # --- 2) 中间 1~3 根 K 线涨跌幅明显缩小 ---
        # 这里我们用 "单根K线的振幅 < 5%" 作为示例
        # middle_bars_count 可以是 1,2,3，分别代表中间的K线数量
        for middle_bars_count in [1, 2, 3]:
            # 最后一根K线的索引
            final_idx = i + 1 + middle_bars_count
            if final_idx >= len(df):
                break  # 超出范围

            # 先检查中间 K 线
            middle_indices = range(i+1, i+1+middle_bars_count)
            all_middle_small = True
            for j in middle_indices:
                open_j = df['Open'].iloc[j]
                close_j = df['Close'].iloc[j]
                if open_j <= 0:
                    all_middle_small = False
                    break
                amplitude_j = abs(close_j - open_j) / open_j
                # 这里用 5% 做一个示例
                if amplitude_j > 0.2:
                    all_middle_small = False
                    break

            if not all_middle_small:
                continue

            # --- 3) 最后一根 K 线判断 ---
            # final_idx = i + 1 + middle_bars_count
            if final_idx >= len(df):
                break

            open_final = df['Open'].iloc[final_idx]
            close_final = df['Close'].iloc[final_idx]
            volume_final = df['Volume'].iloc[final_idx]

            # 3.1 阳线
            cond_final_up = (close_final > open_final)

            # 3.2 成交量 > 它前一根
            volume_prev = df['Volume'].iloc[final_idx - 1]
            cond_final_vol = (volume_final > volume_prev)

            # 3.3 收盘价 > 第一根收盘价
            cond_final_close = (close_final > close_i)

            if cond_final_up and cond_final_vol and cond_final_close:
                # 符合形态
                pattern_indices.append((i, final_idx, middle_bars_count))

    return pattern_indices