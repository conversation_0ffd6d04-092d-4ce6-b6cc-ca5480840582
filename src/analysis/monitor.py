"""
性能监控和诊断模块

该模块提供了用于监控和诊断多股票分析系统性能的工具。
主要功能：
1. 性能指标收集
2. 系统资源监控
3. 性能瓶颈诊断
4. 警报触发
"""

import psutil
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
import numpy as np
import threading
from collections import deque

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """系统资源指标"""
    cpu_percent: float
    memory_percent: float
    disk_io_read: float
    disk_io_write: float
    network_sent: float
    network_recv: float
    timestamp: datetime

@dataclass
class AnalysisMetrics:
    """分析性能指标"""
    analysis_time: float
    memory_usage: float
    cache_hits: int
    cache_misses: int
    error_count: int
    retry_count: int
    stock_count: int
    timestamp: datetime

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, 
                 metrics_window: int = 100,
                 alert_threshold: Dict = None):
        """
        初始化性能监控器
        
        Args:
            metrics_window: 保留的指标数量
            alert_threshold: 警报阈值配置
        """
        self.metrics_window = metrics_window
        self.alert_threshold = alert_threshold or {
            'cpu_percent': 80.0,
            'memory_percent': 80.0,
            'error_rate': 0.1,
            'analysis_time': 5.0
        }
        
        self.system_metrics = deque(maxlen=metrics_window)
        self.analysis_metrics = deque(maxlen=metrics_window)
        self._lock = threading.Lock()
        
        # 启动后台监控线程
        self._monitor_thread = threading.Thread(
            target=self._background_monitor,
            daemon=True
        )
        self._monitor_thread.start()
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统资源指标"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk_io = psutil.disk_io_counters()
            network = psutil.net_io_counters()
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_io_read=disk_io.read_bytes,
                disk_io_write=disk_io.write_bytes,
                network_sent=network.bytes_sent,
                network_recv=network.bytes_recv,
                timestamp=datetime.now()
            )
            
            with self._lock:
                self.system_metrics.append(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")
            return None
    
    def collect_analysis_metrics(self, 
                               analysis_time: float,
                               memory_usage: float,
                               cache_stats: Dict,
                               stock_count: int) -> None:
        """收集分析性能指标"""
        try:
            metrics = AnalysisMetrics(
                analysis_time=analysis_time,
                memory_usage=memory_usage,
                cache_hits=cache_stats['hits'],
                cache_misses=cache_stats['misses'],
                error_count=cache_stats['errors'],
                retry_count=cache_stats['retries'],
                stock_count=stock_count,
                timestamp=datetime.now()
            )
            
            with self._lock:
                self.analysis_metrics.append(metrics)
                
            # 检查是否需要触发警报
            self._check_alerts(metrics)
            
        except Exception as e:
            logger.error(f"Error collecting analysis metrics: {str(e)}")
    
    def get_performance_report(self) -> Dict:
        """生成性能报告"""
        try:
            with self._lock:
                if not self.analysis_metrics or not self.system_metrics:
                    return {}
                
                # 计算分析指标统计
                analysis_times = [m.analysis_time for m in self.analysis_metrics]
                memory_usages = [m.memory_usage for m in self.analysis_metrics]
                error_rates = [m.error_count / m.stock_count if m.stock_count > 0 else 0 
                             for m in self.analysis_metrics]
                
                # 计算系统指标统计
                cpu_usages = [m.cpu_percent for m in self.system_metrics]
                memory_usages_sys = [m.memory_percent for m in self.system_metrics]
                
                return {
                    'analysis_performance': {
                        'avg_time': np.mean(analysis_times),
                        'max_time': np.max(analysis_times),
                        'min_time': np.min(analysis_times),
                        'std_time': np.std(analysis_times),
                        'avg_memory': np.mean(memory_usages) / (1024 * 1024),  # MB
                        'error_rate': np.mean(error_rates),
                        'cache_hit_rate': self._calculate_cache_hit_rate()
                    },
                    'system_resources': {
                        'avg_cpu': np.mean(cpu_usages),
                        'max_cpu': np.max(cpu_usages),
                        'avg_memory': np.mean(memory_usages_sys),
                        'max_memory': np.max(memory_usages_sys)
                    },
                    'recommendations': self._generate_recommendations()
                }
                
        except Exception as e:
            logger.error(f"Error generating performance report: {str(e)}")
            return {}
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        try:
            total_hits = sum(m.cache_hits for m in self.analysis_metrics)
            total_misses = sum(m.cache_misses for m in self.analysis_metrics)
            total = total_hits + total_misses
            return total_hits / total if total > 0 else 0
        except Exception:
            return 0
    
    def _check_alerts(self, metrics: AnalysisMetrics) -> None:
        """检查是否需要触发警报"""
        try:
            # 检查分析时间
            if metrics.analysis_time > self.alert_threshold['analysis_time']:
                logger.warning(f"Analysis time ({metrics.analysis_time:.2f}s) exceeded threshold")
            
            # 检查错误率
            error_rate = metrics.error_count / metrics.stock_count if metrics.stock_count > 0 else 0
            if error_rate > self.alert_threshold['error_rate']:
                logger.warning(f"Error rate ({error_rate:.2%}) exceeded threshold")
            
            # 检查系统资源
            system_metrics = self.system_metrics[-1] if self.system_metrics else None
            if system_metrics:
                if system_metrics.cpu_percent > self.alert_threshold['cpu_percent']:
                    logger.warning(f"CPU usage ({system_metrics.cpu_percent:.1f}%) exceeded threshold")
                if system_metrics.memory_percent > self.alert_threshold['memory_percent']:
                    logger.warning(f"Memory usage ({system_metrics.memory_percent:.1f}%) exceeded threshold")
                    
        except Exception as e:
            logger.error(f"Error checking alerts: {str(e)}")
    
    def _generate_recommendations(self) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        try:
            # 分析性能指标
            avg_time = np.mean([m.analysis_time for m in self.analysis_metrics])
            error_rate = np.mean([m.error_count / m.stock_count if m.stock_count > 0 else 0 
                                for m in self.analysis_metrics])
            cache_hit_rate = self._calculate_cache_hit_rate()
            
            # 系统资源指标
            avg_cpu = np.mean([m.cpu_percent for m in self.system_metrics])
            avg_memory = np.mean([m.memory_percent for m in self.system_metrics])
            
            # 生成建议
            if avg_time > 3.0:
                recommendations.append("考虑增加数据预处理和缓存优化")
            if error_rate > 0.05:
                recommendations.append("检查错误处理和重试机制")
            if cache_hit_rate < 0.7:
                recommendations.append("优化缓存策略，考虑增加缓存容量")
            if avg_cpu > 70:
                recommendations.append("考虑减少并发分析的股票数量或增加CPU资源")
            if avg_memory > 70:
                recommendations.append("检查内存使用，考虑增加内存清理频率")
                
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            
        return recommendations
    
    def _background_monitor(self) -> None:
        """后台监控线程"""
        while True:
            try:
                self.collect_system_metrics()
                time.sleep(5)  # 每5秒收集一次系统指标
            except Exception as e:
                logger.error(f"Error in background monitor: {str(e)}")
                time.sleep(1) 