import concurrent.futures
import pandas as pd
import numpy as np
import logging
import subprocess
from functools import partial
from datetime import datetime
from src.services.sim_trade_stock_futu import *
# from popup import show_signal_message, show_message
# from chip_line import analyze_stock_data
from src.utils.futu_utils import *
# from detect_pattern_test import detect_pattern
from src.utils.constants import *
from src.analysis.key_level_signals import check_break_and_retest
from src.utils.popup import show_signal_message

def create_nested_folders(folder_names, parent_path):
    # 将分割后的文件夹名存储在列表中
    folders = folder_names.split('_')
    for folder_name in folders:
        # 组合当前文件夹的路径
        folder_path = os.path.join(parent_path, folder_name)
        
        # 检查文件夹是否已存在
        if not os.path.exists(folder_path):
            # 如果不存在，则创建文件夹
            os.makedirs(folder_path)
            # print(f"文件夹 {folder_path} 创建成功！")
            logging.info(f"文件夹 {folder_path} 创建成功！")
        else:
            logging.info(f"文件夹 {folder_path} 已存在，跳过创建。")
            # print(f"文件夹 {folder_path} 已存在，跳过创建。")

        # 更新父文件夹路径，用于递归创建子文件夹
        parent_path = folder_path
    return parent_path

def seach_stcok(quote_ctx, sim_trd_ctx, user_choice_stocks_map, stock_transaction_records_df, user_choice_stocks_list_circulatingShares, signal_time, manager_res_dict_test, sound_signal):
    # 获取涨幅TOP股票
    # time.sleep(34)
    # 获取当前时间
    current_time = datetime.now(NY_TZ)
    start_date = current_time.strftime('%Y-%m-%d')
    # start_date = '2024-05-06'
    # print('\n\n' + str(current_time) + '\n')
    logging.info(f' ====================================={str(current_time) } \n')
    # user_choice_stocks_map['MNDR'] = {'stock_id': '83339545640296'}
    # user_choice_stocks_map = {'GPAK': {'stock_id': '84018150475970'}, 'LASE': {'stock_id': '82669530741119'}
    #                           , 'MGAM': {'stock_id': '82429012550413'}, 'CNXA': {'stock_id': '82149839697614'}
    #                           , 'OPFI': {'stock_id': '80865644473179'}, 'MSGM': {'stock_id': '80028125845917'}
    #                           , 'BSGM': {'stock_id': '76424648280888'}}
    
    # user_choice_stocks_map = {'TPET': {'stock_id': '83451214790140'}, 'HUBC': {'stock_id': '83391085247901'}, 'CNXA': {'stock_id': '82149839697614'}, 'NXPL': {'stock_id': '81655918457810'}, 'ALLR': {'stock_id': '81522774471313'}, 'RENT': {'stock_id': '81256486479095'}, 'EZGO': {'stock_id': '80096845322784'}, 'PXMD': {'stock_id': '79547089507946'}, 'INDO': {'stock_id': '78344498664051'}, 'ENSV': {'stock_id': '208610'}, 'HUSA': {'stock_id': '206519'}}
    
    # 将当前日期加一天
    # next_day = (current_time + timedelta(days=1)).strftime('%Y-%m-%d')
    # 删除已存在的提醒股票
    delete_stock_reminder(quote_ctx)
    # 查询当日成交
    # order_data_list = deal_list(sim_trd_ctx)
    # 查询历史订单
    history_order_list_res = history_order_list(sim_trd_ctx, start_date + ' 05:00:00', start_date + ' 17:30:00')
    
    for i in range(0, len(stock_transaction_records_df)):
        for ii in range(0, len(history_order_list_res)):
            if stock_transaction_records_df.index[i] == int(history_order_list_res['order_id'][ii])\
                and stock_transaction_records_df['sell'].iloc[i] == 'False':
                if int(history_order_list_res['dealt_avg_price'][ii]) != 0:
                    stock_transaction_records_df.loc[stock_transaction_records_df.index[i], 'price'] = history_order_list_res['dealt_avg_price'][ii]
                    stock_transaction_records_df.loc[stock_transaction_records_df.index[i], 'stop_limit_price'] = history_order_list_res['dealt_avg_price'][ii] * 0.93
    
    # for stock_code, stock_info in user_choice_stocks_map.items():
    #     if stock_code in dfs:
    #         temp_df = dfs[stock_code]
    #     else:
    #         temp_df = pd.DataFrame()
    #     get_price(stock_code, stock_info, quote_ctx, start_date, sim_trd_ctx, stock_transaction_records_df, temp_df)
    # 创建进程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
        # 遍历股票并提交任务
        futures = []
        for stock_code, stock_info in user_choice_stocks_map.items():
            futures.append(executor.submit(partial(set_df, stock_code, stock_info, quote_ctx, sim_trd_ctx, stock_transaction_records_df, current_time, user_choice_stocks_list_circulatingShares, signal_time, manager_res_dict_test, sound_signal)))
            
        # 创建文件夹
        start_date_ = datetime.now(tz=NY_TZ).strftime('%Y-%m-%d_%H%M%S')
        parent_path = create_nested_folders(str(start_date_), '/Users/<USER>/seach_stcok/')
        
        # 等待所有任务完成
        # 获取所有任务的返回值
        results = []
        to_csv_flg = False
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result is not None:
                if not result[1].empty:
                    results.append(result)
                    # 筛选出当前日期的数据 
                    current_date_df = result[1][result[1].index.normalize() == start_date]
                    # 保存为 CSV 文件
                    current_date_df.to_csv(parent_path + '/' + result[0] + '.csv')
                filtered_df = result[2]
                if not filtered_df.empty:
                    to_csv_flg = True
                    if stock_transaction_records_df.empty:
                        stock_transaction_records_df = filtered_df
                    else:
                        # 获取所有索引
                        indexes_to_replace = filtered_df.index.tolist()
                        # 遍历要替换的索引
                        for idx_to_replace in indexes_to_replace:
                            # 使用 loc 方法替换 df1 中指定索引的行
                            # 如果索引不存在，则将新行添加到 DataFrame 的末尾
                            stock_transaction_records_df.loc[idx_to_replace] = filtered_df.loc[idx_to_replace]
        if to_csv_flg:
            stock_transaction_records_df.to_csv('/Users/<USER>/seach_stcok/stock_transaction_records/' + start_date + '.csv', header=False)
        # print(results)
    # print("--------------")
    logging.info("--------------\n")
    # print("\n")

# 计算macd
def calculate_mack(df):

    # MACD = EMA-Fast - EMA-Slow. Signal = EMA(MACD, Smooth-period)
    df['ema_fast'] = df['Close'].ewm(span=12, adjust=False).mean()
    df['ema_slow'] = df['Close'].ewm(span=26, adjust=False).mean()
    df['DIF'] = df['ema_fast'] - df['ema_slow']
    df['DEA'] = df['DIF'].ewm(span=9, adjust=False).mean()
    # MACD_hist = (MACD - MACD_signal) * 2
    df['MACD'] = (df['DIF'] - df['DEA'])

# 检查 MACD_hist 列是否逐渐增加，容忍tolerance误差
def is_macd_hist_up(series, tolerance):
    series_size = len(series)
    # 计算逐渐增加的数量
    up_count = 0
    # 找到前低点的索引
    previous_low_index = series['MACD'].idxmin()
    index_i = series.index.get_loc(previous_low_index)
    i_ = series_size-1 - index_i
    if i_ == 0:
        return False
    if i_ <= 3:
        tolerance = 0
    elif i_ <= 5:
        tolerance = 2
    for i in range(index_i, series_size-1):
        current_value = series['MACD'].iloc[i]
        next_value = series['MACD'].iloc[i+1]

        if current_value < next_value:
            up_count += 1
    # 如果逐渐减少的数量超过容忍范围返回True，则返回 False
    return up_count > tolerance

# 检查 MACD_hist 列是否逐渐减少，容忍tolerance误差
def is_decreasing_with_tolerance(series, tolerance):
    series_size = len(series)
    if ' 09' in str(series.index[series_size-1]):
        return False
    # 计算逐渐减少的数量
    decreasing_count = 0
    # 找到前高点的索引
    previous_high_index = series['MACD'].idxmax()
    index_i = series.index.get_loc(previous_high_index)
    i_ = series_size-1 - index_i
    if i_ == 0:
        return True
    if i_ <= 3:
        tolerance = 0
    elif i_ <= 5:
        tolerance = 2
    for i in range(index_i, series_size-1):
        current_value = series['MACD'].iloc[i]
        next_value = series['MACD'].iloc[i+1]

        if next_value > 0 and current_value > next_value:
            decreasing_count += 1
        elif next_value < -0.03 and current_value > next_value:
            decreasing_count += 1
    # 如果逐渐减少的数量超过容忍范围返回True，则返回 False
    return decreasing_count > tolerance

# 检查买入macd是否递增
def is_macd_incremental(macd_signal_list):
    macd_signal_list_size = len(macd_signal_list)
    start_i = 1
    end_i = 3
    # 如果第二个元素不递增
    if macd_signal_list[1] <= macd_signal_list[0]:
        start_i = 2
        end_i = 4
    # 检查MACD是否逐渐递增
    for m_i in range(start_i, macd_signal_list_size):
        if macd_signal_list[m_i] <= macd_signal_list[m_i - 1]:
            return False
        if m_i == macd_signal_list_size - 1 or m_i == end_i:
            return True
        
# 判断MACD和DEA（在一些指标中称为Signal线）是否背离
def is_divergence(macd, dea):
    # 判断macd和dea的走势
    macd_current = macd.iloc[-2]
    macd_previous = macd.iloc[-3]
    
    dea_current = dea.iloc[-2]
    dea_previous = dea.iloc[-3]

    # 判断是否背离
    if (macd_current > macd_previous and dea_current < dea_previous) or (macd_current < macd_previous and dea_current > dea_previous):
        return True
    return False

# 判断成交量是否递增
def is_increasing(volumes):
    return (volumes > volumes.shift(1)).iloc[1:].all()

# 计算倾斜率
def tilt_rate(df, row):
    # 计算倾斜率（前后相邻周期的差值）
    df[f'slope_{row}'] = df[row].diff()
    # # 计算EMA20的倾斜角度
    # df[f'angle_{row}'] = np.degrees(np.arctan(df[f'slope_{row}']))
    # # 计算前12个angle_20的和
    # df[f'sum_4_{row}_angle'] = df[f'angle_{row}'].rolling(window=4).sum()
    # 直接评估斜率
    df[f'sum_{row}_angle'] = df[f'slope_{row}'] / df[row].shift(1) * 100  # 百分比表示变化率
        
def get_index(df_, df, i__):
    """找到第一个大于或等于当前1分钟的更高周期的索引
    
    Args:
        df_: 更高周期的DataFrame
        df: 当前周期的DataFrame
        i__: 当前周期的索引位置
        
    Returns:
        int: 更高周期中对应的索引位置
    """
    try:
        # 检查df_是否为空
        if df_ is None or df_.empty:
            return 0
            
        # 检查索引是否有效
        if i__ < 0 or i__ >= len(df.index):
            return 0
            
        # 找到第一个大于或等于当前1分钟的更高周期的索引
        corresponding_indices = df_.index[df_.index >= df.index[i__]]
        
        # 检查是否找到了匹配的索引
        if len(corresponding_indices) == 0:
            return 0
            
        corresponding_index = corresponding_indices[0]
        
        # 获取更高周期索引的整数位置
        return df_.index.get_loc(corresponding_index)
    except Exception as e:
        logging.error(f"获取索引时出错: {e}")
        return 0

def ema_check(df_check, i_check, level, ema_check_50_flg):
    if df_check['EMA9'].iloc[i_check] < df_check['EMA20'].iloc[i_check]\
        or df_check['EMA20'].iloc[i_check] < df_check['EMA50'].iloc[i_check]\
            or ((ema_check_50_flg or level != 15) and df_check['EMA50'].iloc[i_check] < df_check['EMA120'].iloc[i_check]):
        return True

signal_info = {0: '', 1: '09:33-35分钟vwap反弹', 2: '5分钟缩量', 3: "vwap附近反弹的信号"
                   , 4: "ema9 ema20附近反弹的信号", 5: '1分钟vwap附近 5分钟ema9或20反弹'
                   , 8: '1分钟同时在vwap附近和ema20反弹', 9: '5分钟ema20反弹'
                   , 10: '1分钟同时在vwap附近和ema20反弹 ~09:35', 11: '15分钟MACD上升'
                   , 12: '穿', 13: 'vwap增量', 20: '动量上升 5', 21: '动量上升 15'
                   , 22: '关键突破'}
     
def check_signal(stock_code, df, quote_ctx, sim_trd_ctx, stock_transaction_records_df, current_time, user_choice_stocks_list_circulatingShares, signal_time, manager_res_dict_test, sound_signal):
    try:
        if df is None or df.empty:
            logging.info(f"[{stock_code}] df 为空")
            return None, None
        elif len(df) < 3:
            logging.info(f"[{stock_code}] df 长度太短: {len(df)}")
            return None, None

        data_size = len(df)
        # MACD 1分钟
        calculate_mack(df)

        # 使用 resample 方法将 1 分钟数据转换为 5 分钟数据，并对其他列进行聚合操作
        # 对 df 进行 5 分钟重采样，并将起始时间调整为以 :35 结尾
        df_5min = df.resample('5min', label='right', closed='right').agg({
            'Close': 'last',  # 最后一条数据
            'Open': 'first',  # 第一条数据
            'High': 'max',    # 最大值
            'Low': 'min',     # 最小值
            'Volume': 'sum'   # 总和
        }).dropna()
        # 将索引重命名为 'Time_index'
        df_5min.index.name = 'Time_5min'
        # EMA
        # 计算EMA20
        df_5min['EMA9'] = df_5min['Close'].ewm(span=9, min_periods=0, adjust=False).mean()
        df_5min['EMA20'] = df_5min['Close'].ewm(span=20, min_periods=0, adjust=False).mean()
        df_5min['EMA50'] = df_5min['Close'].ewm(span=50, min_periods=0, adjust=False).mean()
        df_5min['EMA120'] = df_5min['Close'].ewm(span=120, min_periods=0, adjust=False).mean()
        # MACD 5分钟
        calculate_mack(df_5min)
        data_size_5 = len(df_5min)

        buy_count = 0
        # 检查买卖状态
        filtered_df = pd.DataFrame()
        if not stock_transaction_records_df.empty:
            buy_count_df = stock_transaction_records_df[(stock_transaction_records_df['stock_code'] == stock_code)]
            buy_count = len(buy_count_df)
            filtered_df = stock_transaction_records_df[(stock_transaction_records_df['stock_code'] == stock_code) & (stock_transaction_records_df['sell'] == False)]
            if not filtered_df.empty:
                for filtered_df_i in range(0, len(filtered_df)):
                    macd_signal = True
                    macd_signal_list = []
                    macd_5_1 = df_5min['MACD'].iloc[data_size_5-1]
                    macd_5_2 = df_5min['MACD'].iloc[data_size_5-2]
                    filtered_index_specified = df.index.get_loc(filtered_df['date_time'].iloc[filtered_df_i]) # 获取index
                    if data_size - 2 - filtered_index_specified > 1 and data_size - 2 - filtered_index_specified < 5:
                        macd_signal_list = df['MACD'].iloc[filtered_index_specified : data_size-1].tolist()
                        macd_signal = is_macd_incremental(macd_signal_list)
                    # 提高止损价
                    if df['Close'].iloc[data_size-1] >= filtered_df['price'].iloc[filtered_df_i] * 1.11:
                        filtered_df.loc[filtered_df.index[filtered_df_i], 'stop_limit_price'] = filtered_df['price'].iloc[filtered_df_i] * 1.03
                        # stock_transaction_records_df.loc[(stock_transaction_records_df['stock_code'] == stock_code) 
                        #                                 & (stock_transaction_records_df['sell'] == False)] = filtered_df

                        # stock_transaction_records_df.to_csv('/Users/<USER>/seach_stcok/stock_transaction_records/' + start_date + '.csv', header=False)
                        # print('%s提高止损(3%%): ' % stock_code, str(filtered_df['stop_limit_price'].iloc[filtered_df_i]))
                        stop_limit_price = str(filtered_df['stop_limit_price'].iloc[filtered_df_i])
                        logging.info(f'{stock_code} 提高止损(3%) {stop_limit_price}')
                    if df['Close'].iloc[data_size-1] <= filtered_df['stop_limit_price'].iloc[filtered_df_i]\
                        or (df['Close'].iloc[data_size-1] >= filtered_df['price'].iloc[filtered_df_i] * 1.25\
                            and macd_5_1 < macd_5_2)\
                            or not macd_signal\
                                or '13:59' in str(current_time):
                        filtered_df.loc[filtered_df.index[filtered_df_i], 'profit'] = df['Close'].iloc[data_size-1] * filtered_df['qty'].iloc[filtered_df_i] - filtered_df['price'].iloc[filtered_df_i] * filtered_df['qty'].iloc[filtered_df_i]
                        filtered_df.loc[filtered_df.index[filtered_df_i], 'sell'] = True
                        # stock_transaction_records_df.loc[(stock_transaction_records_df['stock_code'] == stock_code) 
                        #                                 & (stock_transaction_records_df['sell'] == False)] = filtered_df

                        # stock_transaction_records_df.to_csv('/Users/<USER>/seach_stcok/stock_transaction_records/' + start_date + '.csv', header=False)
                        # 平仓
                        # print('卖出: ', stock_code, str(df['Close'].iloc[data_size-1])
                        #     , str(filtered_df['price'].iloc[filtered_df_i]), str(filtered_df.index[filtered_df_i]))
                        logging.info(f'{stock_code} 5分钟MACD:{macd_5_1} {macd_5_2}')
                        logging.info(f'{stock_code} MACD信号:{macd_signal} MACD信号消失:{macd_signal_list}')
                        close_price = df['Close'].iloc[data_size-1]
                        buy_price = filtered_df['price'].iloc[filtered_df_i]
                        order_index = filtered_df.index[filtered_df_i]
                        logging.info(f'{stock_code}卖出: {close_price} {buy_price} {order_index}')
                        sell_order(sim_trd_ctx, df['Close'].iloc[data_size-1], filtered_df['qty'].iloc[filtered_df_i], stock_code)
                        # if not macd_signal:
                        #     show_message('MACD信号消失', stock_code)
        
        signal_key = 0
        
        df['Turnover'] = df['Volume'] * ((df['Close'] + df['Open']) / 2)
       
        # EMA
        # 计算EMA9
        df['EMA9'] = df['Open'].ewm(span=9, min_periods=0, adjust=False).mean()
        # 计算EMA20
        df['EMA20'] = df['Open'].ewm(span=20, min_periods=0, adjust=False).mean()
        df['EMA50'] = df['Open'].ewm(span=50, min_periods=0, adjust=False).mean()
        # 计算EMA120
        df['EMA120'] = df['Open'].ewm(span=120, min_periods=0, adjust=False).mean()

        # 截取 09:31:00 以后的数据
        start_date = current_time.strftime('%Y-%m-%d')

        # start_date = '2024-08-30'
        # 计算 VWAP
        df_data = df.loc[df.index >= pd.Timestamp(f'{start_date} 09:31:00')]
        vwap_numerator = (df_data['Close'] + df_data['High'] + df_data['Low']) / 3 * df_data['Volume']
        vwap_denominator = np.where(df_data['Volume'] != 0, df_data['Volume'], 1)
        df_data.loc[:, 'vwap'] = vwap_numerator.cumsum() / vwap_denominator.cumsum()
        df['vwap'] = df_data['vwap']
        # 穿过vwap
        df['near_vwap_2'] = (df['High'] >= df['vwap']) & (df['Low'] <= df['vwap'])

        # # 前10根均量
        # # 为避免除零错误，先过滤掉 Open <= 0 的数据（或可以选择在计算中处理）
        # valid_df = df_5min[df_5min['Open'] > 0]
        # # 计算涨幅
        # increase_ratio = (valid_df['Close'] - valid_df['Open']) / valid_df['Open']
        # # 找出涨幅 > 20% 的记录
        # match = increase_ratio[increase_ratio > 0.2]

        # if match.empty:
        #     first_index = None
        #     prev_10_mean_vol = None
        # else:
        #     # 获取第一根满足条件的 K 线的 index（按原 df 的索引）
        #     first_index = match.index[0]
        #     # 找到该 index 在原 DataFrame 中的位置
        #     pos = df_5min.index.get_loc(first_index)
        #     # 添加条件：要求该 K 线必须位于倒数第5根到倒数第3根之间，
        #     # 即其位置必须为 data_size - 5, data_size - 4 或 data_size - 3
        #     if pos not in [data_size_5 - 5, data_size_5 - 4, data_size_5 - 3]:
        #         # 如果不满足条件，则视为没有找到符合要求的 K 线
        #         first_index = None
        #         prev_10_mean_vol = None
        #     else:
        #         # 4. 计算该 K 线前10根 K 线的平均成交量
        #         start_pos = max(0, pos - 10)
        #         prev_10_mean_vol = df['Volume'].iloc[start_pos:pos].mean()


        # 截取 04:01:00 以后的数据
        df_5min = df_5min.loc[df_5min.index >= pd.Timestamp(f'{start_date} 04:05:00')].copy()

        # 截取 09:35:00 以后的数据 计算vwap
        # df_data = df.loc[df.index.time >= pd.Timestamp('09:31:00').time()]
        df_data_5 = df_5min.loc[df_5min.index >= pd.Timestamp(f'{start_date} 09:35:00')].copy()
        vwap_numerator_5 = (df_data_5['Close'] + df_data_5['High'] + df_data_5['Low']) / 3 * df_data_5['Volume']
        vwap_denominator_5 = np.where(df_data_5['Volume'] != 0, df_data_5['Volume'], 1)
        df_data_5.loc[:, 'vwap'] = vwap_numerator_5.cumsum() / vwap_denominator_5.cumsum()
        df_5min['vwap'] = df_data_5['vwap']
        # 穿过vwap
        df_5min['near_vwap'] = (df_5min['High'] >= df_5min['vwap']) & (df_5min['Low'] <= df_5min['vwap'])


        # 对 df 进行 15 分钟重采样
        df_15min = df.resample('15min', label='right', closed='right').agg({
            'Close': 'last',  # 最后一条数据
            'Open': 'first',  # 第一条数据
            'High': 'max',    # 最大值
            'Low': 'min',     # 最小值
            'Volume': 'sum'   # 总和
        }).dropna()
        # 将索引重命名为 'Time_index'
        df_15min.index.name = 'Time_15min'
        # EMA
        # 计算EMA20
        df_15min['EMA9'] = df_15min['Close'].ewm(span=9, min_periods=0, adjust=False).mean()
        df_15min['EMA20'] = df_15min['Close'].ewm(span=20, min_periods=0, adjust=False).mean()
        df_15min['EMA50'] = df_15min['Close'].ewm(span=50, min_periods=0, adjust=False).mean()
        df_15min['EMA120'] = df_15min['Close'].ewm(span=120, min_periods=0, adjust=False).mean()
        # MACD 15分钟
        calculate_mack(df_15min)
        # 截取 04:01:00 以后的数据
        df_15min = df_15min.loc[df_15min.index >= pd.Timestamp(f'{start_date} 04:15:00')].copy()


        # 截取 04:01:00 以后的数据
        df = df.loc[df.index >= pd.Timestamp(f'{start_date} 04:01:00')].copy()
        data_size = len(df)

        # df_offset = df.tail(120)
        # max_ = df_offset['Close'].max()
        # min_ = df_offset['Close'].min()
        # # 计算偏移
        # # df['offset_price_9'] = (abs(df['Open']- df['EMA9']) / df['EMA9']) * 100 
        # df['offset_price_9'] = abs(df_offset['Open']- df_offset['EMA9']) / (max_ - min_) * 100

        # 1. 对数转换
        df['log_open'] = np.log(df['Open'])
        df['log_ema'] = np.log(df['EMA9'])
        df['log_distance'] = df['log_open'] - df['log_ema']

        # 2. 转换为百分比（可选）
        df['percentage_distance'] = (np.exp(df['log_distance']) - 1) * 100

        buy_flg = 1

        # 获取盘前开始1分钟成交额的平均值
        recent_volume_avg_all = df['Volume'].mean()
        df['recent_volume_avg_all'] = recent_volume_avg_all

        df_vol_10 = df['Volume'].tail(10)
        # 判断是否有成交量为0(熔断)
        df_vol_has_zero = (df_vol_10 == 0).any()
        
        # macd_current = abs(df_5min['MACD'].iloc[-1])
        # macd_previous = abs(df_5min['MACD'].iloc[-2])
        # macd_5min_Multiple = max(macd_current, macd_previous) / min(macd_current, macd_previous)

        # MACD和DEA背离
        # df_5min_is_divergence = is_divergence(df_5min['MACD'], df_5min['DEA'])
        
        df_5min_vol_5 = df_5min['Volume'].tail(5).iloc[:-1]
        # 成交量递增
        df_5min_is_increasing = is_increasing(df_5min_vol_5)

        # df['relative_position'] = df['MACD'].rank(pct=True) * 100
        # 提取从倒数第二个元素开始的 5 个元素
        # subset = df['relative_position'].iloc[-6:-1]
        # 筛选出小于 80 的值
        # filtered = subset[subset < 80]
        # 统计满足条件的元素数量 macd少于80
        # df_macd_under_80 = filtered.count() >= 2

        # 计算每根蜡烛的涨幅百分比
        # df['涨幅百分比'] = (df['Close'] - df['Open']) / df['Open'] * 100
        # 倒数第 8 至倒数第 2 个涨幅百分比 是否存在任意一个值超过 13%
        # percentage_increase_flg = (df['涨幅百分比'].iloc[-8:-1] > 13).any()

        # 计算macd在整体中的排名百分比
        # df_5min['relative_position'] = df_5min['MACD'].rank(pct=True) * 100
        # df_15min['relative_position'] = df_15min['MACD'].rank(pct=True) * 100

        # 计算倾斜率
            # tilt_rate(df, 'EMA9')
            # tilt_rate(df_5min, 'EMA9')
            # tilt_rate(df_5min, 'DIF')
        # df_5min_dea_last_10 = df_5min.iloc[-10:].copy()
        # tilt_rate(df_5min_dea_last_10, 'DEA')
        # 统计小于 0 的值的数量
        # df_5min_dea_last_10_zero = (df_5min_dea_last_10['sum_DEA_angle'] < -2).sum()

        # specified_time = '2024-08-30 09:32:00'  # 指定的时间
        # index_of_specified_time = df.index.get_loc(specified_time) # 获取index
        # for i in range(index_of_specified_time, data_size):
        target = pd.Timestamp(f'{start_date} 09:35:00')
        first_min5_i = df_5min.index.get_indexer([target])[0]
        
        # 确保数据量足够进行循环处理
        if data_size < 2:
            logging.info(f"[{stock_code}] 数据量不足，无法进行信号检测: {data_size}")
            return filtered_df, df
        
        for i in range(data_size-1-1, data_size):

            min5_i = get_index(df_5min, df, i)
            min15_i = get_index(df_15min, df, i)

            # 截取数
            # number_interceptions5 = 5
            # temp_df5 = df.iloc[:i].tail(number_interceptions5)
            # temp_df8 = df.iloc[:i].tail(8)
            # # number_interceptions10 = 10
            # temp_df10 = df.iloc[-12:-5]
            # temp_df10_2 = df.iloc[-15:-8]

            # # 倒数第 11 行到倒数第 2 行的所有行，不包括最后一行 总共会获取 10 行数据
            # temp_df_3_pre = df.iloc[-21:-11]
            # temp_df_3 = df.iloc[-11:-1]

            # 最近10个1分钟成交量的平均值
            # recent_volume_avg_10 = temp_df5['Volume'].mean()
            # df.loc[df.index[i], 'recent_volume_avg_10'] = recent_volume_avg_10

            df.loc[df.index[i], 'df_5min_DIF'] = df_5min['DIF'].iloc[min5_i]
            df.loc[df.index[i], 'df_5min_DEA'] = df_5min['DEA'].iloc[min5_i]
            df.loc[df.index[i], 'df_5min_MACD'] = df_5min['MACD'].iloc[min5_i]

            # df.loc[df.index[i], 'macd_5min_Multiple'] = macd_5min_Multiple
            # MACD和DEA背离
            # df.loc[df.index[i], 'df_5min_is_divergence'] = df_5min_is_divergence
            # 成交量递增
            df.loc[df.index[i], 'df_5min_is_increasing'] = df_5min_is_increasing
            # 判断是否有成交量为0(熔断)
            df.loc[df.index[i], 'df_vol_has_zero'] = df_vol_has_zero

            df.loc[df.index[i], 'df_15min_DIF'] = df_15min['DIF'].iloc[min15_i]
            df.loc[df.index[i], 'df_15min_DEA'] = df_15min['DEA'].iloc[min15_i]
            
            # df.loc[df.index[i], 'recent_volume_avg_all_flg'] = df['Volume'].iloc[i] > recent_volume_avg_all * 2
            # df.loc[df.index[i], 'Volume_2'] = (df['Volume'].iloc[i] > df['Volume'].iloc[i-1] * 2 or df['Volume'].iloc[i] > df['Volume'].iloc[i-2] * 2)
            # df.loc[df.index[i], 'Volume_3'] = df['Volume'].iloc[i] > ((df['Volume'].iloc[i-1]+df['Volume'].iloc[i-2]+df['Volume'].iloc[i-3])/3) * 2
            # df.loc[df.index[i], 'red'] = df['Close'].iloc[i] > df['Open'].iloc[i]
            # df.loc[df.index[i], 'recent_volume_avg_10_14'] = df['Volume'].iloc[i] > recent_volume_avg_10 * 1.4
            # df.loc[df.index[i], 'close_up'] = df['Close'].iloc[i] > df['Open'].iloc[i-1]
            
            # 判断是否有成交量为0(熔断)
            if df_vol_has_zero:
                continue

            # 获取最近10个1分钟成交额的平均值
            # recent_turnover_avg = temp_df5['Turnover'].mean()
            # recent_turnover_avg8 = temp_df8['Turnover'].mean()
            # recent_turnover_avg3__pre = temp_df_3_pre['Turnover'].mean()
            # recent_turnover_avg3 = temp_df_3['Turnover'].mean()
            # 倒数第 8 至倒数第 2 个涨幅百分比 是否存在任意一个值超过 13%
            # if percentage_increase_flg:
            #     continue
            # dea向下倾斜 7 个值小于 0
            # if df_5min_dea_last_10_zero >= 7:
            #     continue

            # if recent_turnover_avg < 300000:
            #     continue
            
            # 获取当前时间戳
            t1 = time.time()
            last_time = signal_time.get(stock_code, 0)
            # 少于9分钟不检查
            if buy_count > 2 and (t1 - last_time < 10 * 59):
                continue

            if df['Close'].iloc[i] < df['Open'].iloc[i]:
                continue
            # 添加索引检查，避免i-1为负数导致索引越界
            if i > 0 and df['Close'].iloc[i-1] < df_5min['Open'].iloc[min5_i]\
                and df_5min['Close'].iloc[min5_i] < df_5min['Open'].iloc[min5_i]:
                continue
            # 添加索引检查，避免i-1为负数导致索引越界
            if i > 0 and df['Close'].iloc[i-1] < df_15min['Open'].iloc[min15_i]\
                and df_15min['Close'].iloc[min15_i] < df_15min['Open'].iloc[min15_i]:
                continue
            if df_5min['MACD'].iloc[min5_i] < 0 or df_15min['MACD'].iloc[min15_i] < 0:
                continue
            # 开盘价偏移ema9
            if df['percentage_distance'].iloc[i] > 1.4:
                continue
            if not (df_5min['Close'].iloc[min5_i] > df_5min['vwap'].iloc[min5_i]\
                and df_5min['Low'].iloc[min5_i] <= df_5min['vwap'].iloc[min5_i]):
                continue

            interception = i - data_size

            max_close, max_high_time, retest_df_time = check_break_and_retest(df, i, df_5min, first_min5_i, target, start_date, interception)
            if max_close:
                now_ = datetime.now(tz=NY_TZ).strftime('%Y-%m-%d %H:%M:%S')
                # and stock_code in user_choice_stocks_list_circulatingShares:
                # if (df['STAR'].loc[df.index[i]] == '⭐️⭐️⭐️' or df['STAR'].loc[df.index[i-1]] == '⭐️⭐️⭐️'):
                signal_key = 22
                df.loc[df.index[i], 'STAR'] = '⭐️⭐️⭐️'
                sys.stdout.flush()  # 刷新输出缓冲区
                logging.info(f'股票:{stock_code} ：⭐️⭐️⭐️  突破价格: {max_close} 时间: {max_high_time} 回调时间: {retest_df_time} {signal_info[signal_key]} {now_}')
                # 显示通知
                if (i == data_size-1 or i == data_size-2)\
                    and filtered_df.empty\
                        and buy_flg == 1 and buy_count < 3:
                    # 获取当前时间戳
                    signal_time[stock_code] = time.time()
                    # show_signal_message(signal_info[signal_key], f'{stock_code}\n{df.index[i]}')
                    # print(str(df.index[i]) + ' ' + df['STAR'].loc[df.index[i]])
                    # logging.info(f'{str(df.index[i])} {df['STAR'].loc[df.index[i]]}')
                    # 播放音频
                    subprocess.Popen(['afplay', sound_signal])
                    set_stock_reminder(stock_code, quote_ctx, signal_info[signal_key])
                    show_signal_message(stock_code, f'{signal_info[signal_key]} {now_}')
                    buy_flg = 0
                    # 下单
                    qty = int(80 / df['Close'].iloc[i])
                    buy_order_info = buy_order(sim_trd_ctx, df['Close'].iloc[i], qty, stock_code)
                    df.loc[df.index[i], 'stock_code'] = stock_code
                    df.loc[df.index[i], 'order_id'] = buy_order_info['order_id'][0]
                    df.loc[df.index[i], 'qty'] = qty
                    df.loc[df.index[i], 'price'] = df['Close'].iloc[i]
                    df.loc[df.index[i], 'stop_limit_price'] = df['Close'].iloc[i] * 0.93
                    df.loc[df.index[i], 'sell'] = False

                    order_id = buy_order_info['order_id'][0]
                    filtered_df_buy = pd.DataFrame()
                    filtered_df_buy.loc[order_id, 'date_time'] = str(df.index[i])
                    filtered_df_buy.loc[order_id, 'stock_code'] = stock_code
                    filtered_df_buy.loc[order_id, 'order_id'] = order_id
                    filtered_df_buy.loc[order_id, 'qty'] = qty
                    filtered_df_buy.loc[order_id, 'price'] = df['Close'].iloc[i]
                    filtered_df_buy.loc[order_id, 'stop_limit_price'] = df['Close'].iloc[i] * 0.93
                    filtered_df_buy.loc[order_id, 'sell'] = False
                    filtered_df_buy.loc[order_id, 'profit'] = 0
                    # 使用 .assign 添加一行数据
                    # filtered_df = filtered_df.assign(date_time=[str(df.index[i])], stock_code=[2], qty=[3], price=[3], stop_limit_price=[3], sell=[3], profit=[3])
                    # 使用列名和对应值直接赋值
                    # filtered_df.loc[0, ['date_time', 'stock_code', 'order_id', 'qty', 'price', 'stop_limit_price', 'sell', 'profit']] = [1, 2, 3]
                    # 将 order_id 列设置为索引
                    filtered_df_buy.set_index('order_id', inplace=True)
                    filtered_df = filtered_df_buy.copy()
                    # new_df = df.iloc[:i+1].tail(1)
                    # new_df.to_csv('/Users/<USER>/seach_stcok/stock_transaction_records/' + start_date + '.csv', mode='a', columns=['stock_code', 'order_id', 'qty', 'price', 'stop_limit_price', 'sell', 'profit'], header=False)
                    # print()

            if not filtered_df.empty:
                for filtered_i in range(0, len(filtered_df)):
                    if filtered_df.loc[filtered_df.index[filtered_i], 'date_time'] in str(df.index[i])\
                        and df['STAR'].loc[df.index[i]] != '⭐️⭐️⭐️':
                        filtered_df.loc[filtered_df.index[filtered_i], 'profit'] = df['Close'].iloc[data_size-1] * filtered_df['qty'].iloc[filtered_i] - filtered_df['price'].iloc[filtered_i] * filtered_df['qty'].iloc[filtered_i]
                        filtered_df.loc[filtered_df.index[filtered_i], 'sell'] = True
                        # stock_transaction_records_df.loc[(stock_transaction_records_df['stock_code'] == stock_code) 
                        #                                 & (stock_transaction_records_df['sell'] == False)] = filtered_df

                        # stock_transaction_records_df.to_csv('/Users/<USER>/seach_stcok/stock_transaction_records/' + start_date + '.csv', header=False)
                        # 平仓
                        # print('信号消失卖出: ', stock_code, str(df['Close'].iloc[data_size-1])
                        #     , str(filtered_df['price'].iloc[filtered_i]), str(filtered_df.index[filtered_i]))
                        close_price = str(df['Close'].iloc[data_size-1])
                        buy_price = str(filtered_df['price'].iloc[filtered_i])
                        order_index = str(filtered_df.index[filtered_i])
                        logging.info(f'信号消失卖出: {stock_code} {close_price} {buy_price} {order_index}')
                        sell_order(sim_trd_ctx, df['Close'].iloc[data_size-1], filtered_df['qty'].iloc[filtered_i], stock_code)
        return filtered_df, df
    except Exception as e:
        logging.error(f"处理股票 {stock_code} 信号时出错: {e}", exc_info=True)
        return pd.DataFrame(), df

def set_df(stock_code, df, quote_ctx, sim_trd_ctx, stock_transaction_records_df, current_time, user_choice_stocks_list_circulatingShares, signal_time, manager_res_dict_test, sound_signal):
    filtered_df = pd.DataFrame()
    if df is None or df.empty:
        return [stock_code, None, filtered_df]
    data_size = len(df)
    # print(f'{stock_code} data size: {data_size}')
    logging.info(f'{stock_code} data size: {data_size}')

    #购买信号
    df['STAR'] = None
    df['vwap'] = 0.0
    df['EMA9'] = 0.0
    # 偏移
    df['offset_price_9'] = 0.0
    df['EMA20'] = 0.0
    df['EMA120'] = 0.0
    # df['down'] = 0.0
    # df['nearest_column_vwap'] = 'Close'
    # df['nearest_column'] = 'Close'
    # df['offset5_price_20'] = 10.0
    # 盘前开始1分钟成交额的平均值
    df['recent_volume_avg_all'] = None
    # 最近10个1分钟成交量的平均值
    df['recent_volume_avg_10'] = None
    df['near_vwap_2'] = False
    # df['is_macd_hist_up'] = False
    # df['BUY1'] = False
    # df['BUY_VOL'] = False
    # ema9相比上一个高
    # df['ema9_back_high'] = False
    # dif大于0
    # df['dif_0'] = False
    # dif相比上一个高
    # df['dif_back_high'] = False
    # dea相比上一个高
    # df['dea_back_high'] = False
    # dif高过dea
    # df['dif_higher_dea'] = False
    # 成交量相比上一个增加85%
    # df['vol_up'] = False
    # 价格高过上一个最低价
    # df['close_up'] = False
    # df['df_5min_Close'] = None
    # df['df_5min_Open'] = None
    df['recent_volume_avg_all_flg'] = None
    df['Volume_2'] = None
    df['Volume_3'] = None
    df['red'] = None
    df['recent_volume_avg_10_14'] = None
    df['close_up'] = None
    
    df['df_5min_DIF'] = None
    df['df_5min_DEA'] = None
    df['df_5min_MACD'] = None
    df['macd_5min_Multiple'] = None
    # MACD和DEA背离
    df['df_5min_is_divergence'] = None
    # 成交量递增
    df['df_5min_is_increasing'] = None
    # 判断是否有成交量为0(熔断)
    df['df_vol_has_zero'] = None
    df['df_15min_DIF'] = None
    df['df_15min_DEA'] = None

    df['stock_code'] = None
    df['order_id'] = None
    df['qty'] = None
    df['price'] = None
    df['stop_limit_price'] = None
    df['profit'] = None
    # 卖出 true 未卖出 false
    df['sell'] = None

    sys.stdout.flush()  # 刷新输出缓冲区
    filtered_df = pd.DataFrame()
    df_ = pd.DataFrame()
    try:
        filtered_df, df_ = check_signal(stock_code, df, quote_ctx, sim_trd_ctx, stock_transaction_records_df, current_time, user_choice_stocks_list_circulatingShares, signal_time, manager_res_dict_test, sound_signal)
    except Exception as e:
        # 获取异常发生的具体行数
        traceback.print_exc()  # 在控制台打印异常信息，包括具体的行数
        logging.error(f'{stock_code} error: {e}')
    return [stock_code, df_, filtered_df]

def main_(user_choice_stocks_map, stock_transaction_records_df, quote_ctx, sim_trd_ctx, user_choice_stocks_list_circulatingShares, signal_time, manager_res_dict_test, sound_signal):
    try:
        # # 真实账户
        # quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        # # 模拟账号
        # sim_trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.US, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)

        seach_stcok(quote_ctx, sim_trd_ctx, user_choice_stocks_map, stock_transaction_records_df, user_choice_stocks_list_circulatingShares, signal_time, manager_res_dict_test, sound_signal)
    except Exception as e:
        logging.error(f'seach_stcok error {e}')
        # quote_ctx.close()
        # sim_trd_ctx.close()
    # print('========================================= 处理完毕 =========================================', '\n\n\n')
    logging.info(f'========================================= 处理完毕 =========================================\n\n\n')
    # exit(0)

# main_(xxxx_)