"""
机器学习模式识别模块
用于识别细价股急速拉升时的复杂模式
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
import joblib
import os

@dataclass
class PatternFeatures:
    """模式特征数据类"""
    # 价格特征
    price_volatility: float = 0.0
    price_trend_strength: float = 0.0
    price_momentum: float = 0.0
    
    # 成交量特征
    volume_spike_intensity: float = 0.0
    volume_distribution_skew: float = 0.0
    volume_trend_consistency: float = 0.0
    
    # 订单流特征
    order_flow_imbalance: float = 0.0
    large_order_frequency: float = 0.0
    small_order_density: float = 0.0
    
    # 时间特征
    time_concentration: float = 0.0
    persistence_factor: float = 0.0
    
    # 压力特征
    pressure_accumulation: float = 0.0
    resistance_strength: float = 0.0

class PatternRecognizer:
    """模式识别器"""
    
    def __init__(self, model_path: Optional[str] = None):
        self.model_path = model_path or "models/pattern_model.pkl"
        self.scaler_path = model_path.replace(".pkl", "_scaler.pkl") if model_path else "models/pattern_scaler.pkl"
        
        # 模型组件
        self.pressure_classifier = None
        self.anomaly_detector = None
        self.scaler = StandardScaler()
        
        # 特征名称
        self.feature_names = [
            'price_volatility', 'price_trend_strength', 'price_momentum',
            'volume_spike_intensity', 'volume_distribution_skew', 'volume_trend_consistency',
            'order_flow_imbalance', 'large_order_frequency', 'small_order_density',
            'time_concentration', 'persistence_factor',
            'pressure_accumulation', 'resistance_strength'
        ]
        
        # 加载预训练模型
        self._load_models()
    
    def extract_features(self, ticker_data: pd.DataFrame, orderbook_data: pd.DataFrame) -> PatternFeatures:
        """从原始数据中提取模式特征"""
        features = PatternFeatures()
        
        try:
            if ticker_data.empty or orderbook_data.empty:
                return features
            
            # 价格特征
            prices = ticker_data['price'].values
            if len(prices) > 1:
                features.price_volatility = np.std(prices) / np.mean(prices)
                price_changes = np.diff(prices)
                features.price_trend_strength = np.sum(price_changes > 0) / len(price_changes)
                features.price_momentum = np.mean(price_changes[-5:]) if len(price_changes) >= 5 else 0
            
            # 成交量特征
            volumes = ticker_data['size'].values
            if len(volumes) > 1:
                features.volume_spike_intensity = np.max(volumes) / np.mean(volumes)
                features.volume_distribution_skew = self._calculate_skewness(volumes)
                volume_trend = np.corrcoef(range(len(volumes)), volumes)[0, 1] if len(volumes) > 2 else 0
                features.volume_trend_consistency = abs(volume_trend)
            
            # 订单流特征
            if 'ticker_direction' in ticker_data.columns:
                buy_mask = ticker_data['ticker_direction'].str.contains('BUY', na=False)
                sell_mask = ticker_data['ticker_direction'].str.contains('SELL', na=False)
                
                buy_volume = ticker_data[buy_mask]['size'].sum()
                sell_volume = ticker_data[sell_mask]['size'].sum()
                total_volume = buy_volume + sell_volume
                
                if total_volume > 0:
                    features.order_flow_imbalance = (buy_volume - sell_volume) / total_volume
                
                # 大单和小单分析
                volume_75th = np.percentile(volumes, 75)
                volume_25th = np.percentile(volumes, 25)
                
                large_orders = ticker_data[ticker_data['size'] >= volume_75th]
                small_orders = ticker_data[ticker_data['size'] <= volume_25th]
                
                features.large_order_frequency = len(large_orders) / len(ticker_data)
                features.small_order_density = len(small_orders) / len(ticker_data)
            
            # 时间特征
            if 'date_time' in ticker_data.columns:
                time_diffs = ticker_data['date_time'].diff().dt.total_seconds().dropna()
                if len(time_diffs) > 0 and time_diffs.sum() > 0:
                    features.time_concentration = 1 / (1 + np.std(time_diffs))
                    features.persistence_factor = len(ticker_data) / (time_diffs.sum() / 60)  # 每分钟交易数
            
            # 压力特征
            if not orderbook_data.empty:
                bid_sizes = orderbook_data['bid_size'].values
                ask_sizes = orderbook_data['ask_size'].values
                
                if len(bid_sizes) > 0 and len(ask_sizes) > 0:
                    features.pressure_accumulation = np.mean(ask_sizes) / np.mean(bid_sizes)
                    features.resistance_strength = np.std(ask_sizes) / np.mean(ask_sizes)
        
        except Exception as e:
            logging.error(f"特征提取时出错: {str(e)}")
        
        return features
    
    def _calculate_skewness(self, data: np.ndarray) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0.0
        
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        
        skew = np.mean(((data - mean) / std) ** 3)
        return skew
    
    def predict_pressure_probability(self, features: PatternFeatures) -> float:
        """预测上涨压力概率"""
        if self.pressure_classifier is None:
            return 0.5  # 默认概率
        
        try:
            feature_vector = self._features_to_vector(features)
            feature_vector_scaled = self.scaler.transform([feature_vector])
            probability = self.pressure_classifier.predict_proba(feature_vector_scaled)[0][1]
            return probability
        except Exception as e:
            logging.error(f"预测压力概率时出错: {str(e)}")
            return 0.5
    
    def detect_anomaly(self, features: PatternFeatures) -> bool:
        """检测异常模式"""
        if self.anomaly_detector is None:
            return False
        
        try:
            feature_vector = self._features_to_vector(features)
            feature_vector_scaled = self.scaler.transform([feature_vector])
            anomaly_score = self.anomaly_detector.decision_function(feature_vector_scaled)[0]
            return anomaly_score < 0  # 负值表示异常
        except Exception as e:
            logging.error(f"异常检测时出错: {str(e)}")
            return False
    
    def _features_to_vector(self, features: PatternFeatures) -> List[float]:
        """将特征对象转换为向量"""
        return [
            features.price_volatility, features.price_trend_strength, features.price_momentum,
            features.volume_spike_intensity, features.volume_distribution_skew, features.volume_trend_consistency,
            features.order_flow_imbalance, features.large_order_frequency, features.small_order_density,
            features.time_concentration, features.persistence_factor,
            features.pressure_accumulation, features.resistance_strength
        ]
    
    def train_models(self, training_data: List[Tuple[PatternFeatures, bool]]):
        """训练模型"""
        if len(training_data) < 10:
            logging.warning("训练数据不足，无法训练模型")
            return
        
        # 准备训练数据
        X = []
        y = []
        
        for features, has_pressure in training_data:
            X.append(self._features_to_vector(features))
            y.append(1 if has_pressure else 0)
        
        X = np.array(X)
        y = np.array(y)
        
        # 数据标准化
        X_scaled = self.scaler.fit_transform(X)
        
        # 分割训练和测试数据
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)
        
        # 训练压力分类器
        self.pressure_classifier = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        self.pressure_classifier.fit(X_train, y_train)
        
        # 训练异常检测器
        self.anomaly_detector = IsolationForest(
            contamination=0.1,
            random_state=42
        )
        self.anomaly_detector.fit(X_train)
        
        # 评估模型
        y_pred = self.pressure_classifier.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        logging.info(f"模型训练完成，准确率: {accuracy:.3f}")
        
        # 保存模型
        self._save_models()
    
    def _save_models(self):
        """保存模型"""
        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            
            model_data = {
                'pressure_classifier': self.pressure_classifier,
                'anomaly_detector': self.anomaly_detector
            }
            
            joblib.dump(model_data, self.model_path)
            joblib.dump(self.scaler, self.scaler_path)
            
            logging.info(f"模型已保存到: {self.model_path}")
        except Exception as e:
            logging.error(f"保存模型时出错: {str(e)}")
    
    def _load_models(self):
        """加载模型"""
        try:
            if os.path.exists(self.model_path) and os.path.exists(self.scaler_path):
                model_data = joblib.load(self.model_path)
                self.pressure_classifier = model_data.get('pressure_classifier')
                self.anomaly_detector = model_data.get('anomaly_detector')
                self.scaler = joblib.load(self.scaler_path)
                
                logging.info(f"模型已从 {self.model_path} 加载")
            else:
                logging.info("未找到预训练模型，将使用默认设置")
        except Exception as e:
            logging.error(f"加载模型时出错: {str(e)}")
    
    def get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        if self.pressure_classifier is None:
            return {}
        
        try:
            importance = self.pressure_classifier.feature_importances_
            return dict(zip(self.feature_names, importance))
        except Exception as e:
            logging.error(f"获取特征重要性时出错: {str(e)}")
            return {}
