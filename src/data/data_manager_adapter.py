"""
数据管理器适配器

提供与原有全局变量相同的接口，将访问重定向到ThreadSafeStockDataManager
确保向后兼容性，现有代码无需修改即可使用新的数据管理器
"""

import logging
from typing import Any, Dict, Iterator, KeysView, ValuesView, ItemsView
from .thread_safe_stock_data_manager import get_stock_data_manager


class StockDataDictAdapter:
    """股票数据字典适配器
    
    模拟dict接口，将操作重定向到ThreadSafeStockDataManager的股票数据
    替代原有的 manager_res_dict_test 全局变量
    """
    
    def __init__(self):
        """初始化适配器"""
        self._manager = get_stock_data_manager()
        logging.info("StockDataDictAdapter 初始化完成")
    
    def __getitem__(self, key: str) -> Any:
        """获取数据项"""
        value = self._manager.get_stock_data(key)
        if value is None:
            raise KeyError(key)
        return value
    
    def __setitem__(self, key: str, value: Any) -> None:
        """设置数据项"""
        self._manager.set_stock_data(key, value)
    
    def __delitem__(self, key: str) -> None:
        """删除数据项"""
        # 通过设置为None来模拟删除
        self._manager.set_stock_data(key, None)
    
    def __contains__(self, key: str) -> bool:
        """检查键是否存在"""
        return self._manager.get_stock_data(key) is not None
    
    def __len__(self) -> int:
        """获取数据项数量"""
        stats = self._manager.get_stats()
        return stats['stock_data_count']
    
    def __iter__(self) -> Iterator[str]:
        """迭代键"""
        # 注意：这个实现有限制，因为我们无法直接获取所有键
        # 在实际使用中，建议避免迭代操作
        logging.warning("StockDataDictAdapter 不支持完整的迭代操作")
        return iter([])
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取数据项，支持默认值"""
        return self._manager.get_stock_data(key, default)
    
    def setdefault(self, key: str, default: Any = None) -> Any:
        """获取数据项，如果不存在则设置默认值"""
        value = self._manager.get_stock_data(key)
        if value is None:
            self._manager.set_stock_data(key, default)
            return default
        return value
    
    def update(self, other: Dict[str, Any]) -> None:
        """批量更新数据"""
        if isinstance(other, dict):
            self._manager.batch_update_stock_data(other)
        else:
            for key, value in other:
                self._manager.set_stock_data(key, value)
    
    def clear(self) -> None:
        """清空所有数据"""
        # self._manager.clear_stock_data()  # 注释掉清空操作
        pass
    
    def pop(self, key: str, default: Any = None) -> Any:
        """弹出数据项"""
        value = self._manager.get_stock_data(key, default)
        self._manager.set_stock_data(key, None)
        return value
    
    def keys(self) -> KeysView:
        """获取所有键（有限支持）"""
        logging.warning("StockDataDictAdapter.keys() 返回空视图")
        return {}.keys()
    
    def values(self) -> ValuesView:
        """获取所有值（有限支持）"""
        logging.warning("StockDataDictAdapter.values() 返回空视图")
        return {}.values()
    
    def items(self) -> ItemsView:
        """获取所有键值对（有限支持）"""
        logging.warning("StockDataDictAdapter.items() 返回空视图")
        return {}.items()


class TransactionDataDictAdapter:
    """交易数据字典适配器
    
    模拟dict接口，将操作重定向到ThreadSafeStockDataManager的交易数据
    替代原有的 number_of_transactions_dict 全局变量
    """
    
    def __init__(self):
        """初始化适配器"""
        self._manager = get_stock_data_manager()
        logging.info("TransactionDataDictAdapter 初始化完成")
    
    def __getitem__(self, key: str) -> int:
        """获取交易次数"""
        value = self._manager.get_transaction_data(key)
        # 只有当明确没有设置过这个键时才抛出KeyError
        # 由于我们无法区分"未设置"和"设置为0"，这里简化处理
        # 如果值为0且确实没有设置过，在实际使用中应该用get方法
        return value
    
    def __setitem__(self, key: str, value: int) -> None:
        """设置交易次数"""
        self._manager.set_transaction_data(key, value)
    
    def __delitem__(self, key: str) -> None:
        """删除交易数据"""
        self._manager.set_transaction_data(key, 0)
    
    def __contains__(self, key: str) -> bool:
        """检查股票是否有交易数据"""
        return self._manager.get_transaction_data(key) > 0
    
    def __len__(self) -> int:
        """获取有交易数据的股票数量"""
        stats = self._manager.get_stats()
        return stats['transaction_data_count']
    
    def __iter__(self) -> Iterator[str]:
        """迭代股票代码"""
        logging.warning("TransactionDataDictAdapter 不支持完整的迭代操作")
        return iter([])
    
    def get(self, key: str, default: int = 0) -> int:
        """获取交易次数，支持默认值"""
        return self._manager.get_transaction_data(key) or default
    
    def setdefault(self, key: str, default: int = 0) -> int:
        """获取交易次数，如果不存在则设置默认值"""
        value = self._manager.get_transaction_data(key)
        if value == 0:
            self._manager.set_transaction_data(key, default)
            return default
        return value
    
    def update(self, other: Dict[str, int]) -> None:
        """批量更新交易数据"""
        if isinstance(other, dict):
            for key, value in other.items():
                self._manager.set_transaction_data(key, value)
        else:
            for key, value in other:
                self._manager.set_transaction_data(key, value)
    
    def clear(self) -> None:
        """清空所有交易数据"""
        # self._manager.clear_transaction_data()  # 注释掉清空操作
        pass
    
    def pop(self, key: str, default: int = 0) -> int:
        """弹出交易数据"""
        value = self._manager.get_transaction_data(key) or default
        self._manager.set_transaction_data(key, 0)
        return value


class SharedDataDictAdapter:
    """共享数据字典适配器
    
    模拟dict接口，将操作重定向到ThreadSafeStockDataManager的共享数据
    替代原有的 manager_res_dict 全局变量
    """
    
    def __init__(self):
        """初始化适配器"""
        self._manager = get_stock_data_manager()
        logging.info("SharedDataDictAdapter 初始化完成")
    
    def __getitem__(self, key: Any) -> Any:
        """获取共享数据"""
        value = self._manager.get_shared_data(key)
        if value is None:
            raise KeyError(key)
        return value
    
    def __setitem__(self, key: Any, value: Any) -> None:
        """设置共享数据"""
        self._manager.set_shared_data(key, value)
    
    def __delitem__(self, key: Any) -> None:
        """删除共享数据"""
        self._manager.set_shared_data(key, None)
    
    def __contains__(self, key: Any) -> bool:
        """检查键是否存在"""
        return self._manager.get_shared_data(key) is not None
    
    def __len__(self) -> int:
        """获取共享数据项数量"""
        stats = self._manager.get_stats()
        return stats['shared_data_count']
    
    def __iter__(self) -> Iterator[Any]:
        """迭代键"""
        logging.warning("SharedDataDictAdapter 不支持完整的迭代操作")
        return iter([])
    
    def get(self, key: Any, default: Any = None) -> Any:
        """获取共享数据，支持默认值"""
        return self._manager.get_shared_data(key, default)
    
    def setdefault(self, key: Any, default: Any = None) -> Any:
        """获取共享数据，如果不存在则设置默认值"""
        value = self._manager.get_shared_data(key)
        if value is None:
            self._manager.set_shared_data(key, default)
            return default
        return value
    
    def update(self, other: Dict[Any, Any]) -> None:
        """批量更新共享数据"""
        if isinstance(other, dict):
            for key, value in other.items():
                self._manager.set_shared_data(key, value)
        else:
            for key, value in other:
                self._manager.set_shared_data(key, value)
    
    def clear(self) -> None:
        """清空所有共享数据"""
        # self._manager.clear_shared_data()  # 注释掉清空操作
        pass
    
    def pop(self, key: Any, default: Any = None) -> Any:
        """弹出共享数据"""
        value = self._manager.get_shared_data(key, default)
        self._manager.set_shared_data(key, None)
        return value


# 创建适配器实例，用于替代原有的全局变量
manager_res_dict_test_adapter = StockDataDictAdapter()
number_of_transactions_dict_adapter = TransactionDataDictAdapter()
manager_res_dict_adapter = SharedDataDictAdapter()
