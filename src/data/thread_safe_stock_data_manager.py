"""
线程安全的股票数据管理器

统一管理所有股票相关数据，提供线程安全的访问接口
替代原有的全局字典变量：manager_res_dict_test、number_of_transactions_dict、manager_res_dict
"""

import threading
import logging
from typing import Any, Dict, Callable, Optional, Union
from datetime import datetime


class ThreadSafeStockDataManager:
    """线程安全的股票数据管理器
    
    提供统一的数据管理接口，支持：
    - 股票实时数据管理（替代manager_res_dict_test）
    - 交易次数数据管理（替代number_of_transactions_dict）
    - 共享数据管理（替代manager_res_dict）
    - 线程安全的数据访问
    - 数据类型验证
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ThreadSafeStockDataManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化数据管理器"""
        if hasattr(self, '_initialized'):
            return
            
        self._stock_data: Dict[str, Any] = {}  # 替代 manager_res_dict_test
        self._transaction_data: Dict[str, int] = {}  # 替代 number_of_transactions_dict  
        self._shared_data: Dict[Any, Any] = {}  # 替代 manager_res_dict
        self._data_lock = threading.RLock()  # 数据访问锁
        self._data_validators: Dict[str, Callable] = {}  # 数据验证器
        self._access_count = 0  # 访问计数器
        self._last_access_time = datetime.now()  # 最后访问时间
        self._initialized = True

        # 注册默认的数据验证器
        self._register_default_validators()

        logging.info("ThreadSafeStockDataManager 初始化完成")
    
    def set_stock_data(self, key: str, value: Any) -> None:
        """设置股票数据
        
        Args:
            key: 数据键
            value: 数据值
        """
        with self._data_lock:
            # 数据验证 - 根据键名模式匹配验证器
            validator = self._find_validator_for_key(key)
            if validator:
                try:
                    validator(value)
                except Exception as e:
                    logging.warning(f"股票数据验证失败 key={key}, value={value}, error={e}")
                    return

            self._stock_data[key] = value
            self._update_access_stats()
    
    def get_stock_data(self, key: str, default: Any = None) -> Any:
        """获取股票数据
        
        Args:
            key: 数据键
            default: 默认值
            
        Returns:
            数据值或默认值
        """
        with self._data_lock:
            self._update_access_stats()
            return self._stock_data.get(key, default)
    
    def set_transaction_data(self, stock_code: str, count: int) -> None:
        """设置交易次数数据
        
        Args:
            stock_code: 股票代码
            count: 交易次数
        """
        with self._data_lock:
            # 验证交易次数数据
            if not isinstance(count, (int, float)) or count < 0:
                logging.warning(f"无效的交易次数数据 stock={stock_code}, count={count}")
                return
                
            self._transaction_data[stock_code] = int(count)
            self._update_access_stats()
    
    def get_transaction_data(self, stock_code: str) -> int:
        """获取交易次数数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            交易次数
        """
        with self._data_lock:
            self._update_access_stats()
            return self._transaction_data.get(stock_code, 0)
    
    def set_shared_data(self, key: Any, value: Any) -> None:
        """设置共享数据
        
        Args:
            key: 数据键
            value: 数据值
        """
        with self._data_lock:
            self._shared_data[key] = value
            self._update_access_stats()
    
    def get_shared_data(self, key: Any, default: Any = None) -> Any:
        """获取共享数据
        
        Args:
            key: 数据键
            default: 默认值
            
        Returns:
            数据值或默认值
        """
        with self._data_lock:
            self._update_access_stats()
            return self._shared_data.get(key, default)
    
    def batch_update_stock_data(self, updates: Dict[str, Any]) -> None:
        """批量更新股票数据
        
        Args:
            updates: 更新数据字典
        """
        with self._data_lock:
            for key, value in updates.items():
                # 数据验证 - 根据键名模式匹配验证器
                validator = self._find_validator_for_key(key)
                if validator:
                    try:
                        validator(value)
                    except Exception as e:
                        logging.warning(f"批量更新数据验证失败 key={key}, value={value}, error={e}")
                        continue

                self._stock_data[key] = value

            self._update_access_stats()
    
    def register_validator(self, data_type: str, validator: Callable) -> None:
        """注册数据验证器
        
        Args:
            data_type: 数据类型标识
            validator: 验证函数
        """
        with self._data_lock:
            self._data_validators[data_type] = validator
            logging.info(f"注册数据验证器: {data_type}")
    
    def clear_stock_data(self) -> None:
        """清空股票数据"""
        with self._data_lock:
            # self._stock_data.clear()  # 注释掉清空操作
            pass
            # logging.info("股票数据已清空")  # 注释掉频繁的日志输出

    def clear_transaction_data(self) -> None:
        """清空交易数据"""
        with self._data_lock:
            # self._transaction_data.clear()  # 注释掉清空操作
            pass
            # logging.info("交易数据已清空")  # 注释掉频繁的日志输出

    def clear_shared_data(self) -> None:
        """清空共享数据"""
        with self._data_lock:
            # self._shared_data.clear()  # 注释掉清空操作
            pass
            # logging.info("共享数据已清空")  # 注释掉频繁的日志输出
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            统计信息字典
        """
        with self._data_lock:
            return {
                'stock_data_count': len(self._stock_data),
                'transaction_data_count': len(self._transaction_data),
                'shared_data_count': len(self._shared_data),
                'access_count': self._access_count,
                'last_access_time': self._last_access_time.isoformat(),
                'validators_count': len(self._data_validators)
            }
    
    def _update_access_stats(self) -> None:
        """更新访问统计"""
        self._access_count += 1
        self._last_access_time = datetime.now()

    def _find_validator_for_key(self, key: str) -> Optional[Callable]:
        """根据键名查找对应的验证器

        Args:
            key: 数据键名

        Returns:
            对应的验证器函数，如果没有找到则返回None
        """
        # 直接匹配
        if key in self._data_validators:
            return self._data_validators[key]

        # 模式匹配
        for pattern, validator in self._data_validators.items():
            if pattern in key:
                return validator

        return None

    def _register_default_validators(self) -> None:
        """注册默认的数据验证器"""

        def price_validator(value: Any) -> None:
            """价格验证器"""
            if value is not None and not isinstance(value, (int, float)):
                raise ValueError(f"价格必须是数字类型，当前类型: {type(value)}")
            if value is not None and value < 0:
                raise ValueError(f"价格不能为负数: {value}")

        def volume_validator(value: Any) -> None:
            """成交量验证器"""
            if value is not None and not isinstance(value, (int, float)):
                raise ValueError(f"成交量必须是数字类型，当前类型: {type(value)}")
            if value is not None and value < 0:
                raise ValueError(f"成交量不能为负数: {value}")

        def percentage_validator(value: Any) -> None:
            """百分比验证器"""
            if value is not None and not isinstance(value, (int, float)):
                raise ValueError(f"百分比必须是数字类型，当前类型: {type(value)}")
            if value is not None and (value < -100 or value > 100):
                raise ValueError(f"百分比应在-100到100之间: {value}")

        def boolean_validator(value: Any) -> None:
            """布尔值验证器"""
            import numpy as np
            # 支持 Python bool 和 numpy bool_ 类型
            if value is not None and not isinstance(value, (bool, np.bool_)):
                raise ValueError(f"标志位必须是布尔类型，当前类型: {type(value)}")

        def number_validator(value: Any) -> None:
            """数值验证器（用于交易次数等数值字段）"""
            if value is not None and not isinstance(value, (int, float)):
                raise ValueError(f"数值必须是数字类型，当前类型: {type(value)}")
            if value is not None and value < 0:
                raise ValueError(f"数值不能为负数: {value}")

        # 注册验证器（使用常见的键模式）
        price_patterns = ['_price', '_NOW_PRICE', '_OPEN', '_HIGH', '_LOW', '_CLOSE']
        volume_patterns = ['_volume', '_VOLUME', '_CHECK_VOLUME', '_VOLUME_AVG']
        percentage_patterns = ['_percentage', '_change', '_ratio']
        # 特殊处理：number_flg 使用数值验证器，其他 _flg 使用布尔验证器
        number_patterns = ['_number_flg', '_NUMBER_FLG', '_transaction_rank', '_TRANSACTION_RANK']
        boolean_patterns = ['_flg', '_FLG', '_flag', '_FLAG', '_MARK', '_detected', '_DETECTED']

        for pattern in price_patterns:
            self._data_validators[pattern] = price_validator

        for pattern in volume_patterns:
            self._data_validators[pattern] = volume_validator

        for pattern in percentage_patterns:
            self._data_validators[pattern] = percentage_validator

        for pattern in number_patterns:
            self._data_validators[pattern] = number_validator

        for pattern in boolean_patterns:
            self._data_validators[pattern] = boolean_validator

        logging.info("默认数据验证器注册完成")


# 全局访问函数
_global_manager_instance: Optional[ThreadSafeStockDataManager] = None
_global_manager_lock = threading.Lock()


def get_stock_data_manager() -> ThreadSafeStockDataManager:
    """获取全局股票数据管理器实例
    
    Returns:
        ThreadSafeStockDataManager实例
    """
    global _global_manager_instance
    
    if _global_manager_instance is None:
        with _global_manager_lock:
            if _global_manager_instance is None:
                _global_manager_instance = ThreadSafeStockDataManager()
                logging.info("创建全局股票数据管理器实例")
    
    return _global_manager_instance


def reset_stock_data_manager() -> None:
    """重置全局股票数据管理器（主要用于测试）"""
    global _global_manager_instance

    with _global_manager_lock:
        # 注释掉清空数据的操作，避免频繁清理
        # if _global_manager_instance is not None:
        #     _global_manager_instance.clear_stock_data()
        #     _global_manager_instance.clear_transaction_data()
        #     _global_manager_instance.clear_shared_data()
        _global_manager_instance = None
        logging.info("全局股票数据管理器已重置")
