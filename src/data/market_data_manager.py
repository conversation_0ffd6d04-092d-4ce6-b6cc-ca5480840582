"""市场数据管理器模块"""

import logging
from src.data.thread_safe_stock_data_manager import get_stock_data_manager

from src.config.config import STOCK_THRESHOLDS
from src.utils.constants import *
from src.utils.key_generator import key_generator

class MarketDataManager:
    """市场数据管理器类，处理所有市场数据和股票数据

    重构后的版本统一使用ThreadSafeStockDataManager进行数据管理，
    提供更好的线程安全性和数据一致性。

    提供两种使用方式：

    1. 简洁接口（推荐用于单字段操作）：
        manager = MarketDataManager()

        # 获取单个字段
        price = manager.get('AAPL', NOW_PRICE, 0)
        set_flag = manager.get('AAPL', SET_FLG, False)
        buy_price = manager.get('AAPL', 'buy_price', 0)

        # 设置单个字段
        manager.set('AAPL', NOW_PRICE, 155.50)
        manager.set('AAPL', SET_FLG, True)
        manager.set('AAPL', 'buy_price', 150.0)

        # 特殊字段
        manager.set('AAPL', TRANSACTION_RANK, 500)
        manager.set('AAPL', 'program_status', 1)

    2. 批量接口（用于多字段操作）：
        # 批量设置
        data = {
            NOW_PRICE: 155.50,
            VOLUME: 1000000,
            SET_FLG: True,
            'buy_price': 150.0,
            TRANSACTION_RANK: 500
        }
        manager.set_market_data('AAPL', data)

        # 批量获取
        market_data = manager.get_market_data('AAPL')
        price = market_data.get(NOW_PRICE, 0)
        volume = market_data.get(VOLUME, 0)

    支持的数据字段：
    - 基础数据: NOW_PRICE, VOLUME, VOLUME_AVG, CHECK_VOLUME
    - 股票信息: STOCK_SHARESOUTSTANDING, NEWS
    - 状态标志: SET_FLG, BUY_FLG, IS_SPREAD_NORMAL, BREAKOUT_FLG等
    - 分析指标: CONFIDENCE, BEHAVIOR_SCORE, SIGNAL_STRENGTH等
    - 交易数据: TRANSACTION_RANK, NUMBER_FLG
    - 自定义字段: 'buy_price', 'program_status'

    线程安全：所有操作都是线程安全的，可以在多线程环境中安全使用。
    """

    def __init__(self) -> None:
        """初始化市场数据管理器"""
        self._data_manager = get_stock_data_manager()  # 统一的数据管理器
        self.all_conditions = {}  # 所有条件满足状态字典
        self.high_all_conditions = {}  # 高优先级条件满足状态字典

        logging.info("MarketDataManager 初始化完成")

    def get_active_stocks(self) -> list:
        """获取活跃股票列表

        Returns:
            list: 活跃股票列表
        """
        return self._data_manager.get_shared_data(1, [])

    def get_current_price(self, stock_code: str) -> float:
        """获取股票当前价格

        Args:
            stock_code: 股票代码

        Returns:
            float: 当前价格
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            price = self._data_manager.get_stock_data(keys[NOW_PRICE], 0)
            # 确保返回值不是None
            if price is None:
                return 0
            return float(price)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 价格时发生错误: {e}')
            return 0

    def get_volume(self, stock_code: str) -> float:
        """获取股票成交量

        Args:
            stock_code: 股票代码

        Returns:
            float: 成交量
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            volume = self._data_manager.get_stock_data(keys[VOLUME], 0)
            # 确保返回值不是None
            if volume is None:
                return 0
            return float(volume)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 成交量时发生错误: {e}')
            return 0

    def get_check_volume(self, stock_code: str) -> float:
        """获取股票检查成交量

        Args:
            stock_code: 股票代码

        Returns:
            float: 检查成交量
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            check_volume = self._data_manager.get_stock_data(keys[CHECK_VOLUME], 0)
            # 确保返回值不是None
            if check_volume is None:
                return 0
            return float(check_volume)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 检查成交量时发生错误: {e}')
            return 0

    def get_volume_avg(self, stock_code: str) -> float:
        """获取股票平均成交量

        Args:
            stock_code: 股票代码

        Returns:
            float: 平均成交量
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            volume_avg = self._data_manager.get_stock_data(keys[VOLUME_AVG], 0)
            # 确保返回值不是None
            if volume_avg is None:
                return 0
            return float(volume_avg)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 平均成交量时发生错误: {e}')
            return 0

    def get_shares_outstanding(self, stock_code: str) -> int:
        """获取股票流通股本

        Args:
            stock_code: 股票代码

        Returns:
            int: 流通股本
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            shares = self._data_manager.get_stock_data(keys[STOCK_SHARESOUTSTANDING], 0)
            # 确保返回值不是None
            if shares is None:
                return 0
            return int(shares)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 流通股本时发生错误: {e}')
            return 0

    def get_news(self, stock_code: str) -> list:
        """获取股票新闻

        Args:
            stock_code: 股票代码

        Returns:
            list: 新闻列表
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return self._data_manager.get_stock_data(keys[NEWS], [])
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 新闻时发生错误: {e}')
            return []

    def get_buy_flag(self, stock_code: str) -> bool:
        """获取股票买入标志
        
        Args:
            stock_code: 股票代码
            
        Returns:
            bool: 买入标志
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return self._data_manager.get_stock_data(keys[BUY_FLG], False)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 买入标志时发生错误: {e}')
            return False

    def get_set_flag(self, stock_code: str) -> bool:
        """获取股票设置标志

        Args:
            stock_code: 股票代码

        Returns:
            bool: 设置标志
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return self._data_manager.get_stock_data(keys[SET_FLG], False)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 设置标志时发生错误: {e}')
            return False

    def get_spread_status(self, stock_code: str) -> bool:
        """获取股票价差状态

        Args:
            stock_code: 股票代码

        Returns:
            bool: 价差状态
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return self._data_manager.get_stock_data(keys[IS_SPREAD_NORMAL], False)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 价差状态时发生错误: {e}')
            return False

    def get_transaction_rank(self, stock_code: str) -> int:
        """获取股票交易排名

        Args:
            stock_code: 股票代码

        Returns:
            int: 交易排名
        """
        try:
            rank = self._data_manager.get_transaction_data(stock_code)
            # 确保返回值不是None
            if rank is None:
                return 0
            return int(rank)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 交易排名时发生错误: {e}')
            return 0

    def get_transaction_volume(self, stock_code: str) -> int:
        """获取股票每分钟交易次数

        Args:
            stock_code: 股票代码

        Returns:
            int: 交易次数
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            transaction_volume = self._data_manager.get_stock_data(keys[NUMBER_FLG], 0)
            # 确保返回值不是None
            if transaction_volume is None:
                return 0
            return int(transaction_volume)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 交易次数时发生错误: {e}')
            return 0

    def get_breakout_flags(self, stock_code: str) -> dict:
        """获取股票突破标志

        Args:
            stock_code: 股票代码

        Returns:
            dict: 突破标志
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return {
                'normal': self._data_manager.get_stock_data(keys[BREAKOUT_FLG], False),
                'red': self._data_manager.get_stock_data(keys[BREAKOUT_RED_FLG], False)
            }
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 突破标志时发生错误: {e}')
            return {'normal': False, 'red': False}

    def get_signal_strength(self, stock_code: str) -> str:
        """获取股票信号强度

        Args:
            stock_code: 股票代码

        Returns:
            str: 信号强度
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return self._data_manager.get_stock_data(keys[SIGNAL_STRENGTH], '')
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 信号强度时发生错误: {e}')
            return ''

    def get_confidence(self, stock_code: str) -> float:
        """获取股票置信度

        Args:
            stock_code: 股票代码

        Returns:
            float: 置信度
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            confidence = self._data_manager.get_stock_data(keys[CONFIDENCE], 0.0)
            # 确保返回值不是None
            if confidence is None:
                return 0.0
            return float(confidence)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 置信度时发生错误: {e}')
            return 0.0

    def get_buying_pressure(self, stock_code: str) -> float:
        """获取股票买压

        Args:
            stock_code: 股票代码

        Returns:
            float: 买压
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            buying_pressure = self._data_manager.get_stock_data(keys[BUYING_PRESSURE], 0.0)
            # 确保返回值不是None
            if buying_pressure is None:
                return 0.0
            return float(buying_pressure)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 买压时发生错误: {e}')
            return 0.0

    def get_behavior_score(self, stock_code: str) -> float:
        """获取股票行为评分

        Args:
            stock_code: 股票代码

        Returns:
            float: 行为评分
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            behavior_score = self._data_manager.get_stock_data(keys[BEHAVIOR_SCORE], 0.0)
            # 确保返回值不是None
            if behavior_score is None:
                return 0.0
            return float(behavior_score)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 行为评分时发生错误: {e}')
            return 0.0

    def get_behavior_score_15min(self, stock_code: str) -> float:
        """获取股票15分钟行为评分

        Args:
            stock_code: 股票代码

        Returns:
            float: 15分钟行为评分
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            behavior_score_15min = self._data_manager.get_stock_data(keys[BEHAVIOR_SCORE_15MIN], 0.0)
            # 确保返回值不是None
            if behavior_score_15min is None:
                return 0.0
            return float(behavior_score_15min)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 15分钟行为评分时发生错误: {e}')
            return 0.0

    def get_upward_pressure_detected(self, stock_code: str) -> bool:
        """获取股票上涨压力检测标志

        Args:
            stock_code: 股票代码

        Returns:
            bool: 上涨压力检测标志
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return self._data_manager.get_stock_data(keys[UPWARD_PRESSURE_DETECTED], False)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 上涨压力检测标志时发生错误: {e}')
            return False

    def get_historical_data_end(self, stock_code: str) -> bool:
        """获取股票历史数据结束标志

        Args:
            stock_code: 股票代码

        Returns:
            bool: 历史数据结束标志
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return self._data_manager.get_stock_data(keys[HISTORICAL_DATA_END], False)
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 历史数据结束标志时发生错误: {e}')
            return False

    def get_market_data(self, stock_code: str) -> dict:
        """获取股票的所有市场数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            dict: 市场数据
        """
        try:
            data = {
                NOW_PRICE: self.get_current_price(stock_code),
                VOLUME: self.get_volume(stock_code),
                CHECK_VOLUME: self.get_check_volume(stock_code),
                VOLUME_AVG: self.get_volume_avg(stock_code),
                STOCK_SHARESOUTSTANDING: self.get_shares_outstanding(stock_code),
                NEWS: self.get_news(stock_code),
                IS_SPREAD_NORMAL: self.get_spread_status(stock_code),
                BUY_FLG: self.get_buy_flag(stock_code),
                SET_FLG: self.get_set_flag(stock_code),
                TRANSACTION_RANK: self.get_transaction_rank(stock_code),
                TRANSACTION_VOLUME: self.get_transaction_volume(stock_code),
                SIGNAL_STRENGTH: self.get_signal_strength(stock_code),
                CONFIDENCE: self.get_confidence(stock_code),
                BUYING_PRESSURE: self.get_buying_pressure(stock_code),
                BEHAVIOR_SCORE: self.get_behavior_score(stock_code),
                BEHAVIOR_SCORE_15MIN: self.get_behavior_score_15min(stock_code),
                UPWARD_PRESSURE_DETECTED: self.get_upward_pressure_detected(stock_code),
                HISTORICAL_DATA_END: self.get_historical_data_end(stock_code),
                'buy_price': self._data_manager.get_shared_data(stock_code, 0),  # 添加买入价格
                'program_status': self._data_manager.get_shared_data(0, 1)  # 添加程序状态
            }
            
            # 添加突破标志
            breakout_flags = self.get_breakout_flags(stock_code)
            data[BREAKOUT_FLG] = breakout_flags['normal']
            data[BREAKOUT_RED_FLG] = breakout_flags['red']
            
            return data
            
        except Exception as e:
            logging.error(f'获取股票 {stock_code} 数据时发生错误: {e}')
            return {} 

    def check_stock_conditions(self, stock_code: str, market_data: dict) -> bool:
        """检查股票是否满足条件

        Args:
            stock_code: 股票代码
            market_data: 市场数据

        Returns:
            bool: 是否满足条件
        """
        try:
            # 获取股票的所有键值
            keys = key_generator.get_keys_for_stock(stock_code)

            # 检查成交量，确保不是None
            volume_raw = market_data.get(VOLUME, 0)
            volume_avg_raw = market_data.get(VOLUME_AVG, float('inf'))

            # 处理None值
            if volume_raw is None:
                volume = 0
            else:
                try:
                    volume = float(volume_raw)
                except (ValueError, TypeError):
                    volume = 0

            if volume_avg_raw is None:
                volume_avg = float('inf')
            else:
                try:
                    volume_avg = float(volume_avg_raw)
                except (ValueError, TypeError):
                    volume_avg = float('inf')

            # 如果平均成交量大5_000_000，直接返回
            if volume_avg > STOCK_THRESHOLDS['MAX_VOLUME_AVG']:
                self._data_manager.set_stock_data(keys[SET_FLG], True)
                return

            # 条件2：判断 VOLUME 大于 VOLUME_AVG 的 5 倍
            cond_volume_avg = (volume_avg > 0) and (volume > STOCK_THRESHOLDS['VOLUME_AVG_MULTIPLIER'] * volume_avg)

            # 条件3：判断 NEWS 列表有值
            news_list = self._data_manager.get_stock_data(keys[NEWS], [])
            cond_news = bool(news_list)
            if not cond_news:
                self._data_manager.set_stock_data(keys[SET_FLG], True)
                return

            # 条件4：判断 STOCK_SHARESOUTSTANDING 小于 10,000,000
            try:
                shares_raw = self._data_manager.get_stock_data(keys[STOCK_SHARESOUTSTANDING], float('inf'))
                if shares_raw is None:
                    shares = float('inf')
                else:
                    shares = float(shares_raw)
            except (ValueError, TypeError):
                shares = float('inf')

            cond_shares = shares < STOCK_THRESHOLDS['MAX_SHARES_OUTSTANDING']
            if not cond_shares:
                self._data_manager.set_stock_data(keys[SET_FLG], True)
                return

            # 如果条件满足，设置检查成交量
            if cond_shares and cond_news and not cond_volume_avg:
                market_data[CHECK_VOLUME] = True
                self._data_manager.set_stock_data(keys[SET_FLG], True)
                return

            # 汇总所有条件
            self.all_conditions[stock_code] = cond_volume_avg and cond_shares and cond_news

        except Exception as e:
            logging.error(f'Error checking conditions for {stock_code}: {e}')
    
    def is_buy_flag_set(self, stock_code: str) -> bool:
        """检查是否设置了买入标志
        Args:
            stock_code: 股票代码

        Returns:
            bool: 是否设置了买入标志
        """
        keys = key_generator.get_keys_for_stock(stock_code)
        return self._data_manager.get_stock_data(keys[BUY_FLG], False)

    def set_buy_flag(self, stock_code: str, value: bool) -> None:
        """设置买入标志
        Args:
            stock_code: 股票代码
            value: 买入标志值
        """
        keys = key_generator.get_keys_for_stock(stock_code)
        self._data_manager.set_stock_data(keys[BUY_FLG], value)

    def update_buy_price(self, stock_code: str, price: float) -> None:
        """更新买入价格
        Args:
            stock_code: 股票代码
            price: 买入价格
        """
        self._data_manager.set_shared_data(stock_code, price)

    def batch_get_market_data(self, stock_codes: list) -> dict:
        """批量获取多个股票的市场数据

        Args:
            stock_codes: 股票代码列表

        Returns:
            dict: 股票代码到市场数据的映射
        """
        result = {}
        for stock_code in stock_codes:
            try:
                result[stock_code] = self.get_market_data(stock_code)
            except Exception as e:
                logging.error(f'批量获取股票 {stock_code} 数据时发生错误: {e}')
                result[stock_code] = {}
        return result

    def batch_update_stock_data(self, updates: dict) -> None:
        """批量更新股票数据

        Args:
            updates: 更新数据字典，格式为 {key: value}
        """
        try:
            self._data_manager.batch_update_stock_data(updates)
        except Exception as e:
            logging.error(f'批量更新股票数据时发生错误: {e}')

    def get_data_manager_stats(self) -> dict:
        """获取数据管理器统计信息

        Returns:
            dict: 统计信息
        """
        return self._data_manager.get_stats()

    def set_active_stocks(self, stock_list: list) -> None:
        """设置活跃股票列表

        Args:
            stock_list: 股票列表
        """
        self._data_manager.set_shared_data(1, stock_list)

    def get(self, stock_code: str, field: str, default: any = None) -> any:
        """简洁的数据获取接口

        Args:
            stock_code: 股票代码
            field: 数据字段名
            default: 默认值

        Returns:
            字段值或默认值
        """
        try:
            # 处理特殊字段
            if field == TRANSACTION_RANK:
                return self._data_manager.get_transaction_data(stock_code)
            elif field == 'buy_price':
                return self._data_manager.get_shared_data(stock_code, default)
            elif field == 'program_status':
                return self._data_manager.get_shared_data(0, default)

            # 处理常规股票数据字段
            keys = key_generator.get_keys_for_stock(stock_code)
            
            # 直接使用键值，无需lambda映射
            if field in keys:
                return self._data_manager.get_stock_data(keys[field], default)

            # 未知字段返回默认值
            logging.warning(f"未知字段: {field}")
            return default

        except Exception as e:
            logging.error(f"获取股票 {stock_code} 字段 {field} 时发生错误: {e}")
            return default

    def set(self, stock_code: str, field: str, value: any) -> None:
        """简洁的数据设置接口

        Args:
            stock_code: 股票代码
            field: 数据字段名
            value: 字段值
        """
        try:
            # 处理特殊字段
            if field == TRANSACTION_RANK:
                self._data_manager.set_transaction_data(stock_code, value)
                return
            elif field == 'buy_price':
                self._data_manager.set_shared_data(stock_code, value)
                return
            elif field == 'program_status':
                self._data_manager.set_shared_data(0, value)
                return

            # 处理常规股票数据字段
            keys = key_generator.get_keys_for_stock(stock_code)
            
            # 直接使用键值，无需lambda映射
            if field in keys:
                self._data_manager.set_stock_data(keys[field], value)
                return

            # 未知字段记录警告
            logging.warning(f"尝试设置未知字段: {field} = {value}")

        except Exception as e:
            logging.error(f"设置股票 {stock_code} 字段 {field} = {value} 时发生错误: {e}")
            raise

    def set_market_data(self, stock_code: str, market_data: dict) -> None:
        """设置股票的市场数据

        Args:
            stock_code: 股票代码
            market_data: 市场数据字典，包含各种股票数据
        """
        try:
            keys = key_generator.get_keys_for_stock(stock_code)

            # 准备要更新的数据
            updates = {}

            # 直接使用键值处理常规股票数据，无需lambda映射
            for field, value in market_data.items():
                if field in keys:
                    updates[keys[field]] = value

            # 批量更新股票数据
            if updates:
                self._data_manager.batch_update_stock_data(updates)

            # 处理交易排名数据
            if TRANSACTION_RANK in market_data:
                self._data_manager.set_transaction_data(stock_code, market_data[TRANSACTION_RANK])

            # 处理买入价格
            if 'buy_price' in market_data:
                self._data_manager.set_shared_data(stock_code, market_data['buy_price'])

            # 处理程序状态
            if 'program_status' in market_data:
                self._data_manager.set_shared_data(0, market_data['program_status'])
        except Exception as e:
            logging.error(f'设置股票 {stock_code} 市场数据时发生错误: {e}')
            raise
