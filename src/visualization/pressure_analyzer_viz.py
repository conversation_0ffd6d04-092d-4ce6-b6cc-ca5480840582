"""
上涨压力分析可视化模块
提供直观的图表展示分析结果
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

# 可选依赖
try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False
    logging.warning("seaborn不可用，将使用基础matplotlib功能")

try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    import plotly.express as px
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logging.warning("plotly不可用，将只提供基础图表功能")

from src.analysis.stock_analyzer import OrderBookBehavior, VolumeAnalysis, TechnicalSignal

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class PressureAnalyzerVisualizer:
    """上涨压力分析可视化器"""
    
    def __init__(self, figsize: Tuple[int, int] = (15, 10)):
        self.figsize = figsize
        self.colors = {
            'buy': '#00ff00',
            'sell': '#ff0000', 
            'neutral': '#ffff00',
            'pressure': '#ff6600',
            'support': '#0066ff',
            'background': '#1e1e1e',
            'text': '#ffffff'
        }
    
    def create_comprehensive_dashboard(self,
                                    ticker_data: pd.DataFrame,
                                    orderbook_data: pd.DataFrame,
                                    signal: TechnicalSignal,
                                    save_path: Optional[str] = None):
        """创建综合分析仪表板"""

        if not PLOTLY_AVAILABLE:
            logging.warning("Plotly不可用，无法创建综合仪表板")
            return None

        # 创建子图
        fig = make_subplots(
            rows=4, cols=2,
            subplot_titles=[
                '价格走势与成交量', '买卖盘力量对比',
                '上涨压力信号热图', '订单流分析',
                '时间窗口分析', '风险评估雷达图',
                '成交结构分析', '信号强度历史'
            ],
            specs=[
                [{"secondary_y": True}, {"type": "bar"}],
                [{"type": "heatmap"}, {"type": "scatter"}],
                [{"type": "scatter"}, {"type": "scatterpolar"}],
                [{"type": "pie"}, {"type": "scatter"}]
            ],
            vertical_spacing=0.08,
            horizontal_spacing=0.1
        )
        
        # 1. 价格走势与成交量
        self._add_price_volume_chart(fig, ticker_data, row=1, col=1)
        
        # 2. 买卖盘力量对比
        self._add_orderbook_comparison(fig, orderbook_data, row=1, col=2)
        
        # 3. 上涨压力信号热图
        self._add_pressure_heatmap(fig, signal.book_behavior, row=2, col=1)
        
        # 4. 订单流分析
        self._add_order_flow_analysis(fig, ticker_data, row=2, col=2)
        
        # 5. 时间窗口分析
        self._add_time_window_analysis(fig, ticker_data, row=3, col=1)
        
        # 6. 风险评估雷达图
        self._add_risk_radar_chart(fig, signal, row=3, col=2)
        
        # 7. 成交结构分析
        self._add_trading_structure_pie(fig, ticker_data, row=4, col=1)
        
        # 8. 信号强度历史
        self._add_signal_strength_history(fig, signal, row=4, col=2)
        
        # 更新布局
        fig.update_layout(
            title=f"细价股上涨压力综合分析 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            template="plotly_dark",
            height=1200,
            showlegend=True,
            font=dict(size=10)
        )
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def _add_price_volume_chart(self, fig, ticker_data: pd.DataFrame, row: int, col: int):
        """添加价格走势与成交量图表"""
        if ticker_data.empty:
            return
        
        # 价格线
        fig.add_trace(
            go.Scatter(
                x=ticker_data['date_time'],
                y=ticker_data['price'],
                mode='lines',
                name='价格',
                line=dict(color='#00ff88', width=2)
            ),
            row=row, col=col, secondary_y=False
        )
        
        # 成交量柱状图
        fig.add_trace(
            go.Bar(
                x=ticker_data['date_time'],
                y=ticker_data['size'],
                name='成交量',
                marker_color='rgba(255, 165, 0, 0.6)',
                yaxis='y2'
            ),
            row=row, col=col, secondary_y=True
        )
    
    def _add_orderbook_comparison(self, fig: go.Figure, orderbook_data: pd.DataFrame, row: int, col: int):
        """添加买卖盘力量对比"""
        if orderbook_data.empty:
            return
        
        # 计算平均买卖盘力量
        avg_bid = orderbook_data['bid_size'].mean()
        avg_ask = orderbook_data['ask_size'].mean()
        
        fig.add_trace(
            go.Bar(
                x=['买盘力量', '卖盘力量'],
                y=[avg_bid, avg_ask],
                marker_color=['#00ff00', '#ff0000'],
                name='买卖盘对比'
            ),
            row=row, col=col
        )
    
    def _add_pressure_heatmap(self, fig: go.Figure, behavior: OrderBookBehavior, row: int, col: int):
        """添加上涨压力信号热图"""
        # 压力信号数据
        pressure_signals = {
            '大单减少': behavior.large_buy_orders_decrease,
            '频率下降': behavior.large_buy_frequency_decline,
            '结构转变': behavior.trading_structure_shift,
            '小单密集': behavior.high_density_small_orders,
            '价差缩小': behavior.price_diff_narrowing,
            '卖盘堆积': behavior.sell_orders_piling,
            '买盘变薄': behavior.buy_side_thinning,
            '量价背离': behavior.volume_price_divergence,
            '散户接盘': behavior.retail_takeover_top,
            '压力增强': behavior.pressure_intensifying_top,
            '诱多陷阱': behavior.bull_trap_top,
            '封板疲软': behavior.weak_limit_up_top
        }
        
        # 转换为矩阵格式
        signals_matrix = []
        labels = []
        for name, value in pressure_signals.items():
            signals_matrix.append([1 if value else 0])
            labels.append(name)
        
        fig.add_trace(
            go.Heatmap(
                z=signals_matrix,
                y=labels,
                x=['信号状态'],
                colorscale=[[0, '#000080'], [1, '#ff0000']],
                showscale=False,
                name='压力信号'
            ),
            row=row, col=col
        )
    
    def _add_order_flow_analysis(self, fig: go.Figure, ticker_data: pd.DataFrame, row: int, col: int):
        """添加订单流分析"""
        if ticker_data.empty or 'ticker_direction' not in ticker_data.columns:
            return
        
        # 计算订单流强度
        buy_orders = ticker_data[ticker_data['ticker_direction'].str.contains('BUY', na=False)]
        sell_orders = ticker_data[ticker_data['ticker_direction'].str.contains('SELL', na=False)]
        
        if not buy_orders.empty and not sell_orders.empty:
            # 时间序列订单流
            time_windows = pd.cut(ticker_data.index, bins=10)
            flow_strength = []
            time_labels = []
            
            for window in time_windows.cat.categories:
                window_data = ticker_data[time_windows == window]
                if not window_data.empty:
                    buy_vol = window_data[window_data['ticker_direction'].str.contains('BUY', na=False)]['size'].sum()
                    sell_vol = window_data[window_data['ticker_direction'].str.contains('SELL', na=False)]['size'].sum()
                    net_flow = buy_vol - sell_vol
                    flow_strength.append(net_flow)
                    time_labels.append(f"T{len(time_labels)+1}")
            
            fig.add_trace(
                go.Scatter(
                    x=time_labels,
                    y=flow_strength,
                    mode='lines+markers',
                    name='订单流强度',
                    line=dict(color='#ffaa00', width=3)
                ),
                row=row, col=col
            )
    
    def _add_time_window_analysis(self, fig: go.Figure, ticker_data: pd.DataFrame, row: int, col: int):
        """添加时间窗口分析"""
        if ticker_data.empty:
            return
        
        # 模拟不同时间窗口的压力强度
        time_windows = ['10秒', '1分钟', '5分钟', '15分钟', '30分钟']
        pressure_levels = [0.2, 0.4, 0.6, 0.8, 0.9]  # 模拟数据
        
        fig.add_trace(
            go.Scatter(
                x=time_windows,
                y=pressure_levels,
                mode='lines+markers',
                name='压力强度',
                line=dict(color='#ff6600', width=3),
                marker=dict(size=10)
            ),
            row=row, col=col
        )
    
    def _add_risk_radar_chart(self, fig: go.Figure, signal: TechnicalSignal, row: int, col: int):
        """添加风险评估雷达图"""
        categories = ['价格风险', '成交量风险', '流动性风险', '时间风险', '技术风险']
        
        # 基于信号计算风险值
        risk_values = [
            0.8 if signal.price_trend.name == 'DOWN' else 0.3,
            0.7 if signal.volume_analysis and signal.volume_analysis.volume_ratio > 3 else 0.4,
            0.9 if signal.book_behavior and signal.book_behavior.buy_side_thinning else 0.2,
            0.8 if signal.book_behavior and signal.book_behavior.critical_15_30min_pressure else 0.3,
            0.6 if signal.confidence < 0.5 else 0.2
        ]
        
        fig.add_trace(
            go.Scatterpolar(
                r=risk_values,
                theta=categories,
                fill='toself',
                name='风险评估',
                line_color='#ff0000'
            ),
            row=row, col=col
        )
    
    def _add_trading_structure_pie(self, fig: go.Figure, ticker_data: pd.DataFrame, row: int, col: int):
        """添加成交结构饼图"""
        if ticker_data.empty:
            return
        
        # 按成交量大小分类
        large_orders = ticker_data[ticker_data['size'] >= ticker_data['size'].quantile(0.75)]
        medium_orders = ticker_data[(ticker_data['size'] >= ticker_data['size'].quantile(0.25)) & 
                                   (ticker_data['size'] < ticker_data['size'].quantile(0.75))]
        small_orders = ticker_data[ticker_data['size'] < ticker_data['size'].quantile(0.25)]
        
        sizes = [len(large_orders), len(medium_orders), len(small_orders)]
        labels = ['大单', '中单', '小单']
        colors = ['#ff0000', '#ffaa00', '#00ff00']
        
        fig.add_trace(
            go.Pie(
                values=sizes,
                labels=labels,
                marker_colors=colors,
                name='成交结构'
            ),
            row=row, col=col
        )
    
    def _add_signal_strength_history(self, fig: go.Figure, signal: TechnicalSignal, row: int, col: int):
        """添加信号强度历史"""
        # 模拟历史信号强度数据
        time_points = [datetime.now() - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        strength_values = np.random.uniform(0.2, 0.9, 12)  # 模拟数据
        
        # 当前信号强度
        current_strength = signal.confidence
        time_points.append(datetime.now())
        strength_values = np.append(strength_values, current_strength)
        
        fig.add_trace(
            go.Scatter(
                x=time_points,
                y=strength_values,
                mode='lines+markers',
                name='信号强度',
                line=dict(color='#00aaff', width=2),
                marker=dict(size=6)
            ),
            row=row, col=col
        )
    
    def create_simple_pressure_chart(self, 
                                   ticker_data: pd.DataFrame,
                                   behavior: OrderBookBehavior,
                                   save_path: Optional[str] = None) -> plt.Figure:
        """创建简单的压力分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        fig.suptitle('细价股上涨压力分析', fontsize=16, fontweight='bold')
        
        # 1. 价格走势
        if not ticker_data.empty:
            axes[0, 0].plot(ticker_data.index, ticker_data['price'], 
                           color='blue', linewidth=2, label='价格')
            axes[0, 0].set_title('价格走势')
            axes[0, 0].set_ylabel('价格')
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].legend()
        
        # 2. 成交量分布
        if not ticker_data.empty:
            axes[0, 1].hist(ticker_data['size'], bins=20, alpha=0.7, color='orange')
            axes[0, 1].set_title('成交量分布')
            axes[0, 1].set_xlabel('成交量')
            axes[0, 1].set_ylabel('频次')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 压力信号统计
        pressure_signals = {
            '大单减少': behavior.large_buy_orders_decrease,
            '小单密集': behavior.high_density_small_orders,
            '卖盘堆积': behavior.sell_orders_piling,
            '买盘变薄': behavior.buy_side_thinning,
            '量价背离': behavior.volume_price_divergence
        }
        
        signal_names = list(pressure_signals.keys())
        signal_values = [1 if v else 0 for v in pressure_signals.values()]
        colors = ['red' if v else 'gray' for v in signal_values]
        
        axes[1, 0].bar(signal_names, signal_values, color=colors)
        axes[1, 0].set_title('压力信号状态')
        axes[1, 0].set_ylabel('信号强度')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 4. 典型组合识别
        combinations = {
            '散户接盘': behavior.retail_takeover_top,
            '压力增强': behavior.pressure_intensifying_top,
            '诱多陷阱': behavior.bull_trap_top,
            '封板疲软': behavior.weak_limit_up_top
        }
        
        combo_names = list(combinations.keys())
        combo_values = [1 if v else 0 for v in combinations.values()]
        combo_colors = ['darkred' if v else 'lightgray' for v in combo_values]
        
        axes[1, 1].bar(combo_names, combo_values, color=combo_colors)
        axes[1, 1].set_title('典型压力组合')
        axes[1, 1].set_ylabel('检测状态')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
