"""
请求管理器模块

处理IB API请求的重试策略和时间窗口调整
"""

import datetime
from typing import Optional, Tuple, Dict
import logging
from dataclasses import dataclass
import time

@dataclass
class TimeWindow:
    """时间窗口配置"""
    current: datetime.timedelta
    minimum: datetime.timedelta
    maximum: datetime.timedelta
    
    def increase(self) -> datetime.timedelta:
        """增加时间窗口"""
        self.current = min(self.current * 2, self.maximum)
        return self.current
        
    def decrease(self) -> datetime.timedelta:
        """减小时间窗口"""
        self.current = max(self.current / 2, self.minimum)
        return self.current
        
    @property
    def duration_minutes(self) -> float:
        """获取当前窗口时长（分钟）"""
        return self.current.total_seconds() / 60

class RequestManager:
    """请求管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # 请求统计
        self.request_stats: Dict[str, int] = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "retried_requests": 0
        }
        
        # 性能指标
        self.performance_stats: Dict[str, float] = {
            "avg_response_time": 0,
            "min_response_time": float('inf'),
            "max_response_time": 0
        }
        
        # 最近的请求时间
        self.last_request_time: Optional[datetime.datetime] = None
        
        # 当前请求的开始时间
        self.current_request_start: Optional[float] = None

    def _setup_logging(self):
        """设置日志配置"""
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
        
    def update_performance_stats(self, response_time: float):
        """更新性能统计"""
        self.performance_stats["min_response_time"] = min(
            self.performance_stats["min_response_time"],
            response_time
        )
        self.performance_stats["max_response_time"] = max(
            self.performance_stats["max_response_time"],
            response_time
        )
        
        # 更新平均响应时间
        total_requests = self.request_stats["successful_requests"]
        if total_requests > 0:
            current_avg = self.performance_stats["avg_response_time"]
            self.performance_stats["avg_response_time"] = (
                (current_avg * (total_requests - 1) + response_time) / total_requests
            )
            
    def handle_request(
        self,
        time_window: TimeWindow,
        request_start: datetime.datetime,
        request_end: datetime.datetime,
        data_count: int,
        attempt_count: int,
        max_attempts: int
    ) -> Tuple[TimeWindow, bool, str]:
        """
        处理请求结果并决定下一步操作
        
        Args:
            time_window: 时间窗口配置
            request_start: 请求开始时间
            request_end: 请求结束时间
            data_count: 返回的数据条数
            attempt_count: 当前尝试次数
            max_attempts: 最大尝试次数
            
        Returns:
            Tuple[TimeWindow, bool, str]: 
            - 更新后的时间窗口
            - 是否继续尝试
            - 状态消息
        """
        # 更新请求统计
        self.request_stats["total_requests"] += 1
        
        # 计算响应时间
        if self.last_request_time:
            response_time = (datetime.datetime.now() - self.last_request_time).total_seconds()
            self.update_performance_stats(response_time)
        
        self.last_request_time = datetime.datetime.now()
        
        # 检查是否达到最大重试次数
        if attempt_count >= max_attempts:
            self.request_stats["failed_requests"] += 1
            if time_window.current < time_window.maximum:
                time_window.increase()
                return time_window, False, f"达到最大重试次数 ({max_attempts})，增加时间窗口到 {time_window.duration_minutes} 分钟"
            else:
                return time_window, False, f"达到最大重试次数 ({max_attempts}) 且时间窗口已达最大值"
        
        # 检查数据返回情况
        if data_count == 0:
            self.request_stats["failed_requests"] += 1
            if time_window.current > time_window.minimum:
                time_window.decrease()
                return time_window, False, f"没有数据返回，减小时间窗口到 {time_window.duration_minutes} 分钟"
            else:
                return time_window, False, "时间窗口已达最小值，停止重试"
        
        # 请求成功
        self.request_stats["successful_requests"] += 1
        return time_window, False, "请求成功"
        
    def get_request_stats(self) -> Dict[str, Dict]:
        """获取请求统计信息"""
        return {
            "request_counts": self.request_stats,
            "performance": self.performance_stats
        }
        
    def should_adjust_time_window(
        self,
        success_rate: float,
        response_time: float,
        current_window: datetime.timedelta
    ) -> Tuple[bool, str]:
        """
        根据成功率和响应时间决定是否调整时间窗口
        
        Args:
            success_rate: 请求成功率 (0-1)
            response_time: 平均响应时间（秒）
            current_window: 当前时间窗口
            
        Returns:
            Tuple[bool, str]: 
            - 是否应该调整
            - 调整原因
        """
        if success_rate < 0.5 and current_window.total_seconds() > 300:
            return True, "成功率过低"
            
        if response_time > 10 and current_window.total_seconds() > 300:
            return True, "响应时间过长"
            
        return False, "无需调整"

    def record_request(self):
        """记录新的请求"""
        self.request_stats["total_requests"] += 1
        self.current_request_start = time.time()
        self.logger.debug("记录新请求")
        
    def record_success(self):
        """记录请求成功"""
        self.request_stats["successful_requests"] += 1
        self.logger.debug("记录请求成功")
        
    def record_failure(self):
        """记录请求失败"""
        self.request_stats["failed_requests"] += 1
        self.logger.debug("记录请求失败")
        
    def record_retry(self):
        """记录请求重试"""
        self.request_stats["retried_requests"] += 1
        self.logger.debug("记录请求重试")
        
    def record_response_time(self, response_time: float):
        """记录响应时间
        
        Args:
            response_time: 响应时间（秒）
        """
        self.update_performance_stats(response_time)
        self.logger.debug(f"记录响应时间: {response_time:.2f}秒") 