# 独立回放服务极简静态页面模式

import os
import sys
import signal
from flask import Flask, render_template, request
from flask_socketio import SocketIO
from src.play_back.data_service import StockDataService

# 全局变量
npm_integration = None

def setup_npm_integration():
    """设置NPM集成"""
    global npm_integration
    try:
        from src.utils.npm_integration import (
            npm_integration as global_npm_integration, 
            NPMIntegration,
            find_running_vite_server
        )
        
        # 检查是否有正在运行的 Vite 服务器
        existing_port = find_running_vite_server()
        
        if existing_port:
            print(f"✅ 检测到现有的 Vite 服务器 (端口: {existing_port})")
            if not global_npm_integration or not global_npm_integration.npm_enabled:
                # 创建新的 NPMIntegration 实例并设置端口
                npm_integration = NPMIntegration()
                npm_integration.vite_port = existing_port
                npm_integration.npm_enabled = True
            else:
                npm_integration = global_npm_integration
            return True
            
        # 如果没有运行的 Vite 服务器，启动新的
        print("🚀 未检测到运行中的 Vite 服务器，正在启动新实例...")
        if not npm_integration:
            npm_integration = NPMIntegration()
        
        if npm_integration.start_vite_server():
            print(f"✅ Vite 服务器启动成功 (端口: {npm_integration.vite_port})")
            return True
        else:
            print("❌ Vite 服务器启动失败")
            return False
            
    except ImportError as e:
        print(f"⚠️ NPM集成模块不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ 设置NPM集成时出错: {e}")
        return False

# 初始化 NPM 集成
setup_npm_integration()

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
STATIC_FOLDER = os.path.abspath(os.path.join(BASE_DIR, '../web/static'))
TEMPLATE_FOLDER = os.path.abspath(os.path.join(BASE_DIR, '../web/templates'))

app = Flask(__name__, static_folder=STATIC_FOLDER, template_folder=TEMPLATE_FOLDER)

# 设置 Vite 相关配置
if npm_integration and npm_integration.npm_enabled:
    app.config['VITE_PORT'] = npm_integration.vite_port
    print(f"📊 Vite 开发服务器运行在端口 {npm_integration.vite_port}")

# 创建SocketIO实例，参考现有系统的配置
socketio = SocketIO(
    app,
    cors_allowed_origins="*",
    async_mode='threading',  # 使用threading模式，与现有系统保持一致
    logging=False,
    engineio_logger=False
)

data_service = StockDataService()

def register_socketio_events(app: Flask, socketio: SocketIO):
    """统一注册所有WebSocket事件，确保与app/socketio绑定在同一作用域"""
    print("[WS事件注册] register_socketio_events 已调用，app=", app, "socketio=", socketio, "data_service=", data_service)
    from flask import request
    from datetime import datetime
    import time

    @socketio.on('connect')
    def handle_connect(auth=None):
        session_id = request.sid
        print(f"[WS] 客户端已连接: {session_id}")
        socketio.emit('status', {
            'type': 'connected',
            'message': 'WebSocket连接成功',
            'timestamp': time.time()
        }, room=session_id)
        
        socketio.emit('stocks_list', {'stock_codes': []}, room=session_id)
        return True

    @socketio.on('disconnect')
    def handle_disconnect():
        session_id = request.sid
        print(f"[WS] 客户端已断开: {session_id}")

    # 处理完整日线数据请求
    @socketio.on('complete_daily_data')
    def handle_complete_daily_data(data=None):
        session_id = request.sid
        print(f"[WS] 收到完整日线数据请求: {session_id}, data={data}")
        date = None
        if data and isinstance(data, dict) and 'date' in data:
            date = data['date']
        if not date:
            # 默认用今天
            date = datetime.now().strftime('%Y-%m-%d')
        # 加载数据
        if getattr(data_service, 'current_loaded_date', None) != date:
            data_service.load_daily_data(date)
            data_service.current_loaded_date = date
        # 取top5
        end_time = datetime.strptime(f'{date} 16:00', '%Y-%m-%d %H:%M')
        # stocks_list = data_service.get_top5_stocks(end_time)
        # if not stocks_list:
        #     stocks_list = []
        #     print(f"[WS] Top5为空，使用默认股票列表")
        # else:
        #     print(f"[WS] Top5股票: {stocks_list}")
        # # 兼容前端格式
        # # socketio.emit('stocks_list', {'stock_codes': stocks_list}, room=session_id)
        # print(f"[WS] 已发送股票列表: {stocks_list[:5]}{'...' if len(stocks_list) > 5 else ''}")
        
        # 同时发送完整日线数据，使前端能够初始化回放
        try:
            # print(f"[WS] 开始发送完整日线数据: {date}")
            # 默认使用09:30作为开始时间，1000ms作为速度
            start_time = "09:30"
            speed = 1000
            success = data_service._push_complete_daily_data(date, start_time, speed)
            if success:
                print(f"[WS] 完整日线数据发送成功: {date}")
            else:
                print(f"[WS] 完整日线数据发送失败: {date}")
        except Exception as e:
            print(f"[WS] 发送完整日线数据时出错: {e}")
            import traceback
            print(traceback.format_exc())

    @socketio.on('ping')
    def handle_ping():
        session_id = request.sid
        print(f"[WS] 收到ping请求: {session_id}")
        try:
            socketio.emit('pong', {
                'timestamp': datetime.now().isoformat(),
                'session_id': session_id
            }, room=session_id)
            print(f"[WS] 已发送pong响应: {session_id}")
        except Exception as e:
            print(f"[WS] pong发送失败: {e}")

    @socketio.on('test_push')
    def handle_test_push():
        session_id = request.sid
        print(f"[WS] 收到测试推送请求: {session_id}")
        try:
            test_data = {
                'symbol': 'TEST',
                'price': 123.45,
                'change': 2.5,
                'volume': 1000000,
                'timestamp': datetime.now().isoformat()
            }
            socketio.emit('stock_data', test_data, room=session_id)
            print(f"[WS] 已发送测试数据: {test_data}")
        except Exception as e:
            print(f"[WS] 测试数据发送失败: {e}")

    # 已删除start_simulation和stop_simulation事件处理函数，因为前端不再需要订阅

    @socketio.on('start_live_data')
    def handle_start_live_data():
        session_id = request.sid
        print(f"[WS] 收到演示数据启动请求: {session_id}")
        try:
            start_demo_data_push()
            socketio.emit('live_data_status', {'status': 'demo_data_started', 'message': '演示数据推送已启动'}, room=session_id)
        except Exception as e:
            print(f"[WS] 演示数据启动失败: {e}")
            socketio.emit('live_data_status', {'status': 'error', 'msg': str(e)}, room=session_id)

    @socketio.on('stop_live_data')
    def handle_stop_live_data():
        session_id = request.sid
        print(f"[WS] 收到演示数据停止请求: {session_id}")
        try:
            stop_demo_data_push()
            socketio.emit('live_data_status', {'status': 'demo_data_stopped', 'message': '演示数据推送已停止'}, room=session_id)
        except Exception as e:
            print(f"[WS] 演示数据停止失败: {e}")
            socketio.emit('live_data_status', {'status': 'error', 'msg': str(e)}, room=session_id)

data_service.set_socketio(socketio)

# 注册WebSocket事件处理函数
register_socketio_events(app, socketio)

# NPM集成管理器
npm_integration = None

# 数据推送相关
import threading
import time
from datetime import datetime

# 全局变量控制推送状态
demo_push_running = False
demo_push_thread = None

def start_demo_data_push():
    """启动演示数据推送（用于测试WebSocket连接）"""
    global demo_push_running, demo_push_thread
    
    if demo_push_running:
        print("[演示] 演示数据推送已在运行中")
        return
    
    demo_push_running = True
    
    def demo_push_loop():
        print("[演示] 开始演示数据推送循环...")
        counter = 0
        stocks = []
        
        while demo_push_running:
            try:
                counter += 1
                
                for stock in stocks:
                    if not demo_push_running:  # 检查停止标志
                        break
                        
                    demo_data = {
                        'symbol': stock,
                        'price': round(100 + (counter % 50), 2),  # 简单递增模式
                        'change': round((counter % 10) - 5, 2),
                        'volume': 1000000 + (counter * 10000),
                        'timestamp': datetime.now().isoformat()
                    }
                    socketio.emit('stock_data', demo_data)
                
                print(f"[演示] 第{counter}次推送完成，推送了{len(stocks)}只股票的演示数据")
                
                # 每5秒推送一次
                time.sleep(5)
                
            except Exception as e:
                print(f"[演示] 数据推送错误: {e}")
                time.sleep(1)
        
        print("[演示] 演示数据推送已停止")
    
    demo_push_thread = threading.Thread(target=demo_push_loop, daemon=True)
    demo_push_thread.start()
    print("[演示] 演示数据推送已启动")

def stop_demo_data_push():
    """停止演示数据推送"""
    global demo_push_running
    demo_push_running = False
    print("[演示] 正在停止演示数据推送...")

def cleanup_services():
    """清理所有服务"""
    global npm_integration
    if npm_integration:
        print("🛑 停止Vite开发服务器...")
        npm_integration.stop_vite_server()
        print("✅ 所有服务已停止")

@app.route('/')
def index():
    return render_template('index-play-back.html')

@app.route('/old')
def old_index():
    return render_template('index.html')

@app.route('/test-ws')
def test_ws():
    return render_template('test-ws.html')

@app.route('/playback')
def playback_control():
    return render_template('playback-control.html')

if __name__ == '__main__':
    # 设置信号处理器，确保优雅退出
    def signal_handler(sig, frame):
        print("\n🛑 正在停止回放服务...")
        cleanup_services()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    port = int(os.environ.get('PORT', 5001))
    vite_disabled = os.environ.get('VITE_DISABLED', 'false').lower() == 'true'

    # 检查端口占用，避免主副系统冲突
    import socket as _socket
    def is_port_in_use(port):
        s = _socket.socket(_socket.AF_INET, _socket.SOCK_STREAM)
        try:
            s.bind(("127.0.0.1", port))
            s.close()
            return False
        except OSError:
            return True

    if is_port_in_use(port):
        print(f"❌ 端口 {port} 已被占用，请检查主副系统端口设置，避免冲突！")
        sys.exit(1)

    # 只在主进程中启动NPM服务（避免debug模式重复启动）
    if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
        print("=" * 60)
        print("🚀 回放服务启动中...")
        print("=" * 60)
        print("副系统app.py已启动")
        # 启动Vite开发服务器（如果启用）
        if not vite_disabled:
            print("📦 检测NPM集成...")
            if setup_npm_integration() and npm_integration:
                if npm_integration.start_vite_server():
                    pass
                else:
                    print("⚠️ Vite启动失败，使用静态文件服务")
            else:
                print("⚠️ NPM集成不可用，使用静态文件服务")
        else:
            print("📦 Vite已禁用，使用静态文件服务")
        print(f"🚀 Flask回放服务: http://localhost:{port}")
        print("=" * 60)
        print("📋 访问地址:")
        if not vite_disabled and npm_integration and hasattr(npm_integration, 'npm_enabled') and npm_integration.npm_enabled:
            vite_port = getattr(npm_integration, 'vite_port', '3000')
            print(f"  📊 开发模式 (推荐): http://localhost:{vite_port}")
        print(f"  📦 生产模式: http://localhost:{port}")
        print(f"  🔄 传统模式: http://localhost:{port}/old")
        print(f"  🎮 回放控制: http://localhost:{port}/playback")
        print(f"  🧪 WebSocket测试: http://localhost:{port}/test-ws")
        print("=" * 60)
        print("⏱️ 服务运行中，按 Ctrl+C 退出...")

    try:
        # 已在主流程注册，无需重复注册
        print("🎯 回放服务已就绪，请通过WebSocket事件启动数据回放")
        print("   - 使用 'start_simulation' 启动历史数据回放")
        print("   - 访问 /playback 页面进行回放控制")
        # 使用非debug模式避免多进程问题，强制 use_reloader=False
        socketio.run(app, host='0.0.0.0', port=port, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        pass
    finally:
        cleanup_services()

# ===== Flask Error Handlers =====
@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return render_template('404.html') if os.path.exists(os.path.join(TEMPLATE_FOLDER, '404.html')) else "页面未找到", 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return "服务器内部错误", 500

@socketio.on_error_default
def default_error_handler(e):
    """WebSocket默认错误处理"""
    session_id = request.sid
    print(f"[WS错误] {session_id}: {e}")
    # 不要返回任何值，只记录错误

