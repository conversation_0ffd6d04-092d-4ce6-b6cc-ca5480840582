"""
数据服务模块 - 负责从数据库获取数据并进行实时推送
"""
import pandas as pd
import logging
from datetime import datetime, timedelta
from .db import get_engine
from sqlalchemy import text
import threading
import time

class StockDataService:
    """股票数据服务类"""
    
    def __init__(self):
        self.engine = get_engine()
        self.current_simulation_time = None
        self.simulation_date = None
        self.simulation_speed = 1000  # 毫秒间隔
        self.is_running = False
        self.thread = None
        self.socketio = None
        self.daily_data = {}  # 缓存当天的1分钟数据
        self.current_loaded_date = None  # 当前已加载的日期
        self.current_5min_data = {}  # 当前的5分钟K线数据
        
    def set_socketio(self, socketio):
        """设置SocketIO实例"""
        self.socketio = socketio
        print(f"=== SocketIO实例已设置: {self.socketio is not None} ===")
        
    def load_daily_data(self, date_str):
        """一次性加载当天的全部1分钟数据"""
        try:
            logging.info(f"加载 {date_str} 的全部数据...")
            
            sql = text("""
            SELECT 
                stock_code, 
                DATE_SUB(date_time, INTERVAL 1 MINUTE) as date_time, 
                Open, High, Low, Close, Volume
            FROM stock_price_ibkr
            WHERE DATE(date_time) = :date_param AND live_data = 0 
            ORDER BY stock_code, date_time
            """)
            
            df = pd.read_sql(sql, self.engine, params={'date_param': date_str})
            
            if df.empty:
                print(f"=== 没有找到 {date_str} 的数据 ===", flush=True)
                logging.warning(f"没有找到 {date_str} 的数据")
                # 添加调试查询，检查数据库中有哪些日期的数据
                try:
                    debug_sql = text("SELECT DISTINCT DATE(date_time) as date, COUNT(*) as count FROM stock_price_ibkr WHERE live_data = 0 GROUP BY DATE(date_time) ORDER BY date DESC LIMIT 10")
                    debug_df = pd.read_sql(debug_sql, self.engine)
                    print(f"=== 数据库中最近的数据日期: {debug_df.to_dict('records')} ===", flush=True)
                    logging.info(f"数据库中最近的数据日期: {debug_df.to_dict('records')}")
                except Exception as e:
                    print(f"=== 调试查询失败: {e} ===", flush=True)
                    logging.error(f"调试查询失败: {e}")
                return False
            
            # 按股票代码分组存储
            self.daily_data = {}
            for stock_code, group in df.groupby('stock_code'):
                group = group.sort_values('date_time')
                # 确保date_time是字符串格式
                group['date_time'] = group['date_time'].astype(str)
                self.daily_data[stock_code] = group.reset_index(drop=True)
            
            logging.info(f"成功加载 {len(self.daily_data)} 只股票的数据，共 {len(df)} 条记录")
            return True
            
        except Exception as e:
            logging.error(f"加载数据失败: {e}")
            return False
    
    def get_top5_stocks(self, target_time):
        """获取指定时间点的涨幅前5股票"""
        try:
            print(f"=== 计算Top5股票，目标时间: {target_time} ===")
            print(f"=== 可用股票数量: {len(self.daily_data)} ===")
            
            top5_stocks = []
            stock_changes = []
            
            for stock_code, data in self.daily_data.items():
                # 获取到目标时间为止的数据
                # 如果target_time是datetime对象，将其转换为字符串进行比较
                target_time_str = target_time.strftime("%Y-%m-%d %H:%M:%S") if hasattr(target_time, 'strftime') else target_time
                mask = data['date_time'] <= target_time_str
                stock_data = data[mask]
                
                if len(stock_data) < 2:
                    print(f"=== {stock_code}: 数据点不足 ({len(stock_data)}) ===")
                    continue
                
                # 计算涨幅
                open_price = stock_data.iloc[0]['Open']
                close_price = stock_data.iloc[-1]['Close']
                
                if pd.notna(open_price) and pd.notna(close_price) and open_price > 0:
                    change_pct = (close_price - open_price) / open_price
                    stock_changes.append((stock_code, change_pct))
                    # print(f"=== {stock_code}: 涨幅 {change_pct:.2%} ===")
            
            # 按涨幅排序，取前5
            stock_changes.sort(key=lambda x: x[1], reverse=True)
            top5_stocks = [stock[0] for stock in stock_changes[:5]]
            
            print(f"=== Top5股票计算完成: {top5_stocks} ===")
            logging.info(f"Top5股票 ({target_time}): {top5_stocks}")
            return top5_stocks
            
        except Exception as e:
            logging.error(f"获取Top5股票失败: {e}")
            return []
    
    def _push_complete_daily_data(self, date_str, start_time="09:30", speed=1000):
        """一次性推送指定日期的完整数据供前端模拟时间流逝"""
        try:
            # print(f"=== 开始构建 {date_str} 的完整数据包 ===")
            
            # 获取当天结束时间，以获取完整数据
            end_time = datetime.strptime(f"{date_str} 16:00", "%Y-%m-%d %H:%M")
            
            # 获取Top5股票（基于全天数据）
            top5_stocks = self.get_top5_stocks(end_time)
            # print(f"=== Top5股票: {top5_stocks} ===")

            if not top5_stocks:
                print(f"=== 没有找到有效的Top5股票数据 ===")
                return False

            # 为每只Top5股票收集完整的1分钟原始数据
            complete_stocks_data = {}
            
            for stock_code in top5_stocks:
                # print(f"=== 处理股票 {stock_code} 的完整K线数据 ===")
                
                # 获取股票的原始1分钟K线数据
                if stock_code not in self.daily_data:
                    print(f"=== 股票 {stock_code} 不在daily_data中 ===")
                    continue
                    
                data = self.daily_data[stock_code]
                # 获取到目标时间为止的数据
                # 如果end_time是datetime对象，将其转换为字符串进行比较
                end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S") if hasattr(end_time, 'strftime') else end_time
                mask = data['date_time'] <= end_time_str
                stock_data = data[mask].copy()
                
                if stock_data.empty:
                    print(f"=== 股票 {stock_code} 在 {end_time} 之前无数据 ===")
                    continue
                
                # 转换为图表需要的格式
                chart_data = []
                for _, row in stock_data.iterrows():
                    chart_data.append({
                        'time': str(row['date_time']),  # 确保time是字符串格式
                        'open': float(row['Open']),
                        'high': float(row['High']),
                        'low': float(row['Low']),
                        'close': float(row['Close']),
                        'volume': float(row['Volume'])
                    })
                
                if chart_data:
                    # 按时间排序
                    chart_data.sort(key=lambda x: x['time'])
                    complete_stocks_data[stock_code] = chart_data
                    # print(f"=== 股票 {stock_code}: {len(chart_data)} 个1分钟K线 ===")
                else:
                    print(f"=== 股票 {stock_code}: 无有效数据 ===")

            # 通过WebSocket推送完整数据包
            if self.socketio and complete_stocks_data:
                # print(f"=== 推送完整数据包到前端 ===")
                
                # 构建完整数据包
                complete_data_package = {
                    'type': 'complete_daily_data',
                    'date': date_str,
                    'start_time': start_time,
                    'speed_ms': speed,
                    'top5_stocks': top5_stocks,
                    'stocks_data': complete_stocks_data,
                    'data_info': {
                        'total_stocks': len(complete_stocks_data),
                        'total_candles': sum(len(data) for data in complete_stocks_data.values()),
                        'time_range': {
                            'start': str(min([data[0]['time'] for data in complete_stocks_data.values() if data])),
                            'end': str(max([data[-1]['time'] for data in complete_stocks_data.values() if data]))
                        } if complete_stocks_data else None
                    },
                    'instructions': {
                        'message': '前端可基于此数据模拟时间流逝',
                        'usage': '使用speed_ms控制回放速度，按时间戳顺序显示K线'
                    }
                }
                
                try:
                    # 发送到所有连接的客户端
                    self.socketio.emit('complete_daily_data', complete_data_package, namespace='/')
                    # print(f"=== 完整数据包推送成功: {len(complete_stocks_data)} 只股票 ===")
                    
                    # 同时发送状态更新
                    self.socketio.emit('simulation_status', {
                        'status': 'data_loaded',
                        'message': f'已加载 {date_str} 完整数据，前端可开始模拟',
                        'data_summary': {
                            'stocks': len(complete_stocks_data),
                            'candles': sum(len(data) for data in complete_stocks_data.values())
                        }
                    }, namespace='/')
                    
                    return True
                    
                except Exception as e:
                    print(f"=== 数据包推送失败: {e} ===")
                    import traceback
                    print(f"=== 错误详情: {traceback.format_exc()} ===")
                    return False
            else:
                print(f"=== 推送失败：socketio={self.socketio is not None}, data={len(complete_stocks_data)} ===")
                return False

        except Exception as e:
            logging.error(f"构建完整数据包失败: {e}")
            print(f"=== 构建完整数据包失败: {e} ===")
            import traceback
            logging.error(traceback.format_exc())
            return False
    
    # stop_simulation函数已删除，因为前端不再需要订阅

# 全局数据服务实例
data_service = StockDataService()
print(f"[DEBUG] StockDataService实例已创建: {data_service}")
print(f"[DEBUG] 实例ID: {id(data_service)}")
print(f"[DEBUG] 实例引擎: {data_service.engine}")
