from sqlalchemy import create_engine, text
import pandas as pd
import logging
from datetime import datetime
from .config import Config

# 全局数据库引擎实例
_engine = None

def get_engine():
    """获取数据库引擎，使用单例模式"""
    global _engine
    if _engine is None:
        try:
            _engine = create_engine(
                Config.DATABASE_URL,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,  # 自动重连
                echo=False  # 设为True可以看到SQL日志
            )
            logging.info("数据库连接已建立")
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            raise
    return _engine

def get_1min_data(date_str):
    """获取指定日期的1分钟K线数据"""
    try:
        engine = get_engine()

        # 验证日期格式
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            raise ValueError(f"日期格式错误，应为YYYY-MM-DD格式: {date_str}")

        sql = text("""
        SELECT stock_code, date_time, Open, High, Low, Close, Volume
        FROM stock_price_ibkr
        WHERE DATE(date_time) = :date_param AND live_data = 0
        ORDER BY stock_code, date_time
        """)

        df = pd.read_sql(sql, engine, params={'date_param': date_str})

        if df.empty:
            logging.warning(f"没有找到日期 {date_str} 的数据")
            # 添加调试查询，检查数据库中有哪些日期的数据
            try:
                debug_sql = text("SELECT DISTINCT DATE(date_time) as date, COUNT(*) as count FROM stock_price_ibkr WHERE live_data = 0 GROUP BY DATE(date_time) ORDER BY date DESC LIMIT 10")
                debug_df = pd.read_sql(debug_sql, engine)
                logging.info(f"数据库中最近的数据日期: {debug_df.to_dict('records')}")
            except Exception as e:
                logging.error(f"调试查询失败: {e}")
            return pd.DataFrame()

        # 确保date_time列是datetime类型
        df['date_time'] = pd.to_datetime(df['date_time'])

        logging.info(f"成功获取 {date_str} 的数据，共 {len(df)} 条记录")
        return df

    except Exception as e:
        logging.error(f"获取1分钟数据失败: {e}")
        return pd.DataFrame()

def resample_5min(df, end_time=None):
    """将1分钟数据重采样为5分钟数据"""
    try:
        if df.empty:
            return pd.DataFrame()

        # end_time: 模拟当前推进到的时间，只取<=end_time的数据
        dfs = []
        for code, group in df.groupby('stock_code'):
            group = group.set_index('date_time').sort_index()

            if end_time is not None:
                # 确保end_time是datetime类型
                if isinstance(end_time, str):
                    end_time = pd.to_datetime(end_time)
                group = group[group.index <= end_time]

            if group.empty:
                continue

            # 重采样OHLC数据
            ohlc = group[['Open', 'High', 'Low', 'Close']].resample('5min').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last'
            })

            # 重采样成交量数据
            vol = group['Volume'].resample('5min').sum()
            ohlc['Volume'] = vol
            ohlc['stock_code'] = code

            # 移除包含NaN的行（没有数据的时间段）
            ohlc = ohlc.dropna()

            if not ohlc.empty:
                dfs.append(ohlc.reset_index())

        if dfs:
            result = pd.concat(dfs, ignore_index=True)
            logging.info(f"重采样完成，共 {len(result)} 条5分钟数据")
            return result
        else:
            return pd.DataFrame()

    except Exception as e:
        logging.error(f"重采样5分钟数据失败: {e}")
        return pd.DataFrame()

def get_top5_codes(df_5min):
    """获取涨幅前5的股票代码"""
    try:
        if df_5min.empty:
            return []

        # 取开盘和收盘，计算涨幅
        result = []
        for code, group in df_5min.groupby('stock_code'):
            group = group.sort_values('date_time')
            if len(group) < 1:
                continue

            open_ = group.iloc[0]['Open']
            close_ = group.iloc[-1]['Close']

            # 验证数据有效性
            if pd.isna(open_) or pd.isna(close_) or open_ <= 0:
                continue

            change = (close_ - open_) / open_
            result.append((code, change))

        # 按涨幅排序，取前5
        top5 = sorted(result, key=lambda x: x[1], reverse=True)[:5]
        codes = [x[0] for x in top5]

        logging.info(f"获取Top5股票: {codes}")
        return codes

    except Exception as e:
        logging.error(f"获取Top5股票失败: {e}")
        return []

