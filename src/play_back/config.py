"""
配置文件
"""
import os
from pathlib import Path

class Config:
    """基础配置"""
    # 数据库配置
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = os.getenv('DB_PORT', 3306)
    DB_USER = os.getenv('DB_USER', 'root')
    DB_PASSWORD = os.getenv('DB_PASSWORD', 'admin1234')
    DB_NAME = os.getenv('DB_NAME', 'stock_trand')
    
    # 数据库连接字符串
    DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset=utf8mb4"
    
    # Flask配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    PORT = int(os.getenv('FLASK_PORT', 5001))  # 默认使用5001端口避免与AirPlay冲突
    HOST = os.getenv('HOST', '0.0.0.0')
    
    # Vite集成配置
    VITE_ENABLED = not os.getenv('VITE_DISABLED', 'false').lower() == 'true'
    VITE_PORT = int(os.getenv('VITE_PORT', 3000))
    
    # 回放配置
    DEFAULT_SIMULATION_SPEED = int(os.getenv('DEFAULT_SIMULATION_SPEED', 1000))  # 毫秒
    MAX_SIMULATION_SPEED = int(os.getenv('MAX_SIMULATION_SPEED', 100))  # 最快100ms
    MIN_SIMULATION_SPEED = int(os.getenv('MIN_SIMULATION_SPEED', 5000))  # 最慢5秒
    
    # 性能配置
    MAX_CACHED_DATES = int(os.getenv('MAX_CACHED_DATES', 5))  # 最大缓存日期数
    ENABLE_PERFORMANCE_MONITORING = os.getenv('ENABLE_PERFORMANCE_MONITORING', 'true').lower() == 'true'
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 路径配置
    BASE_DIR = Path(__file__).parent
    STATIC_FOLDER = BASE_DIR / '../web/static'
    TEMPLATE_FOLDER = BASE_DIR / '../web/templates'
    
    # 预设回放速度（毫秒）
    SPEED_PRESETS = {
        'ultra_fast': 100,    # 超快速
        'fast': 250,          # 快速
        'normal': 1000,       # 正常
        'slow': 2000,         # 慢速
        'very_slow': 5000     # 超慢速
    }
    
    # 应用配置
    ITEMS_PER_PAGE = int(os.getenv('ITEMS_PER_PAGE', 20))
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    @classmethod
    def get_vite_target_url(cls):
        """获取Vite代理目标URL"""
        return f"http://localhost:{cls.PORT}"
    
    @classmethod
    def get_speed_preset(cls, preset_name):
        """获取预设速度"""
        return cls.SPEED_PRESETS.get(preset_name, cls.DEFAULT_SIMULATION_SPEED)
    
    @classmethod
    def validate_speed(cls, speed):
        """验证速度值是否在允许范围内"""
        if speed < cls.MAX_SIMULATION_SPEED:
            return cls.MAX_SIMULATION_SPEED
        elif speed > cls.MIN_SIMULATION_SPEED:
            return cls.MIN_SIMULATION_SPEED
        return speed
    
    @classmethod
    def to_dict(cls):
        """将配置转换为字典"""
        return {
            'port': cls.PORT,
            'debug': cls.DEBUG,
            'vite_enabled': cls.VITE_ENABLED,
            'vite_port': cls.VITE_PORT,
            'default_speed': cls.DEFAULT_SIMULATION_SPEED,
            'speed_presets': cls.SPEED_PRESETS,
            'performance_monitoring': cls.ENABLE_PERFORMANCE_MONITORING
        }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
