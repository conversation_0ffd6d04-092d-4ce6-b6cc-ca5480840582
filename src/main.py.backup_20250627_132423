import multiprocessing
import platform
import threading
import logging.config
import sys
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

from futu import OpenQuoteContext, OpenSecTradeContext, TrdMarket, SecurityFirm
from src.config.config import FUTU_CONFIG, PATHS, LOG_CONFIG, WEB_CONFIG
from src.gui.stock_watch_app import StockWatchApp
from src.api.get_stock_price_ibapi import main as get_stock_price_main
from src.web.web_server import start_web_server_thread
from src.data.data_manager_adapter import manager_res_dict_adapter as manager_res_dict

"""
股票监控应用程序的主入口模块

本模块负责初始化和启动整个股票监控应用程序，主要功能包括：
1. 初始化日志系统，确保程序运行日志的正确记录
2. 设置多进程环境，适配不同操作系统
3. 初始化声音系统，用于重要事件的提醒
4. 启动数据获取线程，实时获取股票市场数据
5. 运行GUI应用程序，展示股票信息

主要组件：
- 日志系统：使用Python标准库logging模块
- 多进程支持：使用multiprocessing模块
- 声音系统：使用pygame模块
- 富途API：使用futu-api包
- GUI：使用tkinter构建

依赖：
- multiprocessing
- platform
- threading
- pygame
- logging
- futu
- tkinter
"""

def setup_logging():
    """配置日志系统
    
    使用 dictConfig 配置日志系统，提供更灵活和集中的日志管理。
    确保日志文件目录存在，并正确设置权限。
    
    配置内容：
    - 文件处理器：记录详细日志到文件
    - 控制台处理器：显示基本信息
    - 格式化器：定义日志格式
    - 日志级别：设置不同模块的日志级别
    
    异常：
        如果日志系统初始化失败，程序将终止运行
    """
    try:
        # 确保日志目录存在
        log_path = Path(LOG_CONFIG['handlers']['file']['filename']).parent
        log_path.mkdir(parents=True, exist_ok=True)
        
        # 配置日志系统
        logging.config.dictConfig(LOG_CONFIG)
        
        # 记录初始化信息
        logging.info('日志系统初始化完成')
        logging.info(f'日志文件路径: {LOG_CONFIG["handlers"]["file"]["filename"]}')
        
    except Exception as e:
        print(f'日志系统初始化失败: {e}')
        sys.exit(1)

def init_sound():
    """初始化声音系统
    
    加载警告和信号音效文件路径
    
    Returns:
        tuple: (sound_warn, sound_signal) 警告和信号音效文件路径
        
    Raises:
        Exception: 如果声音系统初始化失败
    """
    try:
        sound_warn = PATHS['WARN_SOUND']
        sound_signal = PATHS['SIGNAL_SOUND']
        return sound_warn, sound_signal
    except Exception as e:
        logging.error(f'声音系统初始化失败: {e}')
        raise

def init_futu_context():
    """初始化富途API上下文
    
    创建并返回富途API的行情上下文对象，用于更新富途自选股票列表。
    
    Returns:
        OpenQuoteContext: 富途API行情上下文对象
        
    Raises:
        Exception: 如果富途API初始化失败
    """
    try:
        return OpenQuoteContext(
            host=FUTU_CONFIG['HOST'],
            port=FUTU_CONFIG['PORT']
        )
    except Exception as e:
        logging.error(f'富途API初始化失败: {e}')
        raise

def get_stock_data(quote_ctx, sound_warn, sound_signal, web_stock_data):
    """获取股票数据的后台进程

    这个函数在独立的进程中运行，负责：
    1. 初始化交易上下文
    2. 获取实时股票数据
    3. 处理数据并更新共享字典
    4. 在发生异常时进行清理

    Args:
        quote_ctx (OpenQuoteContext): 行情上下文对象
        sound_warn (str): 警告音效文件路径
        sound_signal (str): 信号音效文件路径
        web_stock_data (dict): Web股票数据字典
    """
    sim_trd_ctx = None
    try:
        sim_trd_ctx = OpenSecTradeContext(
            filter_trdmarket=TrdMarket.US,
            host=FUTU_CONFIG['HOST'],
            port=FUTU_CONFIG['PORT'],
            security_firm=SecurityFirm.FUTUSECURITIES
        )

        get_stock_price_main(quote_ctx, sim_trd_ctx, sound_warn, sound_signal, web_stock_data)
        print('--------------------------------------------退出程序--------------------------------------------')
        manager_res_dict[0] = 0

    except Exception as e:
        print(f'获取股票数据时发生错误: {e}')
        logging.error(f'获取股票数据时发生错误: {e}', exc_info=True)

    finally:
        if quote_ctx:
            quote_ctx.close()
        if sim_trd_ctx:
            sim_trd_ctx.close()
        logging.info("当前活动线程: %s", threading.enumerate())

def main():
    """主程序入口
    
    处理程序的整体流程，包括：
    1. 初始化日志系统
    2. 设置多进程模式
    3. 初始化声音系统
    4. 启动数据获取线程
    5. 运行GUI应用
    
    异常处理：
    - KeyboardInterrupt: 用户中断程序
    - 其他异常：记录错误并尝试清理资源
    
    清理过程：
    1. 关闭线程池
    2. 清理pygame资源
    3. 关闭富途API连接
    4. 记录最终状态
    """
    # 首先设置日志系统
    setup_logging()
    
    thread_executor = None
    quote_ctx = None
    
    try:
        # 在 macOS 上使用 spawn 模式启动新进程
        if platform.system() == "Darwin":
            multiprocessing.set_start_method('spawn', force=True)
            
        # 初始化声音
        sound_warn, sound_signal = init_sound()
        # 初始化富途API上下文
        quote_ctx = init_futu_context()
        
        # 初始化共享数据 - 使用统一的数据管理器适配器
        manager_res_dict[0] = 1   # 程序运行状态
        manager_res_dict[1] = []  # 股票列表

        # 初始化Web股票数据字典
        web_stock_data = {}

        # 设置最大工作线程数并启动数据获取线程
        thread_executor = ThreadPoolExecutor(max_workers=1)
        thread_executor.submit(
            get_stock_data,
            quote_ctx,
            sound_warn,
            sound_signal,
            web_stock_data
        )

        # 启动Web服务器线程
        if WEB_CONFIG['ENABLED']:
            try:
                start_web_server_thread(
                    host=WEB_CONFIG['HOST'],
                    port=WEB_CONFIG['PORT'],
                    debug=WEB_CONFIG['DEBUG'],
                    web_stock_data=web_stock_data,
                    enable_websocket=False
                )
                logging.info(f"Web服务器已启动: http://{WEB_CONFIG['HOST']}:{WEB_CONFIG['PORT']}")
                print(f"Web服务器已启动: http://localhost:{WEB_CONFIG['PORT']}")
            except Exception as e:
                logging.error(f"Web服务器启动失败: {e}")
                print(f"Web服务器启动失败: {e}")
        else:
            logging.info("Web服务器已禁用")

        # 启动GUI应用
        app = StockWatchApp(sound_warn, manager_res_dict)
        app.run()
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        logging.info("程序被用户中断")
    except Exception as e:
        print(f'主程序发生错误: {e}')
        logging.error(f'主程序发生错误: {e}', exc_info=True)
    finally:
        # 清理资源
        if thread_executor:
            thread_executor.shutdown(wait=True)
        if quote_ctx:
            quote_ctx.close()
            
        logging.info("当前活动线程: %s", threading.enumerate())
        print("程序正常退出")
        logging.info("程序正常退出")

if __name__ == "__main__":
    main() 