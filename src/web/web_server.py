"""
Web服务器模块
提供股票数据的Web接口，集成到src/main.py中
"""
from flask import Flask, jsonify, render_template, request
import threading
import time
import logging
import pandas as pd
from datetime import datetime
import logging as werkzeug_logging
import mimetypes
import traceback

from src.web.websocket_server import init_websocket_server

# 导入数据管理器和相关工具
from src.data.data_manager_adapter import (
    manager_res_dict_adapter as manager_res_dict,
    manager_res_dict_test_adapter as manager_res_dict_test
)
from src.utils.constants import NUMBER_FLG, HISTORICAL_DATA_END, NY_TZ
from src.utils.key_generator import key_generator

# 全局变量存储web_stock_data引用
_web_stock_data = {}
"""web_stock_data"""

def format_volume(volume):
    """
    格式化成交量，添加单位（K、M、G），交易次数不显示小数点。

    Args:
        volume (float): 累计成交量。

    Returns:
        str: 格式化后的字符串，例如 "123K", "1M", "123G"。
    """
    if volume is None:
        return float('inf')
    if volume >= 1_000_000_000:  # 大于等于十亿
        return f"{volume / 1_000_000_000:.0f}G"
    elif volume >= 1_000_000:  # 大于等于百万
        return f"{volume / 1_000_000:.0f}M"
    elif volume >= 1_000:  # 大于等于千
        return f"{volume / 1_000:.0f}K"
    else:
        return f"{volume:.0f}"

def parse_datetime_string(date_time_str):
    """
    解析各种格式的时间字符串，返回Unix时间戳（纽约时间）

    支持的格式：
    - "20250623 14:31:00 US/Eastern"
    - "20250623 14:31:00"
    - "2025-06-23 14:31:00"
    """
    try:
        # 处理包含时区信息的格式
        if ' US/Eastern' in date_time_str:
            date_time_str = date_time_str.replace(' US/Eastern', '')
        elif ' America/New_York' in date_time_str:
            date_time_str = date_time_str.replace(' America/New_York', '')

        # 检查并修复无效的时间格式（如分钟数>=60）
        if ' ' in date_time_str and ':' in date_time_str:
            try:
                date_part, time_part = date_time_str.split(' ', 1)
                if ':' in time_part:
                    time_components = time_part.split(':')
                    if len(time_components) >= 2:
                        hour = int(time_components[0])
                        minute = int(time_components[1])
                        second = int(time_components[2]) if len(time_components) > 2 else 0

                        # 修复无效的分钟数
                        if minute >= 60:
                            hour += minute // 60
                            minute = minute % 60

                        # 修复无效的小时数
                        if hour >= 24:
                            hour = hour % 24

                        # 重构时间字符串
                        date_time_str = f"{date_part} {hour:02d}:{minute:02d}:{second:02d}"
            except (ValueError, IndexError):
                pass  # 如果解析失败，继续使用原始字符串

        # 尝试不同的时间格式
        formats = [
            "%Y%m%d %H:%M:%S",      # IB API格式: "20250623 14:31:00"
            "%Y-%m-%d %H:%M:%S",    # 标准格式: "2025-06-23 14:31:00"
            "%Y/%m/%d %H:%M:%S",    # 斜杠格式: "2025/06/23 14:31:00"
        ]

        for fmt in formats:
            try:
                # 解析为naive datetime
                naive_dt = datetime.strptime(date_time_str, fmt)
                # 将其视为纽约时间
                ny_dt = NY_TZ.localize(naive_dt)
                # 转换为UTC时间戳
                return int(ny_dt.timestamp())
            except ValueError:
                continue

        # 如果所有格式都失败，尝试pandas的智能解析
        try:
            dt = pd.to_datetime(date_time_str)
            # 如果没有时区信息，假设为纽约时间
            if dt.tz is None:
                dt = NY_TZ.localize(dt)
            return int(dt.timestamp())
        except Exception:
            pass

        # 最后的备选方案：返回当前时间戳
        logging.error(f"无法解析时间字符串: {date_time_str}")
        return int(time.time())

    except Exception as e:
        print(f"=== [WebServer] Web服务器启动异常: {e}, 时间: {time.time()} ===", flush=True)
        logging.error(f"解析时间字符串时发生错误: {date_time_str}, 错误: {e}")
        return int(time.time())

def create_web_app(web_stock_data, enable_websocket=True):
    """创建Flask应用，可选择启用WebSocket支持"""
    global _web_stock_data
    _web_stock_data = web_stock_data

    app = Flask(__name__,
                template_folder='templates',
                static_folder='static')
    
    # 配置ES模块的MIME类型支持
    mimetypes.add_type('application/javascript', '.js')
    mimetypes.add_type('text/javascript', '.mjs')
    
    # 添加ES模块静态文件支持
    @app.after_request
    def after_request(response):
        try:
            # 为ES模块文件设置正确的MIME类型
            if response.content_type and response.content_type.startswith('text/html'):
                response.headers['X-Version'] = 'ES-Module-Support'
            # 只有在 request 上下文可用时才处理 JS 文件
            if hasattr(request, 'path') and request.path and request.path.endswith('.js') and '/modules/' in request.path:
                response.headers['Content-Type'] = 'application/javascript; charset=utf-8'
        except Exception as e:
            # 如果出现任何错误，记录但不中断响应
            logging.warning(f"after_request 处理失败: {e}")
        finally:
            # 确保总是返回响应对象
            return response
    
    # 添加WebSocket支持
    websocket_server = None
    if enable_websocket:
        try:
            # 创建一个简单的数据管理器包装器
            class WebDataManager:
                def __init__(self, data_dict):
                    self.data = data_dict
                
                def get_all_stock_data(self):
                    """获取所有股票数据"""
                    return self.data
                
                def get_stock_data(self, symbol):
                    """获取指定股票数据"""
                    return self.data.get(symbol, {})
            
            data_manager = WebDataManager(web_stock_data)
            websocket_server = init_websocket_server(app, data_manager)
            # 将socketio实例附加到app上，便于启动时使用
            app.socketio = websocket_server.socketio
            logging.info("WebSocket服务器已集成到Flask应用")
        except ImportError as e:
            logging.warning(f"无法导入WebSocket服务器: {e}")
            enable_websocket = False
        except Exception as e:
            logging.error(f"WebSocket服务器初始化失败: {e}")
            enable_websocket = False

    @app.route('/')
    def index():
        """主页 - 默认版本"""
        return render_template('index.html')  # 原始UMD版本
    
    @app.route('/npm')
    def index_npm():
        """NPM ES模块版本"""
        return render_template('index-npm.html')

    @app.route('/data')
    def get_data():
        """获取股票数据 - 兼容stock_web格式，支持symbol参数"""
        try:
            # 获取symbol参数
            symbol = request.args.get('symbol')
            candles = []

            # 调试信息：记录数据获取请求
            current_time = datetime.now().strftime('%H:%M:%S')

            if _web_stock_data:
                if symbol:
                    # 获取指定股票的数据
                    stock_data = _web_stock_data.get(symbol, {})
                    if not stock_data:
                        logging.debug(f"[{current_time}] 股票 {symbol} 无数据")
                else:
                    # 获取第一个股票的数据作为默认
                    stock_data = next(iter(_web_stock_data.values()), {})
                    if stock_data:
                        symbol = next(iter(_web_stock_data.keys()), 'Unknown')

                if stock_data and isinstance(stock_data, dict):
                    # req_data是字典结构：{date_time: bar_data}
                    # 按时间排序并取最后300条记录
                    sorted_dates = sorted(stock_data.keys())
                    latest_data_time = sorted_dates[-1] if sorted_dates else 'None'

                    # 调试信息：记录最新数据时间
                    logging.debug(f"[{current_time}] 股票 {symbol}: 数据条数={len(sorted_dates)}, 最新时间={latest_data_time}")

                    for date_str in sorted_dates[-1000:]:
                        bar_data = stock_data[date_str]
                        try:
                            candle = {
                                "time": parse_datetime_string(bar_data["date_time"]),
                                "open": round(float(bar_data["Open"]), 2),
                                "high": round(float(bar_data["High"]), 2),
                                "low": round(float(bar_data["Low"]), 2),
                                "close": round(float(bar_data["Close"]), 2),
                                "volume": round(float(bar_data["Volume"]), 0)
                            }
                            candles.append(candle)
                        except (KeyError, ValueError, TypeError) as e:
                            logging.warning(f"跳过无效数据: {bar_data}, 错误: {e}")
                            continue
            else:
                logging.debug(f"[{current_time}] _web_stock_data 为空")

            return jsonify(candles)
        except Exception as e:
            logging.error(f"获取股票数据失败: {e}")
            return jsonify([])
    
    @app.route('/stocks')
    def get_all_stocks():
        """获取所有股票代码"""
        try:
            # 动态获取最新的股票列表
            codes = manager_res_dict.get(1, [])
            return jsonify({"stock_codes": codes, "count": len(codes)})
        except Exception as e:
            logging.error(f"获取股票代码失败: {e}")
            return jsonify({"stock_codes": [], "count": 0})

    @app.route('/stock/<stock_code>')
    def get_stock_data(stock_code):
        """获取指定股票的数据"""
        try:
            stock_data = _web_stock_data.get(stock_code, {})
            candles = []
            if stock_data and isinstance(stock_data, dict):
                # req_data是字典结构：{date_time: bar_data}
                # 按时间排序并取最后300条记录
                sorted_dates = sorted(stock_data.keys())
                for date_str in sorted_dates[-300:]:
                    bar_data = stock_data[date_str]
                    try:
                        candle = {
                            "time": parse_datetime_string(bar_data["date_time"]),
                            "open": round(float(bar_data["Open"]), 2),
                            "high": round(float(bar_data["High"]), 2),
                            "low": round(float(bar_data["Low"]), 2),
                            "close": round(float(bar_data["Close"]), 2)
                        }
                        candles.append(candle)
                    except (KeyError, ValueError, TypeError) as e:
                        logging.warning(f"跳过无效数据: {bar_data}, 错误: {e}")
                        continue
            return jsonify(candles)
        except Exception as e:
            logging.error(f"获取股票 {stock_code} 数据失败: {e}")
            return jsonify([])

    @app.route('/overview')
    def get_overview():
        """获取股票概览数据"""
        try:
            overview_data = []
            for stock_code, stock_data in _web_stock_data.items():
                if stock_data and isinstance(stock_data, dict) and len(stock_data) > 0:
                    try:
                        # 获取最新数据
                        latest_date = max(stock_data.keys())
                        latest_bar = stock_data[latest_date]

                        # 计算涨跌幅（需要前一个数据点）
                        sorted_dates = sorted(stock_data.keys())
                        change_percent = 0
                        if len(sorted_dates) >= 2:
                            prev_bar = stock_data[sorted_dates[-2]]
                            current_close = float(latest_bar["Close"])
                            prev_close = float(prev_bar["Close"])
                            if prev_close != 0:
                                change_percent = ((current_close - prev_close) / prev_close) * 100

                        overview_data.append({
                            "symbol": stock_code,
                            "price": round(float(latest_bar["Close"]), 2),
                            "change": round(change_percent, 2),
                            "volume": int(latest_bar.get("Volume", 0)),
                            "last_update": parse_datetime_string(latest_bar["date_time"])
                        })
                    except (ValueError, KeyError, TypeError) as e:
                        logging.warning(f"处理股票 {stock_code} 概览数据失败: {e}")
                        continue

            return jsonify(overview_data)
        except Exception as e:
            logging.error(f"获取股票概览失败: {e}")
            return jsonify([])

    @app.route('/transactions')
    def get_transactions():
        """获取所有股票的每分钟交易次数"""
        try:
            transactions_data = {}
            # 获取当前显示的股票列表
            codes = manager_res_dict.get(1, [])

            for stock_code in codes:
                try:
                    # 使用key_generator获取键值
                    keys = key_generator.get_keys_for_stock(stock_code)
                    # 从manager_res_dict_test_adapter获取交易次数
                    transaction_count = manager_res_dict_test.get(keys[NUMBER_FLG], 0)
                    raw_count = int(transaction_count) if transaction_count is not None else 0
                    # 使用format_volume格式化交易次数
                    formatted_count = format_volume(raw_count)
                    transactions_data[stock_code] = formatted_count
                except Exception as e:
                    logging.warning(f"获取股票 {stock_code} 交易次数失败: {e}")
                    transactions_data[stock_code] = "0"

            return jsonify(transactions_data)
        except Exception as e:
            logging.error(f"获取交易次数数据失败: {e}")
            return jsonify({})

    @app.route('/status')
    def get_status():
        """获取系统状态"""
        try:
            # 计算最后更新时间
            last_update = 0
            for stock_data in _web_stock_data.values():
                if stock_data and isinstance(stock_data, dict) and len(stock_data) > 0:
                    # req_data是字典结构，获取最新时间的数据
                    try:
                        latest_date = max(stock_data.keys())
                        latest_bar = stock_data[latest_date]
                        if "date_time" in latest_bar:
                            timestamp = parse_datetime_string(latest_bar["date_time"])
                            last_update = max(last_update, timestamp)
                    except (ValueError, KeyError, TypeError):
                        pass

            return jsonify({
                "status": "running",
                "stock_count": len(manager_res_dict.get(1, [])),
                "last_update": last_update,
                "timestamp": time.time()
            })
        except Exception as e:
            logging.error(f"获取系统状态失败: {e}")
            return jsonify({"status": "error", "message": str(e)})

    @app.route('/debug')
    def debug_info():
        """调试信息端点"""
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            debug_data = {
                "current_time": current_time,
                "web_stock_data_status": {
                    "exists": _web_stock_data is not None,
                    "stock_count": len(_web_stock_data) if _web_stock_data else 0,
                    "stock_codes": list(_web_stock_data.keys()) if _web_stock_data else []
                },
                "latest_data_times": {}
            }

            # 获取每个股票的最新数据时间
            if _web_stock_data:
                for stock_code, stock_data in _web_stock_data.items():
                    if stock_data and isinstance(stock_data, dict):
                        sorted_dates = sorted(stock_data.keys())
                        if sorted_dates:
                            latest_time = sorted_dates[-1]
                            data_count = len(sorted_dates)
                            debug_data["latest_data_times"][stock_code] = {
                                "latest_time": latest_time,
                                "data_count": data_count
                            }

            return jsonify(debug_data)
        except Exception as e:
            logging.error(f"获取调试信息失败: {e}")
            return jsonify({"error": str(e)})

    @app.route('/historical-data-end/<stock_code>')
    def get_historical_data_end_status(stock_code):
        """获取股票历史数据加载状态"""
        try:
            keys = key_generator.get_keys_for_stock(stock_code)
            return jsonify(manager_res_dict_test.get(keys[HISTORICAL_DATA_END], 0) == 1)
        except Exception as e:
            logging.error(f"获取股票 {stock_code} 历史数据状态失败: {e}")
            return jsonify(False)

    # 添加错误处理器
    @app.errorhandler(Exception)
    def handle_exception(e):
        """处理所有未捕获的异常"""
        logging.error(f"未处理的异常: {e}", exc_info=True)
        try:
            return jsonify({"error": "服务器内部错误", "message": str(e)}), 500
        except Exception as fallback_error:
            # 如果连 JSON 响应都失败，返回最简单的响应
            logging.error(f"错误处理器也失败了: {fallback_error}")
            return "Internal Server Error", 500
    
    @app.errorhandler(404)
    def handle_404(e):
        """处理404错误"""
        logging.warning(f"404错误: {request.url if hasattr(request, 'url') else 'unknown URL'}")
        return jsonify({"error": "页面未找到", "status": 404}), 404
    
    @app.errorhandler(500)
    def handle_500(e):
        """处理500错误"""
        logging.error(f"500错误: {e}")
        return jsonify({"error": "服务器内部错误", "status": 500}), 500

    return app

def start_web_server(host='0.0.0.0', port=5001, debug=False, web_stock_data={}, enable_websocket=True):
    print(f"=== [WebServer] 开始启动Web服务器，线程: {threading.current_thread().name}, 时间: {time.time()} ===", flush=True)
    """启动Web服务器，支持WebSocket - 非阻塞运行"""
    try:
        app = create_web_app(web_stock_data, enable_websocket)
        logging.info(f"启动Web服务器: http://{host}:{port}")

        # 禁用werkzeug的HTTP请求日志
        werkzeug_log = werkzeug_logging.getLogger('werkzeug')
        werkzeug_log.setLevel(werkzeug_logging.ERROR)

        # 强制禁用debug模式以避免阻塞，无论传入什么值
        force_debug = False
        if debug:
            logging.info("为避免阻塞，强制禁用debug模式")

        if enable_websocket and hasattr(app, 'socketio'):
            print(f"=== [WebServer] 准备启动SocketIO服务器，时间: {time.time()} ===", flush=True)
            # 使用SocketIO运行，确保完全非阻塞
            print(f"=== [WebServer] 调用app.socketio.run()，时间: {time.time()} ===", flush=True)
            app.socketio.run(
                app, 
                host=host, 
                port=port, 
                debug=force_debug,       # 强制关闭debug避免阻塞
                use_reloader=False,      # 禁用重载器
                log_output=False,        # 禁用输出日志
                allow_unsafe_werkzeug=True  # 允许在生产环境使用
            )
            print(f"=== [WebServer] app.socketio.run()返回，时间: {time.time()} ===", flush=True)
        else:
            # 使用标准Flask运行
            app.run(
                host=host, 
                port=port, 
                debug=force_debug,       # 强制关闭debug避免阻塞
                threaded=True,
                use_reloader=False       # 禁用重载器
            )
    except Exception as e:
        logging.error(f"Web服务器启动失败: {e}")
        logging.error(f"详细错误信息: {traceback.format_exc()}")

def start_web_server_thread(host='0.0.0.0', port=5001, debug=False, web_stock_data={}, enable_websocket=True):
    """在独立线程中启动Web服务器"""
    web_thread = threading.Thread(
        target=start_web_server,
        args=(host, port, debug, web_stock_data, enable_websocket),
        daemon=True,
        name="WebServerThread"
    )
    web_thread.start()
    logging.info(f"Web服务器线程已启动: {web_thread.name}")
    return web_thread
