<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>多股票实时监控</title>
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- 外部CSS样式文件 -->
  <link rel="stylesheet" href="/static/css/main.css">
  
  <!-- 版本偏好管理 -->
  <!-- <script src="/static/js/version-preference.js"></script> -->
  <!-- 性能测试脚本（开发阶段） -->
  <!-- <script src="/static/js/performance_test.js"></script> -->
  
  <!-- 版本指示器样式 -->
  <style>
    .version-indicator {
      position: fixed;
      top: 10px;
      right: 10px;
      background: #16a34a;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      z-index: 1000;
      opacity: 0.8;
    }
  </style>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen">
  <!-- 版本指示器 -->
  <!-- <div class="version-indicator">NPM ES Module</div> -->
  
  <!-- 顶部导航栏 -->
  <header class="bg-gray-800 border-b border-gray-700 px-4 py-3">
    <div class="flex items-center justify-between">
      <h1 class="text-xl font-bold text-white">多股票实时监控</h1>
      <div class="flex items-center space-x-6 text-sm">
        <div class="flex items-center space-x-2">
          <span class="text-gray-400">状态:</span>
          <span id="status" class="text-yellow-400">连接中...</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-gray-400">股票数量:</span>
          <span id="stock-count" class="text-blue-400">0</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-gray-400">最后更新:</span>
          <span id="last-update" class="text-green-400 w-16 font-mono inline-block">--</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-gray-400">当前时间:</span>
          <span id="current-time" class="text-gray-300 w-16 font-mono inline-block">--</span>
        </div>
        <!-- 版本切换按钮 -->
        <div class="flex items-center space-x-2">
          <button 
            id="switch-version" 
            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
            onclick="window.location.href = window.location.pathname"
          >
            切换到CDN版本
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <main class="w-full px-4 py-6">
    <!-- 图表网格 -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4 text-gray-200">实时5分钟K线图</h2>
      <div id="charts-grid" class="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
        <!-- 图表容器将通过NPM ES模块动态生成 -->
      </div>
    </div>
  </main>

  <!-- 主副系统模式标记：请在后端渲染时设置为 true（副系统/回放）或 false（主系统/实时） -->
  <script>window.isPlaybackMode = false; // 副系统回放模式，主系统请渲染为 false</script>
  <!-- NPM ES模块主入口 -->
  <script type="module" src="/static/js/main-npm.js"></script>
</body>
</html>