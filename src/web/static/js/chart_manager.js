/**
 * 图表管理器 - 处理WebSocket实时数据更新
 * 保持与现有TradingView Lightweight Charts集成完全兼容
 */

class ChartManager {
    constructor() {
        this.charts = new Map(); // 存储所有图表实例
        this.chartData = new Map(); // 存储图表数据
        this.updateQueues = new Map(); // 更新队列，避免过频繁更新
        this.isUpdating = false;
        
        // 图表配置（参照index(1).html）
        this.chartOptions = {
            layout: {
                background: { color: "#1f2937" },
                textColor: "#e5e7eb"
            },
            grid: {
                vertLines: { color: "#374151" },
                horzLines: { color: "#374151" }
            },
            timeScale: {
                timeVisible: true,
                secondsVisible: false,
                borderColor: "#6b7280",
                rightOffset: 2,
                // 控制K线宽度，10为常用视觉宽度，可根据需求调整
                barSpacing: 10,
                // 与 index (2).html 保持一致，定制时间轴格式
                tickMarkFormatter: (time, tickMarkType, locale) => {
                    const date = new Date(time * 1000);
                    const nyTimeStr = date.toLocaleString('en-US', {
                        timeZone: 'America/New_York',
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                    if (tickMarkType === 0) {
                        return nyTimeStr.split(',')[0].split('/')[2];
                    } else if (tickMarkType === 1) {
                        const parts = nyTimeStr.split(',')[0].split('/');
                        return `${parts[2]}/${parts[0]}`;
                    } else if (tickMarkType === 2) {
                        const parts = nyTimeStr.split(',')[0].split('/');
                        return `${parts[0]}/${parts[1]}`;
                    } else {
                        const timePart = nyTimeStr.split(', ')[1];
                        return timePart;
                    }
                }
            },
            rightPriceScale: {
                borderColor: "#6b7280"
            },
            localization: {
                timeFormatter: (time) => {
                    const date = new Date(time * 1000);
                    const nyTimeStr = date.toLocaleString('en-US', {
                        timeZone: 'America/New_York',
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                    const formatted = nyTimeStr.replace(/(\d+)\/(\d+)\/(\d+),?\s*(\d+):(\d+)/, '$3/$1/$2 $4:$5');
                    return `${formatted} ET`;
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            width: 0,  // 将由resize设置
            height: 0  // 将由resize设置
        };
        
        this.seriesOptions = {
            upColor: '#10b981',
            downColor: '#ef4444',
            borderVisible: false,
            wickUpColor: '#10b981',
            wickDownColor: '#ef4444',
        };
    }
    
    /**
     * 创建新图表
     * @param {string} containerId 容器ID
     * @param {string} symbol 股票代码
     * @returns {Object} 图表实例
     */
    createChart(containerId, symbol) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`图表容器不存在: ${containerId}`);
            return null;
        }
        
        // 创建图表
        const chart = LightweightCharts.createChart(container, {
            ...this.chartOptions,
            width: container.offsetWidth,
            height: container.offsetHeight || 350,
        });
        
        // 创建K线系列
        const candlestickSeries = chart.addSeries(LightweightCharts.CandlestickSeries, this.seriesOptions);
        
        // 创建EMA系列
        const ema9Series = chart.addSeries(LightweightCharts.LineSeries, { 
            color: '#ff9800', 
            lineWidth: 1 
        });
        const ema20Series = chart.addSeries(LightweightCharts.LineSeries, { 
            color: '#f321e5', 
            lineWidth: 2 
        });
        const ema120Series = chart.addSeries(LightweightCharts.LineSeries, { 
            color: '#41f321', 
            lineWidth: 3 
        });
        
        // 设置主价格轴边距
        chart.priceScale('right').applyOptions({
            scaleMargins: { top: 0.1, bottom: 0.25 },
        });

        // 创建MACD系列
        const macdLineSeries = chart.addSeries(LightweightCharts.LineSeries, { 
            color: '#2196F3', 
            lineWidth: 2 
        }, 1); // 添加到窗格1
        const signalLineSeries = chart.addSeries(LightweightCharts.LineSeries, { 
            color: '#FF9800', 
            lineWidth: 1 
        }, 1); // 添加到窗格1
        const macdHistSeries = chart.addSeries(LightweightCharts.HistogramSeries, { 
            color: '#26a69a' 
        }, 1); // 添加到窗格1
        
        // 立即设置图表尺寸
        setTimeout(() => {
            const rect = container.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
                chart.applyOptions({ width: rect.width, height: rect.height });
            } else {
                // 如果容器尺寸为0，使用默认尺寸
                chart.applyOptions({ width: container.clientWidth || 400, height: 300 });
            }
        }, 100);
        
        // 创建价格突破检测标记系列 - 使用v5的createSeriesMarkers方式
        const seriesMarkers = LightweightCharts.createSeriesMarkers(candlestickSeries, []);

        // 存储图表信息
        this.charts.set(symbol, {
            chart: chart,
            series: candlestickSeries,
            container: container,
            containerId: containerId,
            // 技术指标系列
            ema9Series: ema9Series,
            ema20Series: ema20Series,
            ema120Series: ema120Series,
            macdLineSeries: macdLineSeries,
            signalLineSeries: signalLineSeries,
            macdHistSeries: macdHistSeries,
            // 价格突破检测系列
            surgeMarkers: {
                markersInstance: seriesMarkers,
                lines: []
            }
        });
        
        this.chartData.set(symbol, []);
        this.updateQueues.set(symbol, []);
        
        // 窗口大小变化时调整图表大小
        const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
                const { width, height } = entry.contentRect;
                chart.applyOptions({ width, height });
            }
        });
        resizeObserver.observe(container);
        
        // console.log(`创建图表: ${symbol} (${containerId})`);
        return { 
            chart, 
            series: candlestickSeries,
            ema9Series,
            ema20Series,
            ema120Series,
            macdLineSeries,
            signalLineSeries,
            macdHistSeries
        };
    }
    
    /**
     * 更新图表数据
     * @param {string} symbol 股票代码
     * @param {Array} newData 新的K线数据
     */
    updateChart(symbol, newData) {
        if (!this.charts.has(symbol)) {
            console.warn(`图表不存在: ${symbol}`);
            return;
        }
        
        // 添加到更新队列
        this.updateQueues.set(symbol, newData);
        
        // 节流更新
        if (!this.isUpdating) {
            this.isUpdating = true;
            requestAnimationFrame(() => {
                this.processUpdateQueues();
                this.isUpdating = false;
            });
        }
    }
    
    /**
     * 处理更新队列
     */
    processUpdateQueues() {
        this.updateQueues.forEach((newData, symbol) => {
            this.doUpdateChart(symbol, newData);
        });
        this.updateQueues.clear();
    }
    
    /**
     * 实际执行图表更新
     * @param {string} symbol 股票代码
     * @param {Array} newData 新的K线数据
     */
    doUpdateChart(symbol, newData) {
        const chartInfo = this.charts.get(symbol);
        if (!chartInfo) return;
        
        try {
            // 转换数据格式
            const formattedData = this.formatChartData(newData);
            
            // 将1分钟数据聚合为5分钟数据
            const aggregatedData = this.aggregateTo5MinData(formattedData);
            
            // 检查数据是否有变化
            const currentData = this.chartData.get(symbol);
            if (this.isDataEqual(currentData, aggregatedData)) {
                return; // 数据无变化，跳过更新
            }

            // console.log(`[ChartManager] ${symbol} 1分钟原始数据数量=${Array.isArray(formattedData) ? formattedData.length : 0}`);
            // if (Array.isArray(formattedData)) {
            //     formattedData.forEach((item, idx) => {
            //         if (
            //             typeof item.open !== 'number' ||
            //             typeof item.high !== 'number' ||
            //             typeof item.low !== 'number' ||
            //             typeof item.close !== 'number' ||
            //             typeof item.volume !== 'number' ||
            //             typeof item.time !== 'number' ||
            //             !isFinite(item.open) ||
            //             !isFinite(item.high) ||
            //             !isFinite(item.low) ||
            //             !isFinite(item.close) ||
            //             !isFinite(item.volume) ||
            //             !isFinite(item.time)
            //         ) {
            //             console.warn(`[ChartManager] 1分钟原始数据无效: symbol=${symbol}, idx=${idx}, data=`, item);
            //         }
            //     });
            // }
            // console.log(`[ChartManager] ${symbol} setData: 数据数量=${Array.isArray(aggregatedData) ? aggregatedData.length : 0}`);
            // if (Array.isArray(aggregatedData)) {
            //     aggregatedData.forEach((item, idx) => {
            //         if (
            //             typeof item.open !== 'number' ||
            //             typeof item.high !== 'number' ||
            //             typeof item.low !== 'number' ||
            //             typeof item.close !== 'number' ||
            //             typeof item.volume !== 'number' ||
            //             typeof item.time !== 'number' ||
            //             !isFinite(item.open) ||
            //             !isFinite(item.high) ||
            //             !isFinite(item.low) ||
            //             !isFinite(item.close) ||
            //             !isFinite(item.volume) ||
            //             !isFinite(item.time)
            //         ) {
            //             console.warn(`[ChartManager] setData无效: symbol=${symbol}, idx=${idx}, data=`, item);
            //         }
            //     });
            // }
            // 更新K线图表
            chartInfo.series.setData(aggregatedData);
            this.chartData.set(symbol, aggregatedData);

            // 计算并更新技术指标
            if (aggregatedData.length > 0) {
                // 计算EMA指标
                const ema9 = this.calculateEMA(aggregatedData, 9);
                const ema20 = this.calculateEMA(aggregatedData, 20);
                const ema120 = this.calculateEMA(aggregatedData, 120);
                
                // 更新EMA系列
                if (chartInfo.ema9Series) chartInfo.ema9Series.setData(ema9);
                if (chartInfo.ema20Series) chartInfo.ema20Series.setData(ema20);
                if (chartInfo.ema120Series) chartInfo.ema120Series.setData(ema120);

                // 计算MACD指标
                const macdResult = this.calculateMACD(aggregatedData);
                
                // 更新MACD系列
                if (chartInfo.macdLineSeries) chartInfo.macdLineSeries.setData(macdResult.macdLine);
                if (chartInfo.signalLineSeries) chartInfo.signalLineSeries.setData(macdResult.signalLine);
                if (chartInfo.macdHistSeries) chartInfo.macdHistSeries.setData(macdResult.hist);
            }
            
            // 检测并渲染突破信号
            const surgeData = this.calculatePriceSurgeDetector(aggregatedData);
            if (chartInfo && chartInfo.surgeMarkers && chartInfo.surgeMarkers.markersInstance) {
                // 使用v5的setMarkers方法设置标记
                chartInfo.surgeMarkers.markersInstance.setMarkers(surgeData.signals);
            }

            // 更新形态文字（增加跌破检查功能）
            let shapeText = '';
            if (surgeData.signals.length > 0) {
                const latestSignal = surgeData.signals[surgeData.signals.length - 1];
                if (latestSignal.text.includes('🚀')) {
                    shapeText = '🚀 突破';
                } else if (latestSignal.text.includes('✓')) {
                    shapeText = '✓ 形态';
                }
                
                // 新功能：检查形态完成后是否跌破最后一根突破K线
                if (shapeText && aggregatedData.length > 0) {
                    const currentCandle = aggregatedData[aggregatedData.length - 1];
                    const shouldClearText = this.checkBreakdownAfterCompletion(surgeData.signals, currentCandle);
                    
                    if (shouldClearText) {
                        console.log(`[形态跌破] ${symbol} 最新K线跌破突破价格，清除形态文字`);
                        shapeText = ''; // 清除形态文字
                    }
                }
            }
            
            const surgeEl = document.getElementById(`surge-${symbol}`);
            if (surgeEl) {
                surgeEl.textContent = shapeText;
                // 设置样式类 - 突破为绿色，形态为蓝色
                if (shapeText.includes('🚀')) {
                    surgeEl.className = 'ml-2 font-medium text-green-400';
                } else if (shapeText.includes('✓')) {
                    surgeEl.className = 'ml-2 font-medium text-blue-400';
                } else {
                    surgeEl.className = 'ml-2 font-medium';
                }
            }

            // 自动保持 rightOffset=2，显示全部K线
            if (aggregatedData.length > 0) {
                chartInfo.chart.applyOptions({ timeScale: { rightOffset: 2 } });
            }
            
        } catch (error) {
            console.error(`更新图表失败 ${symbol}:`, error);
        }
    }
    
    /**
     * 格式化图表数据
     * @param {Array} rawData 原始数据
     * @returns {Array} 格式化后的数据
     */
    formatChartData(rawData) {
        if (!Array.isArray(rawData)) {
            return [];
        }
        
        return rawData
            .filter(item => item && typeof item === 'object')
            .map(item => ({
                time: item.time,
                open: parseFloat(item.open) || 0,
                high: parseFloat(item.high) || 0,
                low: parseFloat(item.low) || 0,
                close: parseFloat(item.close) || 0,
                volume: parseFloat(item.volume) || 0 // 添加成交量字段，聚合函数需要
            }))
            .filter(item => item.time > 0) // 过滤无效时间
            .sort((a, b) => a.time - b.time); // 按时间排序
    }
    
    /**
     * 检查数据是否相等
     * @param {Array} data1 数据1
     * @param {Array} data2 数据2
     * @returns {boolean} 是否相等
     */
    isDataEqual(data1, data2) {
        if (!data1 || !data2) return false;
        if (data1.length !== data2.length) return false;
        
        // 只比较最后几个数据点，提高性能
        const compareCount = Math.min(5, data1.length);
        for (let i = 1; i <= compareCount; i++) {
            const idx = data1.length - i;
            if (idx < 0) break;
            
            const item1 = data1[idx];
            const item2 = data2[idx];
            
            if (item1.time !== item2.time ||
                item1.open !== item2.open ||
                item1.high !== item2.high ||
                item1.low !== item2.low ||
                item1.close !== item2.close ||
                item1.volume !== item2.volume) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 添加实时数据点
     * @param {string} symbol 股票代码
     * @param {Object} dataPoint 新数据点
     */
    addDataPoint(symbol, dataPoint) {
        if (!this.charts.has(symbol)) {
            return;
        }
        
        const chartInfo = this.charts.get(symbol);
        const formattedPoint = {
            time: dataPoint.time,
            open: parseFloat(dataPoint.open) || 0,
            high: parseFloat(dataPoint.high) || 0,
            low: parseFloat(dataPoint.low) || 0,
            close: parseFloat(dataPoint.close) || 0
        };
        
        try {
            chartInfo.series.update(formattedPoint);
            
            // 更新本地数据缓存
            const currentData = this.chartData.get(symbol) || [];
            const existingIndex = currentData.findIndex(item => item.time === formattedPoint.time);
            
            if (existingIndex >= 0) {
                currentData[existingIndex] = formattedPoint;
            } else {
                currentData.push(formattedPoint);
                currentData.sort((a, b) => a.time - b.time);
            }
            
            this.chartData.set(symbol, currentData);

            // 重新计算并更新技术指标
            if (currentData.length > 0) {
                // 计算EMA指标
                const ema9 = this.calculateEMA(currentData, 9);
                const ema20 = this.calculateEMA(currentData, 20);
                const ema120 = this.calculateEMA(currentData, 120);
                
                // 更新EMA系列
                if (chartInfo.ema9Series) chartInfo.ema9Series.setData(ema9);
                if (chartInfo.ema20Series) chartInfo.ema20Series.setData(ema20);
                if (chartInfo.ema120Series) chartInfo.ema120Series.setData(ema120);

                // 计算MACD指标
                const macdResult = this.calculateMACD(currentData);
                
                // 更新MACD系列
                if (chartInfo.macdLineSeries) chartInfo.macdLineSeries.setData(macdResult.macdLine);
                if (chartInfo.signalLineSeries) chartInfo.signalLineSeries.setData(macdResult.signalLine);
                if (chartInfo.macdHistSeries) chartInfo.macdHistSeries.setData(macdResult.hist);

                // 更新突破检测标记
                const surgeData = this.calculatePriceSurgeDetector(currentData);
                if (chartInfo.surgeMarkers && chartInfo.surgeMarkers.markersInstance) {
                    chartInfo.surgeMarkers.markersInstance.setMarkers(surgeData.signals);
                }
            }
            
        } catch (error) {
            console.error(`添加数据点失败 ${symbol}:`, error);
        }
    }
    
    /**
     * 将1分钟数据聚合为5分钟K线数据
     * @param {Array} data 1分钟数据
     * @returns {Array} 5分钟K线数据
     */
    aggregateTo5MinData(data) {
        if (!data || data.length === 0) {
            return [];
        }

        // 按时间排序
        const sortedData = [...data].sort((a, b) => a.time - b.time);

        const aggregated = [];
        let currentCandle = null;
        let skippedVolumeZero = 0;
        let skippedInvalid = 0;
        let lastTime = null;
        let timeJumps = 0;

        for (const point of sortedData) {
            // 检查时间戳连续性
            if (lastTime !== null && point.time - lastTime !== 60) {
                timeJumps++;
            }
            lastTime = point.time;

            // 检查无效数据
            if (
                typeof point.open !== 'number' ||
                typeof point.high !== 'number' ||
                typeof point.low !== 'number' ||
                typeof point.close !== 'number' ||
                typeof point.volume !== 'number' ||
                typeof point.time !== 'number' ||
                !isFinite(point.open) ||
                !isFinite(point.high) ||
                !isFinite(point.low) ||
                !isFinite(point.close) ||
                !isFinite(point.volume) ||
                !isFinite(point.time)
            ) {
                skippedInvalid++;
                continue;
            }

            // 跳过成交量为 0 的数据，视为熔断期间
            if (!point.volume || point.volume === 0) {
                skippedVolumeZero++;
                currentCandle = null; // 强制结束当前K线，进入断层
                continue;
            }

            const periodStart = Math.floor(point.time / 300) * 300;

            if (!currentCandle || currentCandle.time !== periodStart) {
                // 新的5分钟K线开始
                currentCandle = {
                    time: periodStart,
                    open: point.open,
                    high: point.high,
                    low: point.low,
                    close: point.close,
                    volume: point.volume,
                };
                aggregated.push(currentCandle);
            } else {
                // 聚合到当前K线
                currentCandle.high = Math.max(currentCandle.high, point.high);
                currentCandle.low = Math.min(currentCandle.low, point.low);
                currentCandle.close = point.close;
                currentCandle.volume += point.volume;
            }
        }

        // console.log(`[ChartManager] 聚合统计: 跳过无效数据=${skippedInvalid}, 跳过volume为0=${skippedVolumeZero}, 时间跳跃次数=${timeJumps}`);
        return aggregated;
    }

    /**
     * 价格突破检测指标（与参考文件完全一致）
     * @param {Array} data 5分钟K线数据
     * @returns {{signals: Array, lines: Array}}
     */
    calculatePriceSurgeDetector(data) {
        if (!data || data.length === 0) return { signals: [], lines: [] };

        const signals = [];
        const lines = [];

        // 状态变量 - 回到原始的单一形态追踪，但改进失效检查逻辑
        let surgePrice = null;
        let surgeBar = null;
        let surgeLow = null;
        let downCount = 0;
        let inTracking = false;
        let waitingDown = false;

        for (let i = 1; i < data.length; i++) {
            const current = data[i];
            const previous = data[i - 1];

            // 计算价格变化百分比
            const priceChangePercent = ((current.close - previous.close) / previous.close) * 100;

            // 判断K线类型
            const isDownCandle = current.close < current.open;
            const isUpCandle = current.close > current.open;

            // 检测突破条件：涨幅超过10%且不在追踪状态
            if (priceChangePercent >= 10 && !inTracking) {
                surgePrice = current.open;
                surgeBar = i;
                surgeLow = current.low;
                downCount = 0;
                inTracking = true;
                waitingDown = true;

                // 添加突破信号
                signals.push({
                    time: current.time,
                    position: 'belowBar',
                    color: '#00ff00',
                    shape: 'arrowUp',
                    text: `🚀${priceChangePercent.toFixed(1)}%`,
                    surgePrice: current.open,  // 记录突破价格用于后续失效检查
                    surgeLow: current.low,     // 记录最低价用于后续失效检查
                    surgeId: `surge_${current.time}_${i}`,  // 唯一标识符
                    isValid: true
                });
            }

            // 形态追踪逻辑
            if (inTracking) {
                if (waitingDown) {
                    // 跳过突破K线，从下一根开始计数
                    waitingDown = false;
                } else {
                    if (isDownCandle) {
                        // 检查是否跌破突破阳线最低价
                        if (current.low < surgeLow) {
                            // 形态失效 - 直接删除当前追踪的突破信号
                            for (let j = signals.length - 1; j >= 0; j--) {
                                if (signals[j].text.includes('🚀') && signals[j].surgeLow === surgeLow) {
                                    signals.splice(j, 1);  // 直接删除信号
                                    break;
                                }
                            }

                            // 重置状态变量
                            surgePrice = null;
                            surgeBar = null;
                            surgeLow = null;
                            downCount = 0;
                            inTracking = false;
                            waitingDown = false;
                        } else {
                            downCount++;
                            if (downCount > 4) {
                                // 阴线过多，形态失效 - 直接删除当前追踪的突破信号
                                for (let j = signals.length - 1; j >= 0; j--) {
                                    if (signals[j].text.includes('🚀') && signals[j].surgeLow === surgeLow) {
                                        signals.splice(j, 1);  // 直接删除信号
                                        break;
                                    }
                                }

                                // 重置状态变量
                                surgePrice = null;
                                surgeBar = null;
                                surgeLow = null;
                                downCount = 0;
                                inTracking = false;
                                waitingDown = false;
                            }
                        }
                    } else if (isUpCandle) {
                        if (downCount >= 1 && downCount <= 4) {
                            // 出现止跌阳线，且阴线数量在1-4根之间，形态有效
                            lines.push({
                                startTime: data[surgeBar].time,
                                startPrice: surgePrice,
                                endTime: current.time,
                                endPrice: surgePrice,
                                color: '#aab3f9',
                                width: 3,
                                style: 'dashed'
                            });

                            // 添加形态完成信号
                            signals.push({
                                time: current.time,
                                position: 'aboveBar',
                                color: '#ff6b6b',
                                shape: 'circle',
                                text: `✓${downCount}`,
                                confirmId: `confirm_${current.time}_${i}`,  // 唯一标识符
                                isValid: true
                            });
                        } else if (downCount === 0) {
                            // 突破K线后直接是阳线，形态失效 - 直接删除突破信号
                            for (let j = signals.length - 1; j >= 0; j--) {
                                if (signals[j].text.includes('🚀') && signals[j].surgeLow === surgeLow) {
                                    signals.splice(j, 1);  // 直接删除信号
                                    break;
                                }
                            }
                        }

                        // 形态完成或失效，重置状态变量（与Pine Script一致：不重置surgeLow）
                        surgePrice = null;
                        surgeBar = null;
                        // 注意：Pine Script中没有重置surge_low，这里保持一致
                        // surgeLow = null;  // 不重置，保持与Pine Script一致
                        downCount = 0;
                        inTracking = false;
                        waitingDown = false;
                    }
                }
            }

            // 检查所有历史突破信号的失效条件
            if (isDownCandle) {
                for (let j = signals.length - 1; j >= 0; j--) {
                    const signal = signals[j];
                    if (signal.text.includes('🚀') && signal.isValid && signal.surgeLow) {
                        // 检查是否跌破这个突破信号的最低价
                        if (current.low < signal.surgeLow) {
                            signals.splice(j, 1);  // 直接删除失效的历史突破信号
                        }
                    }
                }
            }
        }

        // 清理TradingView不需要的属性
        signals.forEach(signal => {
            delete signal.isValid;
            delete signal.surgePrice;
            delete signal.surgeLow;
            delete signal.surgeId;
            delete signal.confirmId;
        });

        return { signals, lines };
    }

    // =================================================================================
    // Testing and Debugging Methods
    // =================================================================================
    
    /**
     * 测试5分钟聚合功能
     * 这个函数用于调试和验证聚合逻辑是否正确工作
     */
    test5MinuteAggregation() {
        // console.log('开始测试5分钟K线聚合功能...');
        
        // 创建测试数据 - 模拟15分钟的1分钟K线数据
        const testData = [];
        const baseTime = Math.floor(Date.now() / 1000); // 当前时间戳
        const alignedTime = Math.floor(baseTime / 300) * 300; // 对齐到5分钟边界
        
        for (let i = 0; i < 15; i++) {
            testData.push({
                time: alignedTime + i * 60, // 每分钟一个数据点
                open: 100 + Math.random() * 5,
                high: 102 + Math.random() * 5,
                low: 98 + Math.random() * 5,
                close: 100 + Math.random() * 5,
                volume: Math.floor(Math.random() * 1000) + 100
            });
        }
        
        // console.log('原始1分钟数据 (15条):', testData);
        
        // 执行聚合
        const aggregated = this.aggregateTo5MinData(testData);
        // console.log('聚合后的5分钟数据 (应该是3条):', aggregated);
        
        // 验证聚合结果
        if (aggregated.length !== 3) {
            console.error(`❌ 聚合失败: 期望3条数据，实际得到${aggregated.length}条`);
            return false;
        }
        
        // 验证时间对齐
        for (let i = 0; i < aggregated.length; i++) {
            const expectedTime = alignedTime + i * 300;
            if (aggregated[i].time !== expectedTime) {
                console.error(`❌ 时间对齐失败: 期望${expectedTime}，实际${aggregated[i].time}`);
                return false;
            }
        }
        
        // 验证OHLC逻辑
        const firstPeriodData = testData.slice(0, 5);
        const firstAggregated = aggregated[0];
        
        const expectedOpen = firstPeriodData[0].open;
        const expectedClose = firstPeriodData[4].close;
        const expectedHigh = Math.max(...firstPeriodData.map(d => d.high));
        const expectedLow = Math.min(...firstPeriodData.map(d => d.low));
        const expectedVolume = firstPeriodData.reduce((sum, d) => sum + d.volume, 0);
        
        if (firstAggregated.open !== expectedOpen) {
            console.error(`❌ 开盘价错误: 期望${expectedOpen}，实际${firstAggregated.open}`);
            return false;
        }
        
        if (firstAggregated.close !== expectedClose) {
            console.error(`❌ 收盘价错误: 期望${expectedClose}，实际${firstAggregated.close}`);
            return false;
        }
        
        if (Math.abs(firstAggregated.high - expectedHigh) > 0.01) {
            console.error(`❌ 最高价错误: 期望${expectedHigh}，实际${firstAggregated.high}`);
            return false;
        }
        
        if (Math.abs(firstAggregated.low - expectedLow) > 0.01) {
            console.error(`❌ 最低价错误: 期望${expectedLow}，实际${firstAggregated.low}`);
            return false;
        }
        
        if (firstAggregated.volume !== expectedVolume) {
            console.error(`❌ 成交量错误: 期望${expectedVolume}，实际${firstAggregated.volume}`);
            return false;
        }
        
        // console.log('✅ 5分钟K线聚合功能测试通过！');
        // console.log('聚合详情:', {
        //     original: `${testData.length}条1分钟数据`,
        //     aggregated: `${aggregated.length}条5分钟数据`,
        //     timeAlignment: '时间对齐正确',
        //     ohlcLogic: 'OHLC逻辑正确',
        //     volumeSum: '成交量合计正确'
        // });
        return true;
    }

    // =================================================================================
    // Technical Indicator Calculation Methods
    // =================================================================================
    
    /**
     * 计算EMA指标
     * @param {Array} data 价格数据
     * @param {number} period 周期
     * @returns {Array} EMA数据
     */
    calculateEMA(data, period) {
        // 如果volume为0，用上一根k线的open
        const prices = [];
        for (let i = 0; i < data.length; i++) {
            if (data[i].volume === 0 && i > 0) {
                prices.push(prices[i - 1]);
            } else {
                prices.push(data[i].open);
            }
        }
        const times = data.map(d => d.time);
        const result = [];
        if (prices.length === 0) return result;

        const k = 2 / (period + 1);
        let ema = 0;
        // 前 period-1 个点用SMA（简单均线）填充
        for (let i = 0; i < prices.length; i++) {
            if (i < period - 1) {
                // 用SMA
                let sum = 0;
                for (let j = 0; j <= i; j++) {
                    sum += prices[j];
                }
                ema = sum / (i + 1);
                result.push({ time: times[i], value: ema });
            } else if (i === period - 1) {
                // 第period个点，SMA初始化
                let sum = 0;
                for (let j = i - period + 1; j <= i; j++) {
                    sum += prices[j];
                }
                ema = sum / period;
                result.push({ time: times[i], value: ema });
            } else {
                ema = prices[i] * k + ema * (1 - k);
                result.push({ time: times[i], value: ema });
            }
        }
        return result;
    }

    /**
     * 计算MACD指标
     * @param {Array} data 价格数据
     * @param {number} fastPeriod 快速周期  
     * @param {number} slowPeriod 慢速周期
     * @param {number} signalPeriod 信号周期
     * @returns {Object} MACD数据
     */
    calculateMACD(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        if (!data || data.length === 0) return { macdLine: [], signalLine: [], histogram: [] };

        // 计算快速和慢速EMA，前n点用SMA填充
        const prices = [];
        for (let i = 0; i < data.length; i++) {
            if (data[i].volume === 0 && i > 0) {
                prices.push(prices[i - 1]);
            } else {
                prices.push(data[i].close);
            }
        }

        // 计算EMA（前n点用SMA填充）
        function calcEMAWithSMA(prices, period) {
            const emaArr = [];
            const k = 2 / (period + 1);
            let ema = 0;
            for (let i = 0; i < prices.length; i++) {
                if (i < period - 1) {
                    // 用SMA
                    let sum = 0;
                    for (let j = 0; j <= i; j++) {
                        sum += prices[j];
                    }
                    ema = sum / (i + 1);
                    emaArr.push(ema);
                } else if (i === period - 1) {
                    let sum = 0;
                    for (let j = i - period + 1; j <= i; j++) {
                        sum += prices[j];
                    }
                    ema = sum / period;
                    emaArr.push(ema);
                } else {
                    ema = prices[i] * k + ema * (1 - k);
                    emaArr.push(ema);
                }
            }
            return emaArr;
        }

        const fastEMA = calcEMAWithSMA(prices, fastPeriod);
        const slowEMA = calcEMAWithSMA(prices, slowPeriod);
        const macdLine = [];
        const signalLine = [];
        const histogram = [];

        // 计算MACD线
        for (let i = 0; i < data.length; i++) {
            // 只要fastEMA和slowEMA有值即可，已保证无null
            const macdValue = fastEMA[i] - slowEMA[i];
            macdLine.push({
                time: data[i].time,
                value: macdValue
            });
        }

        // 信号线（MACD的EMA，前n点用SMA填充）
        function calcEMAFromValuesWithSMA(values, period) {
            const emaArr = [];
            const k = 2 / (period + 1);
            let ema = 0;
            for (let i = 0; i < values.length; i++) {
                if (i < period - 1) {
                    let sum = 0;
                    for (let j = 0; j <= i; j++) {
                        sum += values[j];
                    }
                    ema = sum / (i + 1);
                    emaArr.push(ema);
                } else if (i === period - 1) {
                    let sum = 0;
                    for (let j = i - period + 1; j <= i; j++) {
                        sum += values[j];
                    }
                    ema = sum / period;
                    emaArr.push(ema);
                } else {
                    ema = values[i] * k + ema * (1 - k);
                    emaArr.push(ema);
                }
            }
            return emaArr;
        }

        const macdValues = macdLine.map(item => item.value);
        const signalEMA = calcEMAFromValuesWithSMA(macdValues, signalPeriod);

        for (let i = 0; i < macdLine.length; i++) {
            // signalEMA已保证无null
            signalLine.push({
                time: macdLine[i].time,
                value: signalEMA[i]
            });
        }

        // 计算直方图（MACD - Signal）
        for (let i = 0; i < Math.min(macdLine.length, signalLine.length); i++) {
            // macdLine和signalLine已保证无null
            const macdVal = macdLine[i].value;
            const signalVal = signalLine[i].value;
            const histValue = macdVal - signalVal;
            histogram.push({
                time: macdLine[i].time,
                value: histValue,
                color: histValue >= 0 ? '#26a69a' : '#ef5350'
            });
        }

        return {
            macdLine,
            signalLine,
            hist: histogram
        };
    }

    /**
     * 辅助函数：计算EMA值数组
     * @param {Array} prices 价格数组
     * @param {number} period 周期
     * @returns {Array} EMA值数组
     */
    calculateEMAValuesFromPrices(prices, period) {
        const values = [];
        const multiplier = 2 / (period + 1);
        let ema = null;
        for (let i = 0; i < prices.length; i++) {
            const price = prices[i];
            if (ema === null) {
                if (i >= period - 1) {
                    let sum = 0;
                    for (let j = i - period + 1; j <= i; j++) {
                        sum += prices[j];
                    }
                    ema = sum / period;
                } else {
                    ema = price;
                }
            } else {
                ema = (price * multiplier) + (ema * (1 - multiplier));
            }
            values.push(ema);
        }
        return values;
    }

    /**
     * 辅助函数：计算EMA值数组
     * @param {Array} data 价格数据
     * @param {number} period 周期
     * @returns {Array} EMA值数组
     */
    calculateEMAValues(data, period) {
        const values = [];
        const multiplier = 2 / (period + 1);
        let ema = null;

        for (let i = 0; i < data.length; i++) {
            const price = data[i].close;

            if (ema === null) {
                if (i >= period - 1) {
                    // 使用简单移动平均作为初始值
                    let sum = 0;
                    for (let j = i - period + 1; j <= i; j++) {
                        sum += data[j].close;
                    }
                    ema = sum / period;
                } else {
                    ema = price; // 如果数据不够，使用当前价格
                }
            } else {
                ema = (price * multiplier) + (ema * (1 - multiplier));
            }

            values.push(ema);
        }

        return values;
    }

    /**
     * 辅助函数：从数值数组计算EMA
     * @param {Array} values 数值数组
     * @param {number} period 周期
     * @returns {Array} EMA值数组
     */
    calculateEMAFromValues(values, period) {
        const emaValues = [];
        const multiplier = 2 / (period + 1);
        let ema = null;

        for (let i = 0; i < values.length; i++) {
            if (ema === null) {
                if (i >= period - 1) {
                    // 使用简单移动平均作为初始值
                    let sum = 0;
                    for (let j = i - period + 1; j <= i; j++) {
                        sum += values[j];
                    }
                    ema = sum / period;
                } else {
                    ema = values[i]; // 如果数据不够，使用当前值
                }
            } else {
                ema = (values[i] * multiplier) + (ema * (1 - multiplier));
            }

            emaValues.push(ema);
        }

        return emaValues;
    }

    /**
     * 获取图表实例
     * @param {string} symbol 股票代码
     * @returns {Object|null} 图表实例
     */
    getChart(symbol) {
        return this.charts.get(symbol) || null;
    }
    
    /**
     * 获取图表数据
     * @param {string} symbol 股票代码
     * @returns {Array} 图表数据
     */
    getChartData(symbol) {
        return this.chartData.get(symbol) || [];
    }
    
    /**
     * 调整图表大小
     * @param {string} symbol 股票代码
     */
    resizeChart(symbol) {
        const chartInfo = this.charts.get(symbol);
        if (chartInfo) {
            const container = chartInfo.container;
            chartInfo.chart.applyOptions({
                width: container.offsetWidth,
                height: container.offsetHeight
            });
            // 保持 rightOffset=2（官方推荐用 applyOptions）
            chartInfo.chart.applyOptions({ timeScale: { rightOffset: 2 } });
        }
    }
    
    /**
     * 调整所有图表大小
     */
    resizeAllCharts() {
        this.charts.forEach((chartInfo, symbol) => {
            this.resizeChart(symbol);
        });
    }

    /**
     * 格式化交易量，添加单位（K、M、G），保留1位小数点
     * @param {number} volume 累计成交量
     * @returns {string} 格式化后的字符串，例如 "123.4K", "1.2M", "123.4G"
     */
    formatVolume(volume) {
        if (volume === null || volume === undefined) {
            return '--';
        }
        if (volume >= 1_000_000_000) {  // 大于等于十亿
            return `${(volume / 1_000_000_000).toFixed(1)}G`;
        } else if (volume >= 1_000_000) {  // 大于等于百万
            return `${(volume / 1_000_000).toFixed(1)}M`;
        } else if (volume >= 1_000) {  // 大于等于千
            return `${(volume / 1_000).toFixed(1)}K`;
        } else {
            return `${volume.toFixed(1)}`;
        }
    }

    /**
     * 更新交易次数排名和颜色
     * 获取所有股票的交易次数，对前三名设置不同颜色
     * @param {Object} transactionData 所有股票的原始交易次数数据 {symbol: count}
     */
    updateTransactionsRanking(transactionData) {
        if (!transactionData) return;

        // 收集所有股票的交易次数并排序
        const rankings = [];
        
        // 遍历所有已创建的图表
        this.charts.forEach((chartInfo, symbol) => {
            const transactionEl = document.getElementById(`transactions-${symbol}`);
            if (transactionEl && transactionData[symbol] !== undefined) {
                const count = transactionData[symbol];
                rankings.push({
                    symbol: symbol,
                    count: count,
                    element: transactionEl
                });
            }
        });
        
        // 按交易次数降序排序
        rankings.sort((a, b) => b.count - a.count);
        
        // 更新显示文本和颜色
        rankings.forEach((item, index) => {
            // 格式化交易次数
            const formattedCount = this.formatVolume(item.count);
            item.element.textContent = formattedCount;
            
            // 设置颜色
            if (index === 0) {
                // 第一名：绿色
                item.element.className = 'font-mono text-green-400';
                item.element.style.color = '';
            } else if (index === 1) {
                // 第二名：橙色 (#FF8C00)
                item.element.className = 'font-mono';
                item.element.style.color = '#FF8C00';
            } else if (index === 2) {
                // 第三名：显眼的红色
                item.element.className = 'font-mono';
                item.element.style.color = '#FF0000';
            } else {
                // 其他：默认青色
                item.element.className = 'font-mono text-cyan-400';
                item.element.style.color = '';
            }
        });
    }
}

// 全局图表管理器实例
window.chartManager = new ChartManager();

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChartManager;
}
