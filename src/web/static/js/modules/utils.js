/**
 * 工具函数 ES模块版本
 * 提供与UMD版本兼容的共用工具函数
 */

/**
 * 更新纽约时间显示
 */
export function updateTime() {
    const currentTimeEl = document.getElementById('current-time');
    if (currentTimeEl) {
        const now = new Date();
        // 转为纽约时区
        const nyTime = now.toLocaleTimeString('en-US', { timeZone: 'America/New_York', hour12: false });
        currentTimeEl.textContent = nyTime;
    }
}

/**
 * WebSocket连接失败监控
 */
let connectionFailureStartTime = null;
let connectionMonitorInterval = null;
const CONNECTION_FAILURE_TIMEOUT = 3 * 60 * 1000; // 3分钟

/**
 * 开始监控WebSocket连接失败
 */
export function startConnectionFailureMonitoring() {
    if (!connectionFailureStartTime) {
        connectionFailureStartTime = Date.now();
        console.log('开始监控WebSocket连接失败...');
        
        // 每10秒检查一次连接状态
        connectionMonitorInterval = setInterval(() => {
            const currentTime = Date.now();
            const failureDuration = currentTime - connectionFailureStartTime;
            
            if (failureDuration >= CONNECTION_FAILURE_TIMEOUT) {
                console.log('WebSocket连接失败超过3分钟，自动关闭连接');
                stopConnectionFailureMonitoring();
                
                // 关闭WebSocket连接
                if (window.wsClient && window.wsClient.socket) {
                    window.wsClient.disconnect();
                }
                
                // 更新状态显示
                const statusEl = document.getElementById('status');
                if (statusEl) {
                    statusEl.textContent = '连接已关闭 (超时)';
                    statusEl.className = 'text-red-500';
                }
            }
        }, 10000); // 每10秒检查一次
    }
}

/**
 * 停止监控 WebSocket连接失败
 */
export function stopConnectionFailureMonitoring() {
    connectionFailureStartTime = null;
    if (connectionMonitorInterval) {
        clearInterval(connectionMonitorInterval);
        connectionMonitorInterval = null;
        console.log('停止监控 WebSocket连接失败');
    }
}

/**
 * 为指定的股票代码创建图表和DOM元素
 * @param {string} symbol - 股票代码
 */
export function createChartForSymbol(symbol) {
    if (window.chartManager.getChart(symbol)) {
        return;
    }

    const chartContainer = document.getElementById('charts-grid');
    if (!chartContainer) {
        console.error('图表容器不存在');
        return;
    }

    // 1. 创建DOM容器
    const stockDiv = document.createElement('div');
    stockDiv.id = `chart-container-${symbol}`;
    stockDiv.className = 'bg-gray-800 p-4 rounded-lg shadow-lg';
    stockDiv.innerHTML = `
        <div class="flex justify-between items-center mb-2">
        <h2 class="text-xl font-bold text-white">${symbol}</h2>
        <div class="flex items-center space-x-2 text-sm">
            <span id="price-${symbol}" class="text-white font-medium">--</span>
            <span id="change-${symbol}" class="price-neutral">--</span>
            <span class="text-gray-400">交易次数:</span>
            <span id="transactions-${symbol}" class="font-mono text-cyan-400">--</span>
            <span id="surge-${symbol}" class="ml-2 font-medium"></span>
        </div>
        </div>
        <div id="chart-${symbol}" class="h-80 w-full"></div>
    `;
    chartContainer.appendChild(stockDiv);

    // 2. 使用 ChartManager 创建图表
    window.chartManager.createChart(`chart-${symbol}`, symbol);
}

/**
 * 同步前端显示的图表与后端提供的股票列表，同时处理订阅和取消订阅逻辑
 * @param {list} stockList - 最新的股票代码列表
 */
export function syncStockList(stockList) {
    const newSymbols = new Set(stockList);
    const subscribedStocks = window.subscribedStocks;
    const chartsGrid = document.getElementById('charts-grid');

    // 1. 为新股票发起订阅，并确保现有图表可见
    for (const symbol of newSymbols) {
        const chartDiv = document.getElementById(`chart-container-${symbol}`);
        // 排序图表容器
        chartsGrid.appendChild(chartDiv);
        
        if (chartDiv && chartDiv.style.display === 'none') {
            // 显示图表容器
            chartDiv.style.display = 'block';
        }

        // 2. 为新股票发起订阅
        if (!subscribedStocks.has(symbol)) {
            if (window.socket && window.socket.connected) {
                window.socket.emit('subscribe', { symbol: symbol });
                subscribedStocks.add(symbol);
            }
        }
    }

    // 2. 收集需要隐藏和取消订阅的股票
    const toRemove = [];
    for (const symbol of subscribedStocks) {
        if (!newSymbols.has(symbol)) {
            toRemove.push(symbol);
        }
    }

    // 3. 统一取消订阅和隐藏图表
    for (const symbol of toRemove) {
        if (window.socket && window.socket.connected) {
            window.socket.emit('unsubscribe', { symbol });
        }
        subscribedStocks.delete(symbol);
        const chartDiv = document.getElementById(`chart-container-${symbol}`);
        if (chartDiv && chartDiv.style.display !== 'none') {
            chartDiv.style.display = 'none';
        }
    }
}

/**
 * 更新指定股票的图表数据
 * @param {string} symbol - 股票代码
 * @param {Array} klineData - 完整的K线数据
 * @param {string|number} transactions - 最新交易次数
 */
export function updateStockData(symbol, klineData, transactions) {
    if (!window.chartManager.getChart(symbol)) {
        console.error(`更新错误: 图表 ${symbol} 不存在。`);
        return;
    }

    // 更新全局交易次数数据
    if (transactions !== null && transactions !== undefined && transactions !== '--') {
        window.globalTransactionData[symbol] = parseInt(transactions) || 0;
    }

    // 使用 ChartManager 更新图表
    window.chartManager.updateChart(symbol, klineData);
    
    // 更新交易次数排名和颜色
    window.chartManager.updateTransactionsRanking(window.globalTransactionData);
}

/**
 * 格式化数字显示
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的字符串
 */
export function formatNumber(num, decimals = 2) {
    if (typeof num !== 'number' || isNaN(num)) {
        return '--';
    }
    return num.toLocaleString(undefined, {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

/**
 * 防抖函数
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要执行的函数
 * @param {number} limit - 时间间隔
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 日志记录工具
 */
export const logger = {
    debug: (message, ...args) => {
        if (console.debug) {
            console.debug(`[ES模块] ${message}`, ...args);
        } else {
            console.log(`[ES模块] DEBUG: ${message}`, ...args);
        }
    },
    
    info: (message, ...args) => {
        console.info(`[ES模块] ${message}`, ...args);
    },
    
    warn: (message, ...args) => {
        console.warn(`[ES模块] ${message}`, ...args);
    },
    
    error: (message, ...args) => {
        console.error(`[ES模块] ${message}`, ...args);
    }
};

/**
 * 前端数据补齐功能 - 补齐K线数据中的时间缺口
 * 从后端迁移的逻辑：检测60秒间隔缺口，用前一个收盘价填充
 * @param {Array} candleData - 原始K线数据数组 [{time, open, high, low, close, volume}, ...]
 * @returns {Array} 补齐后的K线数据数组
 */
export function fillDataGaps(candleData) {
    if (!Array.isArray(candleData) || candleData.length === 0) {
        return candleData;
    }

    // 将时间字符串转换为时间戳并排序
    const processedData = candleData.map(candle => ({
        ...candle,
        timeStamp: typeof candle.time === 'string' ? parseDateTime(candle.time) : candle.time,
        originalTime: candle.time
    }));
    
    const sortedData = processedData.sort((a, b) => a.timeStamp - b.timeStamp);
    const filledData = [];
    
    for (let i = 0; i < sortedData.length; i++) {
        const currentCandle = sortedData[i];
        
        // 添加当前数据点（使用数值时间戳）
        filledData.push({
            time: currentCandle.timeStamp,
            open: currentCandle.open,
            high: currentCandle.high,
            low: currentCandle.low,
            close: currentCandle.close,
            volume: currentCandle.volume
        });
        
        // 检查是否需要填充缺口（除了最后一个数据点）
        if (i < sortedData.length - 1) {
            const nextCandle = sortedData[i + 1];
            const currentTime = currentCandle.timeStamp;
            const nextTime = nextCandle.timeStamp;
            const timeDiff = nextTime - currentTime;
            
            // 如果时间差大于60秒，需要填充缺口
            if (timeDiff > 60) {
                let fillTime = currentTime + 60;
                const prevClose = currentCandle.close;
                
                // 填充缺口
                while (fillTime < nextTime) {
                    const filledCandle = {
                        time: fillTime,
                        open: prevClose,
                        high: prevClose,
                        low: prevClose,
                        close: prevClose,
                        volume: 0
                    };
                    filledData.push(filledCandle);
                    fillTime += 60;
                }
            }
        }
    }
    
    return filledData;
}

/**
 * 确保有足够的5分钟分组数据（从后端迁移的逻辑）
 * 将1分钟数据按5个一组分组，确保有120个5分钟组
 * @param {Array} candleData - 原始K线数据数组
 * @param {number} targetGroups - 目标5分钟组数量（默认120）
 * @returns {Array} 处理后的K线数据数组
 */
export function ensureFiveMinuteGroups(candleData, targetGroups = 120) {
    if (!Array.isArray(candleData) || candleData.length === 0) {
        return candleData;
    }

    const groupSize = 5; // 每组5个1分钟数据
    const processedData = [];
    
    // 将时间字符串转换为时间戳并排序
    const sortedData = candleData.map(candle => ({
        ...candle,
        timeStamp: typeof candle.time === 'string' ? parseDateTime(candle.time) : candle.time,
        originalTime: candle.time
    })).sort((a, b) => a.timeStamp - b.timeStamp);
    
    // 按5分钟分组处理
    let currentGroup = [];
    let groupCount = 0;
    let prevClose = null;
    let prevTime = null;
    
    // 倒序处理，从最新数据开始
    for (let i = sortedData.length - 1; i >= 0 && groupCount < targetGroups; i--) {
        const currentCandle = sortedData[i];
        const currentTime = currentCandle.timeStamp;
        
        // 检查时间缺口并填充
        if (prevTime !== null) {
            let expectedTime = prevTime - 60;
            while (currentTime < expectedTime && currentGroup.length < groupSize) {
                // 创建填充数据
                const filledCandle = {
                    time: expectedTime,
                    open: prevClose,
                    high: prevClose,
                    low: prevClose,
                    close: prevClose,
                    volume: 0
                };
                currentGroup.unshift(filledCandle);
                expectedTime -= 60;
                
                // 如果组满了，处理这个组
                if (currentGroup.length === groupSize) {
                    processedData.unshift(...currentGroup);
                    currentGroup = [];
                    groupCount++;
                    if (groupCount >= targetGroups) break;
                }
            }
        }
        
        // 添加真实数据到组
        if (currentGroup.length < groupSize && groupCount < targetGroups) {
            currentGroup.unshift({
                time: currentTime,
                open: currentCandle.open,
                high: currentCandle.high,
                low: currentCandle.low,
                close: currentCandle.close,
                volume: currentCandle.volume
            });
            
            prevClose = currentCandle.close;
            prevTime = currentTime;
            
            // 如果组满了，处理这个组
            if (currentGroup.length === groupSize) {
                processedData.unshift(...currentGroup);
                currentGroup = [];
                groupCount++;
            }
        }
    }
    
    // 处理剩余的不完整组，用最后的收盘价填充
    if (currentGroup.length > 0 && groupCount < targetGroups && prevTime !== null) {
        while (currentGroup.length < groupSize) {
            prevTime -= 60;
            const filledCandle = {
                time: prevTime,
                open: prevClose,
                high: prevClose,
                low: prevClose,
                close: prevClose,
                volume: 0
            };
            currentGroup.unshift(filledCandle);
        }
        processedData.unshift(...currentGroup);
        groupCount++;
    }
    
    return processedData;
}

/**
 * 解析各种格式的时间字符串，返回Unix时间戳（纽约时间）
 * 从后端迁移的逻辑，支持的格式：
 * - "20250623 14:31:00 US/Eastern"
 * - "20250623 14:31:00"
 * - "2025-06-23 14:31:00"
 * @param {string} dateTimeStr - 时间字符串
 * @returns {number} Unix时间戳
 */
export function parseDateTime(dateTimeStr) {
    try {
        if (!dateTimeStr) {
            return Math.floor(Date.now() / 1000);
        }
        
        let processedStr = dateTimeStr;
        
        // 处理包含时区信息的格式
        if (processedStr.includes(' US/Eastern')) {
            processedStr = processedStr.replace(' US/Eastern', '');
        } else if (processedStr.includes(' America/New_York')) {
            processedStr = processedStr.replace(' America/New_York', '');
        }

        // 检查并修复无效的时间格式（如分钟数>=60）
        if (processedStr.includes(' ') && processedStr.includes(':')) {
            try {
                const [datePart, timePart] = processedStr.split(' ', 2);
                if (timePart.includes(':')) {
                    const timeComponents = timePart.split(':');
                    if (timeComponents.length >= 2) {
                        let hour = parseInt(timeComponents[0]);
                        let minute = parseInt(timeComponents[1]);
                        let second = timeComponents.length > 2 ? parseInt(timeComponents[2]) : 0;

                        // 修复无效的分钟数
                        if (minute >= 60) {
                            hour += Math.floor(minute / 60);
                            minute = minute % 60;
                        }

                        // 修复无效的小时数
                        if (hour >= 24) {
                            hour = hour % 24;
                        }

                        // 重构时间字符串
                        processedStr = `${datePart} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;
                    }
                }
            } catch (e) {
                // 如果解析失败，继续使用原始字符串
            }
        }

        // IB API格式: "20250623 14:31:00"
        const ibMatch = processedStr.match(/^(\d{4})(\d{2})(\d{2}) (\d{2}):(\d{2}):(\d{2})$/);
        if (ibMatch) {
            const [_, year, month, day, hour, minute, second] = ibMatch;
            
            // 将纽约时间转换为UTC时间戳的新方法
            // 步骤1: 创建一个日期对象，假设这是UTC时间
            const utcDate = new Date(Date.UTC(
                parseInt(year),
                parseInt(month) - 1, // JS月份从0开始
                parseInt(day),
                parseInt(hour),
                parseInt(minute),
                parseInt(second)
            ));
            
            // 步骤2: 通过格式化器获取这个时间在纽约的表示
            const nyOptions = {
                timeZone: 'America/New_York',
                year: 'numeric',
                month: 'numeric',
                day: 'numeric',
                hour: 'numeric',
                minute: 'numeric',
                second: 'numeric',
                hour12: false
            };
            
            // 获取nyDate的字符串表示，按照格式化器规则
            const nyDateStr = utcDate.toLocaleString('en-US', nyOptions);
            
            // 步骤3: 解析这个纽约时间字符串，将其作为本地时间
            const localNyDate = new Date(nyDateStr);
            
            // 步骤4: 计算时差
            const timeDiff = utcDate.getTime() - localNyDate.getTime();
            
            // 步骤5: 将原始的纽约时间转换为正确的UTC时间戳
            const originalNyDate = new Date(
                parseInt(year),
                parseInt(month) - 1,
                parseInt(day),
                parseInt(hour),
                parseInt(minute),
                parseInt(second)
            );
            
            // 步骤6: 应用时差，获得正确的UTC时间戳
            const correctUtcTimestamp = originalNyDate.getTime() + timeDiff;
            
            return Math.floor(correctUtcTimestamp / 1000);
        }
        
        // 标准格式: "2025-06-23 14:31:00"
        const stdMatch = processedStr.match(/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/);
        if (stdMatch) {
            const [_, year, month, day, hour, minute, second] = stdMatch;
            
            // 同样应用纽约时间转换逻辑
            const utcDate = new Date(Date.UTC(
                parseInt(year),
                parseInt(month) - 1,
                parseInt(day),
                parseInt(hour),
                parseInt(minute),
                parseInt(second)
            ));
            
            const nyOptions = {
                timeZone: 'America/New_York',
                year: 'numeric',
                month: 'numeric',
                day: 'numeric',
                hour: 'numeric',
                minute: 'numeric',
                second: 'numeric',
                hour12: false
            };
            
            const nyDateStr = utcDate.toLocaleString('en-US', nyOptions);
            const localNyDate = new Date(nyDateStr);
            const timeDiff = utcDate.getTime() - localNyDate.getTime();
            
            const originalNyDate = new Date(
                parseInt(year),
                parseInt(month) - 1,
                parseInt(day),
                parseInt(hour),
                parseInt(minute),
                parseInt(second)
            );
            
            const correctUtcTimestamp = originalNyDate.getTime() + timeDiff;
            
            return Math.floor(correctUtcTimestamp / 1000);
        }
        
        // 斜杠格式: "2025/06/23 14:31:00"
        const slashMatch = processedStr.match(/^(\d{4})\/(\d{2})\/(\d{2}) (\d{2}):(\d{2}):(\d{2})$/);
        if (slashMatch) {
            const [_, year, month, day, hour, minute, second] = slashMatch;
            
            // 同样应用纽约时间转换逻辑
            const utcDate = new Date(Date.UTC(
                parseInt(year),
                parseInt(month) - 1,
                parseInt(day),
                parseInt(hour),
                parseInt(minute),
                parseInt(second)
            ));
            
            const nyOptions = {
                timeZone: 'America/New_York',
                year: 'numeric',
                month: 'numeric',
                day: 'numeric',
                hour: 'numeric',
                minute: 'numeric',
                second: 'numeric',
                hour12: false
            };
            
            const nyDateStr = utcDate.toLocaleString('en-US', nyOptions);
            const localNyDate = new Date(nyDateStr);
            const timeDiff = utcDate.getTime() - localNyDate.getTime();
            
            const originalNyDate = new Date(
                parseInt(year),
                parseInt(month) - 1,
                parseInt(day),
                parseInt(hour),
                parseInt(minute),
                parseInt(second)
            );
            
            const correctUtcTimestamp = originalNyDate.getTime() + timeDiff;
            
            return Math.floor(correctUtcTimestamp / 1000);
        }

        // 如果以上格式都失败，尝试直接解析
        const date = new Date(processedStr);
        if (!isNaN(date.getTime())) {
            // 尝试使用相同的时区转换逻辑
            const utcDate = new Date(Date.UTC(
                date.getFullYear(),
                date.getMonth(),
                date.getDate(),
                date.getHours(),
                date.getMinutes(),
                date.getSeconds()
            ));
            
            const nyOptions = {
                timeZone: 'America/New_York',
                year: 'numeric',
                month: 'numeric',
                day: 'numeric',
                hour: 'numeric',
                minute: 'numeric',
                second: 'numeric',
                hour12: false
            };
            
            const nyDateStr = utcDate.toLocaleString('en-US', nyOptions);
            const localNyDate = new Date(nyDateStr);
            const timeDiff = utcDate.getTime() - localNyDate.getTime();
            
            const correctUtcTimestamp = date.getTime() + timeDiff;
            
            return Math.floor(correctUtcTimestamp / 1000);
        }

        // 最后的备选方案：返回当前时间戳
        console.error('无法解析时间字符串:', dateTimeStr);
        return Math.floor(Date.now() / 1000);
    } catch (error) {
        console.error('解析时间字符串时发生错误:', dateTimeStr, error);
        return Math.floor(Date.now() / 1000);
    }
}
