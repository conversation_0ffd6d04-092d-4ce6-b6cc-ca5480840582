/**
 * WebSocket客户端 ES模块版本
 * 与UMD版本保持完全兼容，使用ES模块导入Socket.IO
 */

import { io } from 'socket.io-client';

export class WebSocketClient {
    constructor(url = null) {
        // 对于Socket.IO，不需要指定ws://或wss://协议，直接使用HTTP/HTTPS
        this.url = url || window.location.origin;
        this.socket = null;
        this.connected = false;
        this.reconnectInterval = 5000;
        this.maxReconnectAttempts = 10;
        this.reconnectAttempts = 0;
        
        // 数据回调函数（与现有系统兼容）
        this.onStocksList = null;
        this.onData = null; // FIX: Renamed from onStockData to onData
        this.onTransactions = null;
        this.onStatus = null;
        this.onConnect = null;
        this.onDisconnect = null;
        this.onError = null;
        
        // 订阅管理
        this.subscriptions = new Set();
        
        this.init();
    }
    
    init() {
        try {
            // 使用ES模块导入的SocketIO客户端
            this.socket = io(this.url, {
                transports: ['websocket', 'polling'],
                timeout: 20000,
                forceNew: true
            });
            
            this.setupEventHandlers();
        } catch (error) {
            console.error('WebSocket初始化失败:', error);
            this.handleError(error);
        }
    }
    
    setupEventHandlers() {
        this.socket.on('connect', () => {
            console.log('WebSocket连接成功 (ES模块版本)');
            this.connected = true;
            this.reconnectAttempts = 0;
            
            if (this.onConnect) {
                this.onConnect();
            }
            
            // 重新订阅之前的股票
            this.resubscribe();
        });
        
        this.socket.on('disconnect', (reason) => {
            console.log('WebSocket连接断开:', reason);
            this.connected = false;
            
            if (this.onDisconnect) {
                this.onDisconnect(reason);
            }
            
            // 自动重连
            if (reason !== 'io client disconnect') {
                this.attemptReconnect();
            }
        });
        
        this.socket.on('error', (error) => {
            console.error('WebSocket错误:', error);
            this.handleError(error);
        });
        
        this.socket.on('connect_error', (error) => {
            console.error('WebSocket连接失败:', error);
            this.connected = false;
            this.handleError(error);
        });
        
        // 数据事件处理（与现有REST API格式兼容）
        this.socket.on('stocks_list', (data) => {
            if (this.onStocksList) {
                this.onStocksList(data);
            }
        });
        
        this.socket.on('data', (data) => {
            // FIX: Check for and call this.onData instead of this.onStockData
            if (this.onData) {
                this.onData(data);
            }
        });
        
        this.socket.on('transactions', (data) => {
            if (this.onTransactions) {
                this.onTransactions(data);
            }
        });
        
        this.socket.on('status', (data) => {
            if (this.onStatus) {
                this.onStatus(data);
            }
        });
    }
    
    handleError(error) {
        if (this.onError) {
            this.onError(error);
        }
        this.attemptReconnect();
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数，停止重连');
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`尝试重连 ${this.reconnectAttempts}/${this.maxReconnectAttempts}...`);
        
        setTimeout(() => {
            this.init();
        }, this.reconnectInterval);
    }
    
    resubscribe() {
        // 重新订阅所有之前订阅的股票
        for (const symbol of this.subscriptions) {
            this.socket.emit('subscribe', { symbol });
        }
    }
    
    subscribe(symbol) {
        if (!this.connected) {
            console.warn('WebSocket未连接，无法订阅');
            return;
        }
        
        this.subscriptions.add(symbol);
        this.socket.emit('subscribe', { symbol });
        console.log(`订阅股票: ${symbol}`);
    }
    
    unsubscribe(symbol) {
        if (!this.connected) {
            console.warn('WebSocket未连接，无法取消订阅');
            return;
        }
        
        this.subscriptions.delete(symbol);
        this.socket.emit('unsubscribe', { symbol });
        console.log(`取消订阅股票: ${symbol}`);
    }
    
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.connected = false;
            console.log('WebSocket连接已手动断开');
        }
    }
    
    // 获取连接状态
    isConnected() {
        return this.connected;
    }
    
    // 获取订阅列表
    getSubscriptions() {
        return Array.from(this.subscriptions);
    }
    
    // 清除所有订阅并向后端发送unsubscribe
    unsubscribeAllStocks() {
        if (!this.connected) return;
        for (const symbol of this.subscriptions) {
            this.socket.emit('unsubscribe', { symbol });
            console.log(`批量取消订阅股票: ${symbol}`);
        }
        this.subscriptions.clear();
    }
}
