/**
 * WebSocket客户端 - 替换现有的REST API轮询
 * 保持与现有功能完全兼容
 */

class WebSocketClient {
    constructor(url = null) {
        // 对于Socket.IO，不需要指定ws://或wss://协议，直接使用HTTP/HTTPS
        this.url = url || window.location.origin;
        this.socket = null;
        this.connected = false;
        this.reconnectInterval = 5000;
        this.maxReconnectAttempts = 10;
        this.reconnectAttempts = 0;
        
        // 数据回调函数（与现有系统兼容）
        this.onStocksList = null;
        this.onData = null; // FIX: Renamed from onStockData to onData
        this.onTransactions = null;
        this.onStatus = null;
        this.onConnect = null;
        this.onDisconnect = null;
        this.onError = null;
        
        // 订阅管理
        this.subscriptions = new Set();
        
        this.init();
    }
    
    init() {
        try {
            // 使用SocketIO客户端（需要在HTML中引入socket.io.js）
            this.socket = io(this.url, {
                transports: ['websocket', 'polling'],
                timeout: 20000,
                forceNew: true,
                reconnection: false // 关键：关闭自动重连
            });
            
            this.setupEventHandlers();
        } catch (error) {
            console.error('WebSocket初始化失败:', error);
            this.handleError(error);
        }
    }
    
    setupEventHandlers() {
        this.socket.on('connect', () => {
            console.log('WebSocket连接成功');
            this.connected = true;
            this.reconnectAttempts = 0;
            
            if (this.onConnect) {
                this.onConnect();
            }
            
            // 重新订阅之前的股票
            this.resubscribe();
        });
        
        this.socket.on('disconnect', (reason) => {
            console.log('WebSocket连接断开:', reason);
            this.connected = false;
            
            if (this.onDisconnect) {
                this.onDisconnect(reason);
            }
            
            // 自动重连
            // if (reason !== 'io client disconnect') {
            //     this.attemptReconnect();
            // }
        });
        
        this.socket.on('error', (error) => {
            console.error('WebSocket错误:', error);
            this.handleError(error);
        });
        
        this.socket.on('connect_error', (error) => {
            console.error('WebSocket连接失败:', error);
            this.connected = false;
            this.handleError(error);
        });
        
        // 数据事件处理（与现有REST API格式兼容）
        this.socket.on('stocks_list', (data) => {
            if (this.onStocksList) {
                this.onStocksList(data);
            }
        });
        
        this.socket.on('data', (data) => {
            // FIX: Check for and call this.onData instead of this.onStockData
            if (this.onData) {
                this.onData(data);
            }
        });
        
        this.socket.on('transactions', (data) => {
            if (this.onTransactions) {
                this.onTransactions(data);
            }
        });
        
        this.socket.on('status', (data) => {
            if (this.onStatus) {
                this.onStatus(data);
            }
        });
        
        this.socket.on('heartbeat', (data) => {
            // 响应心跳
            this.socket.emit('heartbeat');
        });
    }
    
    /**
     * 订阅股票数据
     * @param {string} symbol 股票代码
     */
    subscribe(symbol) {
        if (!this.socket || !this.socket.connected) {
            console.warn('WebSocket未连接，无法订阅');
            return;
        }
        
        this.subscriptions.add(symbol.toUpperCase());
        this.socket.emit('subscribe', { symbol: symbol.toUpperCase() });
        console.log(`订阅股票: ${symbol}`);
    }
    
    /**
     * 取消订阅股票数据
     * @param {string} symbol 股票代码
     */
    unsubscribe(symbol) {
        if (!this.connected) {
            return;
        }
        
        this.subscriptions.delete(symbol.toUpperCase());
        this.socket.emit('unsubscribe', { symbol: symbol.toUpperCase() });
        console.log(`取消订阅股票: ${symbol}`);
    }
    
    /**
     * 重新订阅所有股票
     */
    resubscribe() {
        this.subscriptions.forEach(symbol => {
            this.socket.emit('subscribe', { symbol });
        });
    }
    
    /**
     * 尝试重连
     */
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('达到最大重连次数，停止重连');
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`尝试重连... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (!this.connected) {
                this.init();
            }
        }, this.reconnectInterval);
    }
    
    /**
     * 处理错误
     * @param {Error} error 错误对象
     */
    handleError(error) {
        if (this.onError) {
            this.onError(error);
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
        }
        this.connected = false;
    }
    
    /**
     * 检查连接状态
     * @returns {boolean} 是否已连接
     */
    isConnected() {
        return this.connected && this.socket && this.socket.connected;
    }
}

// 兼容层：模拟现有的fetch API调用
class RestApiCompatLayer {
    constructor(wsClient) {
        this.wsClient = wsClient;
        this.dataCache = {
            stocks: [],
            stockData: {},
            transactions: {},
            status: {}
        };
        
        // 设置WebSocket回调来更新缓存
        this.setupCacheUpdates();
    }
    
    setupCacheUpdates() {
        this.wsClient.onStocksList = (data) => {
            this.dataCache.stocks = data.stock_codes || [];
        };
        
        // FIX: Listen to onData callback
        this.wsClient.onData = (data) => {
            this.dataCache.stockData[data.symbol] = data.data || [];
        };
        
        this.wsClient.onTransactions = (data) => {
            this.dataCache.transactions = data.transactions || {};
        };
        
        this.wsClient.onStatus = (data) => {
            this.dataCache.status = data;
        };
    }
    
    /**
     * 模拟 /stocks API调用
     * @returns {Promise} 返回股票列表Promise
     */
    async fetchStocks() {
        return new Promise((resolve) => {
            resolve(this.dataCache.stocks);
        });
    }
    
    /**
     * 模拟 /data?symbol=xxx API调用
     * @param {string} symbol 股票代码
     * @returns {Promise} 返回股票数据Promise
     */
    async fetchStockData(symbol) {
        // 确保订阅了这只股票
        this.wsClient.subscribe(symbol);
        
        return new Promise((resolve) => {
            const data = this.dataCache.stockData[symbol] || [];
            resolve(data);
        });
    }
    
    /**
     * 模拟 /transactions API调用
     * @returns {Promise} 返回交易次数Promise
     */
    async fetchTransactions() {
        return new Promise((resolve) => {
            resolve(this.dataCache.transactions);
        });
    }
    
    /**
     * 模拟 /status API调用
     * @returns {Promise} 返回状态Promise
     */
    async fetchStatus() {
        return new Promise((resolve) => {
            resolve(this.dataCache.status);
        });
    }
}

// 全局WebSocket客户端实例
window.wsClient = null;
window.restCompat = null;

/**
 * 初始化WebSocket客户端
 * 这个函数将替换现有的定时器轮询机制
 */
function initWebSocketClient() {
    if (window.wsClient) {
        window.wsClient.disconnect();
    }
    
    window.wsClient = new WebSocketClient();
    window.restCompat = new RestApiCompatLayer(window.wsClient);
    
    // 设置状态更新回调
    window.wsClient.onConnect = () => {
        updateStatus('已连接', 'text-green-400');
    };
    
    window.wsClient.onDisconnect = () => {
        updateStatus('连接断开', 'text-red-400');
    };
    
    window.wsClient.onError = (error) => {
        updateStatus('连接错误', 'text-red-400');
        console.error('WebSocket错误:', error);
    };
    
    return window.wsClient;
}

/**
 * 更新连接状态显示
 * @param {string} text 状态文本
 * @param {string} className CSS类名
 */
function updateStatus(text, className) {
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.textContent = text;
        statusElement.className = className;
    }
}
