/**
 * 版本偏好管理模块
 * 记录和恢复用户的版本选择偏好
 */

class VersionPreferenceManager {
    constructor() {
        this.storageKey = 'stock_monitor_version_preference';
        this.init();
    }
    
    init() {
        // 检查是否已有版本偏好
        const savedPreference = this.getPreference();
        const currentVersion = this.getCurrentVersion();
        
        // 如果用户有偏好且与当前版本不同，提示切换
        if (savedPreference && savedPreference !== currentVersion) {
            this.showVersionSwitchSuggestion(savedPreference);
        }
        
        // 监听版本切换按钮
        this.setupVersionSwitchListeners();
    }
    
    getCurrentVersion() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('es') === '1' ? 'es' : 'umd';
    }
    
    getPreference() {
        try {
            return localStorage.getItem(this.storageKey);
        } catch (e) {
            console.warn('无法读取版本偏好设置:', e);
            return null;
        }
    }
    
    setPreference(version) {
        try {
            localStorage.setItem(this.storageKey, version);
            console.log(`版本偏好已保存: ${version}`);
        } catch (e) {
            console.warn('无法保存版本偏好设置:', e);
        }
    }
    
    clearPreference() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('版本偏好已清除');
        } catch (e) {
            console.warn('无法清除版本偏好设置:', e);
        }
    }
    
    showVersionSwitchSuggestion(preferredVersion) {
        const versionName = preferredVersion === 'es' ? 'ES模块版本' : 'UMD版本';
        const message = `检测到您偏好使用${versionName}，是否立即切换？`;
        
        // 创建提示框
        const notification = document.createElement('div');
        notification.className = `
            fixed top-16 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50
            max-w-sm animate-pulse
        `;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium">${message}</p>
                    <div class="mt-2 flex space-x-2">
                        <button id="switch-yes" class="bg-blue-800 hover:bg-blue-900 text-white px-3 py-1 rounded text-xs">
                            立即切换
                        </button>
                        <button id="switch-no" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs">
                            保持当前
                        </button>
                    </div>
                </div>
                <button id="close-notification" class="text-white hover:text-gray-300 ml-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 绑定事件
        notification.querySelector('#switch-yes').onclick = () => {
            this.switchToVersion(preferredVersion);
        };
        
        notification.querySelector('#switch-no').onclick = () => {
            this.setPreference(this.getCurrentVersion()); // 更新偏好为当前版本
            this.removeNotification(notification);
        };
        
        notification.querySelector('#close-notification').onclick = () => {
            this.removeNotification(notification);
        };
        
        // 10秒后自动移除
        setTimeout(() => {
            this.removeNotification(notification);
        }, 10000);
    }
    
    removeNotification(notification) {
        if (notification && notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }
    
    switchToVersion(version) {
        const currentUrl = new URL(window.location);
        
        if (version === 'es') {
            currentUrl.searchParams.set('es', '1');
        } else {
            currentUrl.searchParams.delete('es');
        }
        
        window.location.href = currentUrl.toString();
    }
    
    setupVersionSwitchListeners() {
        // 监听版本切换按钮点击
        const switchButton = document.getElementById('switch-version');
        if (switchButton) {
            switchButton.addEventListener('click', (e) => {
                e.preventDefault();
                
                const currentVersion = this.getCurrentVersion();
                const targetVersion = currentVersion === 'es' ? 'umd' : 'es';
                
                // 保存用户偏好
                this.setPreference(targetVersion);
                
                // 执行切换
                this.switchToVersion(targetVersion);
            });
        }
    }
    
    // 提供全局方法供调试使用
    static getInstance() {
        if (!window._versionPreferenceManager) {
            window._versionPreferenceManager = new VersionPreferenceManager();
        }
        return window._versionPreferenceManager;
    }
}

// 页面加载完成后初始化
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        VersionPreferenceManager.getInstance();
    });
}

// 导出供ES模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VersionPreferenceManager;
}
