// ====== 副系统/主系统模式标记 ======
// 副系统入口请设置：window.isPlaybackMode = true;
// 主系统入口请设置：window.isPlaybackMode = false;
// 例如：
// 在 src/play_back/app.py 渲染的 index-npm.html 中 <script>window.isPlaybackMode = true;</script>
// 在主系统 index-npm.html 中 <script>window.isPlaybackMode = false;</script>

import { syncStockList, fillDataGaps, parseDateTime, ensureFiveMinuteGroups } from './modules/utils.js';
/**
 * NPM ES模块版本主入口文件
 * 使用npm安装的lightweight-charts，这是官方推荐的标准方式
 */

// 从npm包导入（官方推荐方式）
import { io } from 'socket.io-client';

// 导入图表管理器（NPM版本）
import { ChartManager } from './modules/chart-manager-npm.js';

// 全局变量
let socket = null;
let chartManager = null;
let stockSymbols = new Set();
let lastUpdateTime = null;
let globalTransactionData = {}; // 全局交易数据管理（与原版保持一致）

// 将socket设置为全局变量，供回放控制使用
window.socket = null;
// 将订阅的股票列表设置为全局变量
window.subscribedStocks = new Set();

// 初始化应用
async function initializeApp() {
    try {
        // 初始化图表管理器
        chartManager = new ChartManager();
        // 将图表管理器暴露给全局对象，方便测试和调试
        window.chartManager = chartManager;
        // 专门为回放模式提供的实例
        window.chartManagerInstance = chartManager;
        // 暴露图表创建函数供回放页面使用
        window.ensureChartExists = ensureChartExists;
        // 暴露为全局函数，供HTML文件使用
        window.processInitialData = processInitialData;
        
        // 连接WebSocket - 所有模式都需要连接
        await connectWebSocket();
        
        // 初始化状态显示
        updateConnectionStatus('已连接', 'text-green-400');
        
        // 开始时间更新
        startTimeUpdate();
        
        // 启用增量更新调试接口
        window.chartManagerDebug = {
            getStats: (symbol) => chartManager.getIncrementalStats(symbol),
            getMemoryStats: () => chartManager.getMemoryStats(),
            monitorMemoryUsage: (showConsoleLog = true) => chartManager.monitorMemoryUsage(showConsoleLog),
            enableIncremental: (enabled) => chartManager.setIncrementalUpdateEnabled(enabled),
            clearCache: (symbol) => chartManager.clearCacheData(symbol),
            chartManager: chartManager
        };
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        updateConnectionStatus('初始化失败', 'text-red-400');
    }
}

// 连接WebSocket
function connectWebSocket() {
    return new Promise((resolve, reject) => {
        try {
            // NPM版本：在Vite开发环境下使用代理
            const isViteDev = window.location.port === '3000';
            
            if (isViteDev) {
                // 开发环境：使用相对路径，让Vite代理处理
                socket = io('/', {
                    transports: ['websocket', 'polling'],
                    timeout: 5000,
                    forceNew: true
                });
            } else {
                // 生产环境：直接连接到当前地址
                socket = io(window.location.origin, {
                    transports: ['websocket', 'polling'],
                    timeout: 5000
                });
            }
            
            // 连接成功
            socket.on('connect', () => {
                updateConnectionStatus('已连接', 'text-green-400');
                
                // 设置全局socket变量供回放控制使用
                window.socket = socket;

                // 监听/接收特定事件
                // 连接成功后请求完整日线数据，带上当前选中的日期
                if (window.isPlaybackMode) {
                    const dateInput = document.getElementById('playback-date');
                    let date = '';
                    if (dateInput) {
                        date = dateInput.value;
                    }
                    if (date) {
                        socket.emit('complete_daily_data', { date });
                    } else {
                        socket.emit('complete_daily_data');
                    }
                } else {
                    // 主系统：请求股票列表
                    socket.emit('request_stocks_list');
                }
                
                resolve();
            });
            
            // 连接失败
            socket.on('connect_error', (error) => {
                console.error('WebSocket连接失败:', error);
                updateConnectionStatus('连接失败', 'text-red-400');
                reject(error);
            });
            
            // 断开连接
            socket.on('disconnect', (reason) => {
                updateConnectionStatus('连接断开', 'text-yellow-400');
                
                // 自动重连
                setTimeout(() => {
                    connectWebSocket();
                }, 3000);
            });

            // 监听/接收特定事件
            // 注册一个事件监听器
            
            // 处理股票数据
            socket.on('stock_data', handleStockData);
            
            // 处理交易量数据
            socket.on('transaction_data', handleTransactionData);
            
            // 接收股票列表推送
            if (!window.isPlaybackMode) {
                socket.on('stocks_list', handleStocksList);
            }
            
            // 处理实时数据推送（单只）
            // 只会接收"已订阅的股票"推送（包括首次订阅的初始快照和后续实时更新），
            // 由后端 emit('data', ...) 推送
            socket.on('data', handleRealtimeData);
            
            // 处理批量推送（新版 all_data）
            socket.on('all_data', handleAllDataPush);
            
            // 订阅回放行情推送
            socket.on('market_update', handleMarketUpdate);
            
            // 处理完整日期数据包（新的一次性推送模式）
            // 数据流程是：1) 用户点击开始回放按钮 
            //           2) 前端发送start_simulation事件 
            //           3) 后端调用 app.py 中的 complete_daily_data 方法 
            //           4) 后端通过socketio.emit发送 complete_daily_data 事件 
            //           5) 前端handleCompleteDailyData函数处理数据并更新图表。
            socket.on('complete_daily_data', (dataPackage) => {
                // 检查是否有另一个处理函数
                if (window.handleCompleteDailyData && typeof window.handleCompleteDailyData === 'function') {
                    window.handleCompleteDailyData(dataPackage);
                } else {
                    // 使用内部函数
                    handleCompleteDailyData(dataPackage);
                }
            });
            
            // 处理模拟状态更新
            socket.on('simulation_status', handleSimulationStatus);
        } catch (error) {
            console.error('WebSocket初始化失败:', error);
            reject(error);
        }
    });
}

// 处理批量推送的所有股票数据
function handleAllDataPush(allDataList) {
    try {
        if (!Array.isArray(allDataList)) {
            console.warn('收到无效的 all_data 批量推送:', allDataList);
            return;
        }
        // 统计本次推送
        // console.log(`收到 all_data 批量推送, 股票数: ${allDataList.length}`);
        // 批量渲染所有股票
        allDataList.forEach(data => {
            if (!data || !data.symbol || !data.data) return;
            const symbol = data.symbol;
            const candleData = data.data;

            // 确保图表存在
            ensureChartExists(symbol);
            
            // 初始批量数据加载：使用5分钟分组处理
            const processedData = processInitialData(candleData, true);
            
            // 更新图表数据
            if (chartManager) {
                chartManager.updateChart(symbol, processedData);
            }

            // 更新价格和涨跌幅
            if (chartManager && data.change_pct !== undefined) {
                chartManager.updateChangePercentage(symbol, data.change_pct);
            }

            // 更新全局交易次数数据
            if (data.transactions !== null && data.transactions !== undefined && data.transactions !== '--') {
                globalTransactionData[symbol] = parseInt(data.transactions) || 0;
            }
        });
        // 批量后统一刷新交易次数排名
        if (chartManager) {
            chartManager.updateTransactionsRanking(globalTransactionData);
        }
        // 更新时间
        lastUpdateTime = new Date();
        updateLastUpdateTime();
    } catch (error) {
        console.error('处理 all_data 批量推送时出错:', error);
    }
}

// 处理股票数据
function handleStockData(data) {
    try {
        if (!data || !data.symbol) {
            console.warn('收到无效的股票数据:', data);
            return;
        }
        
        const symbol = data.symbol;
        // console.log(`收到股票数据 (NPM版本): ${symbol}`, data);
        
        // 确保图表存在
        ensureChartExists(symbol);
        
        // 更新图表数据
        if (data.kline_data && Array.isArray(data.kline_data)) {
            chartManager.updateChart(symbol, data.kline_data);
        }
        
        // 更新最后更新时间
        lastUpdateTime = new Date();
        updateLastUpdateTime();
        
    } catch (error) {
        console.error('处理股票数据时出错:', error);
    }
}

// 处理交易量数据
function handleTransactionData(data) {
    try {
        if (!data || !chartManager) {
            return;
        }
        
        // console.log('收到交易量数据 (NPM版本):', data);
        
        // 更新全局交易数据
        Object.assign(globalTransactionData, data);
        
        // 更新交易次数排名
        chartManager.updateTransactionsRanking(globalTransactionData);
        
    } catch (error) {
        console.error('处理交易量数据时出错:', error);
    }
}

// 处理股票列表
//stockList === ['AAPL', 'GOOG']
function handleStocksList(stockList) {
    try {
        if (stockList.length === 0) {
            console.warn('收到无效的股票列表数据:', stockList);
            return;
        }

        // console.log('window.isPlaybackMode:', window.isPlaybackMode);
        if (window.isPlaybackMode) {
            // 副系统：全量清空和取消订阅
            clearAllCharts();
            if (window.socket && window.socket.connected) {
                if (window.socket.unsubscribeAllStocks) {
                    window.socket.unsubscribeAllStocks();
                } else if (window.socket.subscriptions) {
                    for (const symbol of window.socket.subscriptions) {
                        window.socket.emit('unsubscribe', { symbol });
                    }
                    window.socket.subscriptions.clear();
                }
            }
        }
        
        // 为每个股票代码创建图表并订阅数据
        stockList.forEach(symbol => {
            // 确保图表存在
            ensureChartExists(symbol);
            // 自动订阅这个股票的实时数据
            if (socket && socket.connected) {
                socket.emit('subscribe', { symbol: symbol });
                // console.log(`自动订阅股票: ${symbol}`);
            }
        });

        // 更新股票数量
        updateStockCount(stockList.length);

        // 显示/隐藏图表容器
        // 订阅/取消订阅后
        syncStockList(stockList);
    } catch (error) {
        console.error('处理股票列表时出错:', error);
    }
}

// 处理实时数据推送
function handleRealtimeData(data) {
    try {
        if (!data || !data.symbol || !data.data) {
            console.warn('收到无效的实时数据:', data);
            return;
        }
        
        const symbol = data.symbol;
        const candleData = data.data; // 这是格式化后的K线数组
        
        // console.log(`收到实时数据 (NPM版本): ${symbol}, 数据点: ${candleData.length}`);
        
        // 检查股票是否在当前列表中
        if (!stockSymbols.has(symbol)) {
            // console.log(`忽略不在当前列表中的股票数据: ${symbol}`);
            return;
        }
        
        // 检查图表容器是否被隐藏
        const chartContainer = document.getElementById(`chart-container-${symbol}`);
        if (chartContainer && chartContainer.style.display === 'none') {
            // console.log(`忽略已隐藏图表的更新: ${symbol}`);
            return;
        }
        
        // 确保图表存在
        ensureChartExists(symbol);
        
        // 实时数据更新：只进行简单的数据补齐
        const filledCandleData = processInitialData(candleData, false);
        
        // 更新图表数据
        if (chartManager) {
            chartManager.updateChart(symbol, filledCandleData);
        }
        
        // 更新涨跌幅百分比
        if (chartManager && data.change_pct !== undefined) {
            chartManager.updateChangePercentage(symbol, data.change_pct);
        }
        
        // 更新全局交易次数数据（与原版保持一致）
        if (data.transactions !== null && data.transactions !== undefined && data.transactions !== '--') {
            globalTransactionData[symbol] = parseInt(data.transactions) || 0;
        }
        
        // 更新交易次数排名和颜色
        if (chartManager) {
            chartManager.updateTransactionsRanking(globalTransactionData);
        }
        
        // 更新最后更新时间
        lastUpdateTime = new Date();
        updateLastUpdateTime();
        
    } catch (error) {
        console.error('处理实时数据时出错:', error);
    }
}

// 回放行情推送处理
function handleMarketUpdate(payload) {
    console.log('[回放] 收到 market_update:', payload);
    // 你可以在这里将数据分发到图表、状态栏等
    // 示例：
    if (payload && payload.stocks_data) {
        Object.entries(payload.stocks_data).forEach(([symbol, candleData]) => {
            ensureChartExists(symbol);
            if (chartManager) {
                chartManager.updateChart(symbol, candleData);
            }
        });
    }
    // 更新顶部状态栏
    if (payload && payload.timestamp) {
        const ts = payload.timestamp.replace('T', ' ');
        const el = document.getElementById('current-time');
        if (el) el.textContent = ts;
    }
}

// 回放处理完整日期数据包（一次性推送模式）
function handleCompleteDailyData(dataPackage) {
    try {
        // console.log('📦 收到完整日期数据包:', dataPackage);
        
        // 检查数据是否有效
        if (!dataPackage || !dataPackage.top5_stocks || !Array.isArray(dataPackage.top5_stocks) || dataPackage.top5_stocks.length === 0) {
            console.warn('收到无效的完整数据包，无法渲染图表:', dataPackage);
            return;
        }
        
        // console.log('🔄 使用原始数据包进行回放');
        
        // 提取数据包中的信息
        const { date, top5_stocks, stocks_data } = dataPackage;
        // console.log('📦 收到的数据包:', dataPackage);
        
        // 清空现有图表
        clearAllCharts();
        
        // 处理股票数据
        // console.log(`🔄 开始处理 ${top5_stocks.length} 只股票的图表`);
        
        top5_stocks.forEach(symbol => {
            // console.log(`🔄 处理股票 ${symbol}`);
            
            // 确保图表容器存在
            ensureChartExists(symbol);
            
            if (chartManager) {
                try {
                    chartManager.updateChart(symbol, dataPackage.stocks_data[symbol]);
                } catch (error) {
                    console.error(`❌ ${symbol}: 更新图表失败:`, error);
                }
            } else {
                console.error(`❌ ${symbol}: chartManager不存在`);
            }
        });
        
        // console.log(`🔢 更新股票计数: ${top5_stocks.length}`);
        updateStockCount(top5_stocks.length);
        
        // console.log(`🔄 更新连接状态: 已加载${date}数据`);
        updateConnectionStatus(`已加载${date}数据 (测试数据)`, 'text-green-400');
        
        // console.log(`🔔 显示数据加载通知`);
        showDataLoadedNotification(date, dataPackage.data_info || {});
        
        // console.log(`⏱️ 更新最后更新时间`);
        lastUpdateTime = new Date();
        updateLastUpdateTime();
        
        // console.log(`✅ handleCompleteDailyData处理完成`);
    } catch (error) {
        console.error('❌ 处理完整日期数据包时出错:', error);
    }
}

// 处理模拟状态更新
function handleSimulationStatus(statusData) {
    try {
        console.log('📊 模拟状态更新:', statusData);
        
        const { status, message, data_summary } = statusData;
        
        // 更新状态显示
        let statusText = message || status;
        let statusColor = 'text-blue-400';
        
        // 更新回放按钮状态
        const startBtn = document.getElementById('start-playback-btn');
        const stopBtn = document.getElementById('stop-playback-btn');
        
        switch (status) {
            case 'data_loaded':
                statusColor = 'text-green-400';
                if (data_summary) {
                    statusText += ` (${data_summary.stocks}只股票)`;
                }
                if (startBtn && stopBtn) {
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                }
                break;
            case 'started':
                statusColor = 'text-green-400';
                if (startBtn && stopBtn) {
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                }
                break;
            case 'stopped':
                statusColor = 'text-yellow-400';
                if (startBtn && stopBtn) {
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                }
                break;
            case 'error':
                statusColor = 'text-red-400';
                if (startBtn && stopBtn) {
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                }
                break;
                break;
            case 'stopped':
            case 'completed':
                statusColor = 'text-yellow-400';
                break;
            case 'error':
                statusColor = 'text-red-400';
                break;
        }
        
        updateConnectionStatus(statusText, statusColor);
        
    } catch (error) {
        console.error('处理模拟状态更新时出错:', error);
    }
}

// 清空所有图表
function clearAllCharts() {
    try {
        if (chartManager) {
            chartManager.destroyAllCharts();
        }
        
        // 清空图表网格
        const chartsGrid = document.getElementById('charts-grid');
        if (chartsGrid) {
            chartsGrid.innerHTML = '';
        }
        
        // 重置全局变量
        stockSymbols = new Set();
        globalTransactionData = {};
        
        // 更新股票数量
        updateStockCount(0);
        
        console.log('✅ 已清空所有图表');
        
    } catch (error) {
        console.error('清空图表时出错:', error);
    }
}

// 显示数据加载完成通知
function showDataLoadedNotification(date, dataInfo) {
    try {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'fixed top-20 right-4 bg-green-600 text-white px-4 py-2 rounded shadow-lg z-50 transition-all duration-300';
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                    <div class="font-bold">${date} 数据加载完成</div>
                    ${dataInfo ? `<div class="text-sm">${dataInfo.total_stocks}只股票，${dataInfo.total_candles}根K线</div>` : ''}
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 3000);
        
    } catch (error) {
        console.error('显示通知时出错:', error);
    }
}

// 确保图表存在
function ensureChartExists(symbol) {
    if (!stockSymbols.has(symbol)) {
        createChartForStock(symbol);
        stockSymbols.add(symbol);
    }
}

// 为股票创建图表
function createChartForStock(symbol) {
    try {
        const chartsGrid = document.getElementById('charts-grid');
        if (!chartsGrid) {
            console.error('找不到图表网格容器');
            return;
        }
        
        // console.log(`[HTML结构调试] 开始创建图表容器: ${symbol}`);
        
        // 创建图表容器（与CDN版本完全一致）
        const chartContainer = document.createElement('div');
        chartContainer.id = `chart-container-${symbol}`;
        chartContainer.className = 'bg-gray-800 p-4 rounded-lg shadow-lg w-full max-w-none';
        chartContainer.innerHTML = `
            <div class="flex items-center mb-2 w-full">
                <div class="flex items-center space-x-2">
                    <h2 class="text-xl font-bold text-white mb-0 whitespace-nowrap">${symbol}</h2>
                    <span id="change-${symbol}" class="price-neutral whitespace-nowrap">--</span>
                </div>
                <div class="flex-1"></div>
                <div class="flex items-center space-x-2 text-sm justify-end pr-2">
                    <span class="text-gray-400 whitespace-nowrap">交易次数:</span>
                    <span id="transactions-${symbol}" class="font-mono text-cyan-400 whitespace-nowrap">--</span>
                    <span id="surge-${symbol}" class="ml-2 font-medium whitespace-nowrap"></span>
                </div>
            </div>
            <div id="chart-${symbol}" class="h-80 w-full"></div>
        `;
        
        chartsGrid.appendChild(chartContainer);
        
        // 创建图表实例
        const chartId = `chart-${symbol}`;
        chartManager.createChart(chartId, symbol);
        
        // console.log(`[HTML结构调试] 图表创建完成: ${symbol}`);
        
    } catch (error) {
        console.error(`创建图表失败 (${symbol}):`, error);
    }
}

// 更新连接状态
function updateConnectionStatus(status, className) {
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.textContent = status;
        statusElement.className = className;
    }
}

// 更新股票数量
function updateStockCount(count = stockSymbols.size) {
    const countElement = document.getElementById('stock-count');
    if (countElement) {
        countElement.textContent = count;
    }
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const updateElement = document.getElementById('last-update');
    if (updateElement && lastUpdateTime) {
        // 使用纽约时区，与当前时间保持一致
        updateElement.textContent = lastUpdateTime.toLocaleTimeString('en-US', { timeZone: 'America/New_York', hour12: false });
    }
}

// 开始时间更新
function startTimeUpdate() {
    function updateCurrentTime() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            const now = new Date();
            // 转为纽约时区（与原版保持一致）
            const nyTime = now.toLocaleTimeString('en-US', { timeZone: 'America/New_York', hour12: false });
            timeElement.textContent = nyTime;
        }
    }
    
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
}

/**
 * 处理初始数据加载 - 确保有足够的5分钟分组数据
 * @param {Array} candleData - 原始K线数据
 * @param {boolean} isInitialLoad - 是否为初始加载
 * @returns {Array} 处理后的数据
 */
function processInitialData(candleData, isInitialLoad = false) {
    if (!isInitialLoad) {
        // 非初始加载：只进行简单的数据补齐
        return fillDataGaps(candleData);
    }
    
    // 初始加载：先确保有120个5分钟组，再进行数据补齐
    const groupedData = ensureFiveMinuteGroups(candleData, 120);
    return fillDataGaps(groupedData);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // console.log('DOM加载完成，开始初始化NPM ES模块版本...');
    initializeApp();
    
    // 监听日期选择变化，自动请求新股票列表
    if (window.isPlaybackMode) {
        const dateInput = document.getElementById('playback-date');
        if (dateInput) {
            dateInput.addEventListener('change', () => {
                const date = dateInput.value;
                // 切换日期时先取消所有旧股票订阅
                if (window.socket && window.socket.connected) {
                    if (window.socket.unsubscribeAllStocks) {
                        window.socket.unsubscribeAllStocks();
                    } else if (window.socket.subscriptions) {
                        // 兼容性处理
                        for (const symbol of window.socket.subscriptions) {
                            window.socket.emit('unsubscribe', { symbol });
                        }
                        window.socket.subscriptions.clear();
                    }
                }
                if (window.socket && window.socket.connected && date) {
                    window.socket.emit('complete_daily_data', { date });
                    console.log('[切换日期] 已请求完整日线数据，日期:', date);
                }
            });
        }
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (socket) {
        socket.disconnect();
    }
    if (chartManager) {
        chartManager.destroyAllCharts();
    }
});

// 已删除绑定"开始回放"按钮事件，因为在index-play-back.html中已经处理
