// 回放控制弹窗与交互逻辑

function showPlaybackDialog() {
  if (document.getElementById('playback-modal')) return;
  const modal = document.createElement('div');
  modal.id = 'playback-modal';
  modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';
  modal.innerHTML = `
    <div class="bg-gray-800 rounded-lg shadow-lg p-6 w-80">
      <h2 class="text-lg font-bold mb-4 text-white">回放设置</h2>
      <form id="playback-form" class="space-y-4">
        <div>
          <label class="block text-gray-300 mb-1">日期</label>
          <input type="date" id="playback-date" class="w-full px-2 py-1 rounded bg-gray-700 text-white" required />
        </div>
        <div>
          <label class="block text-gray-300 mb-1">起始时间</label>
          <input type="time" id="playback-time" class="w-full px-2 py-1 rounded bg-gray-700 text-white" value="09:30" required />
        </div>
        <div>
          <label class="block text-gray-300 mb-1">速度 (毫秒/步)</label>
          <input type="number" id="playback-speed" class="w-full px-2 py-1 rounded bg-gray-700 text-white" min="100" max="10000" step="100" value="1000" required />
        </div>
        <div class="flex justify-end space-x-2 mt-4">
          <button type="button" class="px-3 py-1 rounded bg-gray-600 text-white hover:bg-gray-500" onclick="closePlaybackDialog()">取消</button>
          <button type="submit" class="px-3 py-1 rounded bg-green-600 text-white hover:bg-green-700">开始回放</button>
        </div>
      </form>
    </div>
  `;
  document.body.appendChild(modal);
  document.getElementById('playback-form').onsubmit = function(e) {
    e.preventDefault();
    const date = document.getElementById('playback-date').value;
    const time = document.getElementById('playback-time').value;
    const speed = parseInt(document.getElementById('playback-speed').value, 10);
    closePlaybackDialog();
    if (window.socket) {
      window.socket.emit('start_simulation', { date, start_time: time, speed });
    } else {
      alert('WebSocket 未连接，无法启动回放');
    }
  };
}

function closePlaybackDialog() {
  const modal = document.getElementById('playback-modal');
  if (modal) modal.remove();
}

// 挂载到全局
window.showPlaybackDialog = showPlaybackDialog;
window.closePlaybackDialog = closePlaybackDialog;
