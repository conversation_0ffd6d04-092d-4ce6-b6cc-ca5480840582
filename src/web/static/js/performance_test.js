/**
 * NPM ES模块版本性能测试脚本
 * 专门测试使用 lightweight-charts NPM 包的性能表现
 */

class NPMPerformanceMonitor {
    constructor() {
        this.results = {};
        this.testStartTime = Date.now();
        this.version = 'npm-es-module';
    }
    
    // 测试页面加载性能
    testPageLoadPerformance() {
        console.log('🚀 开始NPM ES模块页面加载性能测试...');
        
        // 获取Performance API数据
        const perfData = performance.getEntriesByType('navigation')[0];
        const paintEntries = performance.getEntriesByType('paint');
        
        this.results.loadingPerformance = {
            // DNS查询时间
            dnsTime: perfData.domainLookupEnd - perfData.domainLookupStart,
            // TCP连接时间
            connectTime: perfData.connectEnd - perfData.connectStart,
            // 请求响应时间
            responseTime: perfData.responseEnd - perfData.requestStart,
            // DOM解析时间
            domParseTime: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            // 页面完全加载时间
            totalLoadTime: perfData.loadEventEnd - perfData.navigationStart,
            // 首次渲染时间
            firstPaint: paintEntries.find(entry => entry.name === 'first-paint')?.startTime || 0,
            // 首次内容渲染时间
            firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
        };
        
        console.log('NPM ES模块版本性能数据:', this.results.loadingPerformance);
    }
    
    // 测试NPM模块加载性能
    testNPMModulePerformance() {
        console.log('📦 测试NPM ES模块加载性能...');
        
        const resourceEntries = performance.getEntriesByType('resource');
        
        // 筛选NPM包相关资源
        const npmResources = resourceEntries.filter(entry => 
            entry.name.includes('lightweight-charts') || 
            entry.name.includes('socket.io-client') ||
            entry.name.includes('main-npm.js') ||
            entry.name.includes('chart-manager-npm.js') ||
            (entry.name.includes('.js') && entry.name.includes('static'))
        );
        
        const moduleStats = npmResources.map(resource => ({
            name: resource.name.split('/').pop(),
            fullPath: resource.name,
            size: resource.transferSize || 0,
            loadTime: resource.responseEnd - resource.requestStart,
            cacheHit: resource.transferSize === 0,
            type: this.getModuleType(resource.name)
        }));
        
        this.results.modulePerformance = {
            totalModules: npmResources.length,
            totalSize: moduleStats.reduce((sum, mod) => sum + mod.size, 0),
            totalLoadTime: Math.max(...moduleStats.map(mod => mod.loadTime)),
            npmPackageSize: moduleStats.filter(m => m.type === 'npm').reduce((sum, mod) => sum + mod.size, 0),
            esModuleCount: moduleStats.filter(m => m.type === 'es-module').length,
            modules: moduleStats
        };
        
        console.log('NPM ES模块性能:', this.results.modulePerformance);
    }
    
    // 获取模块类型
    getModuleType(url) {
        if (url.includes('lightweight-charts') || url.includes('socket.io-client')) {
            return 'npm';
        } else if (url.includes('main-npm.js') || url.includes('chart-manager-npm.js')) {
            return 'es-module';
        } else if (url.includes('.js')) {
            return 'script';
        }
        return 'other';
    }
    
    // 测试内存使用情况
    testMemoryUsage() {
        console.log('🧠 测试NPM ES模块内存使用情况...');
        
        if ('memory' in performance) {
            const memInfo = performance.memory;
            
            this.results.memoryUsage = {
                usedJSHeapSize: memInfo.usedJSHeapSize,
                totalJSHeapSize: memInfo.totalJSHeapSize,
                jsHeapSizeLimit: memInfo.jsHeapSizeLimit,
                memoryEfficiency: (memInfo.usedJSHeapSize / memInfo.totalJSHeapSize * 100).toFixed(2) + '%'
            };
            
            console.log('NPM ES模块版本内存使用:', this.results.memoryUsage);
        } else {
            console.warn('浏览器不支持Memory API');
        }
    }
    
    // 测试图表渲染性能
    testChartRenderingPerformance() {
        console.log('📊 测试图表渲染性能...');
        
        const startTime = performance.now();
        
        // 尝试获取图表管理器并测试渲染性能
        if (window.chartManagerDebug && window.chartManagerDebug.chartManager) {
            const chartManager = window.chartManagerDebug.chartManager;
            const charts = chartManager.charts;
            
            const chartCount = charts.size;
            const endTime = performance.now();
            
            this.results.chartPerformance = {
                chartCount: chartCount,
                renderTime: endTime - startTime,
                avgRenderTimePerChart: chartCount > 0 ? (endTime - startTime) / chartCount : 0,
                chartsWithData: Array.from(charts.keys()).filter(symbol => {
                    const data = chartManager.getChartData(symbol);
                    return data && data.length > 0;
                }).length
            };
            
            console.log('图表渲染性能:', this.results.chartPerformance);
        } else {
            console.warn('图表管理器未找到，跳过图表性能测试');
        }
    }
    
    // 测试ES模块兼容性
    async testESModuleCompatibility() {
        console.log('🌐 测试ES模块兼容性...');
        
        const compatibility = {
            // ES6模块支持 - 使用更准确的检测方法
            esModules: this.checkESModuleSupport(),
            // 动态导入支持
            dynamicImport: await this.checkDynamicImportSupport(),
            // Promise支持
            promises: typeof Promise !== 'undefined',
            // Fetch API支持
            fetch: typeof fetch !== 'undefined',
            // WebSocket支持
            webSocket: typeof WebSocket !== 'undefined',
            // Local Storage支持
            localStorage: typeof localStorage !== 'undefined',
            // Performance API支持
            performanceAPI: typeof performance !== 'undefined',
            // ResizeObserver支持
            resizeObserver: typeof ResizeObserver !== 'undefined',
            // NPM包特定功能
            lightweightChartsAvailable: this.checkLightweightChartsSupport(),
            socketIOAvailable: this.checkSocketIOSupport()
        };
        
        this.results.esModuleCompatibility = compatibility;
        
        console.log('ES模块兼容性:', compatibility);
    }
    
    // 异步检测动态导入支持
    async checkDynamicImportSupport() {
        try {
            // 方法1: 检测 Function.constructor 是否支持 import()
            try {
                new Function('return import("data:text/javascript,export default 42")')();
                console.log('✅ 动态导入语法支持检测通过');
            } catch (syntaxError) {
                console.log('❌ 动态导入语法不支持:', syntaxError.message);
                return false;
            }
            
            // 方法2: 实际测试动态导入（使用较短的超时时间）
            const importPromise = import('data:text/javascript,export default 42');
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('动态导入超时')), 1000)
            );
            
            await Promise.race([importPromise, timeoutPromise]);
            console.log('✅ 动态导入功能测试通过');
            return true;
        } catch (error) {
            console.log('❌ 动态导入功能测试失败:', error.message);
            
            // 回退检测：检查浏览器版本
            const userAgent = navigator.userAgent;
            const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
            const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
            const safariMatch = userAgent.match(/Version\/(\d+).*Safari/);
            const edgeMatch = userAgent.match(/Edge\/(\d+)/);
            
            if (chromeMatch && parseInt(chromeMatch[1]) >= 63) {
                console.log('⚡ 基于浏览器版本判断动态导入应该支持 (Chrome 63+)');
                return true;
            } else if (firefoxMatch && parseInt(firefoxMatch[1]) >= 67) {
                console.log('⚡ 基于浏览器版本判断动态导入应该支持 (Firefox 67+)');
                return true;
            } else if (safariMatch && parseInt(safariMatch[1]) >= 11) {
                console.log('⚡ 基于浏览器版本判断动态导入应该支持 (Safari 11.1+)');
                return true;
            } else if (edgeMatch && parseInt(edgeMatch[1]) >= 79) {
                console.log('⚡ 基于浏览器版本判断动态导入应该支持 (Edge 79+)');
                return true;
            }
            
            return false;
        }
    }
    
    // 检测 LightWeight Charts 支持
    checkLightweightChartsSupport() {
        const checks = {
            globalObject: false,
            chartManager: false,
            npmPackage: false
        };
        
        try {
            // 检查全局 LightweightCharts 对象
            if (window.LightweightCharts && typeof window.LightweightCharts.createChart === 'function') {
                checks.globalObject = true;
                console.log('✅ 全局 LightweightCharts 对象检测通过');
            }
            
            // 检查图表管理器
            if (window.chartManagerDebug && 
                window.chartManagerDebug.chartManager && 
                typeof window.chartManagerDebug.chartManager.createChart === 'function') {
                checks.chartManager = true;
                console.log('✅ 图表管理器检测通过');
            }
            
            // 检查是否通过NPM包方式加载（检查模块脚本）
            const moduleScripts = document.querySelectorAll('script[type="module"]');
            for (const script of moduleScripts) {
                if (script.src && (script.src.includes('main-npm.js') || script.src.includes('chart-manager-npm.js'))) {
                    checks.npmPackage = true;
                    console.log('✅ NPM包脚本检测通过');
                    break;
                }
            }
            
        } catch (error) {
            console.log('❌ LightWeight Charts 检测错误:', error.message);
        }
        
        const isSupported = checks.globalObject || checks.chartManager || checks.npmPackage;
        console.log(`LightWeight Charts 支持检测结果: ${isSupported ? '✅' : '❌'}`, checks);
        
        return isSupported;
    }
    
    // 更精确的 Socket.IO 检测
    checkSocketIOSupport() {
        const checks = {
            globalIO: false,
            ioFunction: false,
            socketInstance: false,
            scriptLoaded: false
        };
        
        try {
            // 检查全局 io 对象及其关键方法
            if (window.io && typeof window.io === 'function') {
                checks.globalIO = true;
                console.log('✅ 全局 io 对象检测通过');
                
                // 检查关键方法
                if (typeof window.io.connect === 'function' || typeof window.io === 'function') {
                    checks.ioFunction = true;
                    console.log('✅ Socket.IO 连接方法检测通过');
                }
            }
            
            // 检查是否已经有socket实例
            if (window.socket && typeof window.socket.emit === 'function') {
                checks.socketInstance = true;
                console.log('✅ Socket.IO 实例检测通过');
            }
            
            // 检查Socket.IO脚本是否已加载
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                if (script.src && script.src.includes('socket.io')) {
                    checks.scriptLoaded = true;
                    console.log('✅ Socket.IO 脚本加载检测通过');
                    break;
                }
            }
            
            // 额外检查：验证Socket.IO版本
            if (window.io && window.io.version) {
                console.log(`📋 Socket.IO 版本: ${window.io.version}`);
            }
            
        } catch (error) {
            console.log('❌ Socket.IO 检测错误:', error.message);
        }
        
        const isSupported = checks.globalIO || checks.socketInstance || checks.scriptLoaded;
        console.log(`Socket.IO 支持检测结果: ${isSupported ? '✅' : '❌'}`, checks);
        
        return isSupported;
    }
    
    // 检测ES模块支持的准确方法
    checkESModuleSupport() {
        // 方法1: 检查是否支持 script type="module"
        const script = document.createElement('script');
        const supportsModules = 'noModule' in script;
        
        // 方法2: 检查用户代理字符串 (更精确的检测)
        const userAgent = navigator.userAgent;
        const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
        const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
        const safariMatch = userAgent.match(/Version\/(\d+).*Safari/);
        const edgeMatch = userAgent.match(/Edge\/(\d+)/);
        
        let browserSupported = false;
        
        if (chromeMatch && parseInt(chromeMatch[1]) >= 61) {
            browserSupported = true;
        } else if (firefoxMatch && parseInt(firefoxMatch[1]) >= 60) {
            browserSupported = true;
        } else if (safariMatch && parseInt(safariMatch[1]) >= 11) {
            browserSupported = true;
        } else if (edgeMatch && parseInt(edgeMatch[1]) >= 16) {
            browserSupported = true;
        }
        
        // 方法3: 实际功能检测 - 检查当前页面是否成功加载了ES模块
        const hasLoadedESModules = !!document.querySelector('script[type="module"]');
        
        // 综合判断
        return supportsModules && browserSupported && hasLoadedESModules;
    }
    
    // 运行所有测试
    async runAllTests() {
        console.log('🔍 开始NPM ES模块性能测试...');
        console.log('=====================================');
        
        // 延迟执行，确保页面完全加载
        return new Promise((resolve) => {
            setTimeout(async () => {
                try {
                    this.testPageLoadPerformance();
                    this.testNPMModulePerformance();
                    this.testMemoryUsage();
                    this.testChartRenderingPerformance();
                    
                    // 异步测试ES模块兼容性
                    await this.testESModuleCompatibility();
                    
                    this.generateReport();
                    resolve(this.results);
                } catch (error) {
                    console.error('❌ 性能测试过程中发生错误:', error);
                    this.generateReport();
                    resolve(this.results);
                }
            }, 2000);
        });
    }
    
    // 生成性能报告
    generateReport() {
        console.log('\n📊 NPM ES模块性能报告');
        console.log('=====================================');
        
        if (this.results.loadingPerformance) {
            console.log('\n⏱️ 页面加载性能:');
            console.log(`- 总加载时间: ${this.results.loadingPerformance.totalLoadTime.toFixed(2)}ms`);
            console.log(`- 首次渲染: ${this.results.loadingPerformance.firstPaint.toFixed(2)}ms`);
            console.log(`- 首次内容渲染: ${this.results.loadingPerformance.firstContentfulPaint.toFixed(2)}ms`);
            console.log(`- DOM解析时间: ${this.results.loadingPerformance.domParseTime.toFixed(2)}ms`);
        }
        
        if (this.results.modulePerformance) {
            console.log('\n📦 NPM模块性能:');
            console.log(`- 总模块数量: ${this.results.modulePerformance.totalModules}`);
            console.log(`- 总大小: ${(this.results.modulePerformance.totalSize / 1024).toFixed(2)}KB`);
            console.log(`- NPM包大小: ${(this.results.modulePerformance.npmPackageSize / 1024).toFixed(2)}KB`);
            console.log(`- ES模块数量: ${this.results.modulePerformance.esModuleCount}`);
            console.log(`- 最大加载时间: ${this.results.modulePerformance.totalLoadTime.toFixed(2)}ms`);
        }
        
        if (this.results.memoryUsage) {
            console.log('\n🧠 内存使用:');
            console.log(`- 已用内存: ${(this.results.memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
            console.log(`- 内存效率: ${this.results.memoryUsage.memoryEfficiency}`);
        }
        
        if (this.results.chartPerformance) {
            console.log('\n📊 图表渲染性能:');
            console.log(`- 图表数量: ${this.results.chartPerformance.chartCount}`);
            console.log(`- 有数据的图表: ${this.results.chartPerformance.chartsWithData}`);
            console.log(`- 平均渲染时间: ${this.results.chartPerformance.avgRenderTimePerChart.toFixed(2)}ms/图表`);
        }
        
        if (this.results.esModuleCompatibility) {
            console.log('\n🌐 ES模块兼容性:');
            const compat = this.results.esModuleCompatibility;
            
            // 核心功能检测结果
            console.log('📋 核心功能支持:');
            Object.entries(compat).forEach(([feature, supported]) => {
                const icon = supported ? '✅' : '❌';
                const label = this.getFeatureLabel(feature);
                console.log(`  ${icon} ${label}: ${supported}`);
            });
            
            // 兼容性分析
            const coreFeatures = ['esModules', 'promises', 'fetch', 'webSocket'];
            const advancedFeatures = ['dynamicImport', 'resizeObserver', 'performanceAPI'];
            const npmFeatures = ['lightweightChartsAvailable', 'socketIOAvailable'];
            
            const coreSupport = coreFeatures.every(f => compat[f]);
            const advancedSupport = advancedFeatures.every(f => compat[f]);
            const npmSupport = npmFeatures.every(f => compat[f]);
            
            console.log('\n📊 兼容性评估:');
            console.log(`  🏗️ 核心功能: ${coreSupport ? '✅ 完全支持' : '❌ 部分不支持'}`);
            console.log(`  ⚡ 高级功能: ${advancedSupport ? '✅ 完全支持' : '⚠️ 部分不支持'}`);
            console.log(`  📦 NPM包功能: ${npmSupport ? '✅ 完全支持' : '⚠️ 部分不支持'}`);
            
            // 总体兼容性评分
            const totalFeatures = Object.keys(compat).length;
            const supportedFeatures = Object.values(compat).filter(Boolean).length;
            const compatibilityScore = Math.round((supportedFeatures / totalFeatures) * 100);
            
            console.log(`\n🎯 总体兼容性评分: ${compatibilityScore}%`);
            
            if (compatibilityScore >= 90) {
                console.log('   🎉 优秀！浏览器对NPM ES模块的支持非常好');
            } else if (compatibilityScore >= 75) {
                console.log('   👍 良好！浏览器基本支持NPM ES模块功能');
            } else if (compatibilityScore >= 50) {
                console.log('   ⚠️ 一般！部分功能可能受限，建议升级浏览器');
            } else {
                console.log('   ❌ 较差！建议升级到现代浏览器以获得最佳体验');
            }
        }
        
        // 保存结果到sessionStorage
        sessionStorage.setItem('npm_performance_results', JSON.stringify(this.results));
        
        // 更准确的兼容性警告和建议
        if (this.results.esModuleCompatibility) {
            const compat = this.results.esModuleCompatibility;
            
            console.log('\n💡 NPM ES模块优势:');
            console.log('  🚀 原生ES模块，更好的代码组织');
            console.log('  📦 NPM包管理，版本控制更精确');
            console.log('  🔧 开发工具支持更好（VS Code、Vite等）');
            console.log('  🌳 Tree shaking支持，减少打包体积');
            console.log('  ⚡ 浏览器原生模块缓存机制');
            
            // 详细的问题诊断和解决建议
            if (!compat.esModules) {
                console.warn('\n⚠️ ES模块支持检测失败，可能的原因和解决方案:');
                console.warn('  🔍 问题诊断:');
                console.warn('    - 浏览器版本过旧 (需要Chrome 61+, Firefox 60+, Safari 11+, Edge 16+)');
                console.warn('    - 页面未正确加载ES模块脚本');
                console.warn('    - 服务器MIME类型配置问题');
                console.warn('    - CORS策略阻止模块加载');
                console.warn('  💡 解决建议:');
                console.warn('    1. 升级浏览器到最新版本');
                console.warn('    2. 检查网络控制台是否有模块加载错误');
                console.warn('    3. 确认服务器正确配置 .js 文件的 MIME 类型');
                console.warn('    4. 检查 script[type="module"] 标签是否正确');
            } else {
                console.log('\n✅ ES模块支持正常，性能最佳');
            }
            
            if (!compat.dynamicImport) {
                console.warn('\n⚠️ 动态导入功能受限，影响和解决方案:');
                console.warn('  📊 影响评估:');
                console.warn('    - 无法按需加载模块，可能影响首屏加载性能');
                console.warn('    - 代码分割功能受限');
                console.warn('    - 无法实现懒加载模式');
                console.warn('  💡 解决建议:');
                console.warn('    1. 升级浏览器 (Chrome 63+, Firefox 67+, Safari 11.1+, Edge 79+)');
                console.warn('    2. 考虑使用静态 import 语句预加载所需模块');
                console.warn('    3. 实现传统的脚本标签加载方案作为备选');
            }
            
            if (!compat.lightweightChartsAvailable) {
                console.warn('\n⚠️ LightWeight Charts NPM包未正确加载，排查步骤:');
                console.warn('  🔍 检查项目:');
                console.warn('    1. 检查网络控制台是否有模块加载失败的错误');
                console.warn('    2. 确认 main-npm.js 和 chart-manager-npm.js 是否正常加载');
                console.warn('    3. 检查 package.json 中的 lightweight-charts 依赖版本');
                console.warn('    4. 确认图表容器元素是否存在');
                console.warn('  💡 解决方案:');
                console.warn('    1. 刷新页面重试');
                console.warn('    2. 检查控制台网络面板的加载状态');
                console.warn('    3. 确认服务器正常运行且资源路径正确');
            }
            
            if (!compat.socketIOAvailable) {
                console.warn('\n⚠️ Socket.IO客户端未正确加载，排查步骤:');
                console.warn('  🔍 检查项目:');
                console.warn('    1. 确认 Socket.IO 服务器是否正常运行');
                console.warn('    2. 检查网络控制台是否有连接错误');
                console.warn('    3. 确认 socket.io-client 包是否正确安装');
                console.warn('    4. 检查防火墙和代理设置');
                console.warn('  💡 解决方案:');
                console.warn('    1. 检查 WebSocket 服务器状态');
                console.warn('    2. 确认端口 5001 是否可访问');
                console.warn('    3. 尝试重启 WebSocket 服务器');
                console.warn('    4. 检查浏览器是否阻止了 WebSocket 连接');
            }
            
            // 性能优化建议
            if (compat.esModules && compat.dynamicImport) {
                console.log('\n🚀 性能优化建议:');
                console.log('  ✨ 当前环境已支持所有现代化功能，可以考虑:');
                console.log('    1. 启用代码分割，按需加载图表组件');
                console.log('    2. 使用 Web Workers 处理大量数据计算');
                console.log('    3. 实现渐进式加载策略');
                console.log('    4. 启用 Service Worker 缓存策略');
            }
        }
    }
    
    // 获取性能历史记录
    static getPerformanceHistory() {
        const results = JSON.parse(sessionStorage.getItem('npm_performance_results') || '{}');
        if (Object.keys(results).length === 0) {
            console.warn('⚠️ 未找到性能测试结果，请先运行测试');
            return null;
        }
        return results;
    }
    
    // 清除性能数据
    static clearPerformanceData() {
        sessionStorage.removeItem('npm_performance_results');
        console.log('✅ 性能数据已清除');
    }
    
    // 获取功能标签的友好名称
    getFeatureLabel(feature) {
        const labels = {
            esModules: 'ES模块支持',
            dynamicImport: '动态导入',
            promises: 'Promise支持',
            fetch: 'Fetch API',
            webSocket: 'WebSocket支持',
            localStorage: '本地存储',
            performanceAPI: '性能API',
            resizeObserver: 'ResizeObserver',
            lightweightChartsAvailable: 'LightWeight Charts',
            socketIOAvailable: 'Socket.IO客户端'
        };
        return labels[feature] || feature;
    }
}

// 页面加载完成后自动运行测试
document.addEventListener('DOMContentLoaded', () => {
    // 延迟3秒开始测试，确保所有NPM模块都已加载
    setTimeout(async () => {
        try {
            const monitor = new NPMPerformanceMonitor();
            await monitor.runAllTests();
            
            // 将方法添加到全局作用域
            window.getNPMPerformanceHistory = NPMPerformanceMonitor.getPerformanceHistory;
            window.clearNPMPerformanceData = NPMPerformanceMonitor.clearPerformanceData;
            
            console.log('✅ NPM性能测试完成，可使用以下命令查看历史记录:');
            console.log('  window.getNPMPerformanceHistory() - 获取测试结果');
            console.log('  window.clearNPMPerformanceData() - 清除测试数据');
        } catch (error) {
            console.error('❌ NPM性能测试失败:', error);
        }
    }, 3000);
});

// 提供手动测试方法
window.runNPMPerformanceTest = async () => {
    try {
        const monitor = new NPMPerformanceMonitor();
        const results = await monitor.runAllTests();
        console.log('✅ 手动性能测试完成');
        return results;
    } catch (error) {
        console.error('❌ 手动性能测试失败:', error);
        return null;
    }
};
