/* TradingView Lightweight Charts ES模块版本样式 */

/* 自定义样式 */
.chart-container {
  height: 350px;
}

.stock-card {
  transition: all 0.3s ease;
}

.stock-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.price-up {
  color: #10b981;
}

.price-down {
  color: #ef4444;
}

.price-neutral {
  color: #6b7280;
}

/* 版本切换按钮动画 */
.version-switch-btn {
  transition: all 0.2s ease;
}

.version-switch-btn:hover {
  transform: scale(1.05);
}

/* 图表容器特定样式 */
.chart-stock-container {
  background: #1f2937;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .chart-container {
    height: 280px;
  }
  
  .chart-stock-container {
    padding: 0.75rem;
  }
}

/* 荧光边框效果样式 */
/* 使用 filter: drop-shadow 替代 box-shadow 动画 */
.glow-blue {
  display: inline-block;
  border: 2px solid #3b82f6;
  background: #222;
  color: #fff;
  border-radius: 12px;
  padding: 0 8px;
  filter: drop-shadow(0 0 8px #3b82f6) drop-shadow(0 0 20px #3b82f6);
  transition: all 0.3s ease;
  animation: glowBlueFilter 2s infinite alternate;
}

.glow-green {
  display: inline-block;
  border: 2px solid #22c55e;
  background: #222;
  color: #fff;
  border-radius: 12px;
  padding: 0 8px;
  filter: drop-shadow(0 0 8px #22c55e) drop-shadow(0 0 20px #22c55e);
  transition: all 0.3s ease;
  animation: glowGreenFilter 2s infinite alternate;
}

.glow-none {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

@keyframes glowBlueFilter {
  from { filter: drop-shadow(0 0 8px #3b82f6); }
  to   { filter: drop-shadow(0 0 20px #3b82f6); }
}

@keyframes glowGreenFilter {
  from { filter: drop-shadow(0 0 8px #22c55e); }
  to   { filter: drop-shadow(0 0 20px #22c55e); }
}
