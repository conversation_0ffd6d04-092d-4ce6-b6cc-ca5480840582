# Web服务器整合模块

本模块将stock_web的Web功能整合到src/main.py中，为桌面应用添加Web访问能力。

## 功能特性

- 🌐 **Web界面**: 提供基于浏览器的股票K线图查看
- 📊 **实时数据**: 直接使用IB API的实时股票数据
- 🔄 **自动更新**: 每秒自动刷新图表数据
- 📱 **响应式**: 支持多设备访问（电脑、手机、平板）
- 🚀 **高性能**: 使用单例数据管理器，内存高效

## 架构设计

```
IB API → get_stock_price_ibapi.py → web_stock_data (dict) → Flask Web Server → 浏览器
```

### 核心组件

1. **Web股票数据字典** (`web_stock_data`)
   - 简单的Python字典，存储股票数据
   - 在main.py中创建，传递给IB API和Web服务器
   - 数据结构：`{stock_code: req_data}`，其中req_data是`{date_time: bar_data}`

2. **Web服务器** (`web_server.py`)
   - Flask应用，提供REST API
   - 独立线程运行，不影响桌面GUI
   - 直接读取web_stock_data字典
   - 支持多种数据接口

3. **Web界面** (`templates/index.html`)
   - 基于TradingView Lightweight Charts
   - 实时数据更新和状态显示
   - 响应式设计

## API接口

### REST API

- `GET /` - 主页，显示K线图界面
- `GET /data` - 获取股票数据（兼容stock_web格式）
- `GET /status` - 获取系统状态信息
- `GET /stocks` - 获取所有股票代码列表
- `GET /stock/<code>` - 获取指定股票的数据

### 数据格式

原始数据格式（web_stock_data）：
```json
{
  "AAPL": {
    "20241223 09:30:00": {
      "date_time": "20241223 09:30:00",
      "Open": 150.0,
      "High": 152.0,
      "Low": 149.0,
      "Close": 151.0,
      "Volume": 1000000
    },
    "20241223 09:31:00": {
      "date_time": "20241223 09:31:00",
      "Open": 151.0,
      "High": 152.5,
      "Low": 150.5,
      "Close": 151.5,
      "Volume": 1050000
    }
  }
}
```

蜡烛图数据格式（API返回）：
```json
[
  {
    "time": 1703318400,
    "open": 150.0,
    "high": 152.0,
    "low": 149.0,
    "close": 151.0
  }
]
```

系统状态格式：
```json
{
  "status": "running",
  "stock_count": 5,
  "last_update": 1703318400.123,
  "timestamp": 1703318450.456
}
```

## 配置选项

在 `src/utils/config.py` 中的 `WEB_CONFIG`：

```python
WEB_CONFIG = {
    'ENABLED': True,        # 是否启用Web服务器
    'HOST': '0.0.0.0',     # 监听地址
    'PORT': 5000,          # 监听端口
    'DEBUG': False,        # 调试模式
    'THREADED': True,      # 多线程模式
}
```

## 使用方法

### 启动应用

1. 运行主程序：
   ```bash
   python src/main.py
   ```

2. 访问Web界面：
   ```
   http://localhost:5000
   ```

### 测试功能

运行测试脚本：
```bash
python test_web_integration.py
```

## 技术特点

### 数据流优化
- **零拷贝**: 直接从IB API数据转换，无额外序列化
- **内存高效**: 每只股票最多保存100条记录
- **简单高效**: 使用Python原生字典，无复杂锁机制

### 性能优化
- **独立线程**: Web服务器在独立线程运行
- **异步处理**: 数据更新不阻塞Web响应
- **缓存机制**: 智能数据缓存和清理

### 兼容性
- **向后兼容**: 保持stock_web的API接口
- **无侵入**: 不影响原有桌面GUI功能
- **可配置**: 可通过配置文件启用/禁用

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `WEB_CONFIG['PORT']` 为其他端口
   - 或关闭占用5000端口的其他程序

2. **无数据显示**
   - 确保IB API正常连接
   - 检查股票数据是否正在推送
   - 查看控制台日志信息

3. **Web服务器启动失败**
   - 检查防火墙设置
   - 确保Flask依赖已安装
   - 查看错误日志

### 调试模式

启用调试模式：
```python
WEB_CONFIG['DEBUG'] = True
```

查看详细日志：
```bash
tail -f logs/application.log
```

## 扩展功能

### 添加新的API接口

在 `web_server.py` 中添加新路由：
```python
@app.route('/new_endpoint')
def new_endpoint():
    # 实现新功能
    return jsonify({"result": "success"})
```

### 自定义数据格式

修改 `StockDataManager.update_stock_data()` 方法来支持新的数据格式。

### 添加认证

可以集成Flask-Login或其他认证机制来保护Web接口。

## 注意事项

- Web服务器默认监听所有网络接口（0.0.0.0），请注意网络安全
- 生产环境建议使用HTTPS和认证机制
- 大量并发访问时可能需要使用专业的WSGI服务器（如Gunicorn）
