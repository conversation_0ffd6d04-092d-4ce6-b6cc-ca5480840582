syntax = "proto3";

package stock_xxx;

// 单个K线数据点
message StockDataPoint {
  int64 time = 1;   // Unix timestamp
  double open = 2;
  double high = 3;
  double low = 4;
  double close = 5;
}

// 单只股票的完整更新
message StockUpdate {
  string symbol = 1;
  string transaction_count = 2; // 交易次数（已格式化为字符串，如 "1.2K"）
  bool historical_data_complete = 3; // 历史数据是否加载完成
  repeated StockDataPoint kline_data = 4; // K线数据
  bool is_full_update = 5; // 明确告知前端是否为全量更新
}

// 一次性推送给前端的所有数据
message AllStocksUpdate {
  repeated string stock_codes = 1; // 所有股票代码列表
  repeated StockUpdate updates = 2; // 每只股票的详细数据
  int64 server_time = 3; // 服务器时间戳
}
