# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: stock_data_v2.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13stock_data_v2.proto\x12\x0cstock_xxx.v2\"s\n\x10WebSocketMessage\x12\'\n\x04type\x18\x01 \x01(\x0e\x32\x19.stock_xxx.v2.MessageType\x12\x0f\n\x07payload\x18\x02 \x01(\x0c\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x12\n\nrequest_id\x18\x04 \x01(\t\"c\n\x0bStockCandle\x12\x0c\n\x04time\x18\x01 \x01(\x03\x12\x0c\n\x04open\x18\x02 \x01(\x01\x12\x0c\n\x04high\x18\x03 \x01(\x01\x12\x0b\n\x03low\x18\x04 \x01(\x01\x12\r\n\x05\x63lose\x18\x05 \x01(\x01\x12\x0e\n\x06volume\x18\x06 \x01(\x01\"O\n\x11StockDataResponse\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12*\n\x07\x63\x61ndles\x18\x02 \x03(\x0b\x32\x19.stock_xxx.v2.StockCandle\"8\n\x12StocksListResponse\x12\x13\n\x0bstock_codes\x18\x01 \x03(\t\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\"\x97\x01\n\x14TransactionsResponse\x12J\n\x0ctransactions\x18\x01 \x03(\x0b\x32\x34.stock_xxx.v2.TransactionsResponse.TransactionsEntry\x1a\x33\n\x11TransactionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"]\n\x0eStatusResponse\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x13\n\x0bstock_count\x18\x02 \x01(\x05\x12\x13\n\x0blast_update\x18\x03 \x01(\x03\x12\x11\n\ttimestamp\x18\x04 \x01(\x01\"c\n\rStockOverview\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12\r\n\x05price\x18\x02 \x01(\x01\x12\x0e\n\x06\x63hange\x18\x03 \x01(\x01\x12\x0e\n\x06volume\x18\x04 \x01(\x03\x12\x13\n\x0blast_update\x18\x05 \x01(\x03\"?\n\x10OverviewResponse\x12+\n\x06stocks\x18\x01 \x03(\x0b\x32\x1b.stock_xxx.v2.StockOverview\"x\n\x13SubscriptionRequest\x12\x0f\n\x07symbols\x18\x01 \x03(\t\x12\x1c\n\x14include_transactions\x18\x02 \x01(\x08\x12\x16\n\x0einclude_status\x18\x03 \x01(\x08\x12\x1a\n\x12update_interval_ms\x18\x04 \x01(\x05\">\n\x0c\x45rrorMessage\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07\x64\x65tails\x18\x03 \x01(\t\"\xc4\x01\n\x0b\x42\x61tchUpdate\x12\x33\n\nstock_data\x18\x01 \x01(\x0b\x32\x1f.stock_xxx.v2.StockDataResponse\x12\x38\n\x0ctransactions\x18\x02 \x01(\x0b\x32\".stock_xxx.v2.TransactionsResponse\x12,\n\x06status\x18\x03 \x01(\x0b\x32\x1c.stock_xxx.v2.StatusResponse\x12\x18\n\x10server_timestamp\x18\x04 \x01(\x03*\x85\x01\n\x0bMessageType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0e\n\nSTOCK_DATA\x10\x01\x12\x0f\n\x0bSTOCKS_LIST\x10\x02\x12\x10\n\x0cTRANSACTIONS\x10\x03\x12\n\n\x06STATUS\x10\x04\x12\r\n\tHEARTBEAT\x10\x05\x12\x10\n\x0cSUBSCRIPTION\x10\x06\x12\t\n\x05\x45RROR\x10\x07\x62\x06proto3')

_MESSAGETYPE = DESCRIPTOR.enum_types_by_name['MessageType']
MessageType = enum_type_wrapper.EnumTypeWrapper(_MESSAGETYPE)
UNKNOWN = 0
STOCK_DATA = 1
STOCKS_LIST = 2
TRANSACTIONS = 3
STATUS = 4
HEARTBEAT = 5
SUBSCRIPTION = 6
ERROR = 7


_WEBSOCKETMESSAGE = DESCRIPTOR.message_types_by_name['WebSocketMessage']
_STOCKCANDLE = DESCRIPTOR.message_types_by_name['StockCandle']
_STOCKDATARESPONSE = DESCRIPTOR.message_types_by_name['StockDataResponse']
_STOCKSLISTRESPONSE = DESCRIPTOR.message_types_by_name['StocksListResponse']
_TRANSACTIONSRESPONSE = DESCRIPTOR.message_types_by_name['TransactionsResponse']
_TRANSACTIONSRESPONSE_TRANSACTIONSENTRY = _TRANSACTIONSRESPONSE.nested_types_by_name['TransactionsEntry']
_STATUSRESPONSE = DESCRIPTOR.message_types_by_name['StatusResponse']
_STOCKOVERVIEW = DESCRIPTOR.message_types_by_name['StockOverview']
_OVERVIEWRESPONSE = DESCRIPTOR.message_types_by_name['OverviewResponse']
_SUBSCRIPTIONREQUEST = DESCRIPTOR.message_types_by_name['SubscriptionRequest']
_ERRORMESSAGE = DESCRIPTOR.message_types_by_name['ErrorMessage']
_BATCHUPDATE = DESCRIPTOR.message_types_by_name['BatchUpdate']
WebSocketMessage = _reflection.GeneratedProtocolMessageType('WebSocketMessage', (_message.Message,), {
  'DESCRIPTOR' : _WEBSOCKETMESSAGE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.WebSocketMessage)
  })
_sym_db.RegisterMessage(WebSocketMessage)

StockCandle = _reflection.GeneratedProtocolMessageType('StockCandle', (_message.Message,), {
  'DESCRIPTOR' : _STOCKCANDLE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.StockCandle)
  })
_sym_db.RegisterMessage(StockCandle)

StockDataResponse = _reflection.GeneratedProtocolMessageType('StockDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _STOCKDATARESPONSE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.StockDataResponse)
  })
_sym_db.RegisterMessage(StockDataResponse)

StocksListResponse = _reflection.GeneratedProtocolMessageType('StocksListResponse', (_message.Message,), {
  'DESCRIPTOR' : _STOCKSLISTRESPONSE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.StocksListResponse)
  })
_sym_db.RegisterMessage(StocksListResponse)

TransactionsResponse = _reflection.GeneratedProtocolMessageType('TransactionsResponse', (_message.Message,), {

  'TransactionsEntry' : _reflection.GeneratedProtocolMessageType('TransactionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _TRANSACTIONSRESPONSE_TRANSACTIONSENTRY,
    '__module__' : 'stock_data_v2_pb2'
    # @@protoc_insertion_point(class_scope:stock_xxx.v2.TransactionsResponse.TransactionsEntry)
    })
  ,
  'DESCRIPTOR' : _TRANSACTIONSRESPONSE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.TransactionsResponse)
  })
_sym_db.RegisterMessage(TransactionsResponse)
_sym_db.RegisterMessage(TransactionsResponse.TransactionsEntry)

StatusResponse = _reflection.GeneratedProtocolMessageType('StatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _STATUSRESPONSE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.StatusResponse)
  })
_sym_db.RegisterMessage(StatusResponse)

StockOverview = _reflection.GeneratedProtocolMessageType('StockOverview', (_message.Message,), {
  'DESCRIPTOR' : _STOCKOVERVIEW,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.StockOverview)
  })
_sym_db.RegisterMessage(StockOverview)

OverviewResponse = _reflection.GeneratedProtocolMessageType('OverviewResponse', (_message.Message,), {
  'DESCRIPTOR' : _OVERVIEWRESPONSE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.OverviewResponse)
  })
_sym_db.RegisterMessage(OverviewResponse)

SubscriptionRequest = _reflection.GeneratedProtocolMessageType('SubscriptionRequest', (_message.Message,), {
  'DESCRIPTOR' : _SUBSCRIPTIONREQUEST,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.SubscriptionRequest)
  })
_sym_db.RegisterMessage(SubscriptionRequest)

ErrorMessage = _reflection.GeneratedProtocolMessageType('ErrorMessage', (_message.Message,), {
  'DESCRIPTOR' : _ERRORMESSAGE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.ErrorMessage)
  })
_sym_db.RegisterMessage(ErrorMessage)

BatchUpdate = _reflection.GeneratedProtocolMessageType('BatchUpdate', (_message.Message,), {
  'DESCRIPTOR' : _BATCHUPDATE,
  '__module__' : 'stock_data_v2_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.v2.BatchUpdate)
  })
_sym_db.RegisterMessage(BatchUpdate)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _TRANSACTIONSRESPONSE_TRANSACTIONSENTRY._options = None
  _TRANSACTIONSRESPONSE_TRANSACTIONSENTRY._serialized_options = b'8\001'
  _MESSAGETYPE._serialized_start=1195
  _MESSAGETYPE._serialized_end=1328
  _WEBSOCKETMESSAGE._serialized_start=37
  _WEBSOCKETMESSAGE._serialized_end=152
  _STOCKCANDLE._serialized_start=154
  _STOCKCANDLE._serialized_end=253
  _STOCKDATARESPONSE._serialized_start=255
  _STOCKDATARESPONSE._serialized_end=334
  _STOCKSLISTRESPONSE._serialized_start=336
  _STOCKSLISTRESPONSE._serialized_end=392
  _TRANSACTIONSRESPONSE._serialized_start=395
  _TRANSACTIONSRESPONSE._serialized_end=546
  _TRANSACTIONSRESPONSE_TRANSACTIONSENTRY._serialized_start=495
  _TRANSACTIONSRESPONSE_TRANSACTIONSENTRY._serialized_end=546
  _STATUSRESPONSE._serialized_start=548
  _STATUSRESPONSE._serialized_end=641
  _STOCKOVERVIEW._serialized_start=643
  _STOCKOVERVIEW._serialized_end=742
  _OVERVIEWRESPONSE._serialized_start=744
  _OVERVIEWRESPONSE._serialized_end=807
  _SUBSCRIPTIONREQUEST._serialized_start=809
  _SUBSCRIPTIONREQUEST._serialized_end=929
  _ERRORMESSAGE._serialized_start=931
  _ERRORMESSAGE._serialized_end=993
  _BATCHUPDATE._serialized_start=996
  _BATCHUPDATE._serialized_end=1192
# @@protoc_insertion_point(module_scope)
