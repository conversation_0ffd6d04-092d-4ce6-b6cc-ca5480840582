syntax = "proto3";

package stock_xxx.v2;

// WebSocket消息包装器
message WebSocketMessage {
  MessageType type = 1;
  bytes payload = 2;
  int64 timestamp = 3;
  string request_id = 4; // 用于请求-响应匹配
}

enum MessageType {
  UNKNOWN = 0;
  STOCK_DATA = 1;        // 对应 /data API
  STOCKS_LIST = 2;       // 对应 /stocks API 
  TRANSACTIONS = 3;      // 对应 /transactions API
  STATUS = 4;            // 对应 /status API
  HEARTBEAT = 5;
  SUBSCRIPTION = 6;
  ERROR = 7;
}

// 对应 /data API 的K线数据
message StockCandle {
  int64 time = 1;        // Unix timestamp
  double open = 2;
  double high = 3;
  double low = 4;
  double close = 5;
  double volume = 6;     // 可选，某些端点不包含
}

// 对应 /data?symbol=xxx 的响应
message StockDataResponse {
  string symbol = 1;
  repeated StockCandle candles = 2;
}

// 对应 /stocks API 的响应
message StocksListResponse {
  repeated string stock_codes = 1;
  int32 count = 2;
}

// 对应 /transactions API 的响应
message TransactionsResponse {
  map<string, string> transactions = 1;  // stock_code -> formatted_count
}

// 对应 /status API 的响应  
message StatusResponse {
  string status = 1;
  int32 stock_count = 2;
  int64 last_update = 3;
  double timestamp = 4;
}

// 对应 /overview API 的股票概览
message StockOverview {
  string symbol = 1;
  double price = 2;
  double change = 3;
  int64 volume = 4;
  int64 last_update = 5;
}

message OverviewResponse {
  repeated StockOverview stocks = 1;
}

// 客户端订阅请求
message SubscriptionRequest {
  repeated string symbols = 1;     // 要订阅的股票代码
  bool include_transactions = 2;   // 是否包含交易次数
  bool include_status = 3;         // 是否包含状态更新
  int32 update_interval_ms = 4;    // 更新间隔(毫秒)
}

// 错误消息
message ErrorMessage {
  int32 code = 1;
  string message = 2;
  string details = 3;
}

// 批量更新消息（用于一次推送多种数据）
message BatchUpdate {
  StockDataResponse stock_data = 1;      // 可选
  TransactionsResponse transactions = 2;  // 可选  
  StatusResponse status = 3;             // 可选
  int64 server_timestamp = 4;
}
