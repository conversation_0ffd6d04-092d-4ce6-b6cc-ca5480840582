# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: stock_data.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10stock_data.proto\x12\tstock_xxx\"V\n\x0eStockDataPoint\x12\x0c\n\x04time\x18\x01 \x01(\x03\x12\x0c\n\x04open\x18\x02 \x01(\x01\x12\x0c\n\x04high\x18\x03 \x01(\x01\x12\x0b\n\x03low\x18\x04 \x01(\x01\x12\r\n\x05\x63lose\x18\x05 \x01(\x01\"\xa1\x01\n\x0bStockUpdate\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12\x19\n\x11transaction_count\x18\x02 \x01(\t\x12 \n\x18historical_data_complete\x18\x03 \x01(\x08\x12-\n\nkline_data\x18\x04 \x03(\x0b\x32\x19.stock_xxx.StockDataPoint\x12\x16\n\x0eis_full_update\x18\x05 \x01(\x08\"d\n\x0f\x41llStocksUpdate\x12\x13\n\x0bstock_codes\x18\x01 \x03(\t\x12\'\n\x07updates\x18\x02 \x03(\x0b\x32\x16.stock_xxx.StockUpdate\x12\x13\n\x0bserver_time\x18\x03 \x01(\x03\x62\x06proto3')



_STOCKDATAPOINT = DESCRIPTOR.message_types_by_name['StockDataPoint']
_STOCKUPDATE = DESCRIPTOR.message_types_by_name['StockUpdate']
_ALLSTOCKSUPDATE = DESCRIPTOR.message_types_by_name['AllStocksUpdate']
StockDataPoint = _reflection.GeneratedProtocolMessageType('StockDataPoint', (_message.Message,), {
  'DESCRIPTOR' : _STOCKDATAPOINT,
  '__module__' : 'stock_data_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.StockDataPoint)
  })
_sym_db.RegisterMessage(StockDataPoint)

StockUpdate = _reflection.GeneratedProtocolMessageType('StockUpdate', (_message.Message,), {
  'DESCRIPTOR' : _STOCKUPDATE,
  '__module__' : 'stock_data_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.StockUpdate)
  })
_sym_db.RegisterMessage(StockUpdate)

AllStocksUpdate = _reflection.GeneratedProtocolMessageType('AllStocksUpdate', (_message.Message,), {
  'DESCRIPTOR' : _ALLSTOCKSUPDATE,
  '__module__' : 'stock_data_pb2'
  # @@protoc_insertion_point(class_scope:stock_xxx.AllStocksUpdate)
  })
_sym_db.RegisterMessage(AllStocksUpdate)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _STOCKDATAPOINT._serialized_start=31
  _STOCKDATAPOINT._serialized_end=117
  _STOCKUPDATE._serialized_start=120
  _STOCKUPDATE._serialized_end=281
  _ALLSTOCKSUPDATE._serialized_start=283
  _ALLSTOCKSUPDATE._serialized_end=383
# @@protoc_insertion_point(module_scope)
