import logging
from futu import *

def get_account(trd_ctx):
    ret, data = trd_ctx.get_acc_list()
    if ret == RET_OK:
        print(data)
        print(data['acc_id'][0])  # 取第一个账号
        print(data['acc_id'].values.tolist())  # 转为 list
    else:
        print('get_acc_list error: ', data)

# 下单
def buy_order(trd_ctx, price, qty, code):
    ret, data = trd_ctx.place_order(price, qty, 'US.' + code, trd_side=TrdSide.BUY, order_type=OrderType.MARKET, trd_env=TrdEnv.SIMULATE)
    if ret == RET_OK:
        # print('下单\n', data.to_string(), '\n')
        logging.info(f'下单\n {data.to_string()} \n')
        return data
    else:
        # print('place_order error: ', data)
        logging.info(f'place_order error: {data}')

# 平仓
def sell_order(trd_ctx, price, qty, code):
    ret, data = trd_ctx.place_order(price, qty, 'US.' + code, trd_side=TrdSide.SELL, order_type=OrderType.MARKET, trd_env=TrdEnv.SIMULATE)
    if ret == RET_OK:
        # print('平仓\n', data.to_string(), '\n')
        logging.info(f'平仓\n {data.to_string()} \n')
        return data
    else:
        # print('sell_order error: ', data)
        logging.info(f'sell_order error: {data}')

# 撤单
def cancel_order(trd_ctx, order_id):
    ret, data = trd_ctx.modify_order(ModifyOrderOp.CANCEL, order_id, 0, 0, trd_env=TrdEnv.SIMULATE)
    if ret == RET_OK:
        print('撤单\n', data.to_string())
        return data
    else:
        print('cancel_order error: ', data)

# 查询当日成交
def deal_list(trd_ctx):
    ret, data = trd_ctx.deal_list_query()
    if ret == RET_OK:
        # print('查询当日成交\n', data.to_string(), '\n')
        logging.info(f'查询当日成交\n {data.to_string()} \n')
        data_list = data['order_id'].values.tolist()
        if data.shape[0] > 0:  # 如果成交列表不为空
            return data_list

    else:
        # print('deal_list_query error: ', data)
        logging.info(f'deal_list_query error: {data}')

# 查询历史订单
def history_order_list(trd_ctx, start, end):
    ret, data = trd_ctx.history_order_list_query(start=start, end=end, trd_env=TrdEnv.SIMULATE)
    if ret == RET_OK:
        # print('查询历史订单\n', data.to_string(), '\n')
        logging.info(f'查询历史订单\n {data.to_string()}')
        return data
    else:
        # print('history_order_list_query error: ', data)
        logging.info(f'history_order_list_query error: {data}')

# place_order(4.5, 10, 'INDO')
# history_order_list()