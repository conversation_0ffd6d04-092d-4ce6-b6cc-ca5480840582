import os

# GUI 配置
GUI_CONFIG = {
    'WINDOW_TITLE': '股票提醒列表_v2.0',  # 窗口标题
    'WINDOW_GEOMETRY': '220x550+20+270',  # 窗口大小和位置
    'WINDOW_TOPMOST': True,  # 窗口是否始终置顶
    'REFRESH_INTERVAL': 300  # 刷新间隔（毫秒）
}

# 颜色配置
COLORS = {
    'DEFAULT_BG': '#8B0000',  # 默认背景色（暗红色）
    'ACTIVE_BG': 'green',  # 激活状态背景色（绿色）
    'HIGH_PRIORITY_TEXT': '#359be8',  # 高优先级文本颜色（蓝色）
    'NORMAL_PRIORITY_TEXT': 'darkgoldenrod',  # 普通优先级文本颜色（暗金色）
    'DEFAULT_TEXT': 'white',  # 默认文本颜色（白色）
}

# 股票相关阈值
STOCK_THRESHOLDS = {
    'MIN_VOLUME': 1_000_000,  # 最小成交量
    'VOLUME_AVG_MULTIPLIER': 5,  # 成交量倍数（当前成交量与平均成交量的比较）
    'MAX_SHARES_OUTSTANDING': 10_000_000,  # 最大流通股数
    'MAX_VOLUME_AVG': 5_000_000,  # 最大平均成交量
    'VOLUME_THRESHOLD': 50000,  # 成交量阈值
    'PRICE_THRESHOLD': 0.1,  # 价格阈值
    'SPREAD_THRESHOLD': 0.02,  # 买卖价差阈值
    'TURNOVER_THRESHOLD': 150000,  # 成交额阈值
    'VOLUME_RATIO_THRESHOLD': 1.4,  # 成交量比率阈值
    'EMA_THRESHOLD': 20,  # EMA均线阈值
    'MACD_THRESHOLD': 0,  # MACD阈值
    'BOLLINGER_THRESHOLD': 2,  # 布林带阈值
}

# 文件路径
PATHS = {
    'WARN_SOUND': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'resources', 'sounds', 'noise.mp3'),  # 警告声音文件路径
    'SIGNAL_SOUND': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'resources', 'sounds', 'signal.wav'),  # 信号声音文件路径
}

# FUTU API 配置
FUTU_CONFIG = {
    'HOST': '127.0.0.1',  # API主机地址
    'PORT': 11111,  # API端口号
}

# 键盘快捷键配置
HOTKEYS = {
    'BUY_KEYS': [18, 19, 20],  # 买入快捷键 Command + 1/2/3
    'STOP_SOUND_KEY': 1,  # 停止声音快捷键 Command + S
}

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'password',
    'database': 'stock_db'
}

# 音频配置
AUDIO_CONFIG = {
    'VOLUME': 0.5,  # 音量大小
    'NOISE_FILE': 'resources/sounds/noise.mp3',  # 噪音音频文件路径
    'SIGNAL_FILE': 'resources/sounds/noise.mp3'  # 信号音频文件路径（暂时使用noise.mp3替代）
}

# API配置
API_CONFIG = {
    'IB_HOST': '127.0.0.1',
    'IB_PORT': 7497,
    'IB_CLIENT_ID': 1
}

# 日志配置
LOG_CONFIG = {
    'version': 1,                 # 日志配置版本号
    'disable_existing_loggers': True,  # 禁用已存在的日志记录器
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(levelname)s - %(filename)s - %(lineno)d - %(message)s',  # 日志格式：时间-级别-文件名-行号-消息
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',  # 文件处理器
            'filename': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reindex_out_ibapi.log'),  # 日志文件路径
            'mode': 'a',          # 追加模式
            'formatter': 'detailed',  # 使用detailed格式化器
            'encoding': 'utf-8'   # 文件编码
        },
        'console': {
            'class': 'logging.StreamHandler',  # 控制台处理器
            'formatter': 'detailed',  # 使用detailed格式化器
            'stream': 'ext://sys.stdout'  # 输出到标准输出
        }
    },
    'loggers': {
        'ibapi': {
            'handlers': ['file'],  # IB API的日志使用文件处理器
            'level': 'ERROR',      # IB API的日志级别为ERROR
            'propagate': False     # 不传播到父级logger
        }
    },
    'root': {
        'handlers': ['file'],     # 根logger使用文件处理器
        'level': 'INFO'           # 根logger的日志级别为INFO
    }
}

# 交易配置
TRADE_CONFIG = {
    'MAX_POSITION': 100000,  # 最大持仓
    'STOP_LOSS': 0.05,  # 止损比例
    'TAKE_PROFIT': 0.1  # 止盈比例
}

# IB API配置
IB_CONFIG = {
    'HOST': '127.0.0.1',          # IB Gateway主机地址
    'PORT': 7497,                 # IB Gateway端口
    'CLIENT_ID': 1000,            # 客户端ID
    'MAX_RETRY_ATTEMPTS': 3,      # 最大重试次数
    'MAX_SYMBOLS': 45,            # 最大订阅符号数量
    'RETRY_INTERVAL': 5,          # 重试间隔（秒）
    'CALL_INTERVAL': 8,           # 调用间隔（秒）
    'MAX_WORKERS': 12,            # 最大工作线程数
    'SCANNER': {
        'ROWS': 30,               # 扫描器返回行数
        'MIN_PRICE': 0.13,        # 最小价格
        'MAX_PRICE': 30,          # 最大价格
        'MIN_VOLUME': 20000       # 最小成交量
    }
}

# 市场时间配置
MARKET_CONFIG = {
    'OPEN_HOUR': 9,              # 市场开盘时间（小时）
    'CLOSE_HOUR': 16,            # 市场收盘时间（小时）
    'PRE_MARKET_START_HOUR': 4,  # 盘前开始时间（小时）
    'POST_MARKET_END_HOUR': 20   # 盘后结束时间（小时）
}

# 交易配置
TRADING_CONFIG = {
    'NEWS_EXPIRE_SECONDS': 59 * 5,  # 新闻过期时间（秒）
    'MIN_LOW_AMOUNT': 170000,       # 最小交易金额
}

# Web服务器配置
WEB_CONFIG = {
    'ENABLED': True,                # 是否启用Web服务器
    'HOST': '0.0.0.0',             # 监听地址
    'PORT': 5001,                  # 监听端口
    'DEBUG': False,                # 调试模式
    'THREADED': True,              # 多线程模式
}