"""
进程间通信配置
控制使用HTTP还是共享内存进行进程间通信
"""

import os
from enum import Enum
from typing import Dict, Any


class CommunicationMode(Enum):
    """通信模式枚举"""
    HTTP = "http"
    SHARED_MEMORY = "shared_memory"
    AUTO = "auto"  # 自动选择最佳方案


class CommunicationConfig:
    """进程间通信配置类"""
    
    def __init__(self):
        """初始化配置"""
        # 默认配置
        self._config = {
            'mode': CommunicationMode.AUTO,
            'http_config': {
                'host': 'localhost',
                'port': 5001,
                'timeout': 3.0,
                'max_retries': 2,
                'retry_delay': 0.1
            },
            'shared_memory_config': {
                'use_shared_memory': True,
                'fallback_to_http': True,
                'memory_size_mb': 100
            },
            'performance_config': {
                'high_frequency_threshold': 100,  # 每秒超过100次更新使用共享内存
                'low_frequency_use_http': True    # 低频更新可以使用HTTP
            }
        }
        
        # 从环境变量加载配置
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 通信模式
        mode_str = os.getenv('STOCK_COMMUNICATION_MODE', 'auto').lower()
        if mode_str == 'http':
            self._config['mode'] = CommunicationMode.HTTP
        elif mode_str == 'shared_memory':
            self._config['mode'] = CommunicationMode.SHARED_MEMORY
        else:
            self._config['mode'] = CommunicationMode.AUTO
        
        # HTTP配置
        if os.getenv('STOCK_HTTP_HOST'):
            self._config['http_config']['host'] = os.getenv('STOCK_HTTP_HOST')
        if os.getenv('STOCK_HTTP_PORT'):
            self._config['http_config']['port'] = int(os.getenv('STOCK_HTTP_PORT'))
        if os.getenv('STOCK_HTTP_TIMEOUT'):
            self._config['http_config']['timeout'] = float(os.getenv('STOCK_HTTP_TIMEOUT'))
        
        # 共享内存配置
        if os.getenv('STOCK_USE_SHARED_MEMORY'):
            self._config['shared_memory_config']['use_shared_memory'] = \
                os.getenv('STOCK_USE_SHARED_MEMORY').lower() == 'true'
        if os.getenv('STOCK_FALLBACK_TO_HTTP'):
            self._config['shared_memory_config']['fallback_to_http'] = \
                os.getenv('STOCK_FALLBACK_TO_HTTP').lower() == 'true'
    
    @property
    def mode(self) -> CommunicationMode:
        """获取通信模式"""
        return self._config['mode']
    
    @property
    def http_config(self) -> Dict[str, Any]:
        """获取HTTP配置"""
        return self._config['http_config'].copy()
    
    @property
    def shared_memory_config(self) -> Dict[str, Any]:
        """获取共享内存配置"""
        return self._config['shared_memory_config'].copy()
    
    @property
    def performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self._config['performance_config'].copy()
    
    def should_use_shared_memory(self, data_type: str = None, frequency: int = 0) -> bool:
        """判断是否应该使用共享内存

        Args:
            data_type: 数据类型
            frequency: 数据更新频率（每秒次数）

        Returns:
            是否使用共享内存
        """
        # 强制使用共享内存模式
        return True
    
    def get_http_url(self, endpoint: str) -> str:
        """获取HTTP URL
        
        Args:
            endpoint: API端点
            
        Returns:
            完整的HTTP URL
        """
        config = self.http_config
        return f"http://{config['host']}:{config['port']}{endpoint}"
    
    def update_config(self, **kwargs):
        """更新配置
        
        Args:
            **kwargs: 配置参数
        """
        for key, value in kwargs.items():
            if key in self._config:
                if isinstance(self._config[key], dict) and isinstance(value, dict):
                    self._config[key].update(value)
                else:
                    self._config[key] = value
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要
        
        Returns:
            配置摘要字典
        """
        return {
            'mode': self.mode.value,
            'http_enabled': self.mode in [CommunicationMode.HTTP, CommunicationMode.AUTO],
            'shared_memory_enabled': self.mode in [CommunicationMode.SHARED_MEMORY, CommunicationMode.AUTO],
            'http_url_base': f"http://{self.http_config['host']}:{self.http_config['port']}",
            'shared_memory_available': self.shared_memory_config['use_shared_memory']
        }


# 全局配置实例
communication_config = CommunicationConfig()
