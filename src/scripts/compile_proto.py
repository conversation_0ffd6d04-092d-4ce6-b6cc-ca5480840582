#!/usr/bin/env python3
"""
编译Protobuf文件的脚本
"""

import subprocess
import sys
from pathlib import Path
import logging

def compile_proto_files():
    """编译所有.proto文件"""
    
    # 从src目录执行，proto文件在src/web/proto
    proto_dir = Path('src/web/proto')
    output_dir = proto_dir
    
    # 查找所有.proto文件
    proto_files = list(proto_dir.glob('*.proto'))
    
    if not proto_files:
        print("未找到.proto文件")
        return
    
    for proto_file in proto_files:
        print(f"编译 {proto_file}...")
        
        cmd = [
            'python', '-m', 'grpc_tools.protoc',
            f'--proto_path={proto_dir}',
            f'--python_out={output_dir}',
            str(proto_file)
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"✓ {proto_file.name} 编译成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {proto_file.name} 编译失败: {e.stderr}")
            sys.exit(1)
    
    print("所有Protobuf文件编译完成")

if __name__ == '__main__':
    compile_proto_files()
