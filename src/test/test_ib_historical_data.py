"""
从Interactive Brokers获取历史tick数据

获取指定Interactive Brokers TWS API获取历史tick数据的工具类，功能包括：
1. 历史tick交易数据
2. 历史买卖盘数据
"""

import os
import sys
import time
import datetime
import threading
import queue
import pandas as pd
import pytz
import uuid
from typing import List, Dict, Tuple, Set, Optional
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.common import TickerId
from ibapi.ticktype import TickType, TickTypeEnum
from ibapi.utils import current_fn_name
from ibapi.common import HistoricalTickLast, HistoricalTickBidAsk
from error_handler import ErrorHandler, IBError, IBTimeoutError, IBConnectionError, IBRateLimitError, IBDataError
from request_manager import RequestManager, TimeWindow
from progress_tracker import ProgressTracker, TimeRange

class HistoricalTickDataApp(EWrapper, EClient):
    """用于获取历史tick数据的应用类"""
    
    def __init__(self):
        EWrapper.__init__(self)
        EClient.__init__(self, self)
        self.data_lock = threading.Lock()
        self.last_data: Dict[str, Dict] = {}  # 使用字典存储，UUID作为键
        self.bidask_data: Dict[str, Dict] = {}  # 使用字典存储，UUID作为键
        self.last_done = True  # 初始值设为True，表示可以发送第一个请求
        self.bidask_done = True  # 初始值设为True，表示可以发送第一个请求
        self.error_happened = False
        self.last_tick_time = None
        self.msg_queue = queue.Queue()
        self.is_done = False
        self.max_ticks = 1000000  # 恢复到合理的限制
        self.seen_last_ids: Set[str] = set()
        self.seen_bidask_ids: Set[str] = set()
        self.ny_tz = pytz.timezone('America/New_York')
        self.request_bucket = 0
        self.last_bucket_update = time.time()
        # IBKR API 限制：
        # 1. 全局速率限制：每秒最多50个请求
        # 2. 同时开放的历史数据请求最大数量为50
        # 为了系统稳定性，我们设置更保守的限制
        self.MAX_REQUESTS_PER_SECOND = 10  # 降低请求频率，避免触发限制
        self.error_handler = ErrorHandler()
        self.request_manager = RequestManager()
        self.contract_details_received = False
        self.contract_details_end = False
        self.start = False
        # 用于统计数据
        self.total_received_last = 0
        self.total_received_bidask = 0
        self.total_filtered_last = 0
        self.total_filtered_bidask = 0
        # 用于时间范围控制
        self.target_start_time = None
        self.target_end_time = None
        
    def nextValidId(self, orderId: int):
        """处理下一个有效订单ID的回调"""
        super().nextValidId(orderId)
        print("NextValidId:", orderId)
        self.start = True

    def error(self, reqId: TickerId, errorCode: int, errorString: str, advancedOrderRejectJson: str = ""):
        """处理错误回调"""
        error_msg = f"错误信息: reqId={reqId}, errorCode={errorCode}, errorString={errorString}"
        if advancedOrderRejectJson:
            error_msg += f", 详细信息: {advancedOrderRejectJson}"
        print(error_msg)
        
        # 特殊错误码处理
        if errorCode == 162:  # 历史数据结束
            print("历史数据传输完成")
            if reqId == 1:
                self.last_done = True
            elif reqId == 2:
                self.bidask_done = True
        elif errorCode == 200:  # 没有订阅权限
            print("警告: 没有该合约的市场数据订阅权限")
        elif errorCode == 354:  # 请求被拒绝
            print("警告: 数据请求被拒绝，可能是由于请求频率过高")
        elif errorCode == 165:  # 重新请求
            print("提示: 需要重新请求数据")
        elif errorCode == 366:  # 没有该合约的数据
            print("错误: 没有该合约的历史数据")
        elif errorCode == 102:  # 请求ID冲突
            print("警告: 请求ID冲突，等待前一个请求完成")
            # 不标记为错误，而是等待前一个请求完成
            return
            
        try:
            self.error_handler.handle_error(errorCode, errorString, reqId)
        except IBError as e:
            self.error_happened = True
            print(f"处理错误: {str(e)}")
            if isinstance(e, (IBDataError, IBTimeoutError)):
                if reqId == 1:
                    self.last_done = True
                elif reqId == 2:
                    self.bidask_done = True
            raise

    def contractDetails(self, reqId: int, contractDetails):
        """处理合约详情回调"""
        print(f"收到合约详情: {contractDetails.contract.symbol}")
        print(f"  交易所: {contractDetails.contract.exchange}")
        print(f"  主交易所: {contractDetails.contract.primaryExchange}")
        print(f"  货币: {contractDetails.contract.currency}")
        print(f"  合约ID: {contractDetails.contract.conId}")
        if contractDetails.contract.primaryExchange in ['NASDAQ', 'AMEX', 'NYSE']:
            self.contract_details_received = True

    def contractDetailsEnd(self, reqId: int):
        """合约详情请求结束回调"""
        print("合约详情请求完成")
        self.contract_details_end = True

    def convert_timestamp_to_ny(self, timestamp: int) -> datetime.datetime:
        """将时间戳转换为纽约时间，保留毫秒精度
        
        Args:
            timestamp: IB API返回的Unix时间戳（以秒为单位）
            
        Returns:
            datetime.datetime: 带毫秒精度的纽约时间
        """
        # IB的时间戳是以秒为单位，需要分离秒和毫秒
        seconds = int(timestamp)
        milliseconds = int((timestamp - seconds) * 1000)
        
        # 转换为UTC时间，保留毫秒
        utc_time = datetime.datetime.fromtimestamp(seconds, pytz.UTC)
        utc_time = utc_time.replace(microsecond=milliseconds * 1000)  # 转换为微秒
        
        # 转换为纽约时间
        ny_time = utc_time.astimezone(self.ny_tz)
        return ny_time

    def get_tick_key(self, tick_time: datetime.datetime, tick_data: dict) -> str:
        """生成tick数据的唯一键，包含时间戳（精确到毫秒）和其他关键信息
        
        Args:
            tick_time: tick的时间戳
            tick_data: tick数据字典
            
        Returns:
            str: 唯一标识键
        """
        if 'price' in tick_data:  # 交易数据
            return f"{tick_time.strftime('%Y%m%d %H:%M:%S.%f')}_{tick_data['price']}_{tick_data['size']}_{tick_data['exchange']}"
        else:  # 买卖盘数据
            return f"{tick_time.strftime('%Y%m%d %H:%M:%S.%f')}_{tick_data['bid_price']}_{tick_data['ask_price']}_{tick_data['bid_size']}_{tick_data['ask_size']}"

    def rate_limit_request(self):
        """实现请求频率限制 (令牌桶算法)
        
        IBKR API 限制：
        - 全局速率限制：每秒最多50个请求
        - 建议：保持较低的请求频率以确保稳定性
        """
        current_time = time.time()
        elapsed = current_time - self.last_bucket_update
        
        # 如果桶空了，重新填充
        if elapsed > 1.0:
            self.request_bucket = self.MAX_REQUESTS_PER_SECOND
            self.last_bucket_update = current_time
        else:
            self.request_bucket = min(
                self.request_bucket + int(self.MAX_REQUESTS_PER_SECOND * elapsed),
                self.MAX_REQUESTS_PER_SECOND
            )
        
        # 如果桶空了，等待直到桶满
        if self.request_bucket < 1:
            sleep_time = 1.0 - elapsed
            if sleep_time > 0:
                time.sleep(sleep_time)
            self.request_bucket = self.MAX_REQUESTS_PER_SECOND
            self.last_bucket_update = time.time()
        
        # 执行一次请求
        self.request_bucket -= 1

    def filter_data_by_time(self, data: Dict[str, Dict], start_time: datetime.datetime, end_time: datetime.datetime) -> Dict[str, Dict]:
        """根据时间范围过滤数据"""
        filtered_data = {}
        for uuid, tick in data.items():
            tick_time = datetime.datetime.strptime(tick['date_time'], '%Y-%m-%d %H:%M:%S')
            if start_time <= tick_time <= end_time:
                filtered_data[uuid] = tick
        return filtered_data
        
    def historicalTicksLast(self, reqId: int, ticks: List[HistoricalTickLast], done: bool):
        """处理历史tick交易数据回调"""
        if not ticks:
            if done:
                self.last_done = True
            return
        
        with self.data_lock:
            # 更新接收到的数据总数
            self.total_received_last += len(ticks)
            new_count = 0
            
            # 处理新数据
            for tick in ticks:
                tick_time = self.convert_timestamp_to_ny(tick.time)
                # 移除时区信息并格式化时间
                tick_time = tick_time.replace(tzinfo=None)
                
                # 检查时间范围
                if not (self.target_start_time <= tick_time <= self.target_end_time):
                    continue
                    
                formatted_time = tick_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # 生成UUID作为唯一标识
                unique_id = str(uuid.uuid4())
                
                # 存储数据
                self.last_data[unique_id] = {
                    'date_time': formatted_time,
                    'price': tick.price,
                    'size': tick.size,
                    'exchange': tick.exchange,
                    'special_conditions': '',  # 移除对 specialConditions 的访问
                    'past_limit': getattr(tick.tickAttribLast, 'pastLimit', False),
                    'unreported': getattr(tick.tickAttribLast, 'unreported', False)
                }
                new_count += 1
            
            # 打印数据统计
            if ticks:
                first_time = self.convert_timestamp_to_ny(ticks[0].time).replace(tzinfo=None)
                last_time = self.convert_timestamp_to_ny(ticks[-1].time).replace(tzinfo=None)
                time_range = (
                    first_time.strftime('%Y-%m-%d %H:%M:%S'),
                    last_time.strftime('%Y-%m-%d %H:%M:%S')
                )
                print(f"收到并保存 {new_count} 条数据")
                print(f"时间范围: {time_range[0]} 到 {time_range[1]}")
                print(f"当前历史tick数据总数: {len(self.last_data)} 条")
                print(f"累计接收并保存: {self.total_received_last} 条")
            
        # 设置完成标志
        if done:
            self.last_done = True
            print("交易数据请求完成")

    def historicalTicksBidAsk(self, reqId: int, ticks: List[HistoricalTickBidAsk], done: bool):
        """处理历史tick买卖盘数据回调"""
        if not ticks:
            if done:
                self.bidask_done = True
            return
        
        with self.data_lock:
            # 更新接收到的数据总数
            self.total_received_bidask += len(ticks)
            new_count = 0
            
            # 处理新数据
            for tick in ticks:
                tick_time = self.convert_timestamp_to_ny(tick.time)
                # 移除时区信息并格式化时间
                tick_time = tick_time.replace(tzinfo=None)
                
                # 检查时间范围
                if not (self.target_start_time <= tick_time <= self.target_end_time):
                    continue
                    
                formatted_time = tick_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # 生成UUID作为唯一标识
                unique_id = str(uuid.uuid4())
                
                # 存储数据
                self.bidask_data[unique_id] = {
                    'date_time': formatted_time,
                    'bid_price': tick.priceBid,
                    'ask_price': tick.priceAsk,
                    'bid_size': tick.sizeBid,
                    'ask_size': tick.sizeAsk,
                    'bid_past_low': tick.tickAttribBidAsk.bidPastLow,
                    'ask_past_high': tick.tickAttribBidAsk.askPastHigh
                }
                new_count += 1
            
            # 打印数据统计
            if ticks:
                first_time = self.convert_timestamp_to_ny(ticks[0].time).replace(tzinfo=None)
                last_time = self.convert_timestamp_to_ny(ticks[-1].time).replace(tzinfo=None)
                time_range = (
                    first_time.strftime('%Y-%m-%d %H:%M:%S'),
                    last_time.strftime('%Y-%m-%d %H:%M:%S')
                )
                print(f"收到并保存 {new_count} 条数据")
                print(f"时间范围: {time_range[0]} 到 {time_range[1]}")
                print(f"当前历史买卖盘数据总数: {len(self.bidask_data)} 条")
                print(f"累计接收并保存: {self.total_received_bidask} 条")
            
        # 设置完成标志
        if done:
            self.bidask_done = True
            print("买卖盘数据请求完成")

def message_handler(app: HistoricalTickDataApp):
    """处理消息线程"""
    while not app.is_done:
        try:
            app.run()
        except Exception as e:
            print(f"消息处理异常: {str(e)}")
            time.sleep(0.1)

def get_ib_historical_data(
    end_time_str: str = "09:01",
    date_str: str = None
) -> Tuple[datetime.datetime, datetime.datetime]:
    """
    获取IB历史数据的日期时间范围（美国东部时间）
    
    参数:
        end_time_str: 结束时间字符串，格式"HH:MM"（美东时间）
        date_str: 可选，指定日期字符串，格式"YYYY-MM-DD"（默认为今天）
    
    返回:
        包含开始时间(end_time-15分钟)和结束时间的元组 (start_time, end_time)
    """
    # 设置目标日期（默认为今天）
    if date_str:
        year, month, day = map(int, date_str.split('-'))
        target_date = datetime.date(year, month, day)
    else:
        target_date = datetime.date.today()
    
    # 解析结束时间字符串
    end_hour, end_minute = map(int, end_time_str.split(':'))
    
    # 创建结束时间datetime对象
    end_time = datetime.datetime.combine(
        target_date,
        datetime.time(end_hour, end_minute)
    )
    
    # 计算开始时间（结束时间减去15分钟）
    start_time = end_time - datetime.timedelta(minutes=15)
    
    return start_time, end_time

def format_time_range(start_time: datetime.datetime, end_time: datetime.datetime) -> str:
    """格式化时间范围显示
    
    Args:
        start_time: 开始时间
        end_time: 结束时间
        
    Returns:
        str: 格式化的时间范围字符串
    """
    return f"{start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} - {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}"

def get_historical_data(
    app: HistoricalTickDataApp, 
    contract: Contract, 
    start_datetime: datetime.datetime, 
    end_datetime: datetime.datetime, 
    request_id: int, 
    what_to_show: str
) -> List[Tuple[datetime.datetime, datetime.datetime]]:
    """获取历史数据的辅助函数
    
    遵循IBKR API限制：
    1. 全局速率限制：每秒最多50个请求
    2. 同时开放的历史数据请求最大数量为50
    3. 每个请求窗口限制为1分钟，以减少数据量和提高稳定性
    4. 使用顺序请求模式，确保请求完成后再发送下一个
    """
    chunk_size = 1000  # IB API限制每次最多1000条数据
    max_attempts = 5  # 单个窗口的最大重试次数
    window_size = datetime.timedelta(minutes=1)  # 每次请求1分钟的数据
    data_gaps = []
    
    # 设置目标时间范围
    app.target_start_time = start_datetime
    app.target_end_time = end_datetime
    
    # 创建进度跟踪器
    progress = ProgressTracker(
        total_range=TimeRange(start=start_datetime, end=end_datetime)
    )
    
    print(f"\n{'='*80}")
    print(f"开始获取 {what_to_show} 数据")
    print(f"目标时间范围: {format_time_range(start_datetime, end_datetime)}")
    print(f"{'='*80}\n")
    
    current_start = start_datetime
    attempt_count = 0
    all_data = {}
    last_data_time = None
    window_count = 0
    
    while current_start < end_datetime:
        try:
            window_count += 1
            # 计算当前窗口的结束时间
            window_end = min(current_start + window_size, end_datetime)
            
            print(f"\n{'-'*80}")
            print(f"时间窗口 #{window_count}")
            print(f"{'-'*80}")
            
            # 确保前一个请求已完成，避免超过IBKR同时请求数限制
            if what_to_show == "TRADES":
                if not app.last_done:
                    print("等待前一个交易数据请求完成...")
                    time.sleep(1)  # 等待1秒再检查
                    continue
                app.last_done = False
                app.last_data = {}
                type_str = '逐笔成交'
            else:
                if not app.bidask_done:
                    print("等待前一个买卖盘数据请求完成...")
                    time.sleep(1)  # 等待1秒再检查
                    continue
                app.bidask_done = False
                app.bidask_data = {}
                type_str = '买卖摆盘'

            print(f"\n[请求信息]")
            print(f"类型: {type_str}")
            print(f"时间范围: {format_time_range(current_start, window_end)}")
            
            # 更新进度
            progress.start_range(current_start, window_end)
            print(f"\n[进度信息]")
            progress.print_progress()
            
            # 实现请求频率限制
            app.rate_limit_request()
            
            # 记录请求开始时间
            request_start_time = time.time()
            
            # 发送请求
            app.reqHistoricalTicks(
                reqId=request_id,
                contract=contract,
                startDateTime=f"{current_start.strftime('%Y%m%d %H:%M:%S')} America/New_York",
                endDateTime=f"{window_end.strftime('%Y%m%d %H:%M:%S')} America/New_York",
                numberOfTicks=chunk_size,
                whatToShow=what_to_show,
                useRth=0,
                ignoreSize=False,
                miscOptions=[]
            )
            
            # 更新请求计数
            app.request_manager.record_request()
            
            # 等待响应，超时时间设为10秒
            timeout = 10
            start_time = time.time()
            done_flag = app.last_done if what_to_show == "TRADES" else app.bidask_done
            
            while not done_flag and time.time() - start_time < timeout:
                time.sleep(0.1)  # 小间隔检查，避免CPU占用
                done_flag = app.last_done if what_to_show == "TRADES" else app.bidask_done
                
            # 记录响应时间
            response_time = time.time() - request_start_time
            app.request_manager.record_response_time(response_time)
            
            if time.time() - start_time >= timeout:
                app.request_manager.record_failure()
                # 如果超时，重置done标志，避免卡住
                if what_to_show == "TRADES":
                    app.last_done = True
                else:
                    app.bidask_done = True
                raise IBTimeoutError(f"{what_to_show} 请求超时")
                
            # 检查返回数据
            with app.data_lock:
                if what_to_show == "TRADES":
                    current_data = app.last_data
                else:
                    current_data = app.bidask_data
                    
            data_count = len(current_data)
            print(f"\n[数据统计]")
            print(f"本次获取: {data_count} 条")
            
            if data_count == 0:
                print(f"说明: 该时间段没有数据")
                # 标记为已完成并继续
                progress.complete_range(current_start, window_end)
                # 移动到下一个时间窗口
                current_start = window_end
                continue
                
            # 检查返回数据时间
            timestamps = []
            valid_data = {}
            
            for uuid, data in current_data.items():
                data_time = datetime.datetime.strptime(data['date_time'], '%Y-%m-%d %H:%M:%S')
                # 只处理目标时间范围内的数据
                if current_start <= data_time <= window_end:
                    timestamps.append(data_time)
                    valid_data[uuid] = data
            
            if not timestamps:
                print(f"说明: 返回的数据都在目标时间范围之外")
                # 标记为已完成并继续
                progress.complete_range(current_start, window_end)
                # 移动到下一个时间窗口
                current_start = window_end
                continue
            
            # 获取数据的时间范围
            data_start = min(timestamps)
            data_end = max(timestamps)
            
            print(f"有效数据范围: {format_time_range(data_start, data_end)}")
            print(f"累计数据量: {len(all_data) + len(valid_data)} 条")
            
            # 检查是否获取到新数据
            if last_data_time is not None and data_end <= last_data_time:
                print(f"\n[警告] 检测到重复数据")
                print(f"上次结束时间: {last_data_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                print(f"本次结束时间: {data_end.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                if attempt_count < max_attempts:
                    attempt_count += 1
                    current_start = last_data_time + datetime.timedelta(seconds=1)
                    print(f"处理: 跳过重复数据，从 {current_start.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} 重新请求")
                    continue
                else:
                    print("处理: 达到最大重试次数，移动到下一个时间窗口")
                    progress.complete_range(current_start, window_end)
                    # 移动到下一个时间窗口
                    current_start = window_end
                    continue
                    
            # 更新进度，只标记实际获取到数据的时间范围
            progress.complete_range(data_start, data_end)
            app.request_manager.record_success()
            
            # 保存当前数据
            all_data.update(valid_data)
            
            # 更新最后数据时间和下一次请求的开始时间
            last_data_time = data_end
            current_start = data_end + datetime.timedelta(seconds=1)
            attempt_count = 0
            
            # 检查数据总数
            if len(all_data) >= app.max_ticks:
                print(f"\n[提示] 历史数据达到最大限制 ({app.max_ticks})")
                break
                
        except IBError as e:
            attempt_count += 1
            error_code = getattr(e, 'error_code', None)
            
            print(f"\n[错误处理]")
            # 如果是请求ID冲突错误(102)，等待更长时间确保前一个请求完成
            if error_code == 102:
                print("检测到请求ID冲突(102错误)，等待前一个请求完成...")
                # 重置done标志，避免卡住
                if what_to_show == "TRADES":
                    app.last_done = True
                else:
                    app.bidask_done = True
                time.sleep(2)  # 等待2秒
                attempt_count -= 1  # 不计入重试次数
                continue
            
            if attempt_count < max_attempts:
                app.request_manager.record_retry()
                retry_delay = 1  # 固定1秒重试延迟
                print(f"错误代码: {error_code}")
                print(f"处理: 等待 {retry_delay} 秒后进行第 {attempt_count} 次重试")
                time.sleep(retry_delay)
                continue
            else:
                app.request_manager.record_failure()
                print(f"错误: 达到最大重试次数 ({max_attempts})")
                progress.fail_range(current_start, window_end)
                data_gaps.append((current_start, window_end))
                # 移动到下一个时间窗口
                current_start = window_end
                continue
                
    print(f"\n{'='*80}")
    print("数据获取完成状态:")
    progress.print_progress()
    print(f"{'='*80}\n")
    
    # 更新应用程序的数据
    with app.data_lock:
        if what_to_show == "TRADES":
            app.last_data = all_data
        else:
            app.bidask_data = all_data
    
    return data_gaps

def main(stock_name, end_time_str, date_str):
    output_dir = '/Users/<USER>/git/test_resources'
    os.makedirs(output_dir, exist_ok=True)
    
    app = HistoricalTickDataApp()
    
    try:
        # 连接到IB Gateway
        print("\n正在连接到IB Gateway...")
        app.connect('127.0.0.1', 7497, 1000)
        
        # 启动处理消息线程
        msg_thread = threading.Thread(target=message_handler, args=(app,), daemon=True)
        msg_thread.start()
        
        # 等待连接和启动
        max_wait = 10
        wait_time = 0
        while not app.start and wait_time < max_wait:
            time.sleep(1)
            wait_time += 1
            print(f"等待连接和启动... {wait_time}/{max_wait}")
            
        if not app.start:
            raise IBConnectionError("无法连接到IB Gateway")
            
        print("已连接到IB Gateway")
        
        # 创建合约对象
        contract = Contract()
        contract.symbol = stock_name
        contract.secType = "STK"
        contract.exchange = "SMART"
        contract.currency = "USD"
        
        # 请求合约详情
        print("\n请求合约详情...")
        app.reqContractDetails(100, contract)
        
        # 等待合约详情
        timeout = 10
        start_time = time.time()
        while not app.contract_details_end and time.time() - start_time < timeout:
            time.sleep(0.1)
            
        if not app.contract_details_received:
            print("警告: 未收到合约详情，合约可能不存在或没有权限")
            return
            
        # 设置查询时间
        start_datetime, end_datetime = get_ib_historical_data(end_time_str=end_time_str, date_str=date_str)
        
        print(f"\n请求 {contract.symbol} 的历史tick数据...")
        print(f"时间范围: {start_datetime} 到 {end_datetime} (纽约时间)")
        
        # 记录开始时间
        total_start_time = time.time()
        
        # 获取历史tick交易数据
        print("\n获取历史tick交易数据...")
        trades_gaps = get_historical_data(app, contract, start_datetime, end_datetime, 1, "TRADES")
        
        # 获取历史买卖盘数据
        print("\n获取历史买卖盘数据...")
        bidask_gaps = get_historical_data(app, contract, start_datetime, end_datetime, 2, "BID_ASK")
        
        print("\n数据获取完成")
        
        # 保存数据
        with app.data_lock:
            if app.last_data:
                # 将字典转换为DataFrame
                df_last = pd.DataFrame.from_dict(app.last_data, orient='index')
                # 添加UUID列
                df_last['uuid'] = df_last.index
                # 重置索引
                df_last = df_last.reset_index(drop=True)
                # 按时间排序
                df_last = df_last.sort_values('date_time')
                # 保存到CSV
                output_file = os.path.join(output_dir, f'{end_datetime.strftime("%Y%m%d_%H%M")}_{stock_name}_trades.csv')
                df_last.to_csv(output_file, index=False)
                print(f"保存 {len(app.last_data)} 条交易数据到: {output_file}")
            else:
                print("没有交易数据可保存")
                
            if app.bidask_data:
                # 将字典转换为DataFrame
                df_bidask = pd.DataFrame.from_dict(app.bidask_data, orient='index')
                # 添加UUID列
                df_bidask['uuid'] = df_bidask.index
                # 重置索引
                df_bidask = df_bidask.reset_index(drop=True)
                # 按时间排序
                df_bidask = df_bidask.sort_values('date_time')
                # 保存到CSV
                output_file = os.path.join(output_dir, f'{end_datetime.strftime("%Y%m%d_%H%M")}_{stock_name}_bidask.csv')
                df_bidask.to_csv(output_file, index=False)
                print(f"保存 {len(app.bidask_data)} 条买卖盘数据到: {output_file}")
            else:
                print("没有买卖盘数据可保存")
        
        # 报告数据间隙
        if trades_gaps:
            print("\n交易数据存在以下间隙:")
            for start, end in trades_gaps:
                print(f"  {start} - {end} (duration: {(end-start).total_seconds()/60:.1f}min)")
                
        if bidask_gaps:
            print("\n买卖盘数据存在以下间隙:")
            for start, end in bidask_gaps:
                print(f"  {start} - {end} (duration: {(end-start).total_seconds()/60:.1f}min)")
        
        # 输出请求统计
        request_stats = app.request_manager.get_request_stats()
        print("\n请求统计:")
        print("请求计数:")
        for stat_name, count in request_stats["request_counts"].items():
            print(f"  {stat_name}: {count}")
        print("\n性能指标:")
        for metric_name, value in request_stats["performance"].items():
            print(f"  {metric_name}: {value:.2f}秒")
        
        # 输出错误统计
        error_stats = app.error_handler.get_error_stats()
        if error_stats["total_errors"] > 0:
            print("\n错误统计:")
            print(f"总错误数: {error_stats['total_errors']}")
            print("错误类型统计:")
            for error_type, count in error_stats["error_counts"].items():
                print(f"  {error_type}: {count}")
            if error_stats["last_error_time"]:
                print(f"最后一次错误时间: {error_stats['last_error_time']}")
        
        # 计算并打印总耗时
        total_elapsed_time = time.time() - total_start_time
        minutes = int(total_elapsed_time // 60)
        seconds = total_elapsed_time % 60
        print(f"\n总耗时: {minutes}分{seconds:.2f}秒")
        
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        
    finally:
        app.is_done = True
        if app.isConnected():
            app.disconnect()
            print("\n已断开与IB Gateway的连接")
            
if __name__ == "__main__":
    stock_name = 'icon'
    date_str = '2025-06-13'
    end_time_str = '07:25'
    main(stock_name, end_time_str, date_str)