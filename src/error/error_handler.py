"""
IB数据获取错误处理模块
"""

from typing import Dict, Any, Optional
import logging
from datetime import datetime

class IBError(Exception):
    """IB数据获取基础异常类"""
    def __init__(self, message: str, error_code: Optional[int] = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class IBConnectionError(IBError):
    """连接错误"""
    pass

class IBTimeoutError(IBError):
    """请求超时错误"""
    pass

class IBRateLimitError(IBError):
    """请求频率限制错误"""
    pass

class IBDataError(IBError):
    """数据错误"""
    pass

class ErrorHandler:
    """错误处理器"""
    
    # IB API错误码映射
    ERROR_CODE_MAP = {
        162: "历史数据不存在",
        200: "没有符合条件的数据",
        162: "合约数据不存在",
        504: "不支持请求的数据",
        502: "请求被拒绝",
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        self.error_stats: Dict[str, int] = {}
        self.last_error_time: Optional[datetime] = None
    
    def _setup_logging(self):
        """设置日志配置"""
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def handle_error(self, error_code: int, error_string: str, 
                    request_id: Optional[int] = None) -> None:
        """处理IB API错误"""
        self.last_error_time = datetime.now()
        error_type = self._categorize_error(error_code)
        
        # 更新错误统计
        error_key = f"{error_type}_{error_code}"
        self.error_stats[error_key] = self.error_stats.get(error_key, 0) + 1
        
        # 记录错误
        self.logger.error(
            f"Error {error_code} ({error_type}): {error_string} "
            f"Request ID: {request_id}"
        )
        
        # 根据错误类型抛出相应异常
        if error_type == "connection":
            raise IBConnectionError(error_string, error_code)
        elif error_type == "timeout":
            raise IBTimeoutError(error_string, error_code)
        elif error_type == "rate_limit":
            raise IBRateLimitError(error_string, error_code)
        elif error_type == "data":
            raise IBDataError(error_string, error_code)
    
    def _categorize_error(self, error_code: int) -> str:
        """对错误进行分类"""
        if error_code in (502, 504):
            return "connection"
        elif error_code in (162, 200):
            return "data"
        elif error_code == 420:
            return "rate_limit"
        else:
            return "unknown"
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            "total_errors": sum(self.error_stats.values()),
            "error_counts": self.error_stats,
            "last_error_time": self.last_error_time
        }
    
    def should_retry(self, error_code: int, attempt_count: int,
                    max_attempts: int = 3) -> bool:
        """判断是否应该重试请求"""
        if attempt_count >= max_attempts:
            return False
            
        error_type = self._categorize_error(error_code)
        
        # 连接错误和超时错误可以重试
        if error_type in ("connection", "timeout"):
            return True
            
        # 数据不存在错误不需要重试
        if error_type == "data":
            return False
            
        # 频率限制错误需要等待后重试
        if error_type == "rate_limit":
            return True
            
        return False
    
    def get_retry_delay(self, error_code: int, attempt_count: int) -> float:
        """获取重试延迟时间（秒）"""
        error_type = self._categorize_error(error_code)
        
        if error_type == "rate_limit":
            return 60.0  # 频率限制错误等待1分钟
            
        # 使用指数退避策略
        return min(300, 2 ** attempt_count)  # 最大等待5分钟 