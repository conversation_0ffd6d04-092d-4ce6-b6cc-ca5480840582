#!/usr/bin/env python3
"""
基于InterProcessPyObjects的高性能进程间数据管理器
专门用于股票数据的进程间共享
"""

import os
import logging
import threading
import time
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import IntEnum
from multiprocessing.shared_memory import SharedMemory

# 设置环境变量确保hash一致性
os.environ['PYTHONHASHSEED'] = '0'

try:
    from ipc_py_objects import *
    INTERPROCESS_AVAILABLE = True
    logging.info("InterProcessPyObjects导入成功")
except ImportError as e:
    INTERPROCESS_AVAILABLE = False
    logging.warning(f"InterProcessPyObjects不可用: {e}")


class MessageType(IntEnum):
    """消息类型枚举"""
    STOCK_LIST_UPDATE = 0
    REALTIME_BAR_UPDATE = 1
    HISTORICAL_DATA_END = 2
    IB_API_STATUS_UPDATE = 3


@dataclass
class StockListMessage:
    """股票列表消息"""
    stock_list: List[str]
    timestamp: str
    message_type: int = MessageType.STOCK_LIST_UPDATE


@dataclass
class StockBarMessage:
    """股票Bar数据消息"""
    stock_code: str
    time: str
    open: float
    high: float
    low: float
    close: float
    volume: float
    data_type: str  # 'historical' or 'realtime'
    timestamp: str
    message_type: int = MessageType.REALTIME_BAR_UPDATE


@dataclass
class HistoricalDataEndMessage:
    """历史数据完成消息"""
    stock_code: str
    start_time: str
    end_time: str
    message: str
    timestamp: str
    message_type: int = MessageType.HISTORICAL_DATA_END


@dataclass
class IBAPIStatusMessage:
    """IB API状态消息"""
    is_connected: bool
    server_version: str
    connection_time: str
    active_stocks_count: int
    timestamp: str
    message_type: int = MessageType.IB_API_STATUS_UPDATE


class InterProcessDataManager:
    """基于InterProcessPyObjects的高性能数据管理器"""

    def __init__(self, shared_memory_name: str = 'stock_data_ipc',
                 shared_memory_size: int = 100 * 1024 * 1024,
                 is_creator: bool = False):
        """
        初始化数据管理器

        Args:
            shared_memory_name: 共享内存名称
            shared_memory_size: 共享内存大小(字节)
            is_creator: 是否为创建者(True=IB API进程, False=Flask应用)
        """
        self.shared_memory_name = shared_memory_name
        self.shared_memory_size = shared_memory_size
        self.is_creator = is_creator
        self.available = INTERPROCESS_AVAILABLE

        self._lock = threading.RLock()
        self._callbacks: Dict[str, List[Callable]] = {}
        self._shared_memory = None
        self._ashared_memory_manager = None
        self._context_manager = None
        self._initialized = False

        if not self.available:
            logging.warning("InterProcessPyObjects不可用，数据管理器将无法工作")
    
    async def initialize(self):
        """异步初始化共享内存"""
        if not self.available:
            logging.error("InterProcessPyObjects不可用，无法初始化")
            return False

        try:
            if self.is_creator:
                # 创建者模式：创建共享内存
                self._shared_memory = SharedMemory(
                    name=self.shared_memory_name,
                    create=True,
                    size=self.shared_memory_size
                )
                logging.info(f"创建共享内存: {self.shared_memory_name} ({self.shared_memory_size//1024//1024}MB)")
            else:
                # 消费者模式：连接到现有共享内存
                self._shared_memory = SharedMemory(name=self.shared_memory_name)
                logging.info(f"连接到共享内存: {self.shared_memory_name}")

            # 创建异步共享内存管理器
            self._ashared_memory_manager = ASharedMemoryManager(self._shared_memory)

            # 初始化共享内存
            async with self._ashared_memory_manager as asmm:
                self._context_manager = asmm()
                self._initialized = True
                logging.info("InterProcessDataManager初始化成功")
                return True

        except Exception as e:
            logging.error(f"InterProcessDataManager初始化失败: {e}")
            return False
    
    def register_callback(self, message_type: str, callback: Callable):
        """注册消息回调函数"""
        with self._lock:
            if message_type not in self._callbacks:
                self._callbacks[message_type] = []
            self._callbacks[message_type].append(callback)
            logging.info(f"注册回调函数: {message_type}")

    def _notify_callbacks(self, message_type: str, data: Any):
        """通知回调函数"""
        callbacks = self._callbacks.get(message_type, [])
        for callback in callbacks:
            try:
                callback(message_type, data)
            except Exception as e:
                logging.error(f"回调函数执行失败: {e}")

    async def send_stock_list(self, stock_list: List[str]):
        """发送股票列表更新"""
        if not self._initialized or not self.available:
            logging.error("数据管理器未初始化或不可用")
            return False

        try:
            message = StockListMessage(
                stock_list=stock_list.copy(),
                timestamp=datetime.now().isoformat()
            )

            async with self._context_manager as shared_memory:
                shared_memory.value.put_message(message)

            logging.info(f"发送股票列表: {len(stock_list)}只股票")
            return True

        except Exception as e:
            logging.error(f"发送股票列表失败: {e}")
            return False
    
    async def listen_for_messages(self, callback_handler=None):
        """监听消息（用于消费者端）"""
        if not self._initialized or not self.available:
            logging.error("数据管理器未初始化或不可用")
            return

        logging.info("开始监听消息...")

        while True:
            try:
                async with self._context_manager.if_has_messages() as shared_memory:
                    if shared_memory.existence:
                        message = shared_memory.value.take_message()

                        # 根据消息类型处理
                        if hasattr(message, 'message_type'):
                            if message.message_type == MessageType.STOCK_LIST_UPDATE:
                                self._notify_callbacks('stock_list', message)
                                if callback_handler:
                                    callback_handler('stock_list', message)

                            elif message.message_type == MessageType.REALTIME_BAR_UPDATE:
                                self._notify_callbacks('realtime_bar', message)
                                if callback_handler:
                                    callback_handler('realtime_bar', message)

                            elif message.message_type == MessageType.HISTORICAL_DATA_END:
                                self._notify_callbacks('historical_data_end', message)
                                if callback_handler:
                                    callback_handler('historical_data_end', message)

                            elif message.message_type == MessageType.IB_API_STATUS_UPDATE:
                                self._notify_callbacks('ib_api_status', message)
                                if callback_handler:
                                    callback_handler('ib_api_status', message)

                await asyncio.sleep(0.001)  # 短暂休眠避免CPU占用过高

            except NoMessagesInQueueError:
                await asyncio.sleep(0.01)
            except Exception as e:
                logging.error(f"监听消息时出错: {e}")
                await asyncio.sleep(0.1)

    async def cleanup(self):
        """清理资源"""
        try:
            if self._shared_memory and self.is_creator:
                # 只有创建者负责清理共享内存
                self._shared_memory.unlink()
                logging.info("共享内存已清理")
        except Exception as e:
            logging.error(f"清理资源失败: {e}")

    def get_status(self) -> Dict[str, Any]:
        """获取管理器状态"""
        return {
            'available': self.available,
            'initialized': self._initialized,
            'is_creator': self.is_creator,
            'shared_memory_name': self.shared_memory_name,
            'shared_memory_size': self.shared_memory_size,
            'callbacks_count': {k: len(v) for k, v in self._callbacks.items()}
        }


# 全局实例
_interprocess_data_manager_instance = None
_instance_lock = threading.Lock()


def get_interprocess_data_manager(is_creator: bool = False,
                                 shared_memory_name: str = 'stock_data_ipc',
                                 shared_memory_size: int = 100 * 1024 * 1024) -> InterProcessDataManager:
    """获取进程间数据管理器单例实例"""
    global _interprocess_data_manager_instance

    if _interprocess_data_manager_instance is None:
        with _instance_lock:
            if _interprocess_data_manager_instance is None:
                _interprocess_data_manager_instance = InterProcessDataManager(
                    shared_memory_name=shared_memory_name,
                    shared_memory_size=shared_memory_size,
                    is_creator=is_creator
                )

    return _interprocess_data_manager_instance
