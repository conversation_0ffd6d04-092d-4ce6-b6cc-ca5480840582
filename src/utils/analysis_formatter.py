"""
分析结果格式化工具

该模块提供了将分析结果格式化为易读字符串的功能。
主要用于统一处理股票分析结果的显示格式。
"""
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from src.utils.stock_utils import format_volume
from src.utils.constants import *

def create_analyzer_result_dict(
    signal: Any, 
    current_price: float, 
    price_high: float, 
    price_low: float,
    price_change_ratio: float,
    total_score: float,
    price_score: float,
    volume_score_raw: float,
    volume_score_final: float,
    behavior_score: float,
    analysis_time: Optional[datetime] = None,
    analysis_start_time: Optional[datetime] = None,
    analysis_end_time: Optional[datetime] = None,
    behavior_score_15min: float = 0.0,
    # 只保留强信号联合判定
    strong_fake_pressure_15min: bool = False,
    strong_active_absorption_15min: bool = False,
    strong_bid_support_15min: bool = False,
    strong_ask_pressure_15min: bool = False,
    strong_bid_aggression_15min: bool = False,
    strong_extreme_bid_aggression_15min: bool = False,
    strong_hidden_buying_15min: bool = False,
    # 新增：所有统计字段
    fake_pressure_count_15min: int = 0,
    fake_pressure_ratio_15min: float = 0.0,
    active_absorption_count_15min: int = 0,
    active_absorption_ratio_15min: float = 0.0,
    bid_support_count_15min: int = 0,
    bid_support_ratio_15min: float = 0.0,
    ask_pressure_count_15min: int = 0,
    ask_pressure_ratio_15min: float = 0.0,
    bid_aggression_count_15min: int = 0,
    bid_aggression_ratio_15min: float = 0.0,
    extreme_bid_aggression_count_15min: int = 0,
    extreme_bid_aggression_ratio_15min: float = 0.0,
    hidden_buying_count_15min: int = 0,
    hidden_buying_ratio_15min: float = 0.0,
    total_orderbook_15min: int = 0
) -> Dict[str, Any]:
    """
    创建与stock_analyzer.py中相同结构的结果字典
    
    Args:
        signal: 信号对象，包含信号强度、置信度等信息
        current_price: 当前价格
        price_high: 区间最高价
        price_low: 区间最低价
        price_change_ratio: 价格变化率
        total_score: 总评分
        price_score: 价格评分
        volume_score_raw: 原始成交量评分
        volume_score_final: 最终成交量评分
        behavior_score: 行为评分
        analysis_time: 分析时间，如果为None则使用当前时间
        analysis_start_time: 窗口开始时间，如果为None则不包含此字段
        analysis_end_time: 窗口结束时间，如果为None则不包含此字段
        behavior_score_15min: 15分钟窗口行为评分
        strong_fake_pressure_15min: 15分钟窗口假卖真拉
        strong_active_absorption_15min: 15分钟窗口主动吸筹
        strong_bid_support_15min: 15分钟窗口买盘支撑
        strong_ask_pressure_15min: 15分钟窗口卖盘压力
        strong_bid_aggression_15min: 15分钟窗口买盘积极性
        strong_extreme_bid_aggression_15min: 15分钟窗口极度买盘积极性
        strong_hidden_buying_15min: 15分钟窗口隐藏买盘
        fake_pressure_count_15min: 15分钟窗口假卖真拉次数
        fake_pressure_ratio_15min: 15分钟窗口假卖真拉比例
        active_absorption_count_15min: 15分钟窗口主动吸筹次数
        active_absorption_ratio_15min: 15分钟窗口主动吸筹比例
        bid_support_count_15min: 15分钟窗口买盘支撑次数
        bid_support_ratio_15min: 15分钟窗口买盘支撑比例
        ask_pressure_count_15min: 15分钟窗口卖盘压力次数
        ask_pressure_ratio_15min: 15分钟窗口卖盘压力比例
        bid_aggression_count_15min: 15分钟窗口买盘积极性次数
        bid_aggression_ratio_15min: 15分钟窗口买盘积极性比例
        extreme_bid_aggression_count_15min: 15分钟窗口极度买盘积极性次数
        extreme_bid_aggression_ratio_15min: 15分钟窗口极度买盘积极性比例
        hidden_buying_count_15min: 15分钟窗口隐藏买盘次数
        hidden_buying_ratio_15min: 15分钟窗口隐藏买盘比例
        total_orderbook_15min: 15分钟窗口总订单量
        
    Returns:
        Dict[str, Any]: 标准化的分析结果字典
    """
    # 如果没有提供分析时间，使用当前时间
    if analysis_time is None:
        time_str = time.strftime('%Y-%m-%d %H:%M:%S')
    else:
        time_str = analysis_time.strftime('%Y-%m-%d %H:%M:%S')
    
    # 创建基本结果字典
    result = {
        # === 基本信息 ===
        'time': time_str,  # 分析时间
        'signal_strength': getattr(signal.signal_strength, 'name', signal.signal_strength) if hasattr(signal, 'signal_strength') else 'AVOID',  # 信号强度
        'confidence': round(signal.confidence, 2) if hasattr(signal, 'confidence') else 0.0,  # 信号置信度
        'price_trend': getattr(signal.price_trend, 'name', signal.price_trend) if hasattr(signal, 'price_trend') else 'NEUTRAL',  # 价格趋势
        'volume_trend': getattr(signal.volume_trend, 'name', signal.volume_trend) if hasattr(signal, 'volume_trend') else 'NEUTRAL',  # 成交量趋势
        'buying_pressure': round(signal.buying_pressure, 2) if hasattr(signal, 'buying_pressure') else 0.0,  # 买压指标
        
        # === 价格信息 ===
        'current_price': float(current_price),  # 当前价格
        'price_high': float(price_high),  # 区间最高价
        'price_low': float(price_low),  # 区间最低价
        
        # === 评分指标 ===
        'total_score': round(total_score, 2),  # 总评分
        'price_score': round(price_score, 2),  # 价格评分
        'volume_score_raw': round(volume_score_raw, 2),  # 原始成交量评分
        'volume_score': round(volume_score_final, 2),  # 成交量评分
        'behavior_score': round(behavior_score, 2),  # 行为评分
        'behavior_score_15min': round(behavior_score_15min, 2),  # 15分钟窗口行为评分
        
        # === 交易指标 ===
        'volume_ratio': round(signal.volume_analysis.volume_ratio, 2) if hasattr(signal, 'volume_analysis') and signal.volume_analysis is not None else 0.0,  # 成交量比率
        'price_change_ratio': round(price_change_ratio, 4),  # 价格变化率
        
        # === 主力特征 ===
        'bid_support': signal.book_behavior.bid_support if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 买盘支撑
        'ask_pressure': signal.book_behavior.ask_pressure if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 卖盘压力
        'bid_aggression': signal.book_behavior.bid_aggression if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 买盘积极性
        'extreme_bid_aggression': signal.book_behavior.extreme_bid_aggression if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 极度买盘积极性
        'active_absorption': signal.book_behavior.active_absorption if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 主动吸筹
        'hidden_buying': signal.book_behavior.hidden_buying if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 隐藏买盘
        'fake_pressure': signal.book_behavior.fake_pressure if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 假卖真拉
        
        # === 上涨压力分析特征 ===
        LARGE_BUY_ORDERS_DECREASE: signal.book_behavior.large_buy_orders_decrease if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 大单主动买入减少
        HIGH_DENSITY_SMALL_ORDERS: signal.book_behavior.high_density_small_orders if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 高位密集小单成交
        PRICE_DIFF_NARROWING: signal.book_behavior.price_diff_narrowing if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 逐笔价差变小
        SELL_ORDERS_PILING: signal.book_behavior.sell_orders_piling if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 卖盘堆积
        FAKE_SELL_ORDERS: signal.book_behavior.fake_sell_orders if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 假压单行为
        REAL_SELL_PRESSURE: signal.book_behavior.real_sell_pressure if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 真实卖压
        BUY_SIDE_THINNING: signal.book_behavior.buy_side_thinning if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 买盘变薄
        UPWARD_PRESSURE_DETECTED: signal.book_behavior.upward_pressure_detected if hasattr(signal, 'book_behavior') and signal.book_behavior is not None else False,  # 检测到上涨压力
        
        # === 15分钟窗口强信号特征 ===
        'strong_fake_pressure_15min': strong_fake_pressure_15min,
        'strong_active_absorption_15min': strong_active_absorption_15min,
        'strong_bid_support_15min': strong_bid_support_15min,
        'strong_ask_pressure_15min': strong_ask_pressure_15min,
        'strong_bid_aggression_15min': strong_bid_aggression_15min,
        'strong_extreme_bid_aggression_15min': strong_extreme_bid_aggression_15min,
        'strong_hidden_buying_15min': strong_hidden_buying_15min,
        # === 15分钟窗口统计字段 ===
        'fake_pressure_count_15min': fake_pressure_count_15min,
        'fake_pressure_ratio_15min': fake_pressure_ratio_15min,
        'active_absorption_count_15min': active_absorption_count_15min,
        'active_absorption_ratio_15min': active_absorption_ratio_15min,
        'bid_support_count_15min': bid_support_count_15min,
        'bid_support_ratio_15min': bid_support_ratio_15min,
        'ask_pressure_count_15min': ask_pressure_count_15min,
        'ask_pressure_ratio_15min': ask_pressure_ratio_15min,
        'bid_aggression_count_15min': bid_aggression_count_15min,
        'bid_aggression_ratio_15min': bid_aggression_ratio_15min,
        'extreme_bid_aggression_count_15min': extreme_bid_aggression_count_15min,
        'extreme_bid_aggression_ratio_15min': extreme_bid_aggression_ratio_15min,
        'hidden_buying_count_15min': hidden_buying_count_15min,
        'hidden_buying_ratio_15min': hidden_buying_ratio_15min,
        'total_orderbook_15min': total_orderbook_15min
    }
    
    # 添加可选的窗口时间信息
    if analysis_start_time is not None:
        result['window_start'] = analysis_start_time.strftime('%Y-%m-%d %H:%M:%S')
    if analysis_end_time is not None:
        result['window_end'] = analysis_end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    return result

def create_analysis_results_from_analyzer(analyzer_result: Union[Dict[str, Any], List[Dict[str, Any]]], current_price: float = 0.0) -> dict:
    """
    从stock_analyzer的分析结果创建标准化的分析结果字典
    
    Args:
        analyzer_result: stock_analyzer模块生成的分析结果字典或字典列表
        current_price: 当前价格（如果结果中没有提供）
        
    Returns:
        dict: 标准化的分析结果字典
    """
    # 如果传入的是列表（analyze_stock_data的返回值），取最后一个元素
    if isinstance(analyzer_result, list) and len(analyzer_result) > 0:
        analyzer_result = analyzer_result[-1]
    elif isinstance(analyzer_result, list) and len(analyzer_result) == 0:
        # 如果是空列表，返回默认结果
        return build_analysis_results({}, current_price)
    
    # 从analyzer_result中提取所有需要的字段
    # 使用build_analysis_results函数进行进一步处理和标准化
    return build_analysis_results(analyzer_result, current_price)

def build_analysis_results(result: dict, current_price: float = 0.0) -> dict:
    """
    从原始分析结果构建标准化的分析结果字典
    
    Args:
        result: 原始分析结果字典
        current_price: 当前价格（如果结果中没有提供）
        
    Returns:
        dict: 标准化的分析结果字典
    """
    # 获取信号强度，确保它是字符串类型
    signal_strength = result.get('signal_strength', 'AVOID')
    if hasattr(signal_strength, 'name'):
        signal_strength = signal_strength.name
    
    # 构建并返回标准化的分析结果字典
    return {
        'signal_strength': signal_strength,
        'current_price': float(result.get('current_price', current_price)),
        'price_high': float(result.get('price_high', 0.0)), # 区间最高
        'price_low': float(result.get('price_low', 0.0)), # 区间最低
        'price_trend': result.get('price_trend', ''), # 价格趋势
        'volume_trend': result.get('volume_trend', ''), # 成交量趋势
        'confidence': float(result.get('confidence', 0.0)), # 置信度
        'buying_pressure': float(result.get('buying_pressure', 0.0)), # 买压指标
        'analysis_time': result.get('time', ''),  # 添加分析时间
        # 详细指标评分
        'total_score': float(result.get('total_score', 0.0)), # 总评分
        'price_score': float(result.get('price_score', 0.0)), # 价格评分
        'volume_score': float(result.get('volume_score', 0.0)), # 成交量评分
        'behavior_score': float(result.get('behavior_score', 0.0)), # 行为评分
        'behavior_score_15min': float(result.get('behavior_score_15min', 0.0)), # 15分钟窗口行为评分
        # 详细指标数据
        'volume_ratio': float(result.get('volume_ratio', 0.0)), # 成交量比例
        'price_change_ratio': float(result.get('price_change_ratio', 0.0)), # 价格变化比例
        'bid_support': result.get('bid_support', ''), # 买盘支撑
        'ask_pressure': result.get('ask_pressure', ''), # 卖盘压力
        'bid_aggression': result.get('bid_aggression', ''), # 买盘积极性
        'extreme_bid_aggression': result.get('extreme_bid_aggression', ''), # 极度买盘积极性
        'active_absorption': result.get('active_absorption', ''), # 主动吸筹
        'hidden_buying': result.get('hidden_buying', ''), # 隐藏买盘
        'fake_pressure': result.get('fake_pressure', ''), # 假卖真拉
        # 上涨压力分析特征
        LARGE_BUY_ORDERS_DECREASE: result.get(LARGE_BUY_ORDERS_DECREASE, False), # 大单主动买入减少
        HIGH_DENSITY_SMALL_ORDERS: result.get(HIGH_DENSITY_SMALL_ORDERS, False), # 高位密集小单成交
        PRICE_DIFF_NARROWING: result.get(PRICE_DIFF_NARROWING, False), # 逐笔价差变小
        SELL_ORDERS_PILING: result.get(SELL_ORDERS_PILING, False), # 卖盘堆积
        FAKE_SELL_ORDERS: result.get(FAKE_SELL_ORDERS, False), # 假压单行为
        REAL_SELL_PRESSURE: result.get(REAL_SELL_PRESSURE, False), # 真实卖压
        BUY_SIDE_THINNING: result.get(BUY_SIDE_THINNING, False), # 买盘变薄
        UPWARD_PRESSURE_DETECTED: result.get(UPWARD_PRESSURE_DETECTED, False), # 检测到上涨压力
        WEAK_LIMIT_UP_ORDERS: result.get(WEAK_LIMIT_UP_ORDERS, False), # 封板挂单弱
        # 15分钟窗口的特征
        'strong_fake_pressure_15min': result.get('strong_fake_pressure_15min', False),
        'strong_active_absorption_15min': result.get('strong_active_absorption_15min', False),
        'strong_bid_support_15min': result.get('strong_bid_support_15min', False),
        'strong_ask_pressure_15min': result.get('strong_ask_pressure_15min', False),
        'strong_bid_aggression_15min': result.get('strong_bid_aggression_15min', False),
        'strong_extreme_bid_aggression_15min': result.get('strong_extreme_bid_aggression_15min', False),
        'strong_hidden_buying_15min': result.get('strong_hidden_buying_15min', False),
        # 15分钟窗口统计字段
        'fake_pressure_count_15min': result.get('fake_pressure_count_15min', 0),
        'fake_pressure_ratio_15min': result.get('fake_pressure_ratio_15min', 0.0),
        'active_absorption_count_15min': result.get('active_absorption_count_15min', 0),
        'active_absorption_ratio_15min': result.get('active_absorption_ratio_15min', 0.0),
        'bid_support_count_15min': result.get('bid_support_count_15min', 0),
        'bid_support_ratio_15min': result.get('bid_support_ratio_15min', 0.0),
        'ask_pressure_count_15min': result.get('ask_pressure_count_15min', 0),
        'ask_pressure_ratio_15min': result.get('ask_pressure_ratio_15min', 0.0),
        'bid_aggression_count_15min': result.get('bid_aggression_count_15min', 0),
        'bid_aggression_ratio_15min': result.get('bid_aggression_ratio_15min', 0.0),
        'extreme_bid_aggression_count_15min': result.get('extreme_bid_aggression_count_15min', 0),
        'extreme_bid_aggression_ratio_15min': result.get('extreme_bid_aggression_ratio_15min', 0.0),
        'hidden_buying_count_15min': result.get('hidden_buying_count_15min', 0),
        'hidden_buying_ratio_15min': result.get('hidden_buying_ratio_15min', 0.0),
        'total_orderbook_15min': result.get('total_orderbook_15min', 0)
    }

def format_analysis_results(analysis_results: dict) -> str:
    """
    将分析结果格式化为易读的字符串格式
    
    Args:
        analysis_results: 包含分析结果的字典
        
    Returns:
        str: 格式化后的分析结果字符串
    """
    # 添加上涨压力分析结果
    upward_pressure_section = (
        f'\n--- 上涨压力分析 ---\n'+
        f'检测到上涨压力: {analysis_results[UPWARD_PRESSURE_DETECTED]}       # 综合判断是否存在上涨压力（权重达到{UPWARD_PRESSURE_SCORE_THRESHOLD}即可判定为存在上涨压力）\n'+
        f'卖盘堆积: {analysis_results[SELL_ORDERS_PILING]}       # 卖盘量明显增加，形成上方阻力 权重{PRESSURE_WEIGHTS[SELL_ORDERS_PILING]}（最重要指标）\n'+
        f'真实卖压: {analysis_results[REAL_SELL_PRESSURE]}       # 卖单持续存在且成交量明显萎缩 权重{PRESSURE_WEIGHTS[REAL_SELL_PRESSURE]}（非常重要指标）\n'+
        f'大单买入减少: {analysis_results[LARGE_BUY_ORDERS_DECREASE]}       # 拉升后期大单买入频率和规模明显下降 权重{PRESSURE_WEIGHTS[LARGE_BUY_ORDERS_DECREASE]}（重要指标）\n'+
        f'买盘变薄: {analysis_results[BUY_SIDE_THINNING]}       # 买盘厚度明显减弱，支撑力减弱 权重{PRESSURE_WEIGHTS[BUY_SIDE_THINNING]}（较重要指标）\n'+
        f'高位小单密集: {analysis_results[HIGH_DENSITY_SMALL_ORDERS]}       # 高位出现密集小单成交（散户追高特征） 权重{PRESSURE_WEIGHTS[HIGH_DENSITY_SMALL_ORDERS]}（较重要指标）\n'+
        f'逐笔价差变小: {analysis_results[PRICE_DIFF_NARROWING]}       # 逐笔成交价格变动微小，价格推动力减弱 权重{PRESSURE_WEIGHTS[PRICE_DIFF_NARROWING]}（一般指标）\n'+
        f'假压单行为: {analysis_results[FAKE_SELL_ORDERS]}       # 大量卖单短时间内出现又迅速撤销 权重{PRESSURE_WEIGHTS[FAKE_SELL_ORDERS]}（辅助指标）\n'+
        f'封板挂单弱: {analysis_results[WEAK_LIMIT_UP_ORDERS]}       # 封板挂单弱 权重{PRESSURE_WEIGHTS[WEAK_LIMIT_UP_ORDERS]}（辅助指标）\n'+
        '\n'
    )
    return (
        f'置信度: {analysis_results["confidence"]:.2f}  # 信号的可信程度，范围0-1\n'+
        f'当前价格: {analysis_results["current_price"]:.2f}  # 分析窗口最后一笔成交的价格\n'+
        f'区间最高: {analysis_results["price_high"]:.2f}  # 分析窗口内的最高成交价\n'+
        f'区间最低: {analysis_results["price_low"]:.2f}  # 分析窗口内的最低成交价\n'+
        f'价格趋势: {analysis_results["price_trend"]}  # UP上涨/DOWN下跌/NEUTRAL横盘\n'+
        f'成交量趋势: {analysis_results["volume_trend"]}  # UP放量/DOWN缩量/NEUTRAL正常\n'+
        f'买压指标: {analysis_results["buying_pressure"]:.2f}  # 买入量/卖出量的比率，上限为10\n'+
        f'分析时间: {analysis_results["analysis_time"]}\n'+
        '详细指标评分\n'+
        f'总评分: {analysis_results["total_score"]:.2f}  # 所有评分的总和，决定信号强度\n'+
        f'价格评分: {analysis_results["price_score"]:.2f}  # 评估价格变化的幅度和质量，范围-5至+6\n'+
        f'成交量评分: {analysis_results["volume_score"]:.2f}  # 评估成交量的强度和质量，上限为8分\n'+
        f'行为评分: {analysis_results["behavior_score"]:.2f}  # 评估盘口行为和主力特征\n'+
        f'行为评分15分钟: {analysis_results["behavior_score_15min"]:.2f}  # 评估15分钟窗口内的盘口行为和主力特征 ≥ 5：极高分 ≥ 3：高分，主力行为强烈 < 0：主力行为弱或有负面特征\n'+
        '详细指标数据\n'+
        f'成交量比例: {analysis_results["volume_ratio"]:.2f}  # 买入量/卖出量，与买压指标相同但无上限\n'+
        f'价格变化比例: {analysis_results["price_change_ratio"]:.2f}  # 窗口内的价格变化百分比\n'+
        '\n'+
        f'买盘支撑: {analysis_results["bid_support"]}       # 买盘量是否远大于卖盘量（通常是3倍以上）\n'+
        f'买盘支撑15分钟: {analysis_results["strong_bid_support_15min"]} {analysis_results["bid_support_ratio_15min"]*100:.2f}%/{format_volume(analysis_results["bid_support_count_15min"])} # 15分钟窗口内买盘支撑强信号（比例>{BID_SUPPORT_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(BID_SUPPORT_COUNT_TH_15MIN)}）\n'+
        '\n'+
        f'卖盘压力: {analysis_results["ask_pressure"]}       # 卖盘量是否远大于买盘量（通常是3倍以上）\n'+
        f'卖盘压力15分钟: {analysis_results["strong_ask_pressure_15min"]} {analysis_results["ask_pressure_ratio_15min"]*100:.2f}%/{format_volume(analysis_results["ask_pressure_count_15min"])} # 15分钟窗口内卖盘压力强信号（比例>{ASK_PRESSURE_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(ASK_PRESSURE_COUNT_TH_15MIN)}）\n'+
        '\n'+
        f'买盘积极性: {analysis_results["bid_aggression"]}       # 买价是否接近卖价（买价/卖价>0.95）\n'+
        f'买盘积极性15分钟: {analysis_results["strong_bid_aggression_15min"]} {analysis_results["bid_aggression_ratio_15min"]*100:.2f}%/{format_volume(analysis_results["bid_aggression_count_15min"])} # 15分钟窗口内买盘积极性强信号（比例>{BID_AGGRESSION_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(BID_AGGRESSION_COUNT_TH_15MIN)}）\n'+
        '\n'+
        f'极度买盘积极性: {analysis_results["extreme_bid_aggression"]}       # 买价是否非常接近卖价（买价/卖价>0.98）\n'+
        f'极度买盘积极性15分钟: {analysis_results["strong_extreme_bid_aggression_15min"]} {analysis_results["extreme_bid_aggression_ratio_15min"]*100:.2f}%/{format_volume(analysis_results["extreme_bid_aggression_count_15min"])} # 15分钟窗口内极度买盘积极性强信号（比例>{EXTREME_BID_AGGRESSION_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(EXTREME_BID_AGGRESSION_COUNT_TH_15MIN)}）\n'+
        '\n'+
        f'主动吸筹: {analysis_results["active_absorption"]}       # 主力资金是否在积极买入 1.有持续的买盘支撑 2.卖盘在减少（撤单）或抬高卖价\n'+
        f'主动吸筹15分钟: {analysis_results["strong_active_absorption_15min"]} {analysis_results["active_absorption_ratio_15min"]*100:.2f}%/{format_volume(analysis_results["active_absorption_count_15min"])} # 15分钟窗口内主动吸筹强信号（比例>{ACTIVE_ABSORPTION_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(ACTIVE_ABSORPTION_COUNT_TH_15MIN)}）\n'+
        '\n'+
        f'隐藏买盘: {analysis_results["hidden_buying"]}       # 是否存在隐蔽的买盘行为 1.价格在上涨，说明有买盘推动 2.买卖价差很小，说明买卖双方都很积极\n'+
        f'隐藏买盘15分钟: {analysis_results["strong_hidden_buying_15min"]} {analysis_results["hidden_buying_ratio_15min"]*100:.2f}%/{format_volume(analysis_results["hidden_buying_count_15min"])} # 15分钟窗口内隐藏买盘强信号（比例>{HIDDEN_BUYING_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(HIDDEN_BUYING_COUNT_TH_15MIN)}）\n'+
        '\n'+
        f'假卖真拉: {analysis_results["fake_pressure"]}       # 是否存在制造卖盘压力假象的行为\n'+
        f'假卖真拉15分钟: {analysis_results["strong_fake_pressure_15min"]} {analysis_results["fake_pressure_ratio_15min"]*100:.2f}%/{format_volume(analysis_results["fake_pressure_count_15min"])} # 15分钟窗口内假卖真拉强信号（比例>{FAKE_PRESSURE_RATIO_TH_15MIN*100:.2f}%且次数>{format_volume(FAKE_PRESSURE_COUNT_TH_15MIN)}）\n'+
        '\n'+
        upward_pressure_section
    ) 