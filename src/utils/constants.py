import pytz

NY_TZ = pytz.timezone('America/New_York')
"""纽约时区对象
用于所有涉及美东时间的时间转换
"""

"""
股票交易系统常量定义模块

此模块定义了股票交易系统中使用的所有常量。
每个常量都有详细的文档说明其用途和含义。
"""

NOW_PRICE = 'now_price'
"""当前股价
用于存储和获取股票的实时价格
"""

HISTORICAL_DATA_END = 'historical_data_end'
"""历史数据接收结束
0: 历史数据接收未结束
1: 历史数据接收结束
"""

PREV_DAY_CLOSE = 'prev_day_close'
"""前一日收盘价
用于存储和获取股票的前一日收盘价格
"""

NEWS = 'news'
"""新闻信息
用于存储和获取与股票相关的新闻数据
"""

VOLUME = 'volume'
"""成交量
用于存储和获取股票的当前成交量
"""

CHECK_VOLUME = 'check_volume'
"""检查成交量
用于存储和获取股票的检查成交量
"""

VOLUME_90_AVG = 'volume_90_avg'
"""90天日均交易量
用于存储和获取股票90天内的日均交易量
"""

VOLUME_AVG = 'volume_avg'
"""日均交易量
用于存储和获取股票的日均交易量
"""

VOLUME_AVG_BACKUP = 'volume_avg_backup'
"""日均交易量备份
用于计算的日均交易量临时存储
"""

IS_SPREAD_NORMAL = 'is_spread_normal'
"""价差正常标志
用于判断流动性好的股票的价差是否在正常范围内
"""

STOCK_SHARESOUTSTANDING = 'sharesOutstanding'
"""流通股数量
用于存储和获取股票的流通股数量
"""

COINCIDENCE_FLG = 'coincidence_flg'
"""符合条件标志
用于标记是否符合所有交易条件的股票
"""

BUY_FLG = 'buy_flg'
"""买入标志
用于标记股票是否已经买入
"""

SET_FLG = 'set_flg'
"""符合所有条件
用于标记股票是否所有条件都符合
"""

NUMBER_FLG = 'number_flg'
"""每分钟交易次数
用于存储和获取股票每分钟的交易次数
"""

MACD_FLG = 'macd_flg'
"""MACD指标标志
用于标记MACD指标是否符合条件
"""

MACD_5_FLG = 'macd_5_flg'
"""5分钟MACD指标标志
用于标记5分钟MACD指标是否符合条件
"""

BREAKOUT_FLG = 'breakout_flg'
"""突破第一根5分钟最高点标志
用于标记股票是否突破第一根5分钟K线最高点
"""

BREAKOUT_RED_FLG = 'breakout_red_flg'
"""突破前一根红K线标志
用于标记股票是否突破前一根红色K线
"""

TRANSACTION_RANK = 'transaction_rank'
"""交易排名
用于存储和获取股票在每分钟的交易次数排名中的位置
"""

TRANSACTION_VOLUME = 'transaction_volume'
"""交易次数
用于存储和获取股票的每分钟的交易次数数据
"""

SIGNAL_STRENGTH = 'signal_strength'
"""信号强度
用于标记交易信号的强度级别，可能的值包括：
- STRONG_BUY: 强烈买入信号
- BUY: 买入信号
- WATCH: 观察
- WEAK: 弱信号
- AVOID: 避免
"""

REMOVED_FROM_TOP5 = 'removed_from_top5'
"""从前5名移除
用于标记股票是否从前5名移除
"""

WEB_STOCK_LIST = 'web_stock_list'
"""Web股票列表
用于存储和获取Web端显示的股票列表数据
""" 

SIGNAL_STRENGTH_TEXT = {
    'STRONG_BUY': 'S',
    'BUY': 'B',
    'WATCH': 'WA',
    'WEAK': 'W',
    'AVOID': 'A'
}
"""
用于将信号强度转换为文本
"""

CONFIDENCE = 'confidence'
"""置信度
用于表示交易信号的可信程度，取值范围0-1
- 0: 最低置信度
- 1: 最高置信度
"""

BUYING_PRESSURE = 'buying_pressure'
"""买压指标
用于衡量市场的买入压力，通过买入量与卖出量的比值计算
- >1: 买入压力大于卖出压力
- =1: 买卖压力平衡
- <1: 卖出压力大于买入压力
"""

BEHAVIOR_SCORE = 'behavior_score'
"""行为评分
用于衡量股票的行为特征，包括买盘积极性、卖盘压力等
"""

BEHAVIOR_SCORE_15MIN = 'behavior_score_15min'
"""15分钟窗口行为评分
用于衡量股票在15分钟窗口内的行为特征，提供更长时间维度的行为分析
"""

BID_SUPPORT_15MIN = 'bid_support_15min'
"""15分钟窗口买盘支撑
用于标记股票在15分钟窗口内是否有买盘支撑
"""

ASK_PRESSURE_15MIN = 'ask_pressure_15min'
"""15分钟窗口卖盘压力
用于标记股票在15分钟窗口内是否有卖盘压力
"""

BID_AGGRESSION_15MIN = 'bid_aggression_15min'
"""15分钟窗口买盘积极性
用于标记股票在15分钟窗口内买盘是否积极
"""

EXTREME_BID_AGGRESSION_15MIN = 'extreme_bid_aggression_15min'
"""15分钟窗口极度买盘积极性
用于标记股票在15分钟窗口内买盘是否极度积极
"""

ACTIVE_ABSORPTION_15MIN = 'active_absorption_15min'
"""15分钟窗口主动吸筹
用于标记股票在15分钟窗口内是否有主动吸筹行为
"""

HIDDEN_BUYING_15MIN = 'hidden_buying_15min'
"""15分钟窗口隐藏买盘
用于标记股票在15分钟窗口内是否有隐藏买盘行为
"""

FAKE_PRESSURE_15MIN = 'fake_pressure_15min'
"""15分钟窗口假卖真拉
用于标记股票在15分钟窗口内是否有假卖真拉行为
"""

BID_SUPPORT_RATIO_TH_15MIN = 0.1
"""15分钟窗口买盘支撑比例阈值 10%"""
BID_SUPPORT_COUNT_TH_15MIN = 1500
"""15分钟窗口买盘支撑次数阈值 1.5K"""

ASK_PRESSURE_RATIO_TH_15MIN = 0.1
"""15分钟窗口卖盘压力比例阈值 10%"""
ASK_PRESSURE_COUNT_TH_15MIN = 1500
"""15分钟窗口卖盘压力次数阈值 1.5K"""

BID_AGGRESSION_RATIO_TH_15MIN = 0.95
"""15分钟窗口买盘积极性比例阈值 95%"""
BID_AGGRESSION_COUNT_TH_15MIN = 10000
"""15分钟窗口买盘积极性次数阈值 10K"""

EXTREME_BID_AGGRESSION_RATIO_TH_15MIN = 0.95
"""15分钟窗口极度买盘积极性比例阈值 95%"""
EXTREME_BID_AGGRESSION_COUNT_TH_15MIN = 10000
"""15分钟窗口极度买盘积极性次数阈值 10K"""

ACTIVE_ABSORPTION_RATIO_TH_15MIN = 0.07
"""15分钟窗口主动吸筹比例阈值 8%"""
ACTIVE_ABSORPTION_COUNT_TH_15MIN = 10000
"""15分钟窗口主动吸筹次数阈值 10K"""

HIDDEN_BUYING_RATIO_TH_15MIN = 0.1
"""15分钟窗口隐藏买盘比例阈值 10%"""
HIDDEN_BUYING_COUNT_TH_15MIN = 1000
"""15分钟窗口隐藏买盘次数阈值 1K"""

FAKE_PRESSURE_RATIO_TH_15MIN = 0.04
"""15分钟窗口假卖真拉比例阈值 4%"""
FAKE_PRESSURE_COUNT_TH_15MIN = 4000
"""15分钟窗口假卖真拉次数阈值 4K"""

RANK_COLORS = {
    1: 'green',
    2: '#FF8C00',  # 标准暗橘色
    3: 'red'
}
# 颜色常量
"""排名颜色映射
用于根据交易排名显示不同的颜色
"""

DEFAULT_COLOR = 'black'
"""默认颜色
当没有特定颜色映射时使用的默认颜色
"""

# 数据缓存配置
DATA_CACHE_DIR = "/Users/<USER>/git/test_resources"
"""数据缓存目录"""
PRICE_CHANGE_THRESHOLD = 0.3
"""价格变化阈值 30%"""
DATA_CACHE_EXPIRY = 5
"""数据缓存过期时间 5分钟"""
SAVE_COOLDOWN = 30
"""同一股票保存冷却时间 30分钟"""
DATA_LOOKBACK_MINUTES = 3
"""保存数据时向前查找的分钟数 3分钟"""
ANALYSIS_WINDOW_MINUTES = 15
"""分析时使用的时间窗口 15分钟"""

# 上涨压力分析相关常量
UPWARD_PRESSURE_DETECTED = 'upward_pressure_detected'
"""检测到上涨压力
用于标记是否检测到上涨压力信号
"""

LARGE_BUY_ORDERS_DECREASE = 'large_buy_orders_decrease'
"""大单买入减少
用于标记拉升后期大单买入频率和规模是否明显下降
"""

HIGH_DENSITY_SMALL_ORDERS = 'high_density_small_orders'
"""高位密集小单
用于标记高位是否出现密集小单成交（散户追高特征）
"""

PRICE_DIFF_NARROWING = 'price_diff_narrowing'
"""逐笔价差变小
用于标记逐笔成交价格变动是否变小，价格推动力减弱
"""

SELL_ORDERS_PILING = 'sell_orders_piling'
"""卖盘堆积
用于标记卖盘量是否明显增加，形成上方阻力
"""

FAKE_SELL_ORDERS = 'fake_sell_orders'
"""假压单行为
用于标记是否存在大量卖单短时间内出现又迅速撤销的行为
"""

REAL_SELL_PRESSURE = 'real_sell_pressure'
"""真实卖压
用于标记卖单是否持续存在且成交量明显萎缩
"""

BUY_SIDE_THINNING = 'buy_side_thinning'
"""买盘变薄
用于标记买盘厚度是否明显减弱，支撑力减弱
"""

WEAK_LIMIT_UP_ORDERS = 'weak_limit_up_orders'
"""封板挂单弱
用于标记封板挂单是否弱
"""

# 上涨压力分析指标权重
# PRESSURE_WEIGHTS = {
#     'large_buy_orders_decrease': 1.5,  # 大单买入减少 - 重要指标
#     'high_density_small_orders': 1.2,  # 高位密集小单 - 较重要指标
#     'price_diff_narrowing': 1.0,       # 逐笔价差变小 - 一般指标
#     'sell_orders_piling': 2.0,         # 卖盘堆积 - 最重要指标
#     'real_sell_pressure': 1.8,         # 真实卖压 - 非常重要指标
#     'buy_side_thinning': 1.3,          # 买盘变薄 - 较重要指标
#     'fake_sell_orders': 0.8            # 假压单行为 - 辅助指标
# }
# ---------------------------
# 各种信号的加权得分（用于综合评估上涨压力）
# 值越高，代表该因素在判断中权重越大
# ---------------------------
PRESSURE_WEIGHTS = {
    'large_buy_orders_decrease': 1.2,   # 大单买入减少 → 动能转弱，权重中等偏高
    'high_density_small_orders': 1.0,   # 高位小单密集 → 散户接盘，权重中等
    'price_diff_narrowing': 1.2,        # 价差缩小 → 多空僵持，动能衰竭，偏高
    'sell_orders_piling': 1.0,          # 卖盘堆积 → 被动承接，多杀多迹象
    'real_sell_pressure': 1.5,          # 真压单并成交缩量 → 主力出货，最强信号
    'fake_sell_orders': 0.7,            # 假压单 → 意图扰乱，但不一定真正看跌
    'buy_side_thinning': 1.0,           # 买盘变薄 → 缺乏支撑，回调可能性增加
    'weak_limit_up_orders': 1.2         # 封板挂单弱 → 拉升意愿下降或诱多失败
}
"""上涨压力分析指标权重
不同指标的重要性权重，用于加权计算上涨压力
"""

# ---------------------------
# 综合上涨压力得分阈值
# 达到或超过此分数则判定为存在实质性上涨压力
# ---------------------------
UPWARD_PRESSURE_SCORE_THRESHOLD = 3.5
# UPWARD_PRESSURE_SCORE_THRESHOLD = 2.0
"""上涨压力得分阈值
加权得分达到此阈值时，判定为存在上涨压力
"""

# ---------------------------
# 个别行为识别的判定标准（阈值）
# ---------------------------

# LARGE_BUY_DECREASE_THRESHOLD = 0.7
# 第二阶段大单买入数量少于第一阶段的 60%，认为动能明显下降
LARGE_BUY_DECREASE_THRESHOLD = 0.6
"""大单买入减少阈值
第二阶段大单买入数量少于第一阶段的 60%，认为动能明显下降
"""

# HIGH_DENSITY_SMALL_RATIO_THRESHOLD = 0.6
# 最近12笔成交中若超过65%是小单，判定为“高位密集小单成交”
HIGH_DENSITY_SMALL_RATIO_THRESHOLD = 0.65
"""高位密集小单比例阈值
最近12笔成交中若超过65%是小单，判定为“高位密集小单成交”
"""

# HIGH_PRICE_THRESHOLD = 0.95
# 若当前价高于近期最高价的 99.5%，视为“接近涨停价”
HIGH_PRICE_THRESHOLD = 0.995
"""高位价格阈值
若当前价高于近期最高价的 99.5%，视为“接近涨停价”
"""

# 逐笔成交价差均值下降至60%以下，判定为“价差缩小”
PRICE_DIFF_THRESHOLD = 0.6
"""价差变小阈值
逐笔成交价差均值下降至60%以下，判定为“价差缩小”
"""

# 卖盘挂单增幅超过1.5倍，视为“卖盘堆积”
SELL_ORDERS_INCREASE_THRESHOLD = 1.5
"""卖盘增加阈值
卖盘挂单增幅超过1.5倍，视为“卖盘堆积”
"""

# 假压单识别：
# 第二阶段挂单增幅 > 1.3 倍，随后骤降为上一阶段的 0.5 倍，视为“假压单”
FAKE_SELL_INCREASE_THRESHOLD = 1.3
"""假压单增加阈值
第二阶段挂单增幅 > 1.3 倍，随后骤降为上一阶段的 0.5 倍，视为“假压单”
"""

# FAKE_SELL_DECREASE_THRESHOLD = 0.8
FAKE_SELL_DECREASE_THRESHOLD = 0.5
"""假压单减少阈值
第二阶段挂单增幅 > 1.3 倍，随后骤降为上一阶段的 0.5 倍，视为“假压单”
"""


# 主力真实压单识别：第二阶段挂单量 > 第一阶段的 1.4 倍，且维持 > 第一阶段的 1.2 倍
# REAL_SELL_INCREASE_THRESHOLD = 1.3
REAL_SELL_INCREASE_THRESHOLD = 1.4
"""主力真实压单识别
第二阶段挂单量 > 第一阶段的 1.4 倍，且维持 > 第一阶段的 1.2 倍
"""

# REAL_SELL_SUSTAIN_THRESHOLD = 1.1
REAL_SELL_SUSTAIN_THRESHOLD = 1.2
"""主力真实压单识别
第二阶段挂单量 > 第一阶段的 1.4 倍，且维持 > 第一阶段的 1.2 倍
"""

# VOLUME_SHRINK_THRESHOLD = 0.7
# 若最新三笔成交总量比前面三笔减少 40%，则判定“成交量显著萎缩”
VOLUME_SHRINK_THRESHOLD = 0.6
"""成交量萎缩阈值
若最新三笔成交总量比前面三笔减少 40%，则判定“成交量显著萎缩”
"""

# BUY_SIDE_THIN_THRESHOLD = 0.8
# 买盘变薄识别：
# 最新买一挂单小于起始时的 60%，说明主动承接能力弱化
BUY_SIDE_THIN_THRESHOLD = 0.6
"""买盘变薄识别：
最新买一挂单小于起始时的 60%，说明主动承接能力弱化
"""

# 闪烁相关常量
BLINK_COMPONENT_SIGNAL_STRENGTH = 'signal_strength'
"""信号强度组件名称，用于闪烁功能"""

BLINK_DURATION = 20000
"""闪烁持续时间，单位为毫秒，默认20秒"""

BLINK_INTERVAL = 500
"""闪烁间隔，单位为毫秒，默认0.5秒"""

SIGNAL_STRONG_BUY = 'STRONG_BUY'
"""强烈买入信号值"""

UPDATED_DATA_INTERVAL = 0.5
"""更新数据间隔，单位为秒，默认0.5秒"""

UPDATED_DATA_MARK = False
"""更新数据标记
False: 未更新
True: 已更新
"""