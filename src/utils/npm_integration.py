#!/usr/bin/env python3
"""
NPM集成启动器 - 为main.py添加NPM支持
"""

import subprocess
import sys
import os
import threading
import time
import signal
from pathlib import Path

def is_vite_running(port):
    """检查指定端口是否有 Vite 服务运行"""
    import socket
    import requests
    
    # 首先检查端口是否被占用
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        sock.connect(('localhost', int(port)))
        sock.close()
        
        # 端口被占用，进一步验证是否是 Vite 服务
        try:
            response = requests.get(f'http://localhost:{port}/@vite/client', timeout=1)
            return response.status_code == 200
        except requests.RequestException:
            return False
    except:
        return False

def find_running_vite_server():
    """查找正在运行的 Vite 服务器端口"""
    # 常见的 Vite 端口
    common_ports = [3000, 3001, 3002, 5173, 5174]
    
    for port in common_ports:
        if is_vite_running(port):
            return str(port)
    return None

class NPMIntegration:
    """NPM集成管理器"""
    
    def __init__(self):
        self.vite_process = None
        self.npm_enabled = False
        self.vite_port = "3000"  # 默认端口
        
    def check_npm_available(self):
        """检查NPM是否可用"""
        try:
            subprocess.run(["npm", "--version"], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def install_dependencies(self):
        """安装NPM依赖"""
        if not Path("package.json").exists():
            print("⚠️ package.json不存在，跳过NPM依赖安装")
            return False
            
        if not Path("node_modules").exists():
            print("📦 安装NPM依赖...")
            try:
                subprocess.run(["npm", "install"], check=True)
                print("✅ NPM依赖安装完成")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ NPM依赖安装失败: {e}")
                return False
        return True
    
    def start_vite_server(self):
        """启动Vite开发服务器"""
        if not self.check_npm_available():
            print("⚠️ NPM不可用，跳过Vite服务器启动")
            return False
            
        if not self.install_dependencies():
            return False
            
        try:
            print("🚀 启动Vite开发服务器...")
            self.vite_process = subprocess.Popen(
                ["npm", "run", "dev"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待Vite启动并检测端口
            vite_port = self._detect_vite_port()
            
            if self.vite_process.poll() is None and vite_port:
                print("✅ Vite开发服务器启动成功")
                print(f"📊 NPM版本: http://localhost:{vite_port}/templates/index-npm.html")
                self.vite_port = vite_port
                self.npm_enabled = True
                return True
            else:
                print("❌ Vite开发服务器启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动Vite服务器时出错: {e}")
            return False
    
    def _detect_vite_port(self):
        """检测Vite实际使用的端口"""
        import re
        timeout = 10
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.vite_process.poll() is not None:
                return None
                
            # 尝试读取stdout
            if self.vite_process.stdout.readable():
                try:
                    line = self.vite_process.stdout.readline()
                    if line:
                        # 匹配Vite输出中的端口信息: "Local:   http://localhost:3001/"
                        match = re.search(r'Local:\s+http://localhost:(\d+)/', line)
                        if match:
                            return match.group(1)
                except:
                    pass
            
            time.sleep(0.5)
        
        # 如果无法检测到端口，默认返回3000
        return "3000"
    
    def stop_vite_server(self):
        """停止Vite开发服务器"""
        if self.vite_process and self.vite_process.poll() is None:
            print("🛑 停止Vite开发服务器...")
            self.vite_process.terminate()
            try:
                self.vite_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.vite_process.kill()
            print("✅ Vite开发服务器已停止")
    
    def print_status(self):
        """打印NPM集成状态"""
        print("\n" + "=" * 60)
        print("📊 TradingView Lightweight Charts 双版本状态")
        print("=" * 60)
        
        if self.npm_enabled:
            print("✅ NPM版本: http://localhost:3000/templates/index-npm.html")
            print("🧪 NPM测试: http://localhost:3000/test-npm.html")
        else:
            print("❌ NPM版本: 不可用")
            
        print("✅ NPM版本: http://localhost:5001/npm")
        print("🔄 原始版本: http://localhost:5001/")
        
        print("=" * 60)
        print("💡 推荐使用NPM版本进行生产开发")
        print("=" * 60)

# 全局NPM集成实例
npm_integration = NPMIntegration()

def setup_npm_integration():
    """设置NPM集成 - 在main.py中调用"""
    
    def start_npm_server():
        """在后台线程中启动NPM服务器"""
        npm_integration.start_vite_server()
    
    # 在后台线程中启动Vite服务器
    npm_thread = threading.Thread(target=start_npm_server, daemon=True)
    npm_thread.start()
    
    # 等待NPM服务器启动
    time.sleep(4)
    
    # 打印状态
    npm_integration.print_status()
    
    return npm_integration

def cleanup_npm_integration():
    """清理NPM集成 - 在main.py退出时调用"""
    npm_integration.stop_vite_server()
