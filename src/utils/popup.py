# import os
import applescript
from pync import Notifier
# import subprocess

# command = [
#     'terminal-notifier',
#     '-message', '这是通知内容',
#     '-title', '标题',
#     '-appIcon', '/Users/<USER>/PycharmProjects/openapi-samples-python-master/stock_trade/interactive_brokers_api/signal.png'
# ]
# subprocess.run(command)

def show_signal_message(title: str, message: str) -> None:
    applescript.run(f'display notification "{message}" with title "{title}"')

# show_signal_message('title', 'message\nffff')


def show_message(title: str, message: str) -> None:
    Notifier.notify(message, title=title)
    # message = message.replace('"', r'\"')  # 使用转义字符转义双引号
    # os.system(f"osascript -e 'display notification \"{message}\" with title \"{title}\"'")
    # os.system(f"osascript -e 'delay {duration}' -e 'tell application \"System Events\" to delete every notification'")
   
# 调用示例，duration 参数为显示时间，单位为秒
# show_message("提示", "这是一个提示信息！")
