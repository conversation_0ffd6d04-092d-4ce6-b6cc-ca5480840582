from src.utils.constants import *

# 15分钟主力特征阈值常量
def calculate_fifteen_min_behavior_score(
    bid_support_ratio: float, bid_support_count: int,
    ask_pressure_ratio: float, ask_pressure_count: int,
    bid_aggression_ratio: float, bid_aggression_count: int,
    extreme_bid_aggression_ratio: float, extreme_bid_aggression_count: int,
    active_absorption_ratio: float, active_absorption_count: int,
    hidden_buying_ratio: float, hidden_buying_count: int,
    fake_pressure_ratio: float, fake_pressure_count: int
) -> float:
    """
    计算15分钟主力特征评分
    
    Args:
        bid_support_ratio: 买盘支撑比例
        bid_support_count: 买盘支撑次数
        ask_pressure_ratio: 卖盘压力比例
        ask_pressure_count: 卖盘压力次数
        bid_aggression_ratio: 买盘积极性比例
        bid_aggression_count: 买盘积极性次数
        extreme_bid_aggression_ratio: 极度买盘积极性比例
        extreme_bid_aggression_count: 极度买盘积极性次数
        active_absorption_ratio: 主动吸筹比例
        active_absorption_count: 主动吸筹次数
        hidden_buying_ratio: 隐藏买盘比例
        hidden_buying_count: 隐藏买盘次数
        fake_pressure_ratio: 假卖真拉比例
        fake_pressure_count: 假卖真拉次数
        
    Returns:
        float: 15分钟逐笔特征评分
    """

    # 买盘支撑15分钟评分
    if bid_support_ratio > BID_SUPPORT_RATIO_TH_15MIN and bid_support_count > BID_SUPPORT_COUNT_TH_15MIN:
        bid_support_score = 1.0
    elif bid_support_ratio > BID_SUPPORT_RATIO_TH_15MIN or bid_support_count > BID_SUPPORT_COUNT_TH_15MIN:
        bid_support_score = 0.5
    else:
        bid_support_score = 0.0

    # 卖盘压力15分钟评分
    if ask_pressure_ratio > ASK_PRESSURE_RATIO_TH_15MIN and ask_pressure_count > ASK_PRESSURE_COUNT_TH_15MIN:
        ask_pressure_score = 1.0
    elif ask_pressure_ratio > ASK_PRESSURE_RATIO_TH_15MIN or ask_pressure_count > ASK_PRESSURE_COUNT_TH_15MIN:
        ask_pressure_score = 0.5
    else:
        ask_pressure_score = 0.0

    # 买盘积极性15分钟评分
    if bid_aggression_ratio > BID_AGGRESSION_RATIO_TH_15MIN and bid_aggression_count > BID_AGGRESSION_COUNT_TH_15MIN:
        bid_aggression_score = 0.5
    elif bid_aggression_ratio > BID_AGGRESSION_RATIO_TH_15MIN or bid_aggression_count > BID_AGGRESSION_COUNT_TH_15MIN:
        bid_aggression_score = 0.25
    else:
        bid_aggression_score = 0.0

    # 极度买盘积极性15分钟评分
    if extreme_bid_aggression_ratio == EXTREME_BID_AGGRESSION_RATIO_TH_15MIN and extreme_bid_aggression_count > EXTREME_BID_AGGRESSION_COUNT_TH_15MIN:
        extreme_bid_aggression_score = 0.5
    elif extreme_bid_aggression_ratio == EXTREME_BID_AGGRESSION_RATIO_TH_15MIN or extreme_bid_aggression_count > EXTREME_BID_AGGRESSION_COUNT_TH_15MIN:
        extreme_bid_aggression_score = 0.25
    else:
        extreme_bid_aggression_score = 0.0

    # 主动吸筹15分钟评分
    if active_absorption_ratio > ACTIVE_ABSORPTION_RATIO_TH_15MIN and active_absorption_count > ACTIVE_ABSORPTION_COUNT_TH_15MIN:
        active_absorption_score = 2.0
    elif active_absorption_ratio > ACTIVE_ABSORPTION_RATIO_TH_15MIN or active_absorption_count > ACTIVE_ABSORPTION_COUNT_TH_15MIN:
        active_absorption_score = 1.0
    else:
        active_absorption_score = 0.0

    # 隐藏买盘15分钟评分
    if hidden_buying_ratio > HIDDEN_BUYING_RATIO_TH_15MIN and hidden_buying_count > HIDDEN_BUYING_COUNT_TH_15MIN:
        hidden_buying_score = 1.5
    elif hidden_buying_ratio > HIDDEN_BUYING_RATIO_TH_15MIN or hidden_buying_count > HIDDEN_BUYING_COUNT_TH_15MIN:
        hidden_buying_score = 0.75
    else:
        hidden_buying_score = 0.0

    # 假卖真拉15分钟评分
    if fake_pressure_ratio > FAKE_PRESSURE_RATIO_TH_15MIN and fake_pressure_count > FAKE_PRESSURE_COUNT_TH_15MIN:
        fake_pressure_score = 2.0
    elif fake_pressure_ratio > FAKE_PRESSURE_RATIO_TH_15MIN or fake_pressure_count > FAKE_PRESSURE_COUNT_TH_15MIN:
        fake_pressure_score = 1.0
    else:
        fake_pressure_score = 0.0

    fifteen_min_behavior_score = (
        bid_support_score +
        ask_pressure_score +
        bid_aggression_score +
        extreme_bid_aggression_score +
        active_absorption_score +
        hidden_buying_score +
        fake_pressure_score
    )
    return fifteen_min_behavior_score