from futu import *
from futu import OpenQuoteContext as FutuQuoteContext

def delete_stock_reminder(quote_ctx: FutuQuoteContext) -> None:
    # 获取到价提醒
    class PriceReminderTest(PriceReminderHandlerBase):
        def on_recv_rsp(self, rsp_str):
            ret_code, content = super(PriceReminderTest, self).on_recv_rsp(rsp_str)
            if ret_code != RET_OK:
                # print(sys._getframe().f_lineno, "PriceReminderTest: error, msg: %s" % content)
                logging.info(f'PriceReminderTest: error, msg: {content}')
                return RET_ERROR, content
            # print(sys._getframe().f_lineno, "PriceReminderTest ", content)
            return RET_OK, content

    handler = PriceReminderTest()
    quote_ctx.set_handler(handler)
    ret, exist_stock_list_code = quote_ctx.get_price_reminder(code=None, market=Market.US)
    if ret == RET_OK:
        if exist_stock_list_code.shape[0] > 0:  # 如果到价提醒列表不为空
            # 删除提醒
            for i in exist_stock_list_code['code'].values.tolist():
                quote_ctx.set_price_reminder(code=i, op=SetPriceReminderOp.DEL_ALL)
    else:
        # print(sys._getframe().f_lineno, 'error:', ret)
        logging.error(f'delete_stock_reminder error: {ret}')

def set_stock_reminder(stock_code: str, quote_ctx: FutuQuoteContext, news_source: str) -> None:
    # 添加提醒
    ret_ask, ask_data = quote_ctx.set_price_reminder(code='US.' + stock_code, op=SetPriceReminderOp.ADD, key=None,
                                                                 reminder_type=PriceReminderType.THREE_MIN_CHANGE_RATE_UP
                                                                 , reminder_freq=PriceReminderFreq.ONCE, value=0.1, note=news_source)
    if ret_ask == RET_OK:
        # print(sys._getframe().f_lineno, '3分钟1%涨幅提醒设置成功：', stock_code)
        logging.info(f'3分钟1%涨幅提醒设置成功：{stock_code}')
    else:
        # print(sys._getframe().f_lineno, 'error:', stock_code)
        logging.error(f'set_stock_reminder error：{stock_code} {ask_data}')

def modify_the_watchlist(stock_code_list: list[str], quote_ctx: FutuQuoteContext) -> None:
    get_ret, get_data = quote_ctx.get_user_security("短线")
    if get_ret == RET_OK:
        if get_data.shape[0] > 0:  # 如果自选股列表不为空
            # 删除自选股列表
            quote_ctx.modify_user_security("短线", ModifyUserSecurityOp.DEL, get_data['code'].values.tolist())
    else:
        logging.error(f'modify_the_watchlist error：{get_data}')
    add_watch_stock(stock_code_list, quote_ctx)

def add_watch_stock(stock_code_list: list[str], quote_ctx: FutuQuoteContext) -> None:
    # 修改自选股列表，保持原始顺序，但需要反转因为富途从下往上显示
    modified_list = ['US.' + item for item in stock_code_list if item is not None]
    modified_list.reverse()  # 反转列表以适应富途的显示顺序
    ret, data = quote_ctx.modify_user_security("短线", ModifyUserSecurityOp.ADD, modified_list)
    if ret == RET_OK:
        logging.info(f'修改自选股列表成功：{stock_code_list}')
    else:
        logging.error(f'add_watch_stock error：{data}')