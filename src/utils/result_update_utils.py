from src.utils.constants import *
from src.data.market_data_manager import MarketDataManager

data_manager = MarketDataManager()
"""
市场数据管理器
"""

def update_manager_res_dict_test(stock_code: str, analysis_results: dict) -> None:
    """
    批量更新manager_res_dict_test的各项分析结果。
    
    Args:
        manager_res_dict_test: 市场数据字典
        keys: 键值对
        analysis_results: 分析结果
        
    Returns:
        None
    """
    data_manager.set_market_data(stock_code, {
        SIGNAL_STRENGTH: analysis_results['signal_strength'],
        CONFIDENCE: analysis_results['confidence'],
        BUYING_PRESSURE: analysis_results['buying_pressure'],
        BEHAVIOR_SCORE: analysis_results['behavior_score'],
        BEHAVIOR_SCORE_15MIN: analysis_results['behavior_score_15min'],
        UPWARD_PRESSURE_DETECTED: analysis_results[UPWARD_PRESSURE_DETECTED]
    })
    # # 更新15分钟窗口的特征，全部改为 strong_xxx_15min
    # manager_res_dict_test[keys[BEHAVIOR_SCORE_15MIN]] = analysis_results['behavior_score_15min']
    # manager_res_dict_test[keys[BID_SUPPORT_15MIN]] = analysis_results['strong_bid_support_15min']
    # manager_res_dict_test[keys[ASK_PRESSURE_15MIN]] = analysis_results['strong_ask_pressure_15min']
    # manager_res_dict_test[keys[BID_AGGRESSION_15MIN]] = analysis_results['strong_bid_aggression_15min']
    # manager_res_dict_test[keys[EXTREME_BID_AGGRESSION_15MIN]] = analysis_results['strong_extreme_bid_aggression_15min']
    # manager_res_dict_test[keys[ACTIVE_ABSORPTION_15MIN]] = analysis_results['strong_active_absorption_15min']
    # manager_res_dict_test[keys[HIDDEN_BUYING_15MIN]] = analysis_results['strong_hidden_buying_15min']
    # manager_res_dict_test[keys[FAKE_PRESSURE_15MIN]] = analysis_results['strong_fake_pressure_15min']

def set_manager_res_dict_test_default(stock_code: str) -> None:
    """
    批量设置manager_res_dict_test的各项默认值。

    Args:
        manager_res_dict_test: 市场数据字典
        keys: 键值对
        FAKE_PRESSURE_15MIN
    Returns:
        None
    """
    data_manager.set_market_data(stock_code, {
        SIGNAL_STRENGTH: 'AVOID',
        CONFIDENCE: 0.0,
        BUYING_PRESSURE: 0.0,
        NOW_PRICE: 0.0,
        BEHAVIOR_SCORE: 0.0,
        BEHAVIOR_SCORE_15MIN: 0.0,
        UPWARD_PRESSURE_DETECTED: False
    })
    # manager_res_dict_test[keys[BID_SUPPORT_15MIN]] = False
    # manager_res_dict_test[keys[ASK_PRESSURE_15MIN]] = False
    # manager_res_dict_test[keys[BID_AGGRESSION_15MIN]]= False
    # manager_res_dict_test[keys[EXTREME_BID_AGGRESSION_15MIN]] = False
    # manager_res_dict_test[keys[ACTIVE_ABSORPTION_15MIN]] = False
    # manager_res_dict_test[keys[HIDDEN_BUYING_15MIN]] = False
    # manager_res_dict_test[keys[FAKE_PRESSURE_15MIN]] = False 