def format_volume(volume):
    """
    格式化成交量，添加单位（K、M、G），保留 1 位小数点。
    
    Args:
        volume (float): 累计成交量。
    
    Returns:
        str: 格式化后的字符串，例如 "123.45K", "1.23M", "123.45G"。
    """
    if volume is None:
        return float('inf')
    if volume >= 1_000_000_000:  # 大于等于十亿
        return f"{volume / 1_000_000_000:.1f}G"
    elif volume >= 1_000_000:  # 大于等于百万
        return f"{volume / 1_000_000:.1f}M"
    elif volume >= 1_000:  # 大于等于千
        return f"{volume / 1_000:.1f}K"
    else:
        return f"{volume}"
    
def is_spread_normal(price, bid_price, ask_price):
    """
    判断流动性好的股票的价差是否正常。

    参数:
    - price (float): 股票当前价格。
    - bid_price (float): 买入价（Bid Price）。
    - ask_price (float): 卖出价（Ask Price）。

    返回:
    - (bool, str): 是否正常，以及对应的说明。
    """
    # 如果任何一个值为 None，则返回 False 或者其他适当的默认值
    if price is None or bid_price is None or ask_price is None:
        return False
    # 定义价差阈值
    thresholds = {
        (0.15, 0.2): 0.003,  # 0.15 ~ 0.2 元：价差 <= 0.003 元
        (0.2, 0.5): 0.005,   # 0.2 ~ 0.5 元：价差 <= 0.005 元
        (0.5, 1): 0.01,      # 0.5 ~ 1 元：价差 <= 0.01 元
        (1, 5): 0.02,      # 1 ~ 5 元：价差 <= 0.02 元
        (5, 10): 0.05,     # 5 ~ 10 元：价差 <= 0.05 元
        (10, 15): 0.1,     # 10 ~ 15 元：价差 <= 0.1 元
        (15, 20): 0.15,    # 15 ~ 20 元：价差 <= 0.15 元
        (20, 25): 0.2,     # 20 ~ 25 元：价差 <= 0.2 元
        (25, 30): 0.25     # 25 ~ 30 元：价差 <= 0.25 元
    }

    # 计算实际价差
    spread = ask_price - bid_price

    # 根据价格区间判断是否正常
    for (low, high), threshold in thresholds.items():
        if low <= price < high:
            if spread <= threshold:
                return True
            else:
                return False

    # 如果价格不在定义的区间内
    return False