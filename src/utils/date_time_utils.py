from datetime import datetime
from dateutil import parser
from src.utils.constants import NY_TZ

def get_difference_time(date_str):
    if not date_str:
        return '', ''
    # 解析输入时间，并转换为美国东部时间
    given_date = parser.parse(date_str).astimezone(NY_TZ)
    # 获取当前时间（美国东部时间）
    current_date = datetime.now(tz=NY_TZ)
    current_timestamp = int(current_date.timestamp())
    # 时间差（秒）
    time_difference_seconds = (given_date - current_date).total_seconds()
    time_difference_seconds = abs(time_difference_seconds)  # 取绝对值

    # 格式化日期为 "年/月/日 小时:分:秒"
    formatted_given_date = given_date.strftime("%m/%d %H:%M")
    formatted_given_date_d = given_date.strftime("%y/%m/%d")
    formatted_given_date_y = given_date.strftime("%y/%m/%d %H:%M")
    # 转换为友好字符串
    if time_difference_seconds < 60:  # 少于 1 分钟
        return "现在", formatted_given_date, formatted_given_date_d, formatted_given_date_y, current_timestamp
    elif time_difference_seconds < 3600:  # 少于 1 小时
        minutes = int(time_difference_seconds // 60)
        return f"{minutes}分钟前", formatted_given_date, formatted_given_date_d, formatted_given_date_y, current_timestamp
    elif time_difference_seconds < 86400:  # 少于 1 天
        hours = int(time_difference_seconds // 3600)
        return f"{hours}小时前", formatted_given_date, formatted_given_date_d, formatted_given_date_y, current_timestamp
    elif time_difference_seconds < 2592000:  # 少于 30 天
        days = int(time_difference_seconds // 86400)
        return f"{days}天前", formatted_given_date, formatted_given_date_d, formatted_given_date_y, current_timestamp
    elif time_difference_seconds < 31536000:  # 少于 1 年
        months = int(time_difference_seconds // 2592000)
        return f"{months}个月前", formatted_given_date, formatted_given_date_d, formatted_given_date_y, current_timestamp
    else:  # 大于 1 年
        years = int(time_difference_seconds // 31536000)
        return f"{years}年前", formatted_given_date, formatted_given_date_d, formatted_given_date_y, current_timestamp