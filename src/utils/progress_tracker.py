"""
进度跟踪器模块

用于跟踪和报告数据获取进度
"""

import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field

@dataclass
class TimeRange:
    """时间范围"""
    start: datetime.datetime
    end: datetime.datetime
    
    @property
    def duration(self) -> datetime.timedelta:
        """获取时间范围的持续时间"""
        return self.end - self.start
        
    def overlaps(self, other: 'TimeRange') -> bool:
        """检查是否与另一个时间范围重叠"""
        return (self.start <= other.end and self.end >= other.start)
        
    def merge(self, other: 'TimeRange') -> 'TimeRange':
        """合并两个时间范围"""
        return TimeRange(
            start=min(self.start, other.start),
            end=max(self.end, other.end)
        )

@dataclass
class ProgressTracker:
    """进度跟踪器"""
    
    total_range: TimeRange
    completed_ranges: List[TimeRange] = field(default_factory=list)
    failed_ranges: List[TimeRange] = field(default_factory=list)
    current_range: Optional[TimeRange] = None
    
    def __post_init__(self):
        self.start_time = datetime.datetime.now()
        self.end_time: Optional[datetime.datetime] = None
        self.last_update_time = self.start_time
        self.last_completed_duration = 0.0
        
        # 统计信息
        self.stats = {
            "total_duration": self.total_range.duration.total_seconds(),
            "completed_duration": 0.0,
            "failed_duration": 0.0,
            "remaining_duration": self.total_range.duration.total_seconds(),
            "success_rate": 0.0,
            "estimated_remaining_time": None,
            "processing_speed": 0.0  # 每秒处理的时间范围（秒）
        }
    
    def start_range(self, start: datetime.datetime, end: datetime.datetime):
        """开始处理一个时间范围"""
        self.current_range = TimeRange(start=start, end=end)
        
    def complete_range(self, start: datetime.datetime, end: datetime.datetime):
        """标记一个时间范围为完成"""
        completed = TimeRange(start=start, end=end)
        
        # 确保时间范围在总范围内
        if completed.start < self.total_range.start:
            completed = TimeRange(start=self.total_range.start, end=completed.end)
        if completed.end > self.total_range.end:
            completed = TimeRange(start=completed.start, end=self.total_range.end)
        
        # 合并重叠的时间范围
        merged = False
        for i, existing in enumerate(self.completed_ranges):
            if completed.overlaps(existing):
                self.completed_ranges[i] = completed.merge(existing)
                merged = True
                break
                
        if not merged:
            self.completed_ranges.append(completed)
            
        self._update_stats()
        
    def fail_range(self, start: datetime.datetime, end: datetime.datetime):
        """标记一个时间范围为失败"""
        failed = TimeRange(start=start, end=end)
        
        # 确保时间范围在总范围内
        if failed.start < self.total_range.start:
            failed = TimeRange(start=self.total_range.start, end=failed.end)
        if failed.end > self.total_range.end:
            failed = TimeRange(start=failed.start, end=self.total_range.end)
            
        # 检查是否与已完成的范围重叠
        for completed in self.completed_ranges:
            if failed.overlaps(completed):
                # 如果重叠，只记录未完成的部分
                if failed.start < completed.start:
                    self.failed_ranges.append(TimeRange(start=failed.start, end=completed.start))
                if failed.end > completed.end:
                    self.failed_ranges.append(TimeRange(start=completed.end, end=failed.end))
                return
        
        self.failed_ranges.append(failed)
        self._update_stats()
        
    def _update_stats(self):
        """更新统计信息"""
        current_time = datetime.datetime.now()
        elapsed_time = (current_time - self.last_update_time).total_seconds()
        
        # 计算完成和失败的持续时间
        completed_duration = sum(
            r.duration.total_seconds() for r in self.completed_ranges
        )
        failed_duration = sum(
            r.duration.total_seconds() for r in self.failed_ranges
        )
        
        # 计算处理速度（每秒处理的时间范围）
        if elapsed_time > 0:
            completed_since_last = completed_duration - self.last_completed_duration
            if completed_since_last > 0:  # 只在有新数据时更新速度
                current_speed = completed_since_last / elapsed_time
                # 使用指数移动平均来平滑处理速度
                alpha = 0.3  # 平滑因子
                if self.stats["processing_speed"] > 0:
                    # 已有速度，使用平滑
                    self.stats["processing_speed"] = (alpha * current_speed + 
                        (1 - alpha) * self.stats["processing_speed"])
                else:
                    # 首次计算速度
                    self.stats["processing_speed"] = current_speed
        
        # 更新统计信息
        total_duration = self.stats["total_duration"]
        remaining_duration = total_duration - completed_duration - failed_duration
        
        self.stats.update({
            "completed_duration": completed_duration,
            "failed_duration": failed_duration,
            "remaining_duration": max(0, remaining_duration),  # 确保不为负数
            "success_rate": min(100, (completed_duration / total_duration * 100))
            if total_duration > 0 else 0  # 确保不超过100%
        })
        
        # 基于当前处理速度估算剩余时间
        if self.stats["processing_speed"] > 0:
            # 使用移动平均速度计算剩余时间
            remaining_time = self.stats["remaining_duration"] / self.stats["processing_speed"]
            # 添加10%的缓冲时间
            self.stats["estimated_remaining_time"] = remaining_time * 1.1
        else:
            self.stats["estimated_remaining_time"] = None
            
        # 更新最后更新时间和完成持续时间
        self.last_update_time = current_time
        self.last_completed_duration = completed_duration
            
    def get_progress_report(self) -> Dict:
        """获取进度报告"""
        self._update_stats()
        
        # 格式化预计剩余时间
        if self.stats["estimated_remaining_time"] is not None:
            remaining_minutes = self.stats["estimated_remaining_time"] / 60
            if remaining_minutes < 1:
                remaining_time_str = f"{self.stats['estimated_remaining_time']:.1f}秒"
            elif remaining_minutes < 60:
                remaining_time_str = f"{remaining_minutes:.1f}分钟"
            else:
                hours = remaining_minutes / 60
                minutes = remaining_minutes % 60
                remaining_time_str = f"{int(hours)}小时{int(minutes)}分钟"
        else:
            remaining_time_str = "计算中..."
            
        # 格式化处理速度
        speed = self.stats["processing_speed"]
        if speed > 60:
            speed_str = f"{speed/60:.1f}分钟/秒"
        else:
            speed_str = f"{speed:.1f}秒/秒"
        
        return {
            "总进度": f"{self.stats['success_rate']:.1f}%",
            "已完成时间范围": f"{self.stats['completed_duration']/60:.1f}分钟",
            "失败时间范围": f"{self.stats['failed_duration']/60:.1f}分钟",
            "剩余时间范围": f"{self.stats['remaining_duration']/60:.1f}分钟",
            "预计剩余时间": remaining_time_str,
            "处理速度": speed_str  # 更改了显示名称
        }
        
    def get_completion_status(self) -> str:
        """获取完成状态的描述性文本"""
        if not self.completed_ranges and not self.failed_ranges:
            return "未开始"
            
        if self.stats["success_rate"] >= 99.9:
            return "已完成"
            
        if self.stats["success_rate"] > 0:
            return "进行中"
            
        return "失败"
        
    def print_progress(self):
        """打印进度信息"""
        report = self.get_progress_report()
        status = self.get_completion_status()
        
        print(f"\n数据获取进度 ({status}):")
        for key, value in report.items():
            print(f"  {key}: {value}")
            
        if self.current_range:
            print(f"\n当前处理时间范围:")
            print(f"  {self.current_range.start} - {self.current_range.end}")
            
    def get_failed_ranges(self) -> List[Tuple[datetime.datetime, datetime.datetime]]:
        """获取失败的时间范围列表"""
        return [(r.start, r.end) for r in self.failed_ranges]
        
    def get_completed_ranges(self) -> List[Tuple[datetime.datetime, datetime.datetime]]:
        """获取完成的时间范围列表"""
        return [(r.start, r.end) for r in self.completed_ranges] 