"""
共享内存数据管理器
使用InterProcessPyObjects实现高性能进程间数据共享
"""

import threading
import time
import logging
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict

try:
    import multiprocessing
    from multiprocessing import shared_memory
    import pickle
    INTERPROCESS_AVAILABLE = True
    logging.info("使用Python标准库multiprocessing.shared_memory")
except ImportError:
    INTERPROCESS_AVAILABLE = False
    logging.warning("共享内存不可用，将使用本地内存备用方案")


@dataclass
class StockBarData:
    """股票Bar数据结构"""
    time: str
    open: float
    high: float
    low: float
    close: float
    volume: float


@dataclass
class IBAPIStatus:
    """IB API状态数据结构"""
    is_connected: bool
    server_version: str
    connection_time: str
    active_stocks_count: int
    timestamp: str


class SharedDataManager:
    """共享内存数据管理器

    提供高性能的进程间数据共享功能，支持：
    - 股票列表共享
    - 实时Bar数据共享
    - 历史数据完成状态共享
    - IB API连接状态共享
    """

    def __init__(self, use_shared_memory: bool = True):
        """初始化共享数据管理器

        Args:
            use_shared_memory: 是否使用共享内存，False时使用本地内存
        """
        self.use_shared_memory = use_shared_memory and INTERPROCESS_AVAILABLE
        self._lock = threading.RLock()
        self._data_change_callbacks: Dict[str, List[Callable]] = {}

        # 初始化数据结构
        if self.use_shared_memory:
            self._init_shared_memory()
        else:
            self._init_local_memory()

        logging.info(f"SharedDataManager初始化完成，使用{'共享内存' if self.use_shared_memory else '本地内存'}模式")

    def __del__(self):
        """析构函数，确保资源正确清理"""
        self.cleanup()

    def cleanup(self):
        """清理共享内存资源"""
        if hasattr(self, '_shm') and self._shm is not None:
            try:
                # 如果是创建者，负责删除共享内存块
                if getattr(self, '_is_creator', False):
                    self._shm.unlink()
                    logging.info(f"共享内存已删除: {self._shm_name} (创建者清理)")

                # 关闭连接
                self._shm.close()
                logging.info(f"共享内存已关闭: {self._shm_name}")
            except Exception as e:
                logging.error(f"清理共享内存失败: {e}")
            finally:
                self._shm = None

    def unlink_shared_memory(self):
        """手动删除共享内存块（仅在确认是最后一个进程时调用）"""
        if hasattr(self, '_shm') and self._shm is not None:
            try:
                self._shm.unlink()
                logging.info(f"共享内存已手动删除: {self._shm_name}")
            except Exception as e:
                logging.error(f"手动删除共享内存失败: {e}")
            finally:
                self._shm = None

    def _init_local_memory(self):
        """初始化本地内存数据结构（备用方案）"""
        self._data = {
            'stock_list': [],
            'realtime_bars': {},
            'historical_data_end': {},
            'ib_api_status': {},
            'timestamps': {}
        }

    def _init_shared_memory(self):
        """初始化共享内存数据结构"""
        try:
            # 使用命名共享内存实现真正的进程间共享
            self._init_named_shared_memory()
            logging.info("共享内存初始化成功，使用命名共享内存")
        except Exception as e:
            logging.error(f"共享内存初始化失败: {e}")
            # 回退到本地内存
            self._init_local_memory()
            self.use_shared_memory = False
            logging.warning("回退到本地内存模式")

    def _init_named_shared_memory(self):
        """初始化命名共享内存"""
        import json

        # 共享内存块的名称
        self._shm_name = "stock_data_shared_memory"
        self._shm_size = 10 * 1024 * 1024  # 10MB - 增加到10MB以支持更大的数据
        self._is_creator = False  # 标记是否是创建者（负责清理）

        try:
            # 尝试连接到现有的共享内存
            self._shm = shared_memory.SharedMemory(name=self._shm_name)
            logging.info(f"连接到现有共享内存: {self._shm_name}")
        except FileNotFoundError:
            # 创建新的共享内存
            self._shm = shared_memory.SharedMemory(name=self._shm_name, create=True, size=self._shm_size)
            self._is_creator = True  # 标记为创建者
            # 初始化数据
            initial_data = {
                'stock_list': [],
                'realtime_bars': {},
                'historical_data_end': {},
                'ib_api_status': {},
                'timestamps': {}
            }
            self._write_to_shared_memory(initial_data)
            logging.info(f"创建新的共享内存: {self._shm_name} (创建者)")

        # 读取当前数据
        self._data = self._read_from_shared_memory()

        # 创建锁文件用于同步
        import tempfile
        self._lock_file = os.path.join(tempfile.gettempdir(), f"{self._shm_name}.lock")

    def _read_from_shared_memory(self):
        """从共享内存读取数据"""
        try:
            import gzip

            # 读取数据长度（前4字节）
            data_length = int.from_bytes(self._shm.buf[:4], byteorder='little')
            if data_length == 0:
                return {
                    'stock_list': [],
                    'realtime_bars': {},
                    'historical_data_end': {},
                    'ib_api_status': {},
                    'timestamps': {}
                }

            # 读取压缩标志（第5-8字节）
            is_compressed = int.from_bytes(self._shm.buf[4:8], byteorder='little') == 1

            # 读取实际数据
            data_bytes = bytes(self._shm.buf[8:8+data_length])

            # 如果数据被压缩，先解压
            if is_compressed:
                data_bytes = gzip.decompress(data_bytes)

            data_str = data_bytes.decode('utf-8')
            return json.loads(data_str)
        except Exception as e:
            logging.error(f"从共享内存读取数据失败: {e}")
            return {
                'stock_list': [],
                'realtime_bars': {},
                'historical_data_end': {},
                'ib_api_status': {},
                'timestamps': {}
            }

    def _write_to_shared_memory(self, data):
        """写入数据到共享内存"""
        try:
            import fcntl
            import gzip

            # 序列化数据
            data_str = json.dumps(data, ensure_ascii=False)
            data_bytes = data_str.encode('utf-8')

            # 尝试压缩数据以节省空间
            compressed_data = gzip.compress(data_bytes)

            # 选择更小的数据
            if len(compressed_data) < len(data_bytes):
                final_data = compressed_data
                is_compressed = True
            else:
                final_data = data_bytes
                is_compressed = False

            # 检查数据大小（预留8字节：4字节长度 + 4字节压缩标志）
            if len(final_data) > self._shm_size - 8:
                # 如果数据仍然太大，尝试清理旧数据
                self._cleanup_old_data(data)
                # 重新序列化
                data_str = json.dumps(data, ensure_ascii=False)
                data_bytes = data_str.encode('utf-8')
                compressed_data = gzip.compress(data_bytes)

                if len(compressed_data) < len(data_bytes):
                    final_data = compressed_data
                    is_compressed = True
                else:
                    final_data = data_bytes
                    is_compressed = False

                if len(final_data) > self._shm_size - 8:
                    logging.error(f"数据太大，无法写入共享内存: {len(final_data)} > {self._shm_size - 8}")
                    return False

            # 使用文件锁进行同步（如果锁文件存在）
            if hasattr(self, '_lock_file'):
                with open(self._lock_file, 'w') as lock_file:
                    fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX)

                    # 写入数据长度（前4字节）
                    self._shm.buf[:4] = len(final_data).to_bytes(4, byteorder='little')

                    # 写入压缩标志（第5-8字节）
                    self._shm.buf[4:8] = (1 if is_compressed else 0).to_bytes(4, byteorder='little')

                    # 写入实际数据
                    self._shm.buf[8:8+len(final_data)] = final_data

                    # 清零剩余空间
                    if len(final_data) + 8 < self._shm_size:
                        self._shm.buf[8+len(final_data):] = b'\x00' * (self._shm_size - 8 - len(final_data))
            else:
                # 没有锁文件时直接写入（简化版本）
                # 写入数据长度（前4字节）
                self._shm.buf[:4] = len(final_data).to_bytes(4, byteorder='little')

                # 写入压缩标志（第5-8字节）
                self._shm.buf[4:8] = (1 if is_compressed else 0).to_bytes(4, byteorder='little')

                # 写入实际数据
                self._shm.buf[8:8+len(final_data)] = final_data

                # 清零剩余空间
                if len(final_data) + 8 < self._shm_size:
                    self._shm.buf[8+len(final_data):] = b'\x00' * (self._shm_size - 8 - len(final_data))

            return True
        except Exception as e:
            logging.error(f"写入数据到共享内存失败: {e}")
            return False

    def _cleanup_old_data(self, data):
        """清理旧数据以节省空间"""
        try:
            # 清理实时Bar数据，只保留最近的100条记录
            for stock_code in data.get('realtime_bars', {}):
                bars = data['realtime_bars'][stock_code]
                if len(bars) > 100:
                    data['realtime_bars'][stock_code] = bars[-100:]
                    logging.info(f"清理{stock_code}的旧Bar数据，保留最近100条")

            # 清理历史数据完成状态，只保留最近的50个
            hist_data = data.get('historical_data_end', {})
            if len(hist_data) > 50:
                # 按时间戳排序，保留最新的50个
                sorted_items = sorted(hist_data.items(),
                                    key=lambda x: x[1].get('timestamp', ''),
                                    reverse=True)
                data['historical_data_end'] = dict(sorted_items[:50])
                logging.info("清理历史数据完成状态，保留最近50个")

        except Exception as e:
            logging.error(f"清理旧数据失败: {e}")

    def register_callback(self, data_type: str, callback: Callable):
        """注册数据变化回调函数

        Args:
            data_type: 数据类型 ('stock_list', 'realtime_bars', 'historical_data_end', 'ib_api_status')
            callback: 回调函数
        """
        with self._lock:
            if data_type not in self._data_change_callbacks:
                self._data_change_callbacks[data_type] = []
            self._data_change_callbacks[data_type].append(callback)

    def _notify_callbacks(self, data_type: str, data: Any):
        """通知数据变化回调函数"""
        callbacks = self._data_change_callbacks.get(data_type, [])
        for callback in callbacks:
            try:
                callback(data_type, data)
            except Exception as e:
                logging.error(f"回调函数执行失败: {e}")

    # 股票列表相关方法
    def update_stock_list(self, stock_list: List[str], timestamp: Optional[str] = None):
        """更新股票列表

        Args:
            stock_list: 股票代码列表
            timestamp: 时间戳，如果为None则使用当前时间
        """
        if timestamp is None:
            timestamp = datetime.now().isoformat()

        with self._lock:
            if self.use_shared_memory and hasattr(self, '_shm'):
                # 使用命名共享内存
                current_data = self._read_from_shared_memory()
                current_data['stock_list'] = stock_list.copy()
                current_data['timestamps']['stock_list'] = timestamp
                self._write_to_shared_memory(current_data)
                self._data = current_data
            else:
                # 使用本地内存
                self._data['stock_list'] = stock_list.copy()
                self._data['timestamps']['stock_list'] = timestamp

        self._notify_callbacks('stock_list', {
            'stock_list': stock_list,
            'timestamp': timestamp
        })

        logging.info(f"股票列表已更新: {len(stock_list)} 只股票")

    def get_stock_list(self) -> Dict[str, Any]:
        """获取股票列表

        Returns:
            包含股票列表和时间戳的字典
        """
        with self._lock:
            if self.use_shared_memory and hasattr(self, '_shm'):
                # 从共享内存读取最新数据
                current_data = self._read_from_shared_memory()
                return {
                    'stock_list': current_data['stock_list'].copy(),
                    'timestamp': current_data['timestamps'].get('stock_list', '')
                }
            else:
                # 使用本地内存
                stock_list = self._data['stock_list']
                if hasattr(stock_list, 'copy'):
                    stock_list_copy = stock_list.copy()
                else:
                    stock_list_copy = list(stock_list)

                return {
                    'stock_list': stock_list_copy,
                    'timestamp': self._data['timestamps'].get('stock_list', '')
                }

    # 实时Bar数据相关方法
    def update_realtime_bar(self, stock_code: str, bar_data: Dict[str, Any], data_type: str = 'realtime'):
        """更新实时Bar数据

        Args:
            stock_code: 股票代码
            bar_data: Bar数据字典
            data_type: 数据类型 ('realtime', 'historical')
        """
        timestamp = datetime.now().isoformat()

        with self._lock:
            if self.use_shared_memory and hasattr(self, '_shm'):
                # 使用命名共享内存
                current_data = self._read_from_shared_memory()

                # 确保股票代码的列表存在
                if stock_code not in current_data['realtime_bars']:
                    current_data['realtime_bars'][stock_code] = []

                # 添加时间戳和数据类型
                bar_with_meta = bar_data.copy()
                bar_with_meta['data_type'] = data_type
                bar_with_meta['received_at'] = timestamp

                current_data['realtime_bars'][stock_code].append(bar_with_meta)
                current_data['timestamps'][f'realtime_bars_{stock_code}'] = timestamp

                self._write_to_shared_memory(current_data)
                self._data = current_data
            else:
                # 使用本地内存
                if stock_code not in self._data['realtime_bars']:
                    self._data['realtime_bars'][stock_code] = []

                # 添加时间戳和数据类型
                bar_with_meta = bar_data.copy()
                bar_with_meta['data_type'] = data_type
                bar_with_meta['received_at'] = timestamp

                self._data['realtime_bars'][stock_code].append(bar_with_meta)
                self._data['timestamps'][f'realtime_bars_{stock_code}'] = timestamp

        self._notify_callbacks('realtime_bars', {
            'stock_code': stock_code,
            'bar_data': bar_data,
            'data_type': data_type,
            'timestamp': timestamp
        })

        logging.debug(f"实时Bar数据已更新: {stock_code}, 类型: {data_type}")

    def get_realtime_bars(self, stock_code: Optional[str] = None) -> Dict[str, Any]:
        """获取实时Bar数据

        Args:
            stock_code: 股票代码，如果为None则返回所有股票的数据

        Returns:
            实时Bar数据字典
        """
        with self._lock:
            if stock_code:
                bars = self._data['realtime_bars'].get(stock_code, [])
                # 处理共享内存和本地内存的差异
                if hasattr(bars, 'copy'):
                    bars_copy = bars.copy()
                else:
                    bars_copy = list(bars)

                return {
                    'bars': bars_copy,
                    'timestamp': self._data['timestamps'].get(f'realtime_bars_{stock_code}', '')
                }
            else:
                # 处理所有股票的数据
                all_bars = {}
                for k, v in self._data['realtime_bars'].items():
                    if hasattr(v, 'copy'):
                        all_bars[k] = v.copy()
                    else:
                        all_bars[k] = list(v)

                return {
                    'all_bars': all_bars,
                    'timestamps': {k: v for k, v in self._data['timestamps'].items() if k.startswith('realtime_bars_')}
                }

    # 历史数据完成状态相关方法
    def update_historical_data_end(self, stock_code: str, data_type: str, start_time: str, end_time: str):
        """更新历史数据完成状态

        Args:
            stock_code: 股票代码
            data_type: 数据类型
            start_time: 开始时间
            end_time: 结束时间
        """
        timestamp = datetime.now().isoformat()

        with self._lock:
            if self.use_shared_memory and hasattr(self, '_shm'):
                # 使用命名共享内存
                current_data = self._read_from_shared_memory()
                current_data['historical_data_end'][stock_code] = {
                    'data_type': data_type,
                    'start_time': start_time,
                    'end_time': end_time,
                    'timestamp': timestamp
                }
                current_data['timestamps'][f'historical_data_end_{stock_code}'] = timestamp
                self._write_to_shared_memory(current_data)
                self._data = current_data
            else:
                # 使用本地内存
                self._data['historical_data_end'][stock_code] = {
                    'data_type': data_type,
                    'start_time': start_time,
                    'end_time': end_time,
                    'timestamp': timestamp
                }
                self._data['timestamps'][f'historical_data_end_{stock_code}'] = timestamp

        self._notify_callbacks('historical_data_end', {
            'stock_code': stock_code,
            'data_type': data_type,
            'start_time': start_time,
            'end_time': end_time,
            'timestamp': timestamp
        })

        logging.info(f"历史数据完成状态已更新: {stock_code}")

    def get_historical_data_end(self, stock_code: Optional[str] = None) -> Dict[str, Any]:
        """获取历史数据完成状态

        Args:
            stock_code: 股票代码，如果为None则返回所有股票的状态

        Returns:
            历史数据完成状态字典
        """
        with self._lock:
            if stock_code:
                return self._data['historical_data_end'].get(stock_code, {})
            else:
                # 处理共享内存和本地内存的差异
                hist_data = self._data['historical_data_end']
                if hasattr(hist_data, 'copy'):
                    return hist_data.copy()
                else:
                    return dict(hist_data)

    # IB API状态相关方法
    def update_ib_api_status(self, is_connected: bool, server_version: str = "",
                           connection_time: str = "", active_stocks_count: int = 0):
        """更新IB API连接状态

        Args:
            is_connected: 是否连接
            server_version: 服务器版本
            connection_time: 连接时间
            active_stocks_count: 活跃股票数量
        """
        timestamp = datetime.now().isoformat()

        status_data = {
            'is_connected': is_connected,
            'server_version': server_version,
            'connection_time': connection_time,
            'active_stocks_count': active_stocks_count,
            'timestamp': timestamp
        }

        with self._lock:
            if self.use_shared_memory and hasattr(self, '_shm'):
                # 使用命名共享内存
                current_data = self._read_from_shared_memory()
                current_data['ib_api_status'] = status_data
                current_data['timestamps']['ib_api_status'] = timestamp
                self._write_to_shared_memory(current_data)
                self._data = current_data
            else:
                # 使用本地内存
                self._data['ib_api_status'] = status_data
                self._data['timestamps']['ib_api_status'] = timestamp

        self._notify_callbacks('ib_api_status', status_data)

        logging.info(f"IB API状态已更新: 连接={is_connected}, 活跃股票={active_stocks_count}")

    def get_ib_api_status(self) -> Dict[str, Any]:
        """获取IB API连接状态

        Returns:
            IB API状态字典
        """
        with self._lock:
            # 处理共享内存和本地内存的差异
            api_status = self._data['ib_api_status']
            if hasattr(api_status, 'copy'):
                return api_status.copy()
            else:
                return dict(api_status)

    # 工具方法
    def clear_data(self, data_type: Optional[str] = None):
        """清理数据

        Args:
            data_type: 要清理的数据类型，如果为None则清理所有数据
        """
        with self._lock:
            if data_type:
                if data_type in self._data:
                    if isinstance(self._data[data_type], dict):
                        self._data[data_type].clear()
                    elif isinstance(self._data[data_type], list):
                        self._data[data_type].clear()
                    # 清理相关时间戳
                    keys_to_remove = [k for k in self._data['timestamps'] if k.startswith(data_type)]
                    for key in keys_to_remove:
                        del self._data['timestamps'][key]
            else:
                # 清理所有数据
                self._data['stock_list'].clear()
                self._data['realtime_bars'].clear()
                self._data['historical_data_end'].clear()
                self._data['ib_api_status'].clear()
                self._data['timestamps'].clear()

        logging.info(f"数据已清理: {data_type if data_type else '全部'}")

    def get_status(self) -> Dict[str, Any]:
        """获取管理器状态信息

        Returns:
            状态信息字典
        """
        with self._lock:
            return {
                'use_shared_memory': self.use_shared_memory,
                'interprocess_available': INTERPROCESS_AVAILABLE,
                'data_counts': {
                    'stock_list_count': len(self._data['stock_list']),
                    'realtime_bars_stocks': len(self._data['realtime_bars']),
                    'historical_data_end_stocks': len(self._data['historical_data_end']),
                    'ib_api_status_available': bool(self._data['ib_api_status']),
                    'total_timestamps': len(self._data['timestamps'])
                },
                'callbacks_registered': {k: len(v) for k, v in self._data_change_callbacks.items()}
            }


# 全局Manager实例（用于进程间共享）
_global_manager = None
_global_manager_lock = threading.Lock()

def get_global_manager():
    """获取全局Manager实例"""
    global _global_manager, _global_manager_lock

    if _global_manager is None:
        with _global_manager_lock:
            if _global_manager is None:
                try:
                    _global_manager = multiprocessing.Manager()
                    logging.info("全局Manager创建成功")
                except Exception as e:
                    logging.error(f"全局Manager创建失败: {e}")
                    _global_manager = None

    return _global_manager

# 单例模式实现
_shared_data_manager_instance = None
_instance_lock = threading.Lock()


def get_shared_data_manager(use_shared_memory: bool = True) -> SharedDataManager:
    """获取共享数据管理器单例实例

    Args:
        use_shared_memory: 是否使用共享内存

    Returns:
        SharedDataManager实例
    """
    global _shared_data_manager_instance

    if _shared_data_manager_instance is None:
        with _instance_lock:
            if _shared_data_manager_instance is None:
                _shared_data_manager_instance = SharedDataManager(use_shared_memory)

    return _shared_data_manager_instance