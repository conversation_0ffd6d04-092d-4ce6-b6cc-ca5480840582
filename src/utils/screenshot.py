from PIL import ImageGrab
import pytesseract
import numpy as np
import logging

# 导入PyTorch配置工具
from .pytorch_config import configure_pytorch_for_mps

# 延迟导入easyocr
_reader = None

def _get_reader():
    """延迟初始化EasyOCR Reader，使用PyTorch配置以避免MPS警告"""
    global _reader
    if _reader is None:
        try:
            # 配置PyTorch以避免MPS警告
            device = configure_pytorch_for_mps()

            # 现在安全地导入easyocr
            import easyocr

            # 创建Reader实例
            _reader = easyocr.Reader(['en'])
            logging.info(f'EasyOCR模型已加载到内存，使用设备: {device}')

        except Exception as e:
            logging.error(f"初始化EasyOCR时出错: {e}")
            _reader = None

    return _reader

def get_stock_code():
    """使用 OCR 识别固定区域的股票代码"""
    # 获取EasyOCR Reader实例
    reader = _get_reader()
    if reader is None:
        logging.error("EasyOCR Reader未能初始化")
        return ""

    try:
        # 设定固定区域(左上角X, 左上角Y, 右下角X, 右下角Y)
        bbox = (1094, 97, 1258, 125)  # 左上角 (x, y) 到 (x+width, y+height)
        # 截取屏幕
        img = ImageGrab.grab(bbox)
        image = np.array(img)  # 将 PIL 图像转换为 numpy 数组
        results = reader.readtext(image, detail=0, paragraph=False, decoder='greedy')

        if results:
            text = results[0].split(" ")[0]
            print(f"识别结果: {text}")
            return text
        else:
            logging.warning("OCR未识别到任何文字")
            return ""
    except Exception as e:
        logging.error(f"OCR识别股票代码时出错: {e}")
        return ""
    # img.save("captured_image.png") 
    # 使用 Tesseract OCR 提取文字，仅提取字母
    # custom_config = r'--psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
    # text = pytesseract.image_to_string(img, config=custom_config)
    # return text.strip()

def get_stock_price():
    """使用 OCR 识别固定区域的股票买入卖出价"""
    # 设定固定区域(左上角X, 左上角Y, 右下角X, 右下角Y)
    bbox = (580, 745, 925, 767)  # 左上角 (x, y) 到 (x+width, y+height)
    # 截取屏幕
    img = ImageGrab.grab(bbox)
    # img.save("pcaptured_image.png") 
    # 转换为 OpenCV 格式
    # img = cv2.cvtColor(np.array(img), cv2.COLOR_BGR2GRAY)

    # 图像预处理：提高对比度，应用二值化
    # _, img = cv2.threshold(img, 150, 255, cv2.THRESH_BINARY)

    # 使用 Tesseract OCR 识别
    # custom_config = r'--psm 6 -c tessedit_char_whitelist=0123456789.'
    text = pytesseract.image_to_string(img, lang="eng")
    # cv2.imwrite("processed_image.png", img)
    logging.info(f'get_stock_price {text}')
    return text.strip()

# get_stock_code()
# get_stock_price()