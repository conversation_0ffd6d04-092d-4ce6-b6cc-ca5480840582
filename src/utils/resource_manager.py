"""
资源管理器模块

该模块提供了统一的资源管理机制，主要功能包括：
1. 资源的生命周期管理
2. 资源清理的原子性保证
3. 超时控制
4. 资源状态追踪
5. 清理过程的可重入性保证

主要组件：
- ResourceManager: 资源管理器类
- ResourceStatus: 资源状态枚举
- CleanupError: 清理错误异常类
"""

import threading
import logging
import time
from enum import Enum
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from functools import wraps

class ResourceStatus(Enum):
    """资源状态枚举"""
    ACTIVE = "active"           # 资源活跃
    CLEANING = "cleaning"       # 正在清理
    CLEANED = "cleaned"         # 已清理
    ERROR = "error"            # 清理错误

class CleanupError(Exception):
    """清理错误异常"""
    pass

class ResourceManager:
    """资源管理器类
    
    该类负责管理资源的生命周期，确保资源被正确清理。
    
    特性：
    1. 线程安全
    2. 支持超时控制
    3. 防止重复清理
    4. 提供清理状态追踪
    5. 支持回滚机制
    
    属性:
        _resources (Dict): 受管理的资源字典
        _status (Dict): 资源状态字典
        _cleanup_handlers (Dict): 资源清理处理函数字典
        _lock (threading.Lock): 线程锁
        _start_time (Dict): 资源创建时间字典
    """
    
    def __init__(self, cleanup_timeout: int = 30):
        """初始化资源管理器
        
        Args:
            cleanup_timeout: 清理超时时间（秒）
        """
        self._resources: Dict[str, Any] = {}
        self._status: Dict[str, ResourceStatus] = {}
        self._cleanup_handlers: Dict[str, Callable] = {}
        self._lock = threading.Lock()
        self._start_time: Dict[str, datetime] = {}
        self._cleanup_timeout = cleanup_timeout
        self._logger = logging.getLogger(__name__)
        
    def register(self, resource_id: str, resource: Any, 
                cleanup_handler: Optional[Callable] = None) -> None:
        """注册资源
        
        Args:
            resource_id: 资源标识符
            resource: 资源对象
            cleanup_handler: 自定义清理处理函数
        """
        with self._lock:
            if resource_id in self._resources:
                raise ValueError(f"资源 {resource_id} 已经注册")
                
            self._resources[resource_id] = resource
            self._status[resource_id] = ResourceStatus.ACTIVE
            self._start_time[resource_id] = datetime.now()
            
            if cleanup_handler:
                self._cleanup_handlers[resource_id] = cleanup_handler
                
            self._logger.info(f"资源 {resource_id} 注册成功")
            
    def _cleanup_resource(self, resource_id: str) -> None:
        """清理单个资源
        
        Args:
            resource_id: 资源标识符
            
        Raises:
            CleanupError: 清理过程中发生错误
        """
        try:
            # 获取资源和清理处理函数
            resource = self._resources.get(resource_id)
            cleanup_handler = self._cleanup_handlers.get(resource_id)
            
            # 如果有自定义清理函数，使用它
            if cleanup_handler:
                cleanup_handler(resource)
            # 否则尝试调用资源的cleanup方法
            elif hasattr(resource, 'cleanup'):
                resource.cleanup()
            # 对于锁资源，特别处理
            elif hasattr(resource, 'acquire') and hasattr(resource, 'release'):
                # 检查是否是锁类型的对象（通过方法检查而不是类型检查）
                try:
                    # 尝试检查锁状态，如果锁被当前线程持有则释放
                    if hasattr(resource, '_is_owned') and callable(getattr(resource, '_is_owned', None)):
                        if resource._is_owned():
                            resource.release()
                    elif hasattr(resource, 'locked') and callable(getattr(resource, 'locked', None)):
                        # 对于某些锁类型，使用locked()方法检查
                        if resource.locked():
                            try:
                                resource.release()
                            except RuntimeError:
                                # 忽略"release unlocked lock"错误
                                self._logger.warning(f"尝试释放未锁定的锁 {resource_id}")
                except (AttributeError, RuntimeError) as e:
                    # 锁清理失败，记录警告但继续
                    self._logger.warning(f"锁资源 {resource_id} 清理时出现问题: {str(e)}")
            # 如果资源是上下文管理器，调用__exit__
            elif hasattr(resource, '__exit__'):
                resource.__exit__(None, None, None)
            # 如果资源有close方法，调用close
            elif hasattr(resource, 'close'):
                resource.close()
                
            self._logger.info(f"资源 {resource_id} 清理成功")
            
        except Exception as e:
            self._logger.error(f"清理资源 {resource_id} 时发生错误: {str(e)}", 
                             exc_info=True)
            # 记录错误但不抛出异常，避免影响清理线程
            self._logger.error(f"清理资源 {resource_id} 失败: {str(e)}")
            # raise CleanupError(f"清理资源 {resource_id} 失败: {str(e)}")
            
    def cleanup(self, resource_id: str) -> bool:
        """清理资源
        
        Args:
            resource_id: 资源标识符
            
        Returns:
            bool: 清理是否成功
            
        Raises:
            ValueError: 资源不存在
            CleanupError: 清理过程中发生错误
        """
        with self._lock:
            # 检查资源是否存在
            if resource_id not in self._resources:
                raise ValueError(f"资源 {resource_id} 不存在")
                
            # 检查资源状态
            current_status = self._status[resource_id]
            if current_status == ResourceStatus.CLEANED:
                self._logger.info(f"资源 {resource_id} 已经清理完毕")
                return True
            elif current_status == ResourceStatus.CLEANING:
                self._logger.info(f"资源 {resource_id} 正在清理中")
                return False
                
            # 更新状态为清理中
            self._status[resource_id] = ResourceStatus.CLEANING
            
        try:
            # 设置清理超时
            cleanup_start = datetime.now()
            cleanup_thread = threading.Thread(
                target=self._cleanup_resource,
                args=(resource_id,)
            )
            cleanup_thread.start()
            cleanup_thread.join(timeout=self._cleanup_timeout)
            
            # 检查是否超时
            if cleanup_thread.is_alive():
                self._logger.error(f"清理资源 {resource_id} 超时")
                with self._lock:
                    self._status[resource_id] = ResourceStatus.ERROR
                return False
                
            # 更新状态为已清理
            with self._lock:
                self._status[resource_id] = ResourceStatus.CLEANED
                self._resources.pop(resource_id, None)
                self._cleanup_handlers.pop(resource_id, None)
                self._start_time.pop(resource_id, None)
                
            cleanup_duration = (datetime.now() - cleanup_start).total_seconds()
            self._logger.info(f"资源 {resource_id} 清理完成，耗时 {cleanup_duration:.2f} 秒")
            return True
            
        except Exception as e:
            with self._lock:
                self._status[resource_id] = ResourceStatus.ERROR
            self._logger.error(f"清理资源 {resource_id} 时发生错误: {str(e)}", 
                             exc_info=True)
            return False
            
    def cleanup_all(self) -> Dict[str, bool]:
        """清理所有资源
        
        Returns:
            Dict[str, bool]: 资源清理结果字典
        """
        results = {}
        resource_ids = list(self._resources.keys())
        
        for resource_id in resource_ids:
            try:
                results[resource_id] = self.cleanup(resource_id)
            except Exception as e:
                self._logger.error(f"清理资源 {resource_id} 时发生错误: {str(e)}", 
                                 exc_info=True)
                results[resource_id] = False
                
        return results
        
    def get_status(self, resource_id: str) -> ResourceStatus:
        """获取资源状态
        
        Args:
            resource_id: 资源标识符
            
        Returns:
            ResourceStatus: 资源状态
            
        Raises:
            ValueError: 资源不存在
        """
        with self._lock:
            if resource_id not in self._status:
                raise ValueError(f"资源 {resource_id} 不存在")
            return self._status[resource_id]
            
    def get_resource_info(self, resource_id: str) -> Dict[str, Any]:
        """获取资源信息
        
        Args:
            resource_id: 资源标识符
            
        Returns:
            Dict: 资源信息字典
            
        Raises:
            ValueError: 资源不存在
        """
        with self._lock:
            if resource_id not in self._resources:
                raise ValueError(f"资源 {resource_id} 不存在")
                
            return {
                'resource': self._resources[resource_id],
                'status': self._status[resource_id],
                'start_time': self._start_time[resource_id],
                'age': (datetime.now() - self._start_time[resource_id]).total_seconds()
            }
            
    def __enter__(self):
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup_all() 