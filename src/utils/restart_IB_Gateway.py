import os
import sys
import subprocess
import psutil, time

# 设置Trader Workstation的路径
TWS_PATH = "/Users/<USER>/Applications/IB Gateway/IB Gateway 10.30.app"

def kill_tws():
    # 使用 pkill 杀死 IB Gateway 进程
    subprocess.run(['pkill', '-f', 'IB Gateway'])
    time.sleep(2)  # 等待几秒钟以确保进程完全终止

def start_tws():
    print("启动IB Gateway...")
    subprocess.Popen(["open", TWS_PATH])
    # time.sleep(15)  # 等待 TWS 启动

def login_tws(username, password):
    script = f'''
    tell application "System Events"
        delay 2
        set frontmost of process "JavaApplicationStub" to true
        delay 2
        set the clipboard to "{username}"
        keystroke "v" using control down
        delay 0.5
        keystroke tab
        delay 0.5
        set the clipboard to "{password}"
        keystroke "v" using control down
        delay 0.5
        keystroke return
    end tell
    '''
    subprocess.run(['osascript', '-e', script])
    print("已输入账号和密码（粘贴方式）")

def close_warning_window():
    # 模拟按下回车键关闭警告窗口
    # 使用 osascript 执行 AppleScript，添加 2 秒延迟
    subprocess.run([
        'osascript', 
        '-e', '''
        tell application "System Events"
            delay 2
            keystroke return
        end tell
        '''
    ])

def wait_for_ib_gateway(timeout=10):
    for _ in range(timeout):
        if any("JavaApplicationStub" in p.name() for p in psutil.process_iter()):
            return True
        time.sleep(1)
    return False

def restart_tws():
    kill_tws()
    start_tws()
    if wait_for_ib_gateway():
        login_tws(os.getenv('TW_USER_NAME'), os.getenv('TW_PASSWORD'))
    else:
        print("IB Gateway 启动超时")
    time.sleep(3)
    close_warning_window()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        restart_tws()
