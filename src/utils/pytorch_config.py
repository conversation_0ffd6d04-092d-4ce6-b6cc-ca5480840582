"""
PyTorch配置工具模块
用于在Mac M1/M2设备上配置PyTorch以避免MPS相关警告
"""

import warnings
import logging
import os

def configure_pytorch_for_mps():
    """
    配置PyTorch以在MPS设备上正常工作，避免pin_memory警告
    
    这个函数应该在导入任何使用PyTorch的模块之前调用
    """
    try:
        # 抑制PyTorch MPS相关警告
        warnings.filterwarnings("ignore", message=".*pin_memory.*MPS.*")
        warnings.filterwarnings("ignore", message=".*MPS.*pin_memory.*")
        
        # 设置环境变量以禁用某些MPS功能
        os.environ.setdefault('PYTORCH_ENABLE_MPS_FALLBACK', '1')
        
        # 导入torch并进行配置
        import torch
        
        if torch.backends.mps.is_available():
            # 在MPS设备上的特殊配置
            logging.info("检测到MPS设备，正在配置PyTorch兼容性设置...")
            
            # 设置默认设备
            device = torch.device("mps")
            
            # 记录设备信息
            logging.info(f"PyTorch版本: {torch.__version__}")
            logging.info(f"MPS设备已启用")
            
            return device
        elif torch.cuda.is_available():
            device = torch.device("cuda")
            logging.info(f"CUDA设备已启用")
            return device
        else:
            device = torch.device("cpu")
            logging.info(f"使用CPU设备")
            return device
            
    except ImportError:
        logging.warning("PyTorch未安装，跳过MPS配置")
        return None
    except Exception as e:
        logging.error(f"配置PyTorch时出错: {e}")
        return None

def get_optimal_dataloader_config():
    """
    获取针对当前设备优化的DataLoader配置
    
    Returns:
        dict: DataLoader配置参数
    """
    try:
        import torch
        
        config = {
            'num_workers': 0,  # MPS设备上建议使用0
            'persistent_workers': False,
            'prefetch_factor': None,  # 使用默认值
        }
        
        if torch.backends.mps.is_available():
            # MPS设备特殊配置
            config.update({
                'pin_memory': False,  # MPS不支持pin_memory
                'drop_last': False,
            })
            logging.debug("使用MPS优化的DataLoader配置")
        elif torch.cuda.is_available():
            # CUDA设备配置
            config.update({
                'pin_memory': True,  # CUDA支持pin_memory
                'num_workers': min(4, os.cpu_count() or 1),
                'persistent_workers': True,
            })
            logging.debug("使用CUDA优化的DataLoader配置")
        else:
            # CPU设备配置
            config.update({
                'pin_memory': False,
                'num_workers': min(2, os.cpu_count() or 1),
            })
            logging.debug("使用CPU优化的DataLoader配置")
        
        return config
        
    except ImportError:
        logging.warning("PyTorch未安装，返回默认DataLoader配置")
        return {
            'pin_memory': False,
            'num_workers': 0,
        }
    except Exception as e:
        logging.error(f"获取DataLoader配置时出错: {e}")
        return {
            'pin_memory': False,
            'num_workers': 0,
        }

def suppress_pytorch_warnings():
    """
    抑制常见的PyTorch警告
    """
    # 抑制MPS相关警告
    warnings.filterwarnings("ignore", message=".*pin_memory.*MPS.*")
    warnings.filterwarnings("ignore", message=".*MPS.*pin_memory.*")
    
    # 抑制其他常见的PyTorch警告
    warnings.filterwarnings("ignore", message=".*UserWarning.*")
    warnings.filterwarnings("ignore", category=UserWarning, module="torch.*")
    
    logging.debug("PyTorch警告已被抑制")

# 自动配置（当模块被导入时）
_device = None

def get_device():
    """获取配置好的PyTorch设备"""
    global _device
    if _device is None:
        _device = configure_pytorch_for_mps()
    return _device

# 在模块导入时自动抑制警告
suppress_pytorch_warnings()
