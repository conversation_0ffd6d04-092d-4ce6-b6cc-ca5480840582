"""
情感分析工具模块 - 修复版本
提供文本情感分析功能，处理 NLTK 依赖问题
"""

import logging

logger = logging.getLogger(__name__)

def get_sentiment(text):
    """
    根据文本返回情绪类别：正面、中性或负面。
    修复版本：处理 NLTK 依赖问题和 super() 错误

    :param text: 输入的文本
    :return: 情绪类别字符串（正面, 中性, 负面）
    """
    try:
        # 尝试导入和初始化 NLTK SentimentIntensityAnalyzer
        from nltk.sentiment import SentimentIntensityAnalyzer
        import nltk
        
        # 确保下载了 vader_lexicon 数据
        try:
            nltk.data.find('vader_lexicon')
        except LookupError:
            logger.warning("VADER lexicon 未找到，正在下载...")
            try:
                nltk.download('vader_lexicon', quiet=True)
            except Exception as e:
                logger.error(f"下载 VADER lexicon 失败: {e}")
                return _simple_sentiment_analysis(text)  # 降级处理
        
        # 初始化情感分析器
        sia = SentimentIntensityAnalyzer()
        
        # 获取情感分数
        scores = sia.polarity_scores(text)
        compound_score = scores['compound']
        
        # 根据 compound 值判断情绪
        if compound_score >= 0.05:
            return "正面"
        elif compound_score <= -0.05:
            return "负面"
        else:
            return "中性"
            
    except Exception as e:
        logger.warning(f"NLTK 情感分析失败，使用简单规则: {e}")
        return _simple_sentiment_analysis(text)

def _simple_sentiment_analysis(text):
    """
    简单的基于关键词的情感分析（备用方案）
    
    :param text: 输入文本
    :return: 情绪类别
    """
    text_lower = text.lower()
    
    # 正面关键词
    positive_words = [
        'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic',
        'positive', 'gain', 'rise', 'up', 'increase', 'profit', 'win',
        'success', 'growth', 'boost', 'strong', 'bullish', 'buy',
        '好', '优秀', '出色', '增长', '上涨', '利润', '成功', '强劲', '看涨'
    ]
    
    # 负面关键词
    negative_words = [
        'bad', 'terrible', 'awful', 'horrible', 'negative', 'loss', 'fall',
        'down', 'decrease', 'decline', 'drop', 'crash', 'fail', 'weak',
        'bearish', 'sell', 'risk', 'concern', 'warning',
        '坏', '糟糕', '下跌', '损失', '失败', '弱', '看跌', '风险', '警告'
    ]
    
    positive_count = sum(1 for word in positive_words if word in text_lower)
    negative_count = sum(1 for word in negative_words if word in text_lower)
    
    if positive_count > negative_count:
        return "正面"
    elif negative_count > positive_count:
        return "负面"
    else:
        return "中性"