"""
键值生成器模块

用于生成和缓存股票相关的键值对。
主要功能:
1. 缓存常用的键值组合
2. 提供统一的键值生成接口
3. 减少重复的字符串拼接操作
"""

from functools import lru_cache
from typing import Dict, Optional
from src.utils.constants import *

class KeyGenerator:
    """键值生成器类
    
    用于生成和缓存股票相关的键值。使用LRU缓存来优化性能。
    """
    
    def __init__(self):
        """初始化键值生成器"""
        self._cache: Dict[str, Dict[str, str]] = {}
        
    @lru_cache(maxsize=1000)
    def get_key(self, stock_code: str, key_type: str) -> str:
        """获取缓存的键值
        
        Args:
            stock_code: 股票代码
            key_type: 键值类型(如 NUMBER_FLG, VOLUME 等)
            
        Returns:
            str: 生成的键值
        """
        return f"{stock_code}_{key_type}"
        
    def get_keys_for_stock(self, stock_code: str) -> Dict[str, str]:
        """获取股票的所有相关键值
        
        为特定股票生成所有可能需要的键值组合。
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Dict[str, str]: 键值字典
        """
        if stock_code not in self._cache:
            self._cache[stock_code] = {
                NUMBER_FLG: self.get_key(stock_code, NUMBER_FLG),
                VOLUME: self.get_key(stock_code, VOLUME),
                CHECK_VOLUME: self.get_key(stock_code, CHECK_VOLUME),
                VOLUME_AVG: self.get_key(stock_code, VOLUME_AVG),
                NOW_PRICE: self.get_key(stock_code, NOW_PRICE),
                HISTORICAL_DATA_END: self.get_key(stock_code, HISTORICAL_DATA_END),
                PREV_DAY_CLOSE: self.get_key(stock_code, PREV_DAY_CLOSE),
                NEWS: self.get_key(stock_code, NEWS),
                IS_SPREAD_NORMAL: self.get_key(stock_code, IS_SPREAD_NORMAL),
                STOCK_SHARESOUTSTANDING: self.get_key(stock_code, STOCK_SHARESOUTSTANDING),
                BUY_FLG: self.get_key(stock_code, BUY_FLG),
                SET_FLG: self.get_key(stock_code, SET_FLG),
                MACD_FLG: self.get_key(stock_code, MACD_FLG),
                MACD_5_FLG: self.get_key(stock_code, MACD_5_FLG),
                BREAKOUT_FLG: self.get_key(stock_code, BREAKOUT_FLG),
                BREAKOUT_RED_FLG: self.get_key(stock_code, BREAKOUT_RED_FLG),
                SIGNAL_STRENGTH: self.get_key(stock_code, SIGNAL_STRENGTH),
                CONFIDENCE: self.get_key(stock_code, CONFIDENCE),
                BEHAVIOR_SCORE: self.get_key(stock_code, BEHAVIOR_SCORE),
                BEHAVIOR_SCORE_15MIN: self.get_key(stock_code, BEHAVIOR_SCORE_15MIN),
                BUYING_PRESSURE: self.get_key(stock_code, BUYING_PRESSURE),
                BID_SUPPORT_15MIN: self.get_key(stock_code, BID_SUPPORT_15MIN),
                ASK_PRESSURE_15MIN: self.get_key(stock_code, ASK_PRESSURE_15MIN),
                BID_AGGRESSION_15MIN: self.get_key(stock_code, BID_AGGRESSION_15MIN),
                EXTREME_BID_AGGRESSION_15MIN: self.get_key(stock_code, EXTREME_BID_AGGRESSION_15MIN),
                ACTIVE_ABSORPTION_15MIN: self.get_key(stock_code, ACTIVE_ABSORPTION_15MIN),
                HIDDEN_BUYING_15MIN: self.get_key(stock_code, HIDDEN_BUYING_15MIN),
                FAKE_PRESSURE_15MIN: self.get_key(stock_code, FAKE_PRESSURE_15MIN),
                UPWARD_PRESSURE_DETECTED: self.get_key(stock_code, UPWARD_PRESSURE_DETECTED),
                UPDATED_DATA_MARK: self.get_key(stock_code, UPDATED_DATA_MARK)
            }
        return self._cache[stock_code]
        
    def clear_cache(self, stock_code: Optional[str] = None):
        """清除缓存
        
        Args:
            stock_code: 可选，特定股票代码。如果不指定，清除所有缓存。
        """
        if stock_code:
            self._cache.pop(stock_code, None)
            self.get_key.cache_clear()  # 清除LRU缓存
        else:
            self._cache.clear()
            self.get_key.cache_clear()

# 创建全局实例
key_generator = KeyGenerator() 