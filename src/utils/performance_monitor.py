"""
性能监控模块

该模块提供系统资源使用情况的监控功能，包括：
1. CPU使用率
2. 内存使用情况
3. 磁盘I/O
4. 网络I/O
5. 线程统计

主要功能：
- 实时监控系统资源
- 性能数据收集和分析
- 资源使用预警
- 性能报告生成

使用方式：
```python
# 创建监控器
monitor = PerformanceMonitor()

# 开始监控
monitor.start()

# 获取性能数据
stats = monitor.get_stats()

# 停止监控
monitor.stop()
```
"""

import psutil
import threading
import time
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import json
from pathlib import Path
import re
import inspect

class PerformanceMonitor:
    """性能监控器类
    
    该类负责收集和分析系统性能数据。
    
    特性：
    1. 低开销监控
    2. 可配置的采样间隔
    3. 可配置的预警阈值
    4. 自动日志轮转
    
    属性：
        _monitoring_thread (Thread): 监控线程
        _stop_flag (Event): 停止标志
        _stats (Dict): 性能统计数据
        _log_file (Path): 日志文件路径
    """
    
    def __init__(self, 
                 sampling_interval: float = 5.0,
                 log_dir: str = "logs/performance",
                 cpu_threshold: float = 80.0,
                 memory_threshold: float = 80.0,
                 disk_threshold: float = 80.0):
        """初始化性能监控器
        
        Args:
            sampling_interval: 采样间隔（秒）
            log_dir: 日志目录
            cpu_threshold: CPU使用率预警阈值（%）
            memory_threshold: 内存使用率预警阈值（%）
            disk_threshold: 磁盘使用率预警阈值（%）
        """
        self._monitoring_thread = None
        self._stop_flag = threading.Event()
        self._stats = {}
        self._lock = threading.Lock()
        self._sampling_interval = sampling_interval
        
        # 设置预警阈值
        self._thresholds = {
            'cpu': cpu_threshold,
            'memory': memory_threshold,
            'disk': disk_threshold
        }
        
        # 设置日志
        self._log_dir = Path(log_dir)
        self._log_dir.mkdir(parents=True, exist_ok=True)
        self._log_file = self._log_dir / f"performance_{datetime.now().strftime('%Y%m%d')}.log"
        
        # 设置日志格式
        self._logger = logging.getLogger("performance_monitor")
        self._logger.setLevel(logging.INFO)
        
        # 添加文件处理器
        file_handler = logging.FileHandler(self._log_file)
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        )
        self._logger.addHandler(file_handler)
        
    def start(self) -> None:
        """启动性能监控
        
        创建并启动监控线程，开始收集性能数据。
        """
        if self._monitoring_thread is not None and self._monitoring_thread.is_alive():
            self._logger.warning("监控器已经在运行")
            return
            
        self._stop_flag.clear()
        self._monitoring_thread = threading.Thread(
            target=self._monitor_loop,
            name="PerformanceMonitorThread"
        )
        self._monitoring_thread.daemon = True
        self._monitoring_thread.start()
        self._logger.info("性能监控已启动")
        
    def stop(self) -> None:
        """停止性能监控
        
        停止监控线程，保存最终数据。
        """
        if self._monitoring_thread is None:
            return
            
        self._stop_flag.set()
        self._monitoring_thread.join()
        self._monitoring_thread = None
        self._logger.info("性能监控已停止")
        
    def _monitor_loop(self) -> None:
        """监控循环
        
        持续收集系统性能数据，直到收到停止信号。
        """
        while not self._stop_flag.is_set():
            try:
                stats = self._collect_stats()
                self._check_thresholds(stats)
                
                with self._lock:
                    self._stats = stats
                    
                # 每小时保存一次详细数据
                if datetime.now().minute == 0:
                    self._save_detailed_stats(stats)
                    
                time.sleep(self._sampling_interval)
                
            except Exception as e:
                self._logger.error(f"监控过程中发生错误: {str(e)}", exc_info=True)
                
    def _collect_stats(self) -> Dict[str, Any]:
        """收集性能统计数据
        
        Returns:
            Dict[str, Any]: 性能统计数据字典
        """
        try:
            # 获取CPU频率（处理MacOS上的特殊情况）
            try:
                cpu_freq = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            except (FileNotFoundError, AttributeError):
                cpu_freq = None
                
            # 获取当前进程
            process = psutil.Process()
            
            # 获取基本CPU信息
            stats = {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'system': {
                        'percent': psutil.cpu_percent(interval=1),
                        'count': psutil.cpu_count(),
                        'frequency': cpu_freq,
                        'per_cpu_percent': psutil.cpu_percent(interval=1, percpu=True)
                    },
                    'process': {
                        'percent': process.cpu_percent(interval=1),
                        'threads': process.num_threads(),
                        'children': len(process.children())
                    }
                }
            }
            
            # 获取内存信息
            try:
                virtual_memory = psutil.virtual_memory()
                process_memory = process.memory_info()
                
                stats['memory'] = {
                    'system': {
                        'total': virtual_memory.total,
                        'available': virtual_memory.available,
                        'used': virtual_memory.used,
                        'free': virtual_memory.free,
                        'percent': virtual_memory.percent
                    },
                    'process': {
                        'rss': process_memory.rss,  # 实际内存使用
                        'vms': process_memory.vms,  # 虚拟内存使用
                        'percent': process.memory_percent(),
                        'details': self._get_memory_details()
                    }
                }
            except Exception as e:
                self._logger.error(f"获取内存信息时发生错误: {str(e)}")
                stats['memory'] = None
                
            # 获取磁盘信息
            try:
                disk = psutil.disk_usage('/')
                io_counters = psutil.disk_io_counters()
                process_io = process.io_counters()
                
                stats['disk'] = {
                    'system': {
                        'total': disk.total,
                        'used': disk.used,
                        'free': disk.free,
                        'percent': disk.percent,
                        'io_counters': {
                            'read_bytes': io_counters.read_bytes,
                            'write_bytes': io_counters.write_bytes,
                            'read_count': io_counters.read_count,
                            'write_count': io_counters.write_count
                        }
                    },
                    'process': {
                        'read_bytes': process_io.read_bytes,
                        'write_bytes': process_io.write_bytes,
                        'read_count': process_io.read_count,
                        'write_count': process_io.write_count
                    } if hasattr(process_io, 'read_bytes') else None
                }
            except Exception as e:
                self._logger.error(f"获取磁盘信息时发生错误: {str(e)}")
                stats['disk'] = None
                
            # 获取网络信息
            try:
                net_io = psutil.net_io_counters()
                stats['network'] = {
                    'system': {
                        'bytes_sent': net_io.bytes_sent,
                        'bytes_recv': net_io.bytes_recv,
                        'packets_sent': net_io.packets_sent,
                        'packets_recv': net_io.packets_recv,
                        'errin': net_io.errin,
                        'errout': net_io.errout,
                        'dropin': net_io.dropin,
                        'dropout': net_io.dropout
                    },
                    'process': {
                        'connections': len(process.connections())
                    }
                }
            except Exception as e:
                self._logger.error(f"获取网络信息时发生错误: {str(e)}")
                stats['network'] = None
                
            return stats
            
        except Exception as e:
            self._logger.error(f"收集性能统计数据时发生错误: {str(e)}", exc_info=True)
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
        
    def _get_memory_details(self) -> Dict[str, Any]:
        """获取详细的内存使用信息
        
        Returns:
            Dict[str, Any]: 详细的内存使用信息
        """
        try:
            process = psutil.Process()
            memory_details = {
                'process_info': {
                    'pid': process.pid,
                    'name': process.name(),
                    'memory_percent': process.memory_percent(),
                    'memory_info': process.memory_info()._asdict()
                },
                'top_memory_processes': [],
                'python_objects': self._get_python_objects_memory()
            }
            
            # 获取内存占用最多的进程
            for proc in sorted(psutil.process_iter(['pid', 'name', 'memory_percent']), 
                             key=lambda p: p.info['memory_percent'] or 0, 
                             reverse=True)[:5]:
                try:
                    memory_details['top_memory_processes'].append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'memory_percent': proc.info['memory_percent']
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            return memory_details
            
        except Exception as e:
            self._logger.error(f"获取内存详情时发生错误: {str(e)}")
            return {}
            
    def _get_python_objects_memory(self) -> Dict[str, Any]:
        """获取Python对象的内存使用情况
        
        Returns:
            Dict[str, Any]: Python对象的内存使用情况
        """
        import sys
        import gc
        import pandas as pd
        import numpy as np
        from types import ModuleType, FunctionType
        from typing import Dict, List, Set, Tuple
        
        memory_usage = {
            'by_type': {},
            'large_objects': [],
            'dataframes': [],
            'numpy_arrays': [],
            'custom_objects': [],
            'circular_refs': []  # 新增：循环引用检测
        }
        
        # 定义感兴趣的变量名模式
        patterns = [
            r'df_.*',           # DataFrame变量
            r'data_.*',         # 数据相关变量
            r'.*_data',         # 数据相关变量
            r'array_.*',        # 数组变量
            r'.*_array',        # 数组变量
            r'cache_.*',        # 缓存变量
            r'.*_cache',        # 缓存变量
            r'buffer_.*',       # 缓冲区变量
            r'.*_buffer',       # 缓冲区变量
            r'temp_.*',         # 临时变量
            r'.*_temp',         # 临时变量
            r'result_.*',       # 结果变量
            r'.*_result'        # 结果变量
        ]
        
        # 编译正则表达式
        patterns = [re.compile(p) for p in patterns]
        
        def is_interesting_variable(name: str) -> bool:
            """检查变量名是否是我们感兴趣的"""
            return any(p.match(name) for p in patterns)
            
        def get_object_size(obj: Any) -> int:
            """获取对象的总内存使用量"""
            try:
                if isinstance(obj, pd.DataFrame):
                    return obj.memory_usage(deep=True).sum()
                elif isinstance(obj, np.ndarray):
                    return obj.nbytes
                else:
                    # 递归计算复杂对象的大小
                    seen = set()
                    def sizeof(obj):
                        obj_id = id(obj)
                        if obj_id in seen:
                            return 0
                        seen.add(obj_id)
                        size = sys.getsizeof(obj)
                        
                        if isinstance(obj, dict):
                            size += sum(sizeof(k) + sizeof(v) for k, v in obj.items())
                        elif isinstance(obj, (list, tuple, set, frozenset)):
                            size += sum(sizeof(i) for i in obj)
                        elif hasattr(obj, '__dict__'):
                            size += sizeof(obj.__dict__)
                            
                        return size
                    return sizeof(obj)
            except Exception:
                return 0
                
        def find_circular_refs() -> List[Tuple[str, List[str]]]:
            """查找循环引用"""
            circular_refs = []
            gc.collect()  # 强制垃圾回收
            
            for obj in gc.get_objects():
                try:
                    if isinstance(obj, (ModuleType, FunctionType, type)):
                        continue
                        
                    refs = [r for r in gc.get_referrers(obj)
                           if not isinstance(r, (ModuleType, FunctionType, type, dict))]
                           
                    if len(refs) > 1:
                        ref_paths = []
                        for ref in refs:
                            if hasattr(ref, '__name__'):
                                ref_paths.append(ref.__name__)
                            elif hasattr(ref, '__class__'):
                                ref_paths.append(ref.__class__.__name__)
                                
                        if ref_paths:
                            obj_name = (obj.__name__ if hasattr(obj, '__name__')
                                      else obj.__class__.__name__)
                            circular_refs.append((obj_name, ref_paths))
                except Exception:
                    continue
                    
            return circular_refs
            
        # 收集所有对象
        gc.collect()
        objects = gc.get_objects()
        
        # 按类型统计
        type_counts = {}
        for obj in objects:
            obj_type = type(obj).__name__
            if obj_type not in type_counts:
                type_counts[obj_type] = {
                    'count': 0,
                    'size': 0
                }
            type_counts[obj_type]['count'] += 1
            try:
                type_counts[obj_type]['size'] += sys.getsizeof(obj)
            except Exception:
                continue
                
        # 获取所有局部变量和全局变量
        stack = inspect.stack()
        variables = {}
        
        # 收集所有帧的变量
        for frame_info in stack:
            frame = frame_info.frame
            # 合并局部变量和全局变量
            variables.update(frame.f_locals)
            variables.update(frame.f_globals)
            
        # 分析对象
        large_objects = []
        dataframes = []
        numpy_arrays = []
        custom_objects = []
        
        # 使用生成器减少内存使用
        def analyze_variables():
            for name, obj in variables.items():
                if not is_interesting_variable(name):
                    continue
                    
                try:
                    # 跳过模块和函数
                    if isinstance(obj, (ModuleType, FunctionType)):
                        continue
                        
                    # 获取对象大小
                    size = get_object_size(obj)
                    
                    if isinstance(obj, pd.DataFrame):
                        yield ('dataframe', {
                            'name': name,
                            'type': 'DataFrame',
                            'size': size,
                            'shape': obj.shape,
                            'columns': list(obj.columns),
                            'memory_usage': obj.memory_usage(deep=True).to_dict()
                        })
                    elif isinstance(obj, np.ndarray):
                        yield ('array', {
                            'name': name,
                            'type': 'ndarray',
                            'size': size,
                            'shape': obj.shape,
                            'dtype': str(obj.dtype)
                        })
                    elif size > 1024 * 1024:  # 大于1MB的对象
                        obj_info = {
                            'name': name,
                            'type': type(obj).__name__,
                            'size': size
                        }
                        
                        # 添加自定义对象的属性信息
                        if hasattr(obj, '__dict__'):
                            obj_info['attributes'] = {
                                attr: type(val).__name__
                                for attr, val in obj.__dict__.items()
                                if not attr.startswith('_')
                            }
                            
                        yield ('large', obj_info)
                        
                        # 记录自定义对象
                        if not isinstance(obj, (pd.DataFrame, np.ndarray, list, dict, set, str, int, float, bool)):
                            yield ('custom', obj_info)
                            
                except Exception:
                    continue
                    
        # 使用生成器分析变量
        for obj_type, obj_info in analyze_variables():
            if obj_type == 'dataframe':
                dataframes.append(obj_info)
            elif obj_type == 'array':
                numpy_arrays.append(obj_info)
            elif obj_type == 'large':
                large_objects.append(obj_info)
            elif obj_type == 'custom':
                custom_objects.append(obj_info)
                
        # 查找循环引用
        memory_usage['circular_refs'] = find_circular_refs()
        
        # 更新内存使用统计
        memory_usage['by_type'] = type_counts
        memory_usage['large_objects'] = sorted(large_objects, key=lambda x: x['size'], reverse=True)[:10]
        memory_usage['dataframes'] = sorted(dataframes, key=lambda x: x['size'], reverse=True)
        memory_usage['numpy_arrays'] = sorted(numpy_arrays, key=lambda x: x['size'], reverse=True)
        memory_usage['custom_objects'] = sorted(custom_objects, key=lambda x: x['size'], reverse=True)
        
        return memory_usage
        
    def _check_thresholds(self, stats: Dict[str, Any]) -> None:
        """检查性能指标是否超过预警阈值"""
        warnings = []
        context = self._get_call_context()
        
        # 只检查进程级别的资源使用
        if stats.get('cpu'):
            process_cpu = stats['cpu'].get('process', {}).get('percent', 0)
            if process_cpu > self._thresholds['cpu'] * 0.5:  # 进程CPU阈值设为系统阈值的一半
                warnings.append({
                    'type': 'ProcessCPU',
                    'message': f"进程CPU使用率 ({process_cpu}%) 过高",
                    'value': process_cpu,
                    'threshold': self._thresholds['cpu'] * 0.5,
                    'context': context
                })
                
        if stats.get('memory'):
            process_memory = stats['memory'].get('process', {}).get('percent', 0)
            if process_memory > self._thresholds['memory'] * 0.5:  # 进程内存阈值设为系统阈值的一半
                warnings.append({
                    'type': 'ProcessMemory',
                    'message': f"进程内存使用率 ({process_memory}%) 过高",
                    'value': process_memory,
                    'threshold': self._thresholds['memory'] * 0.5,
                    'context': context
                })
                
        # 记录预警信息
        for warning in warnings:
            self._log_warning(warning)
            
    def _get_call_context(self) -> Dict[str, Any]:
        """获取调用上下文信息
        
        Returns:
            Dict[str, Any]: 上下文信息字典
        """
        import os
        
        # 获取调用栈
        stack = inspect.stack()
        
        # 查找第一个非监控器类的调用者
        caller_frame = None
        for frame in stack[1:]:  # 跳过当前函数
            if 'performance_monitor.py' not in frame.filename:
                caller_frame = frame
                break
                
        if not caller_frame:
            return {'source': 'unknown'}
            
        return {
            'file': os.path.basename(caller_frame.filename),
            'function': caller_frame.function,
            'line': caller_frame.lineno,
            'code': caller_frame.code_context[0].strip() if caller_frame.code_context else None
        }
        
    def _log_warning(self, warning: Dict[str, Any]) -> None:
        """记录预警信息"""
        context = warning['context']
        context_str = (
            f"[来源: {context['file']}:{context['line']} "
            f"在函数 {context['function']} 中"
        )
        if context.get('code'):
            context_str += f" 执行 '{context['code']}'"
        context_str += "]"
        
        # 添加详细的资源使用信息
        details = []
        stats = self.get_stats()
        
        if warning['type'] == 'ProcessMemory' and stats.get('memory'):
            mem_stats = stats['memory']
            
            # 进程内存信息
            proc_mem = mem_stats.get('process', {})
            if proc_mem:
                details.append("进程内存使用情况:")
                details.append(f"  - 实际使用: {self._format_bytes(proc_mem['rss'])}")
                details.append(f"  - 虚拟内存: {self._format_bytes(proc_mem['vms'])}")
                details.append(f"  - 占系统比例: {proc_mem['percent']:.1f}%")
                
            # 内存占用详情
            mem_details = proc_mem.get('details', {})
            if mem_details:
                # DataFrame信息
                dataframes = mem_details.get('python_objects', {}).get('dataframes', [])
                if dataframes:
                    details.append("\nDataFrame对象:")
                    for df in dataframes[:5]:
                        details.append(f"  - {df['name']} ({self._format_bytes(df['size'])})")
                        details.append(f"    形状: {df['shape']}")
                        details.append(f"    列: {', '.join(df['columns'])}")
                        details.append("    列内存使用:")
                        for col, usage in df['memory_usage'].items():
                            details.append(f"      {col}: {self._format_bytes(usage)}")
                            
                # NumPy数组信息
                arrays = mem_details.get('python_objects', {}).get('numpy_arrays', [])
                if arrays:
                    details.append("\nNumPy数组:")
                    for arr in arrays[:5]:
                        details.append(f"  - {arr['name']} ({self._format_bytes(arr['size'])})")
                        details.append(f"    形状: {arr['shape']}")
                        details.append(f"    类型: {arr['dtype']}")
                        
                # 自定义对象信息
                custom_objects = mem_details.get('python_objects', {}).get('custom_objects', [])
                if custom_objects:
                    details.append("\n自定义对象:")
                    for obj in custom_objects[:5]:
                        details.append(f"  - {obj['name']} ({self._format_bytes(obj['size'])})")
                        if 'attributes' in obj:
                            details.append("    属性:")
                            for attr, type_name in obj['attributes'].items():
                                details.append(f"      {attr}: {type_name}")
                                
                # 循环引用信息
                circular_refs = mem_details.get('python_objects', {}).get('circular_refs', [])
                if circular_refs:
                    details.append("\n检测到循环引用:")
                    for obj_name, ref_paths in circular_refs[:5]:
                        details.append(f"  - {obj_name} 被引用于:")
                        for path in ref_paths:
                            details.append(f"    - {path}")
                            
        elif warning['type'] == 'ProcessCPU' and stats.get('cpu'):
            cpu_stats = stats['cpu']
            
            # 进程CPU信息
            proc_cpu = cpu_stats.get('process', {})
            if proc_cpu:
                details.append("进程CPU使用情况:")
                details.append(f"  - CPU使用率: {proc_cpu['percent']}%")
                details.append(f"  - 线程数: {proc_cpu['threads']}")
                details.append(f"  - 子进程数: {proc_cpu['children']}")
                
        detail_str = "\n".join(details) if details else ""
        
        message = (
            f"{warning['type']}预警: {warning['message']} "
            f"{context_str}\n{detail_str}"
        )
        
        self._logger.warning(message)
        
        # 保存详细信息到JSON文件
        warning_file = self._log_dir / f"warnings_{datetime.now().strftime('%Y%m%d')}.json"
        try:
            # 读取现有警告
            if warning_file.exists():
                with open(warning_file, 'r') as f:
                    warnings = json.load(f)
            else:
                warnings = []
                
            # 添加新警告
            warnings.append({
                'timestamp': datetime.now().isoformat(),
                'type': warning['type'],
                'message': warning['message'],
                'value': warning['value'],
                'threshold': warning['threshold'],
                'context': warning['context']
            })
            
            # 保存警告
            with open(warning_file, 'w') as f:
                json.dump(warnings, f, indent=2)
                
        except Exception as e:
            self._logger.error(f"保存警告信息时发生错误: {str(e)}")
        
    def _save_detailed_stats(self, stats: Dict[str, Any]) -> None:
        """保存详细的性能统计数据
        
        Args:
            stats: 性能统计数据
        """
        try:
            detailed_log_file = self._log_dir / f"detailed_stats_{datetime.now().strftime('%Y%m%d_%H')}.json"
            with open(detailed_log_file, 'w') as f:
                json.dump(stats, f, indent=2)
        except Exception as e:
            self._logger.error(f"保存详细统计数据时发生错误: {str(e)}", exc_info=True)
            
    def get_stats(self) -> Dict[str, Any]:
        """获取当前性能统计数据
        
        Returns:
            Dict[str, Any]: 性能统计数据字典
        """
        with self._lock:
            return self._stats.copy()
            
    def get_summary(self) -> str:
        """获取性能摘要
        
        Returns:
            str: 性能摘要字符串
        """
        stats = self.get_stats()
        if not stats:
            return "无可用的性能数据"
            
        if 'error' in stats:
            return f"性能数据收集错误: {stats['error']}"
            
        summary = ["性能摘要:"]
        
        # CPU信息
        if stats.get('cpu'):
            summary.append(f"CPU:")
            summary.append(f"  - 总体使用率: {stats['cpu']['system']['percent']}%")
            if stats['cpu']['system'].get('per_cpu_percent'):
                for i, percent in enumerate(stats['cpu']['system']['per_cpu_percent']):
                    summary.append(f"  - CPU{i} 使用率: {percent}%")
                    
        # 内存信息
        if stats.get('memory'):
            summary.append(f"内存:")
            summary.append(f"  - 系统使用率: {stats['memory']['system']['percent']}%")
            summary.append(f"  - 可用: {self._format_bytes(stats['memory']['system']['available'])}")
            summary.append(f"  - 总量: {self._format_bytes(stats['memory']['system']['total'])}")
            
        # 磁盘信息
        if stats.get('disk'):
            summary.append(f"磁盘:")
            summary.append(f"  - 系统使用率: {stats['disk']['system']['percent']}%")
            summary.append(f"  - 可用: {self._format_bytes(stats['disk']['system']['free'])}")
            summary.append(f"  - 总量: {self._format_bytes(stats['disk']['system']['total'])}")
            
        # 网络信息
        if stats.get('network'):
            summary.append(f"网络:")
            summary.append(f"  - 系统发送: {self._format_bytes(stats['network']['system']['bytes_sent'])}")
            summary.append(f"  - 系统接收: {self._format_bytes(stats['network']['system']['bytes_recv'])}")
            
        # 进程信息
        if stats.get('process'):
            summary.append(f"进程:")
            summary.append(f"  - 系统CPU使用率: {stats['cpu']['process']['percent']}%")
            summary.append(f"  - 线程数: {stats['cpu']['process']['threads']}")
            summary.append(f"  - 子进程数: {stats['cpu']['process']['children']}")
            
        return "\n".join(summary)
        
    def _format_bytes(self, bytes_: int) -> str:
        """格式化字节数
        
        Args:
            bytes_: 字节数
            
        Returns:
            str: 格式化后的字符串
        """
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_ < 1024:
                return f"{bytes_:.1f}{unit}"
            bytes_ /= 1024
        return f"{bytes_:.1f}PB" 