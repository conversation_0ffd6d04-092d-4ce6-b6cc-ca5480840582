from typing import Any
import mysql.connector
from mysql.connector import pooling
import logging
import pandas as pd

# 定义数据库配置参数
dbconfig = {
    "host": "localhost",         # 数据库主机地址
    "user": "root",              # 数据库用户名
    "password": "admin1234",     # 数据库密码
    "database": "stock_trand"    # 数据库名称
}

# 创建一个全局的连接池，pool_size 可根据需求调整
pool = pooling.MySQLConnectionPool(
    pool_name="mypool",
    pool_size=20,
    pool_reset_session=True,
    **dbconfig
)

def get_db_connection():
    """
    从连接池中获取一个连接
    """
    return pool.get_connection()

def store_news_to_db(stock_code: str, source: str, formatted_date: str, formatted_date_time: str, news_content: str) -> None:
    """
    将新闻信息存储到 MySQL 数据库中。

    参数：
        stock_code: 股票代号
        published: 新闻发布时间（原始字符串）
        formatted_date: 格式化后的日期（例如 "01/06 07:15"）
        title: 翻译后的新闻标题
        sentiment: 新闻情绪
        news_content: 拼接后的完整新闻内容
    """
    try:
        conn = get_db_connection()  # 从连接池获取连接
        cursor = conn.cursor()
        insert_sql = """
        INSERT INTO stock_news (stock_code, source, date_, date_time, news_content)
        VALUES (%s, %s, %s, %s, %s)
        """
        cursor.execute(insert_sql, (stock_code, source, formatted_date, formatted_date_time, news_content))
        conn.commit()
        logging.info(f"News stored to DB for {stock_code}: {news_content}")
    except Exception as e:
        if "Duplicate entry" in str(e):
            logging.info(f"News already exists in DB for {stock_code}: {news_content}")
            return
        logging.error(f"Error storing news to DB: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()  # 关闭连接将归还给连接池

def get_todays_news(stock_code: str, date_: str) -> dict[str, list[dict[str, Any]]]:
    try:
        conn = get_db_connection()  # 从连接池获取连接
        # 使用字典模式，查询结果以字典形式返回
        cursor = conn.cursor(dictionary=True)
        
        # 构造 SQL 查询语句（假设 date_time 字段存储新闻发布时间）
        query = "SELECT stock_code, date_time, news_content FROM stock_news WHERE stock_code = %s AND date_ = %s ORDER BY date_time ASC"
        cursor.execute(query, (stock_code, date_))
        
        # 获取所有查询结果
        results = cursor.fetchall()
        
        # 将查询结果分组为字典，key 为 stock_code，value 为新闻列表
        news_dict = {}
        for row in results:
            code = row.get("stock_code")
            if code:
                news_dict.setdefault(code, []).append(row)
        return news_dict
    except mysql.connector.Error as err:
        logging.error("数据库错误：%s", err)
        return {}
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()  # 关闭连接将归还给连接池

def insert_ticker_data_to_db(df):
    """
    批量插入ticker_data表
    参数：
        df: DataFrame，包含与ticker_data表结构一致的列
    """
    if df is None or df.empty:
        return
    # 统一格式化date_time为美东无时区
    df = df.copy()
    
    # 如果uuid是索引名，将其重置为普通列
    if df.index.name == 'uuid':
        df = df.reset_index()
    
    if 'date_time' in df.columns:
        df['date_time'] = pd.to_datetime(df['date_time'], unit='s', errors='coerce')  # 先转为UTC
        df['date_time'] = df['date_time'].dt.tz_localize('UTC').dt.tz_convert('America/New_York')  # 转为纽约时区
        df['date_time'] = df['date_time'].dt.tz_localize(None)  # 去掉时区
        df['date_time'] = df['date_time'].dt.strftime('%Y-%m-%d %H:%M:%S')  # 格式化
        # 从 date_time 截取日期部分，改为 date_
        df['date_'] = pd.to_datetime(df['date_time']).dt.date.astype(str)
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        insert_sql = """
        INSERT IGNORE INTO ticker_data (uuid, stock_code, date_, date_time, price, size, exchange, special_conditions)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        data = [tuple(row) for row in df[['uuid','stock_code','date_','date_time','price','size','exchange','special_conditions']].values]
        cursor.executemany(insert_sql, data)
        conn.commit()
        logging.info(f"批量插入ticker_data成功, 行数: {len(data)}")
    except Exception as e:
        logging.error(f"批量插入ticker_data失败: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def insert_orderbook_data_to_db(df):
    """
    批量插入orderbook_data表
    参数：
        df: DataFrame，包含与orderbook_data表结构一致的列
    """
    if df is None or df.empty:
        return
    # 统一格式化date_time为美东无时区
    df = df.copy()
    
    # 如果uuid是索引名，将其重置为普通列
    if df.index.name == 'uuid':
        df = df.reset_index()
    
    if 'date_time' in df.columns:
        df['date_time'] = pd.to_datetime(df['date_time'], unit='s', errors='coerce')  # 先转为UTC
        df['date_time'] = df['date_time'].dt.tz_localize('UTC').dt.tz_convert('America/New_York')  # 转为纽约时区
        df['date_time'] = df['date_time'].dt.tz_localize(None)  # 去掉时区
        df['date_time'] = df['date_time'].dt.strftime('%Y-%m-%d %H:%M:%S')  # 格式化
        # 从 date_time 截取日期部分，改为 date_
        df['date_'] = pd.to_datetime(df['date_time']).dt.date.astype(str)
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        insert_sql = """
        INSERT IGNORE INTO orderbook_data (uuid, stock_code, date_, date_time, bid_price, ask_price, bid_size, ask_size, bid_past_low, ask_past_high)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        data = [tuple(row) for row in df[['uuid','stock_code','date_','date_time','bid_price','ask_price','bid_size','ask_size','bid_past_low','ask_past_high']].values]
        cursor.executemany(insert_sql, data)
        conn.commit()
        logging.info(f"批量插入orderbook_data成功, 行数: {len(data)}")
    except Exception as e:
        logging.error(f"批量插入orderbook_data失败: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# get_todays_news('MASS', '2025-03-05')
# store_news_to_db('MASS', '2025-03-04 09:12', 'news_content')