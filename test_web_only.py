#!/usr/bin/env python3
"""
仅测试Web服务器功能
不启动IB API，只测试Web界面和数据接口
"""
import time
import threading
import math
import random

# 导入需要的模块来模拟交易次数数据
from src.utils.constants import HISTORICAL_DATA_END, NUMBER_FLG, PREV_DAY_CLOSE, WEB_STOCK_LIST
from src.utils.key_generator import key_generator
# 全局变量 - 使用新的线程安全数据管理器适配器
from src.data.data_manager_adapter import (
    manager_res_dict_test_adapter as manager_res_dict_test,
    manager_res_dict_adapter as manager_res_dict
)

def create_mock_data():
    """创建模拟数据 - 包含价格突破检测测试场景"""
    web_stock_data = {}

    # 模拟多只股票的数据
    stocks = ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA", "TEST"]
    # manager_res_dict[1] = stocks

    for i, stock in enumerate(stocks):
        web_stock_data[stock] = {}  # req_data是字典结构

        # 基础价格
        base_price = 100 + i * 20

        # 生成测试场景数据
        scenarios = create_surge_test_scenarios(base_price, stock)

        for j, scenario in enumerate(scenarios):
            # 确保分钟数不超过59，使用5分钟间隔
            minutes = (30 + j * 5) % 60
            hour = 9 + (30 + j * 5) // 60
            date_time = f"20241223 {hour:02d}:{minutes:02d}:00"

            bar_data = {
                "date_time": date_time,
                "Open": scenario["open"],
                "High": scenario["high"],
                "Low": scenario["low"],
                "Close": scenario["close"],
                "Volume": 100000 + j * 10000
            }

            # req_data结构：{date_time: bar_data}
            web_stock_data[stock][date_time] = bar_data

        keys = key_generator.get_keys_for_stock(stock)
        manager_res_dict_test[keys[HISTORICAL_DATA_END]] = 1
        # 添加前天收盘价（PREV_DAY_CLOSE），用于涨幅比测试
        # 取第一个bar的close作为前天收盘价（模拟）
        first_bar = next(iter(web_stock_data[stock].values()))
        manager_res_dict_test[keys[PREV_DAY_CLOSE]] = first_bar["Close"]
    return web_stock_data

def create_surge_test_scenarios(base_price, stock):
    """创建价格突破检测测试场景"""
    scenarios = []

    if stock == "AAPL":
        # AAPL: 完整的突破-阴线-确认模式
        scenarios = [
            # 1. 正常K线
            {"open": base_price, "high": base_price + 1, "low": base_price - 0.5, "close": base_price + 0.5},
            {"open": base_price + 0.5, "high": base_price + 1.5, "low": base_price, "close": base_price + 1},

            # 2. 突破K线 (涨幅12%)
            {"open": base_price + 1, "high": base_price + 13.5, "low": base_price + 1, "close": base_price + 13.2},

            # 3. 第一根阴线
            {"open": base_price + 13.2, "high": base_price + 13.5, "low": base_price + 11.8, "close": base_price + 12},

            # 4. 第二根阴线
            {"open": base_price + 12, "high": base_price + 12.2, "low": base_price + 10.5, "close": base_price + 11},

            # 5. 确认阳线 (应该触发形态确认)
            {"open": base_price + 11, "high": base_price + 12.5, "low": base_price + 10.8, "close": base_price + 12.2},

            # 6. 后续正常K线
            {"open": base_price + 12.2, "high": base_price + 12.8, "low": base_price + 11.8, "close": base_price + 12.5},
        ]

    elif stock == "GOOGL":
        # GOOGL: 突破后跌破最低价失效的场景
        scenarios = [
            # 1-2. 正常K线
            {"open": base_price, "high": base_price + 2, "low": base_price - 1, "close": base_price + 1},
            {"open": base_price + 1, "high": base_price + 3, "low": base_price, "close": base_price + 2},

            # 3. 突破K线 (涨幅15%)
            {"open": base_price + 2, "high": base_price + 17.5, "low": base_price + 2, "close": base_price + 17.3},

            # 4. 第一根阴线
            {"open": base_price + 17.3, "high": base_price + 17.5, "low": base_price + 15.8, "close": base_price + 16},

            # 5. 跌破突破K线最低价的阴线 (应该触发失效)
            {"open": base_price + 16, "high": base_price + 16.2, "low": base_price + 1.5, "close": base_price + 1.8},

            # 6-7. 后续正常K线
            {"open": base_price + 1.8, "high": base_price + 2.5, "low": base_price + 1.5, "close": base_price + 2.2},
            {"open": base_price + 2.2, "high": base_price + 3, "low": base_price + 1.8, "close": base_price + 2.8},
        ]

    elif stock == "MSFT":
        # MSFT: 连续两根突破K线的场景
        scenarios = [
            # 1. 正常K线
            {"open": base_price, "high": base_price + 1, "low": base_price - 0.5, "close": base_price + 0.8},

            # 2. 第一根突破K线 (涨幅11%)
            {"open": base_price + 0.8, "high": base_price + 12, "low": base_price + 0.8, "close": base_price + 11.7},

            # 3. 第二根突破K线 (涨幅10.5%，应该重置追踪)
            {"open": base_price + 11.7, "high": base_price + 24.5, "low": base_price + 11.7, "close": base_price + 24.2},

            # 4. 第一根阴线
            {"open": base_price + 24.2, "high": base_price + 24.5, "low": base_price + 22.8, "close": base_price + 23},

            # 5. 确认阳线
            {"open": base_price + 23, "high": base_price + 24.8, "low": base_price + 22.5, "close": base_price + 24.5},

            # 6-7. 后续K线
            {"open": base_price + 24.5, "high": base_price + 25.2, "low": base_price + 24, "close": base_price + 24.8},
            {"open": base_price + 24.8, "high": base_price + 25.5, "low": base_price + 24.2, "close": base_price + 25.2},
        ]

    elif stock == "TSLA":
        # TSLA: 阴线过多失效的场景
        scenarios = [
            # 1. 正常K线
            {"open": base_price, "high": base_price + 2, "low": base_price - 1, "close": base_price + 1.5},

            # 2. 突破K线 (涨幅13%)
            {"open": base_price + 1.5, "high": base_price + 16, "low": base_price + 1.5, "close": base_price + 15.8},

            # 3-7. 连续5根阴线 (超过4根，应该失效)
            {"open": base_price + 15.8, "high": base_price + 16, "low": base_price + 14.5, "close": base_price + 15},
            {"open": base_price + 15, "high": base_price + 15.2, "low": base_price + 13.8, "close": base_price + 14.2},
            {"open": base_price + 14.2, "high": base_price + 14.5, "low": base_price + 13.2, "close": base_price + 13.5},
            {"open": base_price + 13.5, "high": base_price + 13.8, "low": base_price + 12.5, "close": base_price + 12.8},
            {"open": base_price + 12.8, "high": base_price + 13, "low": base_price + 11.8, "close": base_price + 12.2},
        ]

    elif stock == "NVDA":
        # NVDA: 正常波动，无突破
        scenarios = [
            {"open": base_price, "high": base_price + 2, "low": base_price - 1, "close": base_price + 1},
            {"open": base_price + 1, "high": base_price + 3, "low": base_price, "close": base_price + 2.5},
            {"open": base_price + 2.5, "high": base_price + 4, "low": base_price + 1.5, "close": base_price + 3.2},
            {"open": base_price + 3.2, "high": base_price + 4.5, "low": base_price + 2.8, "close": base_price + 3.8},
            {"open": base_price + 3.8, "high": base_price + 5, "low": base_price + 3.2, "close": base_price + 4.5},
            {"open": base_price + 4.5, "high": base_price + 5.8, "low": base_price + 4, "close": base_price + 5.2},
            {"open": base_price + 5.2, "high": base_price + 6, "low": base_price + 4.8, "close": base_price + 5.5},
        ]

    else:  # TEST
        # TEST: 测试突破K线后直接是阳线的失效场景
        scenarios = [
            # 1. 正常K线
            {"open": base_price, "high": base_price + 1, "low": base_price - 0.5, "close": base_price + 0.5},

            # 2. 突破K线 (涨幅12%) - 确保达到突破条件
            # 从220.5涨到244.755，涨幅 = (244.755-220.5)/220.5 = 11%
            {"open": base_price + 0.5, "high": base_price + 25, "low": base_price + 0.5, "close": base_price + 24.255},

            # 3. 直接是阳线 (形态失效！突破标识应该被删除)
            {"open": base_price + 24.255, "high": base_price + 26, "low": base_price + 24, "close": base_price + 25.5},

            # 4. 后续正常K线
            {"open": base_price + 25.5, "high": base_price + 26, "low": base_price + 25, "close": base_price + 25.2},

            # 5. 新的突破K线 (涨幅10.5%)
            # 从25.2涨到27.846，涨幅 = (247.846-225.2)/225.2 = 10.04%
            {"open": base_price + 25.2, "high": base_price + 28, "low": base_price + 25.2, "close": base_price + 27.846},

            # 6. 第一根阴线
            {"open": base_price + 27.846, "high": base_price + 28, "low": base_price + 26.5, "close": base_price + 27},

            # 7. 第二根阴线
            {"open": base_price + 27, "high": base_price + 27.2, "low": base_price + 25.8, "close": base_price + 26.2},
        ]

    return scenarios

def simulate_realtime_updates(web_stock_data):
    """模拟实时数据更新 - 包含更多突破测试场景和实时价格变化"""
    stocks = list(web_stock_data.keys())
    counter = 7  # 从已有数据后开始
    surge_cycle = 0  # 突破周期计数器
    tick_counter = 0  # 实时价格变化计数器

    # 为每个股票维护独立的counter，确保测试场景按预期执行
    stock_counters = {stock: 7 for stock in stocks}

    # 初始化模拟的交易次数数据
    # print("🔄 初始化模拟交易次数数据...")
    try:
        # 为每个股票创建模拟的交易次数数据
        for i, stock in enumerate(stocks):
            keys = key_generator.get_keys_for_stock(stock)
            # 模拟不同的交易次数，让数据更有变化
            base_transactions = 150 + i * 500  # 基础交易次数：150, 200, 250, 300, 350
            manager_res_dict_test[keys[NUMBER_FLG]] = base_transactions
            # print(f"  {stock}: {base_transactions} 次/分钟")

        # print("✅ 交易次数数据初始化完成")
    except Exception as e:
        print(f"❌ 交易次数数据初始化失败: {e}")

    while True:
        try:
            tick_counter += 1

            # 每10次tick更新交易次数数据
            if tick_counter % 10 == 0:
                try:
                    for i, stock in enumerate(stocks):
                        keys = key_generator.get_keys_for_stock(stock)
                        # 模拟交易次数的变化
                        current_value = manager_res_dict_test.get(keys[NUMBER_FLG], 150)
                        # 随机变化 ±20
                        change = random.randint(-20, 20)
                        new_value = max(50, current_value + change)  # 确保不小于50
                        manager_res_dict_test[keys[NUMBER_FLG]] = new_value
                    # print(f"🔄 更新交易次数数据 (tick: {tick_counter})")
                except Exception as e:
                    print(f"❌ 更新交易次数失败: {e}")

            # 每6次tick（3秒）生成新的K线，更频繁的K线生成
            if tick_counter % 6 == 0:
                for i, stock in enumerate(stocks):
                    # 为每个股票独立递增counter
                    stock_counters[stock] += 1
                    current_counter = stock_counters[stock]

                    # 生成递增的时间戳 (模拟5分钟K线间隔)
                    # 为了避免时间重复，使用唯一的时间生成逻辑
                    total_minutes_from_start = 30 + current_counter * 5
                    
                    # 计算从9:30开始的分钟数，确保不会重复
                    # 交易时间：9:30-11:30 (120分钟) + 13:00-15:00 (120分钟) = 240分钟/天
                    day_offset = current_counter // 48  # 每天48个5分钟K线
                    intraday_counter = current_counter % 48
                    
                    if intraday_counter < 24:  # 上午时段 9:30-11:30
                        base_minutes = 9 * 60 + 30 + intraday_counter * 5  # 从9:30开始
                    else:  # 下午时段 13:00-15:00  
                        base_minutes = 13 * 60 + (intraday_counter - 24) * 5  # 从13:00开始
                    
                    hour = base_minutes // 60
                    minutes = base_minutes % 60
                    
                    # 为了确保唯一性，如果是多天，改变日期
                    day = 23 + day_offset
                    date_time = f"202412{day:02d} {hour:02d}:{minutes:02d}:00"

                    # 获取最后一个价格作为基础
                    if web_stock_data[stock]:
                        # req_data是字典结构，获取最新时间的数据
                        latest_date = max(web_stock_data[stock].keys())
                        last_data = web_stock_data[stock][latest_date]
                        last_close = last_data["Close"]
                        last_open = last_data["Open"]
                    else:
                        last_close = 100 + i * 20
                        last_open = last_close

                    # 根据股票和周期生成不同的测试场景
                    bar_data = generate_realtime_scenario(stock, current_counter, surge_cycle, last_close, last_open, date_time)

                    # 添加新数据到字典
                    web_stock_data[stock][date_time] = bar_data

                    # 保持最多100条记录
                    if len(web_stock_data[stock]) > 100:
                        oldest_date = min(web_stock_data[stock].keys())
                        del web_stock_data[stock][oldest_date]

                # 每20次更新增加一个突破周期
                if counter % 20 == 0:
                    surge_cycle += 1
                    # print(f"🚀 开始新的突破测试周期: {surge_cycle}")
            else:
                # 更新当前K线的实时价格（模拟tick数据）
                for i, stock in enumerate(stocks):
                    if web_stock_data[stock]:
                        # 获取最新K线
                        latest_date = max(web_stock_data[stock].keys())
                        current_bar = web_stock_data[stock][latest_date].copy()

                        # 模拟实时价格变化（更大的变化幅度）
                        price_range = current_bar["High"] - current_bar["Low"]
                        base_price = current_bar["Close"]

                        # 更明显的价格变化，基于tick计数器产生波动
                        wave_factor = math.sin(tick_counter * 0.5) * 0.8  # 正弦波动，幅度更大
                        trend_factor = (tick_counter % 12 - 6) / 20  # -0.3 到 +0.3 的趋势
                        random_factor = (random.random() - 0.5) * 0.4  # -0.2 到 +0.2 的随机变化

                        # 综合变化因子
                        total_change = (wave_factor + trend_factor + random_factor) * base_price * 0.02
                        new_close = base_price + total_change

                        # 确保价格在合理范围内，但允许突破原有范围
                        min_price = current_bar["Low"] * 0.95  # 允许向下突破5%
                        max_price = current_bar["High"] * 1.05  # 允许向上突破5%
                        new_close = max(min_price, min(max_price, new_close))

                        # 更新当前K线的收盘价和最高/最低价
                        current_bar["Close"] = new_close
                        current_bar["High"] = max(current_bar["High"], new_close)
                        current_bar["Low"] = min(current_bar["Low"], new_close)

                        # 更新数据
                        web_stock_data[stock][latest_date] = current_bar

                        # 每10次tick打印一次调试信息
                        # if tick_counter % 10 == 0:
                            # print(f"📊 {stock} 实时价格更新: ${new_close:.2f} (变化: {total_change:+.2f})")

            time.sleep(0.5)  # 每0.5秒更新一次，提供更流畅的实时更新

        except Exception as e:
            print(f"❌ 实时数据更新失败: {e}")
            time.sleep(5)

def generate_realtime_scenario(stock, counter, surge_cycle, last_close, last_open, date_time):
    """生成实时测试场景数据"""

    # 为每个股票独立计算场景类型，确保测试场景按预期执行
    if stock == "GOOGL":
        # GOOGL: 突破(0) -> 阴线(1) -> 跌破失效(2) -> 正常(3+)
        scenario_type = (counter - 8) % 10  # 从counter=8开始的独立序列
        # print(f"🔍 GOOGL debug: counter={counter}, scenario_type={scenario_type}")
    elif stock == "TSLA":
        # TSLA: 突破(0) -> 阴线1(1) -> 阴线2(2) -> 阴线3(3) -> 阴线4(4) -> 阴线5失效(5) -> 正常(6+)
        scenario_type = (counter - 8) % 10  # 从counter=8开始的独立序列
        # print(f"🔍 TSLA debug: counter={counter}, scenario_type={scenario_type}")
    elif stock == "TEST":
        # TEST: 正常波动，不添加新的突破场景（测试初始失效形态）
        scenario_type = 99  # 强制使用正常波动
        # print(f"🔍 TEST debug: counter={counter}, scenario_type={scenario_type} (正常波动)")
    else:
        # AAPL、MSFT、NVDA使用原有逻辑
        scenario_type = (counter + surge_cycle * 5) % 15

    # AAPL和MSFT: 正常突破-确认模式
    if scenario_type == 0 and stock in ["AAPL", "MSFT"]:
        # 生成突破K线 (10-15%涨幅)
        surge_percent = 10 + (counter % 5)
        new_close = last_close * (1 + surge_percent / 100)
        bar_data = {
            "date_time": date_time,
            "Open": last_open if last_open else last_close,
            "High": new_close + 0.5,
            "Low": last_close,
            "Close": new_close,
            "Volume": 200000 + counter * 10000
        }
        # print(f"🚀 {stock} 生成突破K线: +{surge_percent:.1f}%")

    elif scenario_type in [1, 2, 3] and stock in ["AAPL", "MSFT"]:
        # 生成阴线
        decline = 0.5 + (scenario_type * 0.3)
        new_close = last_close - decline
        bar_data = {
            "date_time": date_time,
            "Open": last_close,
            "High": last_close + 0.2,
            "Low": new_close - 0.3,
            "Close": new_close,
            "Volume": 150000 + counter * 8000
        }
        # print(f"📉 {stock} 生成阴线 #{scenario_type}: -{decline:.1f}")

    elif scenario_type == 4 and stock in ["AAPL", "MSFT"]:
        # 生成确认阳线
        rise = 0.8 + (counter % 3) * 0.2
        new_close = last_close + rise
        bar_data = {
            "date_time": date_time,
            "Open": last_close,
            "High": new_close + 0.3,
            "Low": last_close - 0.1,
            "Close": new_close,
            "Volume": 180000 + counter * 9000
        }
        # print(f"✅ {stock} 生成确认阳线: +{rise:.1f}")

    # GOOGL: 突破后跌破最低价失效场景
    elif scenario_type == 0 and stock == "GOOGL":
        # 生成突破K线 (15%涨幅)
        surge_percent = 15
        new_close = last_close * (1 + surge_percent / 100)
        bar_data = {
            "date_time": date_time,
            "Open": last_close,
            "High": new_close + 0.5,
            "Low": last_close,  # 记录突破K线最低价
            "Close": new_close,
            "Volume": 200000 + counter * 10000
        }
        # print(f"🚀 {stock} 生成突破K线: +{surge_percent:.1f}%")

    elif scenario_type == 1 and stock == "GOOGL":
        # 第一根阴线
        decline = 1.3
        new_close = last_close - decline
        bar_data = {
            "date_time": date_time,
            "Open": last_close,
            "High": last_close + 0.2,
            "Low": new_close - 0.2,
            "Close": new_close,
            "Volume": 150000 + counter * 8000
        }
        # print(f"📉 {stock} 生成第一根阴线: -{decline:.1f}")

    elif scenario_type == 2 and stock == "GOOGL":
        # 跌破突破K线最低价的阴线 (形态失效)
        # 假设突破K线最低价约为初始价格，这里跌破它
        decline_percent = 20  # 大幅下跌，确保跌破最低价
        new_close = last_close * (1 - decline_percent / 100)
        bar_data = {
            "date_time": date_time,
            "Open": last_close,
            "High": last_close + 0.2,
            "Low": new_close - 1,  # 确保跌破突破K线最低价
            "Close": new_close,
            "Volume": 180000 + counter * 9000
        }
        # print(f"💥 {stock} 跌破突破K线最低价，形态失效: -{decline_percent:.1f}%")

    # TSLA: 阴线过多失效场景
    elif scenario_type == 0 and stock == "TSLA":
        # 生成突破K线 (13%涨幅)
        surge_percent = 13
        new_close = last_close * (1 + surge_percent / 100)
        bar_data = {
            "date_time": date_time,
            "Open": last_close,
            "High": new_close + 0.5,
            "Low": last_close,
            "Close": new_close,
            "Volume": 200000 + counter * 10000
        }
        # print(f"🚀 {stock} 生成突破K线: +{surge_percent:.1f}%")

    elif scenario_type in [1, 2, 3, 4, 5] and stock == "TSLA":
        # 连续5根阴线 (超过4根，应该失效)
        decline = 0.8 + (scenario_type * 0.2)
        new_close = last_close - decline
        bar_data = {
            "date_time": date_time,
            "Open": last_close,
            "High": last_close + 0.1,
            "Low": new_close - 0.3,
            "Close": new_close,
            "Volume": 150000 + counter * 8000
        }
        # print(f"📉 {stock} 生成阴线 #{scenario_type}/5: -{decline:.1f}")
        # if scenario_type == 5:
        #     print(f"💥 {stock} 连续5根阴线，形态失效")

    else:
        # 生成正常波动K线
        change_percent = ((counter % 10) - 5) * 0.8  # -4% 到 +4%
        new_close = last_close * (1 + change_percent / 100)

        # 确保价格合理性
        if new_close <= 0:
            new_close = last_close * 0.98

        bar_data = {
            "date_time": date_time,
            "Open": last_close,
            "High": max(last_close, new_close) + abs(change_percent) * 0.1,
            "Low": min(last_close, new_close) - abs(change_percent) * 0.1,
            "Close": new_close,
            "Volume": 120000 + counter * 6000
        }

    return bar_data

def test_initial_invalid_pattern():
    """测试图表创建时就存在的失效形态"""
    # print("\n🧪 测试图表创建时的失效形态处理...")

    # 创建TEST股票的测试数据
    base_price = 200  # TEST股票的基础价格
    test_scenarios = [
        # 1. 正常K线
        {"open": base_price, "high": base_price + 1, "low": base_price - 0.5, "close": base_price + 0.5},

        # 2. 突破K线 (涨幅11%) - 应该添加突破信号
        # 从200.5涨到222.555，涨幅 = (222.555-200.5)/200.5 = 11%
        {"open": base_price + 0.5, "high": base_price + 23, "low": base_price + 0.5, "close": base_price + 22.055},

        # 3. 直接是阳线 (形态失效！突破标识应该被删除)
        {"open": base_price + 22.055, "high": base_price + 24, "low": base_price + 21.5, "close": base_price + 23.5},

        # 4. 后续正常K线
        {"open": base_price + 23.5, "high": base_price + 24, "low": base_price + 23, "close": base_price + 23.2},
    ]

    # 转换为TradingView格式的数据
    chart_data = []
    for i, scenario in enumerate(test_scenarios):
        chart_data.append({
            'time': 1734965400 + i * 300,  # 每5分钟一个数据点
            'open': scenario['open'],
            'high': scenario['high'],
            'low': scenario['low'],
            'close': scenario['close']
        })

    # print(f"📊 创建了 {len(chart_data)} 个K线数据点")
    # print("📋 测试场景:")
    # print("  K线1: 正常K线")
    # print("  K线2: 突破K线 (+12%) - 应添加突破信号")
    # print("  K线3: 直接阳线 - 应删除K线2的突破信号")
    # print("  K线4: 正常K线")

    # 这里我们需要模拟前端的calculatePriceSurgeDetector函数
    # 由于这是Python测试，我们需要创建一个简化版本来验证逻辑
    # print("\n🔍 模拟形态分析过程...")
    signals = []

    # 模拟JavaScript逻辑
    surge_price = None
    surge_bar = None
    surge_low = None
    down_count = 0
    in_tracking = False
    waiting_down = False

    for i in range(1, len(chart_data)):
        current = chart_data[i]
        previous = chart_data[i - 1]

        # 计算价格变化百分比
        price_change_percent = ((current['close'] - previous['close']) / previous['close']) * 100

        # 判断K线类型
        is_down_candle = current['close'] < current['open']
        is_up_candle = current['close'] > current['open']

        # print(f"  K线{i+1}: 价格变化 {price_change_percent:+.1f}%, {'阴线' if is_down_candle else '阳线' if is_up_candle else '十字'}")

        # 检测突破条件
        if price_change_percent >= 10 and not in_tracking:
            surge_price = current['open']
            surge_bar = i
            surge_low = current['low']
            down_count = 0
            in_tracking = True
            waiting_down = True

            # 添加突破信号
            signals.append({
                'time': current['time'],
                'text': f'🚀{price_change_percent:.1f}%',
                'surge_low': surge_low,
                'type': 'surge'
            })
            # print(f"    ✅ 添加突破信号: 🚀{price_change_percent:.1f}%")

        # 如果在追踪状态
        elif in_tracking and waiting_down:
            if is_down_candle:
                down_count += 1
                # print(f"    📉 阴线计数: {down_count}")
            elif is_up_candle and down_count == 0:
                # 突破K线后直接是阳线，形态失效
                # print(f"    💥 直接阳线失效！删除突破信号")
                # 删除突破信号
                for j in range(len(signals) - 1, -1, -1):
                    if signals[j]['type'] == 'surge' and signals[j]['surge_low'] == surge_low:
                        del signals[j]
                        # print(f"    🗑️ 已删除突破信号")
                        break

                # 重置状态
                surge_price = None
                surge_bar = None
                surge_low = None
                down_count = 0
                in_tracking = False
                waiting_down = False

    # print(f"\n📊 最终信号数量: {len(signals)}")
    # for signal in signals:
    #     print(f"  - {signal['text']} (时间: {signal['time']})")

    # 验证结果
    if len(signals) == 0:
        # print("✅ 测试通过：失效的突破信号被正确删除")
        return True
    else:
        # print("❌ 测试失败：失效的突破信号未被删除")
        return False

def main():
    """主函数"""
    # print("=== Web服务器独立测试 ===")
    # print("功能:")
    # print("  - 模拟股票数据")
    # print("  - Web界面展示")
    # print("  - 实时数据更新")
    # print()

    # 先运行失效形态测试
    test_result = test_initial_invalid_pattern()
    # if not test_result:
    #     print("\n⚠️ 失效形态测试未通过，但继续启动Web服务器进行实际验证...")

    # 创建模拟数据
    # print("\n创建模拟股票数据...")
    web_stock_data = create_mock_data()
    # print(f"✅ 创建了 {len(web_stock_data)} 只股票的模拟数据")
# 
    # 显示测试场景说明
    # print("\n📊 测试场景说明:")
    # print("  AAPL: 完整的突破-阴线-确认模式 (应显示突破和形态确认)")
    # print("  GOOGL: 突破后跌破最低价失效场景 (应显示突破但形态失效)")
    # print("  MSFT: 连续两根突破K线场景 (应显示重置追踪)")
    # print("  TSLA: 阴线过多失效场景 (应显示突破但阴线过多失效)")
    # print("  NVDA: 正常波动无突破 (应无特殊标记)")
    # print()
    
    # 设置股票列表数据
    stock_list = list(web_stock_data.keys())
    manager_res_dict[0] = 1
    manager_res_dict_test[WEB_STOCK_LIST] = stock_list
    
    print(f"📊 设置股票列表: {stock_list}")

    # 启动Web服务器
    print("启动Web服务器...")
    try:
        from src.web.web_server import start_web_server_thread
        web_thread = start_web_server_thread(
            host='0.0.0.0',
            port=5001,
            debug=False,
            web_stock_data=web_stock_data,
            enable_websocket=True  # 启用WebSocket进行测试！
        )
        print(f"✅ Web服务器已启动: http://localhost:5001 (线程: {web_thread.name})")
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")
        return
    
    from src.utils.npm_integration import setup_npm_integration, cleanup_npm_integration
        
    # 启动NPM集成 (Vite开发服务器)
    print(f"=== [Main] 准备启动NPM集成，时间: {time.time()} ===", flush=True)
    try:
        npm_integration = setup_npm_integration()
        print(f"=== [Main] NPM集成启动完成，时间: {time.time()} ===", flush=True)
    except Exception as e:
        print(f"NPM集成启动失败: {e}")
    # 启动实时数据更新线程
    # print("启动实时数据更新...")
    update_thread = threading.Thread(
        target=simulate_realtime_updates,
        args=(web_stock_data,),
        daemon=True
    )
    update_thread.start()
    # print("✅ 实时数据更新已启动")
    
    # print()
    # print("=" * 50)
    # print("Web服务器测试已启动！")
    # print("请访问: http://localhost:5004")
    # print("按 Ctrl+C 退出测试")
    # print("=" * 50)
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n测试结束")

if __name__ == "__main__":
    main()
