#!/usr/bin/env node

const fs = require('fs');

// 从utils.js文件中读取实际的函数内容
const utilsContent = fs.readFileSync('/Users/<USER>/git/stock_xxx/src/web/static/js/modules/utils.js', 'utf8');

// 创建一个模拟的浏览器环境
global.window = {};
global.console = console;

// 提取parseDateTime函数
function extractParseDateTime() {
    const parseDateTimeMatch = utilsContent.match(/function parseDateTime\(timeStr\) \{[\s\S]*?\n\}/);
    if (parseDateTimeMatch) {
        return eval(`(${parseDateTimeMatch[0]})`);
    }
    return null;
}

// 提取fillDataGaps和所有相关辅助函数
function extractFillDataGaps() {
    try {
        // 构建完整的代码块，包含所有相关函数
        let codeBlock = '';
        
        // 提取所有辅助函数
        const helperFunctions = [
            '_detectGaps',
            '_generateFillerData', 
            '_ensureTimeContinuity',
            '_adaptiveFillStrategy',
            '_trendAwareFill',
            '_volatilityBasedFill',
            '_estimateVolume',
            '_anomalyDetection',
            '_detectCircuitBreakerPeriod',
            '_generateCircuitBreakerFill',
            '_calculateTrendFromData'
        ];
        
        for (const funcName of helperFunctions) {
            const regex = new RegExp(`function ${funcName}\\([^)]*\\)\\s*\\{[\\s\\S]*?\\n\\s*\\}(?=\\s*(?:function|$|\\n\\s*\\/\\*))`);
            const match = utilsContent.match(regex);
            if (match) {
                codeBlock += match[0] + '\n\n';
            }
        }
        
        // 提取主函数
        const mainFuncMatch = utilsContent.match(/function fillDataGaps\(data\) \{[\s\S]*?\n\}(?=\s*(?:function|$|\/\*))/);
        if (mainFuncMatch) {
            codeBlock += mainFuncMatch[0];
        }
        
        if (codeBlock) {
            // 创建函数
            eval(codeBlock);
            return fillDataGaps;
        }
        
        return null;
    } catch (error) {
        console.error('提取fillDataGaps失败:', error.message);
        return null;
    }
}

// 简化的原始版本
function fillDataGapsOriginal(data) {
    if (!data || data.length < 2) return data;
    
    const result = [];
    
    for (let i = 0; i < data.length; i++) {
        result.push(data[i]);
        
        if (i < data.length - 1) {
            const current = parseDateTime(data[i].time);
            const next = parseDateTime(data[i + 1].time);
            
            if (!current || !next) continue;
            
            let timeDiff = next - current;
            if (next < current) { // 跨日处理
                timeDiff += 24 * 60 * 60 * 1000;
            }
            
            if (timeDiff > 60000) { // 超过1分钟
                const gapsToFill = Math.floor(timeDiff / 60000) - 1;
                
                for (let j = 1; j <= gapsToFill; j++) {
                    const fillTime = new Date(current.getTime() + j * 60000);
                    const ratio = j / (gapsToFill + 1);
                    
                    const interpolatedPrice = data[i].close + (data[i + 1].open - data[i].close) * ratio;
                    const estimatedVolume = Math.max(1, Math.round((data[i].volume + data[i + 1].volume) / 10));
                    
                    result.push({
                        time: fillTime.toLocaleTimeString('en-US', { hour12: false }),
                        open: interpolatedPrice,
                        high: interpolatedPrice * (1 + Math.random() * 0.002),
                        low: interpolatedPrice * (1 - Math.random() * 0.002),
                        close: interpolatedPrice,
                        volume: estimatedVolume
                    });
                }
            }
        }
    }
    
    return result;
}

// 质量评估函数
function assessDataQuality(data, label = '') {
    if (!data || data.length === 0) {
        return { 
            score: 0, 
            issues: ['无数据'],
            details: { timeGaps: 0, ohlcErrors: 0, unreasonableVolume: 0, zeroVolume: 0, priceStagnant: 0 }
        };
    }
    
    let issues = [];
    let timeGaps = 0;
    let ohlcErrors = 0;
    let unreasonableVolume = 0;
    let zeroVolume = 0;
    let priceStagnant = 0;
    
    // 检查时间连续性
    for (let i = 1; i < data.length; i++) {
        const prevTime = parseDateTime(data[i-1].time);
        const currTime = parseDateTime(data[i].time);
        
        if (!prevTime || !currTime) continue;
        
        let timeDiff = (currTime - prevTime) / 60000; // 分钟
        
        // 跨日处理
        if (timeDiff < 0) {
            timeDiff += 24 * 60; // 加一天
        }
        
        if (timeDiff > 1.1) { // 允许微小误差
            timeGaps++;
        }
    }
    
    // 检查每个数据点
    data.forEach((point, index) => {
        const { open, high, low, close, volume } = point;
        
        // OHLC关系检查
        if (high < Math.max(open, close) || low > Math.min(open, close)) {
            ohlcErrors++;
        }
        
        // 成交量检查
        if (volume === 0) {
            zeroVolume++;
        } else if (volume < 50 && volume > 0) {
            unreasonableVolume++;
        }
        
        // 价格停滞检查
        if (open === high && high === low && low === close) {
            priceStagnant++;
        }
    });
    
    const totalIssues = timeGaps + ohlcErrors + unreasonableVolume + zeroVolume + priceStagnant;
    const score = Math.max(0, Math.round((1 - totalIssues / data.length) * 100));
    
    return {
        score,
        issues,
        details: { timeGaps, ohlcErrors, unreasonableVolume, zeroVolume, priceStagnant, totalIssues }
    };
}

// 性能测试
function performanceTest(fillFunction, data, iterations = 50) {
    const start = Date.now();
    
    for (let i = 0; i < iterations; i++) {
        fillFunction(JSON.parse(JSON.stringify(data)));
    }
    
    const end = Date.now();
    return (end - start) / iterations;
}

// 主测试函数
function runRealFunctionTest() {
    console.log('🎯 真实函数完整评估测试');
    console.log('='.repeat(60));
    
    // 提取真实函数
    const parseDateTime = extractParseDateTime();
    const fillDataGaps = extractFillDataGaps();
    
    if (!parseDateTime || !fillDataGaps) {
        console.log('❌ 无法提取函数');
        return;
    }
    
    console.log('✅ 成功提取真实函数');
    
    // 测试场景
    const scenarios = {
        normal: [
            { time: '23:30:00', open: 100, high: 100.5, low: 99.5, close: 100.2, volume: 2000 },
            { time: '23:31:00', open: 100.2, high: 100.7, low: 99.9, close: 100.4, volume: 1800 },
            { time: '23:33:00', open: 100.4, high: 101.0, low: 100.1, close: 100.8, volume: 2200 }
        ],
        
        largeGap: [
            { time: '23:30:00', open: 100, high: 100.5, low: 99.5, close: 100.2, volume: 2000 },
            { time: '23:45:00', open: 102, high: 102.5, low: 101.8, close: 102.3, volume: 3000 }
        ],
        
        circuitBreaker: [
            { time: '23:30:00', open: 100, high: 100.5, low: 99.5, close: 100.2, volume: 2000 },
            { time: '23:31:00', open: 100.2, high: 100.4, low: 100.0, close: 100.3, volume: 1800 },
            { time: '23:32:00', open: 100.3, high: 100.5, low: 100.1, close: 100.4, volume: 2200 },
            { time: '23:48:00', open: 100.4, high: 100.6, low: 100.2, close: 100.5, volume: 0 },
            { time: '23:49:00', open: 100.5, high: 100.7, low: 100.3, close: 100.6, volume: 1500 }
        ],
        
        challenging: [
            { time: '23:30:00', open: 100, high: 100.5, low: 99.7, close: 100.2, volume: 2000 },
            { time: '23:31:00', open: 100.2, high: 100.7, low: 99.9, close: 100.4, volume: 2000 },
            { time: '23:32:00', open: 100.4, high: 100.9, low: 100.1, close: 100.6, volume: 2000 },
            { time: '23:33:00', open: 100.6, high: 101.6, low: 100.1, close: 101.4, volume: 50000 },
            { time: '23:54:00', open: 102.6, high: 102.9, low: 101.8, close: 102.1, volume: 1500 },
            { time: '23:55:00', open: 102.6, high: 102.7, low: 102.5, close: 102.65, volume: 10 },
            { time: '23:56:00', open: 102.65, high: 102.75, low: 102.55, close: 102.7, volume: 10 },
            { time: '23:57:00', open: 102.7, high: 102.8, low: 102.6, close: 102.75, volume: 10 },
            { time: '00:07:00', open: 102.75, high: 102.75, low: 102.75, close: 102.75, volume: 0 }
        ]
    };
    
    console.log('\n📊 各场景对比结果');
    console.log('='.repeat(60));
    
    let totalOriginalQuality = 0;
    let totalCurrentQuality = 0;
    let totalOriginalPerf = 0;
    let totalCurrentPerf = 0;
    let qualityWins = 0;
    let qualityTies = 0;
    let qualityLosses = 0;
    
    Object.entries(scenarios).forEach(([scenarioName, testData]) => {
        console.log(`\n🧪 ${scenarioName.toUpperCase()} 场景:`);
        
        // 原始版本测试
        const originalResult = fillDataGapsOriginal(testData);
        const originalQuality = assessDataQuality(originalResult);
        const originalPerf = performanceTest(fillDataGapsOriginal, testData);
        
        // 当前版本测试
        const currentResult = fillDataGaps(testData);
        const currentQuality = assessDataQuality(currentResult);
        const currentPerf = performanceTest(fillDataGaps, testData);
        
        totalOriginalQuality += originalQuality.score;
        totalCurrentQuality += currentQuality.score;
        totalOriginalPerf += originalPerf;
        totalCurrentPerf += currentPerf;
        
        const qualityChange = currentQuality.score - originalQuality.score;
        if (qualityChange > 0) qualityWins++;
        else if (qualityChange === 0) qualityTies++;
        else qualityLosses++;
        
        console.log(`原始版本: ${originalResult.length}点, 质量${originalQuality.score}%, 性能${originalPerf.toFixed(2)}ms`);
        console.log(`当前版本: ${currentResult.length}点, 质量${currentQuality.score}%, 性能${currentPerf.toFixed(2)}ms`);
        console.log(`质量变化: ${qualityChange >= 0 ? '+' : ''}${qualityChange}%`);
        console.log(`性能变化: ${((currentPerf - originalPerf) / originalPerf * 100).toFixed(1)}%`);
        
        if (Math.abs(qualityChange) > 5) {
            console.log(`  原始问题: 间隙${originalQuality.details.timeGaps}, OHLC${originalQuality.details.ohlcErrors}, 不合理量${originalQuality.details.unreasonableVolume}, 零量${originalQuality.details.zeroVolume}, 停滞${originalQuality.details.priceStagnant}`);
            console.log(`  当前问题: 间隙${currentQuality.details.timeGaps}, OHLC${currentQuality.details.ohlcErrors}, 不合理量${currentQuality.details.unreasonableVolume}, 零量${currentQuality.details.zeroVolume}, 停滞${currentQuality.details.priceStagnant}`);
        }
    });
    
    const scenarioCount = Object.keys(scenarios).length;
    const avgOriginalQuality = totalOriginalQuality / scenarioCount;
    const avgCurrentQuality = totalCurrentQuality / scenarioCount;
    const avgOriginalPerf = totalOriginalPerf / scenarioCount;
    const avgCurrentPerf = totalCurrentPerf / scenarioCount;
    
    console.log('\n📋 综合评估结果');
    console.log('='.repeat(60));
    console.log(`质量比较: 胜${qualityWins} 平${qualityTies} 负${qualityLosses}`);
    console.log(`平均质量: 原始${avgOriginalQuality.toFixed(1)}% vs 当前${avgCurrentQuality.toFixed(1)}%`);
    console.log(`平均性能: 原始${avgOriginalPerf.toFixed(2)}ms vs 当前${avgCurrentPerf.toFixed(2)}ms`);
    console.log(`质量变化: ${((avgCurrentQuality - avgOriginalQuality) / avgOriginalQuality * 100).toFixed(1)}%`);
    console.log(`性能变化: ${((avgCurrentPerf - avgOriginalPerf) / avgOriginalPerf * 100).toFixed(1)}%`);
    
    console.log('\n🎯 最终评价');
    console.log('='.repeat(60));
    
    const qualityRatio = avgCurrentQuality / avgOriginalQuality;
    const perfRatio = avgOriginalPerf / avgCurrentPerf;
    
    if (qualityRatio >= 0.95) {
        console.log('✅ 数据质量: 优秀 (≥95%原始质量)');
    } else if (qualityRatio >= 0.90) {
        console.log('⚠️  数据质量: 良好 (90-95%原始质量)');
    } else {
        console.log('❌ 数据质量: 需改进 (<90%原始质量)');
    }
    
    if (perfRatio >= 0.8) {
        console.log('✅ 性能表现: 优秀 (≤125%原始时间)');
    } else if (perfRatio >= 0.67) {
        console.log('⚠️  性能表现: 可接受 (125-150%原始时间)');
    } else {
        console.log('❌ 性能表现: 需优化 (>150%原始时间)');
    }
    
    const overallScore = qualityRatio * 0.7 + perfRatio * 0.3;
    console.log(`\n🏆 综合评分: ${(overallScore * 100).toFixed(1)}%`);
    
    if (overallScore >= 0.9) {
        console.log('🌟 评级: 优秀 - 生产就绪');
    } else if (overallScore >= 0.8) {
        console.log('👍 评级: 良好 - 基本就绪');
    } else {
        console.log('🔧 评级: 需改进');
    }
}

// 运行测试
runRealFunctionTest();
